ARG PHP_VERSION

FROM php:${PHP_VERSION}-apache

RUN apt-get update \
    && apt-get install -y \
         libfreetype6-dev \
         libjpeg62-turbo-dev \
         libpng-dev \
         libmcrypt-dev \
         libicu-dev \
         git \
         unzip \
         bash \
    && docker-php-ext-install \
         pdo_mysql \
         mysqli \
         mbstring \
         gd \
         iconv \
         intl \
         opcache \
    # xdebugインストール
    && pecl install -f xdebug \
    && docker-php-ext-enable xdebug \
    && apt-get clean \
    # composerインストール
    && curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer \
    && composer global require hirak/prestissimo \
    && composer global require phpunit/phpunit \
    # rewriteモジュールを有効化
    && a2enmod rewrite

# ルートディレクトリを作成
RUN mkdir -p /var/www/html

# 導入企業ごとのサブディレクトリを作成する
ARG APP_CONTAINER
RUN ln -s ${APP_CONTAINER} /var/www/html/001 \
 && ln -s ${APP_CONTAINER} /var/www/html/002 \
 && ln -s ${APP_CONTAINER} /var/www/html/101 \
 && ln -s ${APP_CONTAINER} /var/www/html/999

# Setup working directory
ARG APP_CONTAINER
WORKDIR ${APP_CONTAINER}