# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    # container parameters
    container.dumper.inline_class_loader: true

    # ec-cube parameters
    env(ECCUBE_LOCALE): 'ja'
    env(ECCUBE_TIMEZONE): 'Asia/Tokyo'
    env(ECCUBE_CURRENCY): 'JPY'
    locale: '%env(ECCUBE_LOCALE)%'
    timezone: '%env(ECCUBE_TIMEZONE)%'
    currency: '%env(ECCUBE_CURRENCY)%'

services:
    # default configuration for services in *this* file
    _defaults:
        # automatically injects dependencies in your services
        autowire: true
        # automatically registers your services as commands, event subscribers, etc.
        autoconfigure: true
        # this means you cannot fetch services directly from the container via $container->get()
        # if you need to do this, you can override this setting on individual services
        public: false

        bind:
          $cartPurchaseFlow: '@eccube.purchase.flow.cart'
          $shoppingPurchaseFlow: '@eccube.purchase.flow.shopping'
          $orderPurchaseFlow: '@eccube.purchase.flow.order'
          $_orderStateMachine: '@state_machine.order'


    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    Eccube\:
        resource: '../../../src/Eccube/*'
        # you can exclude directories or files
        # but if a service is unused, it's removed anyway
        exclude: '../../../src/Eccube/{Annotation,Common,Entity,Exception,Log,Plugin,ServiceProvider,Resource,Doctrine/ORM/tools/}'

    Eccube\Common\EccubeConfig:
        public: true

    Eccube\Service\TaxRuleService:
        lazy: true
        public: true

    Eccube\Repository\TaxRuleRepository:
        lazy: true

    Eccube\Service\CartService:
        lazy: true

    Eccube\Service\SystemService:
        lazy: true
        public: true

    Eccube\Service\Composer\ComposerProcessService:
        lazy: true

    Eccube\Service\Composer\ComposerServiceInterface:
        factory: ['Eccube\Service\Composer\ComposerServiceFactory', createService]

    # controllers are imported separately to make sure they're public
    # and have a tag that allows actions to type-hint services
    Eccube\Controller\:
        resource: '../../../src/Eccube/Controller'
        tags: ['controller.service_arguments']

    Eccube\Twig\Extension\EccubeBlockExtension:
        tags: ['twig.extension']
        bind:
          $blockTemplates: '%eccube_twig_block_templates%'

    Plugin\:
        resource: '../../../app/Plugin/*'
        exclude: '../../../app/Plugin/*/{Entity,Resource,ServiceProvider,Tests,DoctrineMigrations}'

    Customize\:
        resource: '../../../app/Customize/*'
        exclude: '../../../app/Customize/{Entity,Resource,Tests}'

    Customize\Controller\:
        resource: '../../../app/Customize/Controller'
        tags: ['controller.service_arguments']
        
    Customize\Service\PurchaseFlow\Processor\CatalogPriceChangeValidator:
        public: false
        autowire: true
        decorates: Eccube\Service\PurchaseFlow\Processor\PriceChangeValidator

    twig.extension.stringloader:
        class: Twig_Extension_StringLoader
        tags: ['twig.extension']

    eccube.collector.core:
        class: Eccube\DataCollector\EccubeDataCollector
        tags:
          - { name: 'data_collector', template: '@toolbar/eccube.html.twig', id: 'eccube_core', priority: -512 }

    eccube.security.success_handler:
        class: Eccube\Security\Http\Authentication\EccubeAuthenticationSuccessHandler

    eccube.security.failure_handler:
        class: Eccube\Security\Http\Authentication\EccubeAuthenticationFailureHandler

    eccube.security.logout_success_handler:
        class: Customize\Security\Http\Authentication\EccubeLogoutSuccessHandler

    # Autowiring can't guess the constructor arguments that are not type-hinted with
    # classes (e.g. container parameters) so you must define those arguments explicitly
    # Eccube\Command\ListUsersCommand:
    #     $emailSender: '%app.notifications.email_sender%'

    # when the service definition only contains arguments, you can omit the
    # 'arguments' key and define the arguments just below the service class
    # Eccube\Twig\AppExtension:
    #     $locales: '%app_locales%'

    # Eccube\EventSubscriber\CommentNotificationSubscriber:
    #     $sender: '%app.notifications.email_sender%'

    # Eccube\EventSubscriber\RedirectToPreferredLocaleSubscriber:
    #     $locales: '%app_locales%'
    #     $defaultLocale: '%locale%'

    # needed for the 'localizeddate' Twig filter
    # Twig\Extensions\IntlExtension: ~
    eccube.logger:
        class: Eccube\Log\Logger
        arguments:
          - '@Eccube\Request\Context'
          - '@monolog.logger'
          - '@monolog.logger.front'
          - '@monolog.logger.admin'
        lazy: true
        public: true

    eccube.log.formatter.line:
        class: Monolog\Formatter\LineFormatter
        arguments:
            - "[%%datetime%%] %%channel%%.%%level_name%% [%%extra.session_id%%] [%%extra.uid%%] [%%extra.user_id%%] [%%extra.class%%:%%extra.function%%:%%extra.line%%] - %%message%% %%context%% [%%extra.http_method%%, %%extra.url%%, %%extra.ip%%, %%extra.referrer%%, %%extra.user_agent%%]\n"

    Eccube\Log\Processor\SessionProcessor:
        tags:
            - { name: monolog.processor }

    Eccube\Log\Processor\TokenProcessor:
        tags:
            - { name: monolog.processor }

    Monolog\Processor\UidProcessor:
        tags:
            - { name: monolog.processor }

    Monolog\Processor\IntrospectionProcessor:
        arguments:
            - '!php/const:Monolog\Logger::DEBUG'
            - ['Eccube\\Log', 'Psr\\Log']
        tags:
            - { name: monolog.processor }

    Symfony\Bridge\Monolog\Processor\WebProcessor:
        arguments:
          - url: REQUEST_URI
            ip: REMOTE_ADDR
            http_method: REQUEST_METHOD
            server: SERVER_NAME
            referrer: HTTP_REFERER
            # add user aegnt
            user_agent: HTTP_USER_AGENT
        tags:
            - { name: monolog.processor }
            - { name: kernel.event_listener, event: kernel.request, priority: 1024 }

    Symfony\Component\HttpFoundation\ParameterBag:

    Eccube\Twig\Extension\IgnoreRoutingNotFoundExtension:
        # Symfony\Bridge\Twig\Extension\RoutingExtensionの後に登録するため,
        # autoconfigureはfalseにし, CompilerPassで追加する.
        autoconfigure: false
