{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% set totalProductNum = get_cart_total_product_num() %}
<a class="ec-cartNaviWrap" href="{{ url('cart') }}">
    <div class="ec-cartNavi">
        <i class="ec-cartNavi__icon fas fa-shopping-cart">
            <span class="ec-cartNavi__badge">{{ totalProductNum|number_format }}</span>
        </i>
    </div>
</a>
