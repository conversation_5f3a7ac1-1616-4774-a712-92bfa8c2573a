{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
<div class="ec-headerRole">
    <div class="ec-headerRole__title">
        <div class="ec-headerTitle">
            <div class="ec-headerTitle__title">
                <h1>
                    <a href="{{ url('homepage') }}">
                        {# {{ BaseInfo.shop_name }} #}
                        {% set logoPath = get_logo_image() %}
                        {% if logoPath == '' %}
                            {% set logoPath = asset('assets/img/top/logo.png', 'catalog') %}
                        {% endif %}
                        <img src="{{ logoPath }}">
                    </a>
                </h1>
            </div>
        </div>
    </div>
</div>
