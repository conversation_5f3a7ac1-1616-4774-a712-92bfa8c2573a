{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}

{% set topicsData = get_topic_data(asset('', 'user_data')) %}

{%  if topicsData != [] %}
<div class="ec-topicRole">
    <div class="ec-role">
        <div class="ec-secHeading">
            <span class="ec-secHeading__en">TOPICS</span>
            <span class="ec-secHeading__line"></span>
            <span class="ec-secHeading__ja">おすすめ情報</span>
        </div>

        <div class="ec-topicRole__list">
        {% for topic in topicsData %}
                <div class="ec-topicRole__listItem">
                    <a href="{{ topic['url'] }}" target="_blank">
                        {% if check_topics_img(asset(topic['img'], 'user_data')) %}
                            <img src="{{ file_update_time(asset(topic['img'], 'user_data')) }}">
                        {% else %}
                            <img src="{{ file_update_time(asset('no_image_topics.png', 'save_image')) }}">
                        {% endif %}
                    </a>
                    <p class="ec-topicRole__listItemTitle">{{ topic['title'] }}</p>
                </div>
            {% if topic['line'] % 2 == 0 %}
            </div>
            <div class="ec-topicRole__list">
            {% endif %}
        {% endfor %}
        </div>
    </div>
</div>

{% endif %}