{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% extends 'default_frame.twig' %}

{% set body_class = 'product_page' %}

{% block stylesheet %}
    <style>
        .slick-slider {
            margin-bottom: 30px;
        }

        .slick-dots {
            position: absolute;
            bottom: -45px;
            display: block;
            width: 100%;
            padding: 0;
            list-style: none;
            text-align: center;
        }

        .slick-dots li {
            position: relative;
            display: inline-block;
            width: 20px;
            height: 20px;
            margin: 0 5px;
            padding: 0;

            cursor: pointer;
        }

        .slick-dots li button {
            font-size: 0;
            line-height: 0;
            display: block;
            width: 20px;
            height: 20px;
            padding: 5px;
            cursor: pointer;
            color: transparent;
            border: 0;
            outline: none;
            background: transparent;
        }

        .slick-dots li button:hover,
        .slick-dots li button:focus {
            outline: none;
        }

        .slick-dots li button:hover:before,
        .slick-dots li button:focus:before {
            opacity: 1;
        }

        .slick-dots li button:before {
            content: " ";
            line-height: 20px;
            position: absolute;
            top: 0;
            left: 0;
            width: 12px;
            height: 12px;
            text-align: center;
            opacity: .25;
            background-color: black;
            border-radius: 50%;

        }

        .slick-dots li.slick-active button:before {
            opacity: .75;
            background-color: black;
        }

        .slick-dots li button.thumbnail img {
            width: 0;
            height: 0;
        }


        .slick-track {
          display: table;
        }

        .slide-item {
          max-height: 460px;
        }
        .slide-item__flex{
          display: flex !important;
        }

        .slide-item__image{
          width: 100%;
          height: auto;
          max-height: 400px;
          vertical-align: middle;
        }

        .slide-item__object-fit {
          object-fit: contain;
        }

        .slideThumb {
          display: inherit;
        }
        .slideThumb img {
          object-fit: contain;
          width: 90% !important;
        }

        .ec-numberInput select {
          -webkit-appearance: menulist;
          display: inline;
          width: auto;
          padding-left: 0px;
          padding-right: 24px;
        }

    </style>
{% endblock %}

{% block javascript %}
<script>
eccube.classCategories = {{ class_categories_as_json(Product)|raw }};

        // 規格2に選択肢を割り当てる。
        function fnSetClassCategories(form, classcat_id2_selected) {
            var $form = $(form);
            var product_id = $form.find('input[name=product_id]').val();
            var $sele1 = $form.find('select[name=classcategory_id1]');
            var $sele2 = $form.find('select[name=classcategory_id2]');
            eccube.setClassCategories($form, product_id, $sele1, $sele2, classcat_id2_selected);
        }

        {% if form.classcategory_id2 is defined %}
        fnSetClassCategories(
            $('#form1'), {{ form.classcategory_id2.vars.value|json_encode|raw }}
        );
        {% elseif form.classcategory_id1 is defined %}
        eccube.checkStock($('#form1'), {{ Product.id }}, {{ form.classcategory_id1.vars.value|json_encode|raw }}, null);
        {% endif %}
    </script>
    <script>
        $(function() {
            // bfcache無効化
            $(window).bind('pageshow', function(event) {
                if (event.originalEvent.persisted) {
                    location.reload(true);
                }
            });

            $('.item_visual').slick({
                dots: false,
                arrows: false,
                responsive: [{
                    breakpoint: 768,
                    settings: {
                        dots: true
                    }
                }]
            });

            $('.slideThumb').on('click', function() {
                var index = $(this).attr('data-index');
                $('.item_visual').slick('slickGoTo', index, false);
            });

            let timer = false;
            $(window).resize(function (){
                if (timer !== false) {
                    clearTimeout(timer);
                }
                timer = setTimeout(function() {
                    let userAgent = window.navigator.userAgent;
                    if (userAgent.indexOf('MSIE') === -1
                    && userAgent.indexOf('Msie') === -1
                    && userAgent.indexOf('Trident') === -1
                    && userAgent.indexOf('Trident') === -1) {
                        // IE以外のブラウザでアクセス
                        resizeSlideArea();
                    } else {
                        // IEの場合、表示がおかしくなるためブラウザ更新
                        // location.reload();
                        resizeSlideAreaIe();
                    }

                    // リサイズが終了した時点で行う処理または関数を記述
                }, 200);
            });
        });

        // ロード時のブラウザ判定
        let userAgent = window.navigator.userAgent;
        if (userAgent.indexOf('MSIE') === -1
        && userAgent.indexOf('Msie') === -1
        && userAgent.indexOf('Trident') === -1
        && userAgent.indexOf('Trident') === -1) {
            // IE以外のブラウザでアクセス
            $('.slide-item img').removeClass('slide-item__object-fit')
            $('.slide-item img').addClass('slide-item__object-fit')
            resizeSlideArea();
        } else {
            // IEの場合、表示がおかしくなるためブラウザ更新
            // location.reload();
            $('.slide-item img').removeClass('slide-item__object-fit')
            $('.slide-item img').addClass('slide-item__object-fit')
            $('.slide-item').removeClass('slide-item__flex')
            resizeSlideAreaIe();
        }

        // 画像を正方形の枠内に収める
        function resizeSlideArea (){
           const width = $('.slide-item').width();
           $('.slide-item').height(width);

           $('.slideThumb').each(function (){
               const width = $(this).width();
               $(this).height(width);
           })

        }

        // 画像を正方形の枠内に収める
        function resizeSlideAreaIe (){
           const width = $('.slide-item').width();

           $('.slideThumb').each(function (){
               const width = $(this).width();
               $(this).height(width);
           })

        }
    </script>
    <script>
        $(function() {
            $('.add-cart').on('click', function(event) {
                // 単位リストのチェック
                var unit = $('#unit_list');
                if (unit != null) {
                    if (unit.val() == "dummy") {
                    alert("計り商品であるため、単位を選択してください。");
                    unit.focus().select();
                    return false;
                    }
                }

                {% if form.classcategory_id1 is defined %}
                // 規格1フォームの必須チェック
                if ($('#classcategory_id1').val() == '__unselected' || $('#classcategory_id1').val() == '') {
                    $('#classcategory_id1')[0].setCustomValidity('{{ 'front.product.product_class_unselected'|trans }}');
                    return true;
                } else {
                    $('#classcategory_id1')[0].setCustomValidity('');
                }
                {% endif %}

                {% if form.classcategory_id2 is defined %}
                // 規格2フォームの必須チェック
                if ($('#classcategory_id2').val() == '__unselected' || $('#classcategory_id2').val() == '') {
                    $('#classcategory_id2')[0].setCustomValidity('{{ 'front.product.product_class_unselected'|trans }}');
                    return true;
                } else {
                    $('#classcategory_id2')[0].setCustomValidity('');
                }
                {% endif %}

                // 個数フォームのチェック
                if(!isCorrectValue($('#quantity'))) return false;

                if ($('#quantity').val() < 1) {
                    $('#quantity')[0].setCustomValidity('{{ 'front.product.invalid_quantity'|trans }}');
                    return true;
                } else {
                    $('#quantity')[0].setCustomValidity('');
                }

                event.preventDefault();
                $form = $('#form1');
                $.ajax({
                    url: $form.attr('action'),
                    type: $form.attr('method'),
                    data: $form.serialize(),
                    dataType: 'json',
                    beforeSend: function(xhr, settings) {
                        // Buttonを無効にする
                        $('.add-cart').prop('disabled', true);
                    }
                }).done(function(data) {
                    // レスポンス内のメッセージをalertで表示
                    $.each(data.messages, function() {
                        $('#ec-modal-header').html(this);
                    });

                    $('#ec-modal-checkbox').prop('checked', true);

                    // カートブロックを更新する
                    $.ajax({
                        url: "{{ url('block_cart') }}",
                        type: 'GET',
                        dataType: 'html'
                    }).done(function(html) {
                        $('.ec-headerRole__cart').html(html);
                    });
                }).fail(function(data) {
                    alert('{{ 'front.product.add_cart_error'|trans }}');
                }).always(function(data) {
                    // Buttonを有効にする
                    $('.add-cart').prop('disabled', false);
                });
            });
        });

function isCorrectValue(quantityObj) {
    var num = quantityObj.val()

    const maxValue = 9999;
    const minValue = 0;

    // 正規表現で数値判定
    const pattern = /^\d+$/;
    const isNumber = pattern.test(num);

    // 数値ではない場合
    if (!isNumber) {
        alert('発注数量は整数で入力してください。');
        quantityObj.focus().select();
        return false;
    }

    const valueNum = Number(num);

    // 規定値以下
    if (valueNum < minValue) {
        alert('発注数量は0以上の値を入力してください。');
        quantityObj.focus().select();
        return false;
    }

    // 規定値以上
    if (valueNum > maxValue) {
        alert('発注数量は9,999以下の値を入力してください。');
        quantityObj.focus().select();
        return false;
    }

    return true;
 }

  window.onload = function() {
    // ケース/バラリストにオプションを追加
    var select = document.getElementsByClassName("unit_list");
    var cout = select.length;
    // ケース/バラリストを作成
    let unitList = [
        {
            "unit_code": "1",
            "unit_name": "バラ",
        },
        {
            "unit_code": "2",
            "unit_name": "ケース",
        },
    ];
    for (var i = 0;i < cout;i++) {
        option = document.createElement("option");
        if (unitList) {
            for (var j = 0; j < unitList.length; j++) {
                option = document.createElement("option");
                option.value = unitList[j]['unit_code'];
                option.text = unitList[j]['unit_name'];
                select[i].appendChild(option);
            }
        }
    }

};
</script>
{% endblock %}

{% block main %}
    {% set smartProductData = get_single_product_info(Product,
        app.request.cookies.get('SmartOrderKey_customerCode'),
        app.request.cookies.get('SmartOrderKey_orderCode'),
        app.request.cookies.get('SmartOrderKey_useOrderCode'),
        app.request.cookies.get('SmartOrderKey_closeTime'))
    %}

    {% set smartPriceType = app.request.cookies.get('SmartOrderKey_terminal_price_type') %}
    {% set isBusinessMan = app.request.cookies.get('SmartOrderKey_isBusinessman') %}

    {% set smartData = get_smart_product_data(smartProductData, Product.getCodeMin) %}

    <div class="ec-productRole">
        <div class="ec-grid3">
            <div class="ec-grid3__cell">
                <div class="ec-sliderItemRole">

                    <div class="item_visual">
                        {% set imgList = get_product_images(smartData.code) %}
                        {% for ProductImage in imgList %}
                            <div class="slide-item slide-item__flex">
                                <img src="{{ asset(ProductImage, 'save_image') }}" class="slide-item__image" />
                            </div>
                        {% else %}
                            <div class="slide-item"><img src="{{ asset(''|no_image_product, 'save_image') }}"/></div>
                        {% endfor %}
                    </div>
                    <div class="item_nav">
                        {% for ProductImage in imgList %}
                            <div class="slideThumb" data-index="{{ loop.index0 }}">
                                <img class="ec-shelfGrid__item-image"
                                   style="background-image: url({{ asset(ProductImage, 'save_image') }});
                                           background-size: contain;
                                           background-repeat: no-repeat;
                                           background-position: center;">
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="ec-grid3__cell2">
                <div class="ec-productRole__profile">
                    {# 商品名 #}
                    <div class="ec-productRole__title">
                        <h2 class="ec-headingTitle">{{ trim_space(Product.name) }}</h2>
                        <p>
                            {% if smartData.order_division_name != '' %}
                                <span class="label order-division--{{ smartData.order_division_code }}" style="margin-right: 10px;">
                                    {{ smartData.order_division_name }}
                                </span>
                            {% endif %}
                            {% if smartData.in_net_order %}
                                <span class="label in-smart-order">
                                    Net Order
                                </span>
                            {% endif %}
                        </p>
                    </div>
                    {# タグ #}
                    <ul class="ec-productRole__tags">
                        {% for Tag in Product.Tags %}
                            <li class="ec-productRole__tag tag_{{ Tag.id }}">{{ Tag }}</li>
                        {% endfor %}
                    </ul>
                    {# 販売価格 #}
                    {% if smartPriceType == 1 or isBusinessMan == 1 %}
                    <div class="ec-productRole__price">
                        <div class="ec-price">
                            {% if smartData.price != 0 or smartData.case_price != 0 %}
                                {% if smartData.case_separately == 1 and smartData.price != 0 and smartData.case_price != 0 %}
                                    <span class="ec-price__price">バラ：{{ smartData.price|price }}&nbsp;／&nbsp;ケース：{{ smartData.case_price|price }}</span>
                                {% elseif smartData.case_separately == 2 and smartData.case_price != 0 %}
                                    <span class="ec-price__price">{{ smartData.case_price|price }}</span>
                                {% elseif smartData.case_separately == 3 and smartData.price != 0 %}
                                    <span class="ec-price__price">{{ smartData.price|price }}</span>
                                {% endif %}
                                <span class="ec-price__tax">税抜</span>
                                    {% if smartData.price_switch_day != '' %}
                                    <span style="color:red; font-size: 0.5em;">({{ smartData.price_switch_day }}価格改定予定)</span>
                                    {% endif %}
                            {% else %}
                                <span class="ec-price__price">￥ －</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    {# 商品コード #}
                    <div class="ec-productRole__code">
                        <div>{{ 'front.product.code'|trans }}： <span class="product-code-default">{{ smartData.code }}</span></div>
                        <div>規格：{{ smartData.standard }}</div>
                        <div>入数：{{ smartData.in_number | number_format }}</div>
                        <div>単位：{{ smartData.unit_name }}</div>
                        <div>{{ app.request.cookies.get('SmartOrderKey_orderDivision[0]') }}</div>
                        {% if smartData.lead_time < 0 %}
                            <div>最短納品予定日：-</div>
                        {% else %}
                            <div>最短納品予定日：{{ smartData.lead_time }}営業日</div>
                        {% endif %}
                        {% if smartData.case_separately == 1 %}
                            <div>発注単位：バラ/ケース</div>
                        {% elseif smartData.case_separately == 2 %}
                            <div>発注単位：ケース</div>
                        {% elseif smartData.case_separately == 3 %}
                            <div>発注単位：バラ</div>
                        {% endif %}
                    </div>
                    {# 関連カテゴリ #}
                    {% if Product.ProductCategories is not empty %}
                        <div class="ec-productRole__category">
                            <div>{{ 'front.product.related_category'|trans }}</div>
                            {% for ProductCategory in Product.ProductCategories %}
                                <ul>
                                    <li>
                                        {% for Category in ProductCategory.Category.path %}
                                            <a href="{{ url('product_list') }}?category_id={{ Category.id }}">{{ Category.name }}</a> {%- if loop.last == false %}
                                            <span>＞</span>{% endif -%}
                                        {% endfor %}
                                    </li>
                                </ul>
                            {% endfor %}
                        </div>
                    {% endif %}

                    {% if smartPriceType == 1 and isBusinessMan == 0 %}
                    <form action="{{ url('product_add_cart', {id:Product.id}) }}" method="post" id="form1" name="form1">
                        {% if Product.stock_find %}
                            <div class="ec-productRole__actions">
                                {% if form.classcategory_id1 is defined %}
                                    <div class="ec-select">
                                        {{ form_widget(form.classcategory_id1) }}
                                        {{ form_errors(form.classcategory_id1) }}
                                    </div>
                                    {% if form.classcategory_id2 is defined %}
                                        <div class="ec-select">
                                            {{ form_widget(form.classcategory_id2) }}
                                            {{ form_errors(form.classcategory_id2) }}
                                        </div>
                                    {% endif %}
                                {% endif %}
                                <div class="form-inline">
                                    <div class="ec-numberInput"><span>{{ 'common.quantity'|trans }}</span>
                                        {{ form_widget(form.quantity,{'attr': {'pattern': '\\d*', 'inputmode': 'numeric', 'maxlength': '4', 'max': '1000'}}) }}
                                        {{ form_errors(form.quantity) }}
                                        {% if smartData.case_separately == 1 %}
                                            <select name="unit_list" id="unit_list" class="unit_list form-control"></select>
                                        {% elseif smartData.case_separately == 2 %}
                                            ケース
                                        {% else %}
                                            {{ smartData.unit_name }}
                                        {% endif %}

                                        <div class="ec-productRole__smart-num smart-order-num__area">数量(NetOrder):
                                            <span class="smart-order-num smart-order-num-text" data-code="{{ smartData.code }}"></span>
                                            {% if smartData.case_separately == 1 %}
                                                <span class="smart-order-num smart-order-unit-text" data-code="{{ smartData.code }}" data-unit-name="{{ smartData.unit_name }}"></span>
                                            {% elseif smartData.case_separately == 2 %}
                                                ケース
                                            {% else %}
                                                {{ smartData.unit_name }}
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="ec-productRole__btn">
                                <button type="submit" class="ec-blockBtn--action add-cart">
                                    {{ 'front.product.add_cart'|trans }}
                                </button>
                            </div>
                        {% else %}
                            <div class="ec-productRole__btn">
                                <button type="button" class="ec-blockBtn--action" disabled="disabled">
                                    {{ 'front.product.out_of_stock'|trans }}
                                </button>
                            </div>
                        {% endif %}

                        {{ form_rest(form) }}
                    </form>

                    <div class="ec-modal">
                        <input type="checkbox" id="ec-modal-checkbox" class="checkbox">
                        <div class="ec-modal-overlay">
                            <label for="ec-modal-checkbox" class="ec-modal-overlay-close"></label>
                            <div class="ec-modal-wrap">
                                <label for="ec-modal-checkbox" class="ec-modal-close"><span class="ec-icon"><img src="{{ asset('assets/icon/cross-dark.svg') }}" alt=""/></span></label>
                                <div id="ec-modal-header" class="text-center">{{ 'front.product.add_cart_complete'|trans }}</div>
                                <div class="ec-modal-box">
                                    <div class="ec-role">
                                        <label for="ec-modal-checkbox" class="ec-inlineBtn--cancel">{{ 'front.product.continue'|trans }}</label>
                                        <a href="{{ url('cart') }}" class="ec-inlineBtn--action">{{ 'common.go_to_cart'|trans }}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if smartPriceType == 0 or isBusinessMan == 1 %}
                        <div style="margin-bottom: 1rem;"> </div>
                    {% endif %}

                    {% if BaseInfo.option_favorite_product %}
                        <form action="{{ url('product_add_favorite', {id:Product.id}) }}" method="post">
                            <div class="ec-productRole__btn">
                                {% if is_favorite == false %}
                                    <button type="submit" id="favorite" class="ec-blockBtn--cancel">
                                        {{ 'front.product.add_favorite'|trans }}
                                    </button>
                                {% else %}
                                    <button type="submit" id="favorite" class="ec-blockBtn--cancel"
                                            disabled="disabled">{{ 'front.product.add_favorite_alrady'|trans }}
                                    </button>
                                {% endif %}
                            </div>
                        </form>
                    {% endif %}
                    <div class="ec-productRole__description">
                        {{ Product.description_detail|raw|nl2br }}
                    </div>
                    <br>
                    {% if Product.free_area %}
                        <div class="ec-productRole__description">
                            {{ Product.free_area|raw|nl2br }}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

    </div>

{% endblock %}
