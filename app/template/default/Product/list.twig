{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% extends 'default_frame.twig' %}

{% set body_class = 'product_page' %}

{% block javascript %}
<script>
eccube.productsClassCategories = {
{% for Product in pagination %}
"{{ Product.id|escape('js') }}": {{ class_categories_as_json(Product)|raw }}{% if loop.last == false %}, {% endif %}
{% endfor %}
};

$(function() {

// 表示件数を変更
$('.disp-number').change(function() {
    var dispNumber = $(this).val();
    $('#disp_number').val(dispNumber);
    $('#pageno').val(1);
    $("#form1").submit();
});

// 並び順を変更
$('.order-by').change(function() {
    var orderBy = $(this).val();
    $('#orderby').val(orderBy);
    $('#pageno').val(1);
    $("#form1").submit();
});

$('.add-cart').on('click', function(e) {
    var $form = $(this).parents('li').find('form');

    // 個数フォームのチェック
    var $quantity = $form.parent().find('.quantity');

    if(!isCorrectValue($quantity)) return false;

    if ($quantity.val() < 1) {
        $quantity[0].setCustomValidity('{{ 'front.product.invalid_quantity'|trans }}');
        setTimeout(function() {
            loadingOverlay('hide');
        }, 100);
        return true;
    } else {
        $quantity[0].setCustomValidity('');
    }

    // 単位リストのチェック
    var unit = $form.parent().find('.unit_list');
    if (unit != null) {
        const test = unit.val();
        if (unit.val() == "dummy") {
        alert("計り商品であるため、単位を選択してください。");
        unit.focus().select();
        return false;
        }
    }

    e.preventDefault();
    $.ajax({
        url: $form.attr('action'),
        type: $form.attr('method'),
        data: $form.serialize(),
        dataType: 'json',
        beforeSend: function(xhr, settings) {
            // Buttonを無効にする
            $('.add-cart').prop('disabled', true);
        }
    }).done(function(data) {
        // レスポンス内のメッセージをalertで表示
        $.each(data.messages, function() {
            $('#ec-modal-header').html(this);
        });

        $('#ec-modal-checkbox').prop('checked', true);

        // カートブロックを更新する
        $.ajax({
            url: '{{ url('block_cart') }}',
            type: 'GET',
            dataType: 'html'
        }).done(function(html) {
            $('.ec-headerRole__cart').html(html);
        });
    }).fail(function(data) {
        alert('{{ 'front.product.add_cart_error'|trans }}');
    }).always(function(data) {
        // Buttonを有効にする
        $('.add-cart').prop('disabled', false);
    });
});
});

function isCorrectValue(quantityObj) {
    var num = quantityObj.val()

    const maxValue = 9999;
    const minValue = 0;

    // 正規表現で数値判定
    const pattern = /^\d+$/;
    const isNumber = pattern.test(num);

    // 数値ではない場合
    if (!isNumber) {
        alert('発注数量は整数で入力してください。');
        quantityObj.focus().select();
        return false;
    }

    const valueNum = Number(num);

    // 規定値以下
    if (valueNum < minValue) {
        alert('発注数量は0以上の値を入力してください。');
        quantityObj.focus().select();
        return false;
    }

    // 規定値以上
    if (valueNum > maxValue) {
        alert('発注数量は9,999以下の値を入力してください。');
        quantityObj.focus().select();
        return false;
    }

    return true;
 }
 window.onload = function() {
    // ケース/バラリストにオプションを追加
    var select = document.getElementsByClassName("unit_list");
    var cout = select.length;
    // ケース/バラリストを作成
    let unitList = [
        {
            "unit_code": "1",
            "unit_name": "バラ",
        },
        {
            "unit_code": "2",
            "unit_name": "ケース",
        },
    ];
    for (var i = 0;i < cout;i++) {
        option = document.createElement("option");
        if (unitList) {
            for (var j = 0; j < unitList.length; j++) {
                option = document.createElement("option");
                option.value = unitList[j]['unit_code'];
                option.text = unitList[j]['unit_name'];
                select[i].appendChild(option);
            }
        }
    }

};

</script>
{% endblock %}

{% block main %}
    {% set smartProductData = get_products_info(pagination,
        app.request.cookies.get('SmartOrderKey_customerCode'),
        app.request.cookies.get('SmartOrderKey_orderCode'),
        app.request.cookies.get('SmartOrderKey_useOrderCode'),
        app.request.cookies.get('SmartOrderKey_closeTime')) %}

    {% set smartPriceType = app.request.cookies.get('SmartOrderKey_terminal_price_type') %}
    {% set isBusinessMan = app.request.cookies.get('SmartOrderKey_isBusinessman') %}

    {% if search_form.category_id.vars.errors|length > 0 %}
        <div class="ec-searchnavRole">
            <p class="errormsg text-danger">{{ 'front.product.search__category_not_found'|trans }}</p>
        </div>
    {% else %}
        <div class="ec-searchnavRole">
            <form name="form1" id="form1" method="get" action="?">
                {% for item in search_form %}
                    <input type="hidden" id="{{ item.vars.id }}"
                           name="{{ item.vars.full_name }}"
                           {% if item.vars.value is not empty %}value="{{ item.vars.value }}" {% endif %}/>
                {% endfor %}
            </form>
            <div class="ec-searchnavRole__topicpath">
                <ol class="ec-topicpath">
                    <li class="ec-topicpath__item"><a href="{{ url('product_list') }}">{{ 'front.product.all_category'|trans }}</a>
                    </li>
                    {% if Category is not null %}
                        {% for Path in Category.path %}
                            <li class="ec-topicpath__divider">|</li>
                            <li class="ec-topicpath__item{% if loop.last %}--active{% endif %}"><a
                                        href="{{ url('product_list') }}?category_id={{ Path.id }}">{{ Path.name }}</a>
                            </li>
                        {% endfor %}
                    {% endif %}
                    {% if search_form.vars.value and search_form.vars.value.name %}
                        <li class="ec-topicpath__divider">|</li>
                        <li class="ec-topicpath__item">{{ 'front.product.search_result__keyword'|trans({ '%name%': search_form.vars.value.name }) }}</li>
                    {% endif %}
                </ol>
            </div>
            <div class="ec-searchnavRole__infos">
                <div class="ec-searchnavRole__counter">
                    {% if pagination.totalItemCount > 0 %}
                        {{ 'front.product.search_result__detail'|trans({ '%count%': pagination.totalItemCount })|raw }}
                    {% else %}
                        <span>{{ 'front.product.search__product_not_found'|trans }}</span>
                    {% endif %}
                </div>
                {% if pagination.totalItemCount > 0 %}
                    <div class="ec-searchnavRole__actions">
                        <div class="ec-select">
                            {{ form_widget(disp_number_form, {'id': '', 'attr': {'class': 'disp-number'}}) }}
                            {{ form_widget(order_by_form, {'id': '', 'attr': {'class': 'order-by'}}) }}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
        {% if pagination.totalItemCount > 0 %}
            <div class="ec-shelfRole">
                <ul class="ec-shelfGrid">
                    {% for Product in pagination %}
                        {% set smartData = get_smart_product_data(smartProductData, Product.getCodeMin) %}

                        <li class="ec-shelfGrid__item">
                            <a href="{{ url('product_detail', {'id': Product.id}) }}">
                                {% set product_image_url = get_product_top_image(smartData.code) %}
                                {% if product_image_url == '' %}
                                    {% set product_image_url = asset(''|no_image_product, 'save_image') %}
                                {% endif %}
                                <p class="ec-shelfGrid__item-image"
                                   style="background-image: url({{ product_image_url }});
                                            background-size: contain;
                                            background-repeat: no-repeat;
                                            background-position: center;">
                                </p>
                                <p class="ec-shelfGrid__item-header">
                                    <span class="ec-shelfGrid__item-header--code">
                                        {{ smartData.code }}
                                    </span>
                                    <span class="flex-right">
                                    {% if smartData.in_net_order != '' %}
                                        <span class="label in-smart-order">
                                        Net Order
                                        </span>
                                    {% endif %}

                                    {% if smartData.order_division_name != '' %}
                                        <span class="label order-division--{{ smartData.order_division_code }}">
                                            {{ smartData.order_division_name }}
                                        </span>
                                    {% endif %}
                                    </span>
                                </p>
                                <p class="ec-shelfGrid__item-title">{{ trim_space(Product.name) }}</p>
                                <div class="ec-shelfGrid__item-detail">
{#                                    <div>{{ 'front.product.code'|trans }}： <span class="product-code-default">{{ smartData.code }}{% if Product.code_min != Product.code_max %} ～ {{ Product.code_max }}{% endif %}</span></div>#}
                                    <div>規格：{{ smartData.standard }}</div>
                                    <div>入数：{{ smartData.in_number | number_format }}</div>
                                    <div>単位：{{ smartData.unit_name }}</div>
                                    {% if smartData.lead_time < 0 %}
                                        <div>最短納品予定日：-</div>
                                    {% else %}
                                        <div>最短納品予定日：{{ smartData.lead_time }}営業日</div>
                                    {% endif %}
                                    {% if smartData.case_separately == 1 %}
                                        <div>発注単位：バラ/ケース</div>
                                    {% elseif smartData.case_separately == 2 %}
                                        <div>発注単位：ケース</div>
                                    {% elseif smartData.case_separately == 3 %}
                                        <div>発注単位：バラ</div>
                                    {% endif %}
                                    {% if smartPriceType == 1 or isBusinessMan == 1 %}
                                        {% if smartData.case_separately == 1 %}
                                        <div class="price02-default">
                                            バラ単価：
                                            {% if smartData.price != 0 %}
                                                {{ smartData.price|price }} (税抜)
                                            {% else %}
                                                ￥ －
                                            {% endif %}
                                        </div>
                                        <div class="price02-default">
                                        {% endif %}
                                        {% if smartData.case_separately == 1 %}
                                            ケース単価：
                                            {% if smartData.case_price != 0 %}
                                                {{ smartData.case_price|price }} (税抜)
                                            {% else %}
                                                ￥ －
                                            {% endif %}
                                        </div>
                                        {% elseif smartData.case_separately == 2 %}
                                        <div class="price02-default">
                                            単価：
                                            {% if smartData.case_price != 0 %}
                                                {{ smartData.case_price|price }} (税抜)
                                            {% else %}
                                                ￥ －
                                            {% endif %}
                                        </div>
                                        <br/>
                                        {% elseif smartData.case_separately == 3 %}
                                        <div class="price02-default">
                                            単価：
                                            {% if smartData.price != 0 %}
                                                {{ smartData.price|price }} (税抜)
                                            {% else %}
                                                ￥ －
                                            {% endif %}
                                        </div>
                                        <br/>
                                        {% endif %}
                                    <div>
                                        {% if smartData.price != 0 %}
                                            {% if smartData.price_switch_day != '' %}
                                                <span style="color:red; font-size: 0.9em;">({{ smartData.price_switch_day }}価格改定予定)</span>
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                </div>
                            </a>

                            {% if smartPriceType == 1 and isBusinessMan == 0 %}
                            {% if Product.stock_find %}
                                {% set form = forms[Product.id] %}
                                <form name="form{{ Product.id }}" id="productForm{{ Product.id }}" action="{{ url('product_add_cart', {id:Product.id}) }}" method="POST">
                                    <div class="ec-productRole__actions ec-shelfGrid__item-detail">
                                        <div class="form-inline">
                                            <span class="ec-numberInput ec-shelfGrid__item--smart-num"><span>{{ 'common.quantity'|trans }}</span>：</span>
                                            <div class="ec-numberIndention"></div>
                                            <span class="ec-numberInput ec-shelfGrid__item--smart-num">
                                                {{ form_widget(form.quantity,{'attr': {'class': 'quantity', 'pattern': '\\d*', 'inputmode': 'numeric', 'maxlength': '4', 'max': '9999'}}) }}
                                                {{ form_errors(form.quantity) }}
                                            </span>
                                            {% if smartData.case_separately == 1 %}
                                                <select name="unit_list" id="unit_list{{ Product.id }}" class="unit_list form-control ec-unitList"></select>
                                            {% elseif smartData.case_separately == 2 %}
                                                ケース
                                            {% else %}
                                                {{ smartData.unit_name }}
                                            {% endif %}
                                        </div>
                                        <div class="ec-shelfGrid__item--smart-num smart-order-num__area">
                                            数量(NetOrder): <span class="smart-order-num smart-order-num-text" data-code="{{ smartData.code }}"></span>
                                            {% if smartData.case_separately == 1 %}
                                                <span class="smart-order-num smart-order-unit-text" data-code="{{ smartData.code }}" data-unit-name="{{ smartData.unit_name }}"></span>
                                            {% elseif smartData.case_separately == 2 %}
                                                ケース
                                            {% else %}
                                                {{ smartData.unit_name }}
                                            {% endif %}
                                        </div>
                                    </div>
                                    {{ form_rest(form) }}
                                </form>
                                <div class="ec-productRole__btn">
                                    <button type="submit" class="ec-blockBtn--action add-cart" data-cartid="{{ Product.id }}" form="productForm{{ Product.id }}">
                                        {{ 'front.product.add_cart'|trans }}
                                    </button>
                                </div>
                            {% else %}
                                <div class="ec-productRole__btn">
                                    <button type="button" class="ec-blockBtn--action" disabled="disabled">
                                        {{ 'front.product.out_of_stock'|trans }}
                                    </button>
                                </div>
                            {% endif %}
                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>
            </div>
            <div class="ec-modal">
                <input type="checkbox" id="ec-modal-checkbox" class="checkbox">
                <div class="ec-modal-overlay">
                    <label for="ec-modal-checkbox" class="ec-modal-overlay-close"></label>
                    <div class="ec-modal-wrap">
                        <label for="ec-modal-checkbox" class="ec-modal-close"><span class="ec-icon"><img src="{{ asset('assets/icon/cross-dark.svg') }}" alt=""/></span></label>
                        <div id="ec-modal-header" class="text-center">{{ 'front.product.add_cart_complete'|trans }}</div>
                        <div class="ec-modal-box">
                            <div class="ec-role">
                                <label for="ec-modal-checkbox" class="ec-inlineBtn--cancel">{{ 'front.product.continue'|trans }}</label>
                                <a href="{{ url('cart') }}" class="ec-inlineBtn--action">{{ 'common.go_to_cart'|trans }}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="ec-pagerRole">
                {% include "pager.twig" with {'pages': pagination.paginationData} %}
            </div>
        {% endif %}
    {% endif %}

{% endblock %}
