<?php

namespace Customize\Repository;

use Eccube\Common\EccubeConfig;
use Eccube\Repository\CategoryRepository;
use Symfony\Bridge\Doctrine\RegistryInterface;

/**
 * Class ExtendsCategoryRepository
 *
 * カテゴリリスト作成のカスタマイズ
 * - カテゴリごとの商品数を取得
 *
 * @package Customize\Repository
 */
class ExtendsCategoryRepository extends CategoryRepository
{
    /**
     * CategoryRepository constructor.
     *
     * @param RegistryInterface $registry
     * @param EccubeConfig $eccubeConfig
     */
    public function __construct(
        RegistryInterface $registry,
        EccubeConfig $eccubeConfig
    )
    {
        parent::__construct($registry, $eccubeConfig);
    }

    /**
     * 各カテゴリの商品数を取得する
     * @return array カテゴリIDに対する商品数
     */
    public function getCategoriesProductNum()
    {
        // Cookieから拠点IDを取得
        $baseId = $_COOKIE['SmartOrderKey_baseId'] ?? '';

        // Cookieからデモフラグを取得
        $isDemo = $_COOKIE['SmartOrderKey_isDemo'] ?? '';

        // デモ商品を利用するか、条件判定
        $status[] = '1';
        if ($isDemo == 1)
            $status[] = '2';

        $qb = $this->createQueryBuilder('dc');
        $qb->addSelect([
            'dc.id as id',
            'count(dc.id) as num'
        ])
            ->innerJoin('dc.ProductCategories', 'dpc', 'WITH', 'dc.id = dpc.category_id')
            ->innerJoin('dpc.Product', 'dp', 'WITH', "dpc.product_id = dp.id")
            ->innerJoin('dp.ProductClasses', 'dpcl')
            ->innerJoin('dpcl.SmaProducts', 'sp')
            ->where($qb->expr()->in('dp.Status', $status))
            ->andWhere('sp.base_id = :base_id')
            ->setParameter('base_id', $baseId)
            ->groupBy('dc.id');

//        var_dump($qb->getQuery()->getSQL());

        $category = $qb->getQuery()
            ->useResultCache(true, $this->getCacheLifetime())
            ->getArrayResult();

        // 取得結果を連想配列にして返す
        $categoryNums = [];
        foreach ($category as $value) {
            $categoryNums[$value['id']] = $value['num'];
        }

        return $categoryNums;
    }
}
