<?php

namespace Customize\Command;

use Customize\Controller\Admin\Product\ExtendsCsvImportController;
use Customize\Controller\Admin\Product\ExtendsProductController;
use Doctrine\ORM\NoResultException;
use Eccube\Util\CacheUtil;
use SplFileObject;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\HttpFoundation\Request;

/**
 * Class DeleteProductsCommand
 * スマ発DBに存在しない商品データを削除するコマンド
 * dtb_productテーブルのproduct_status_idを3に変更する
 *
 * ※スマ発側コードから呼び出し
 * 呼び出し方法：bin/console eccube:delete:products
 * @package Customize\Command
 */
class DeleteProductsCommand extends Command
{
    protected static $defaultName = 'eccube:delete:products';

    /** @var ExtendsProductController */
    protected $extendsProductController;

    public function __construct(
        ExtendsProductController $extendsProductController
    )
    {
        parent::__construct();

        $this->extendsProductController = $extendsProductController;
    }

    protected function configure()
    {
        $this
            ->setDescription('Delete products info tahat does not exist in smart_order');
    }

    /**
     * コマンドメイン処理
     *
     * @param InputInterface $input 入力データ
     * @param OutputInterface $output 出力データ（出力されるログが配列でセットされる）
     * @return int|void|null
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $output->writeln('カタログDB delete処理開始');

        try {
            $deleteNum = $this->extendsProductController->removeProductIdList();

            $output->writeln('       削除対象： ' . $deleteNum . '件');

            $output->writeln('カタログDB delete処理終了');
        } catch (\Exception $e) {
            $output->writeln($e->getMessage());
        }
    }
}
