<?php

namespace Customize\EventListener;

use Customize\Service\CatalogLoginControlService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Event\GetResponseEvent;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * Class SmartOrderLoginListener
 * 各ページを開いた際に行うスマ発ログイン確認処理
 * イベントリスナーからページアクセスを検知して実行する
 *
 * スマ発未ログインの場合、スマ発ログイン画面に遷移
 * スマ発ログイン、カタログ未ログインの場合、スマ発ログイン情報を元にカタログログイン
 *
 * @package Customize\EventListener
 */
class SmartOrderLoginListener implements EventSubscriberInterface
{
    public function onResponse(GetResponseEvent $event)
    {
        $request = $event->getRequest();

        // アクセスURL
        $requestUri = $request->getUri();

        // 確認対象外のURLは無視する
        // 対象外： ログインページ、管理者ページ
        if ($requestUri === getenv('SMART_ORDER_URL') . getenv('CATALOG_LOGIN_PAGE')
            || strpos($requestUri, '/admin') !== false
        ) {
            log_info('ログイン認証 スキップ');
            return false;
        }

        $catalogLoginControlService = new CatalogLoginControlService($request);

        log_info('ログイン認証 Cookie判定');

        // Cookieを確認し、スマ発のログイン状況を判定する
        if(!$catalogLoginControlService->checkSmartOrderLogin()){
            log_info('ログイン認証 スマ発NG');

            // スマ発未ログインの場合、スマ発ログインページへ遷移
            // 遷移先はカタログ用のログインページ。（営業マン用は無く、一般ユーザ専用）
            $response = new RedirectResponse(getenv('SMART_ORDER_CATALOG_LOGIN_PAGE'));
            $response->send();

            return false;
        }

        // スマ発はログイン済みだが、EC-CUBE側のログイン情報が存在しない
        // （もしくはスマ発とログインユーザーが違う）場合はカタログログイン処理を行う
        if (!$catalogLoginControlService->checkSameLogin()) {
            log_info('ログイン認証 スマ発OK、カタログNG');

            // Cookieにログイン情報を登録
            $catalogLoginControlService->addLogin(
                $catalogLoginControlService->getLoginId(),
                $catalogLoginControlService->getCustomerCode(),
                $catalogLoginControlService->getBusinessmanFlag()
            );

            log_info('ログイン認証 カタログ側Cookie設定');

            // カタログログイン画面に遷移して、ログイン処理を行う(Customize/Controller/SmartOrderLoginController.php:login)
            $response = new RedirectResponse(getenv('CATALOG_LOGIN_PAGE'));
            $response->send();

            return false;
        }

        log_info('ログイン認証 スマ発OK、カタログOK');
    }

    public static function getSubscribedEvents()
    {
        return [
            KernelEvents::REQUEST => 'onResponse',
        ];
    }
}