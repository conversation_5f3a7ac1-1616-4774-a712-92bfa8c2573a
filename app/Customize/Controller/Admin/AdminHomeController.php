<?php

namespace Customize\Controller\Admin;

use Eccube\Controller\Admin\AdminController;
use Eccube\Entity\Master\ProductStatus;
use Eccube\Repository\CustomerRepository;
use Eccube\Repository\Master\OrderStatusRepository;
use Eccube\Repository\MemberRepository;
use Eccube\Repository\OrderRepository;
use Eccube\Repository\ProductRepository;
use Eccube\Service\PluginApiService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\Encoder\EncoderFactoryInterface;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;

class AdminHomeController extends AdminController
{

    /**
     * AdminController constructor.
     *
     * @param AuthorizationCheckerInterface $authorizationChecker
     * @param AuthenticationUtils $helper
     * @param MemberRepository $memberRepository
     * @param EncoderFactoryInterface $encoderFactory
     * @param OrderRepository $orderRepository
     * @param OrderStatusRepository $orderStatusRepository
     * @param CustomerRepository $custmerRepository
     * @param ProductRepository $productRepository
     * @param PluginApiService $pluginApiService
     */
    public function __construct(
        AuthorizationCheckerInterface $authorizationChecker,
        AuthenticationUtils $helper,
        MemberRepository $memberRepository,
        EncoderFactoryInterface $encoderFactory,
        OrderRepository $orderRepository,
        OrderStatusRepository $orderStatusRepository,
        CustomerRepository $custmerRepository,
        ProductRepository $productRepository,
        PluginApiService $pluginApiService
    )
    {
        parent::__construct(
            $authorizationChecker,
            $helper,
            $memberRepository,
            $encoderFactory,
            $orderRepository,
            $orderStatusRepository,
            $custmerRepository,
            $productRepository,
            $pluginApiService
        );
    }

    /**
     * 一般ユーザ商品の商品数を取得
     *
     * @return mixed
     *
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function countUserProducts()
    {
        $qb = $this->productRepository->createQueryBuilder('p')
            ->select('count(DISTINCT p.id)')
            ->innerJoin('p.ProductClasses', 'pc')
            ->where('p.Status = :ProductStatusId AND pc.visible = :Visible')
            ->setParameter('ProductStatusId', ProductStatus::DISPLAY_SHOW)
            ->setParameter('Visible', true);

        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * デモユーザ商品の商品数を取得
     *
     * @return mixed
     *
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function countDemoProducts()
    {
        $qb = $this->productRepository->createQueryBuilder('p')
            ->select('count(DISTINCT p.id)')
            ->innerJoin('p.ProductClasses', 'pc')
            ->where('p.Status = :ProductStatusId AND pc.visible = :Visible')
            ->setParameter('ProductStatusId', ProductStatus::DISPLAY_HIDE)
            ->setParameter('Visible', true);

        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * 廃止以外の商品の検索結果を表示する.
     *
     * @Route("/%eccube_admin_route%/search_active", name="admin_homepage_active_product")
     *
     * @param Request $request
     *
     * @return Response
     */
    public function searchActiveProducts(Request $request)
    {
        // 在庫なし商品の検索条件をセッションに付与し, 商品マスタへリダイレクトする.
        $searchData = [];
        $searchData['status'] = [ProductStatus::DISPLAY_SHOW, ProductStatus::DISPLAY_HIDE];
        $session = $request->getSession();
        $session->set('eccube.admin.product.search', $searchData);

        return $this->redirectToRoute('admin_product_page', [
            'page_no' => 1,
        ]);
    }

    /**
     * 一般ユーザ商品の検索結果を表示する.
     *
     * @Route("/%eccube_admin_route%/search_users", name="admin_homepage_users_product")
     *
     * @param Request $request
     *
     * @return Response
     */
    public function searchUsersProducts(Request $request)
    {
        // 在庫なし商品の検索条件をセッションに付与し, 商品マスタへリダイレクトする.
        $searchData = [];
        $searchData['status'] = [ProductStatus::DISPLAY_SHOW];
        $session = $request->getSession();
        $session->set('eccube.admin.product.search', $searchData);

        return $this->redirectToRoute('admin_product_page', [
            'page_no' => 1,
        ]);
    }

    /**
     * デモユーザ商品の検索結果を表示する.
     *
     * @Route("/%eccube_admin_route%/search_demo", name="admin_homepage_demo_product")
     *
     * @param Request $request
     *
     * @return Response
     */
    public function searchDemoProducts(Request $request)
    {
        // 在庫なし商品の検索条件をセッションに付与し, 商品マスタへリダイレクトする.
        $searchData = [];
        $searchData['status'] = [ProductStatus::DISPLAY_HIDE];
        $session = $request->getSession();
        $session->set('eccube.admin.product.search', $searchData);

        return $this->redirectToRoute('admin_product_page', [
            'page_no' => 1,
        ]);
    }
}
