<?php

namespace Customize\Controller;

use Customize\Service\CatalogLoginControlService;
use Eccube\Controller\AbstractController;
use Eccube\Entity\Master\CustomerStatus;
use Eccube\Repository\CustomerRepository;
use Eccube\Repository\Master\CustomerStatusRepository;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;

/**
 * Class SmartOrderLoginController
 * カタログに自動ログインするためのクラス
 * スマ発と同じログインIDのユーザでログインする
 * 　スマ発のログインIDはCookieから取得する
 * ユーザがカタログに存在しない場合は新規作成する
 *
 * @package Customize\Controller
 */
class SmartOrderLoginController extends AbstractController
{
    /**
     * @var string
     */
    private $apiUrl;

    /**
     * @var CustomerRepository
     */
    protected $customerRepository;

    /**
     * @var CustomerStatusRepository
     */
    protected $customerStatusRepository;

    /**
     * @var TokenStorageInterface
     */
    protected $tokenStorage;

    /**
     * MypageController constructor.
     *
     * @param CustomerRepository $customerRepository
     * @param CustomerStatusRepository $customerStatusRepository
     * @param TokenStorageInterface $tokenStorage
     */
    public function __construct(
        CustomerRepository $customerRepository,
        CustomerStatusRepository $customerStatusRepository,
        TokenStorageInterface $tokenStorage
    )
    {
        $this->customerRepository = $customerRepository;
        $this->customerStatusRepository = $customerStatusRepository;
        $this->tokenStorage = $tokenStorage;
    }

    /**
     * カタログ用ログイン画面
     * ログイン処理/アカウント登録を行い、トップページへリダイレクト
     *
     * @Route("/catalog/login", name="catalog_login")
     */
    public function login(Request $request)
    {
        log_info('ログインコントローラ');

        $catalogLoginControlService = new CatalogLoginControlService($request);

        // Cookie中のスマ発ログインIDを取得
        $getId = $catalogLoginControlService->getLoginId();
        // Cookie中のスマ発ユーザ名を取得
        $getName = $catalogLoginControlService->getLoginUserName();

        // カタログのログイン状態を確認
        if ($this->isGranted('ROLE_USER')) {
            // ログイン中のユーザ情報を取得して前半部分（スマ発ログインID）のみ取り出す
            $nowId = explode(" ", $this->getUser());

            // スマ発とカタログで同名ユーザでログインしている場合
            if ($nowId[0] === $getId
                && $catalogLoginControlService->checkSameLogin()) {
                // カタログとスマ発のログインIDを等しい場合はカタログログイン済みと判断する
                log_info('ログイン済みのためログイン処理をスキップ');

                return $this->redirectToRoute('homepage');
            }
        }

        // カタログ未ログインか、スマ発のユーザが変わった場合
        log_info('ログインコントローラ 未ログイン or ユーザ変更');

        // ログイン情報をカタログDBから取得
        $Customer = $this->entityManager
            ->getRepository('Eccube\Entity\Customer')
            ->findOneBy(['name01' => $getId]);

        // カタログ側にログイン情報が登録されていなかった場合は、新規登録する
        if (!$Customer) {
            // ====================================================
            // name01: スマ発のログインIDと同じものを設定する
            //        （営業マンの場合はb_[loginId]_[customerCode]となっている想定）
            // name02: スマ発の担当者名(初回登録時の名前に固定される)
            // その他パラメータは未使用（デフォルト値かランダム）
            //   customer_status_id: 本会員(2)で登録
            //   email: 絶対に届かないであろう値(トップレベルドメイン.testは存在しないはず)
            //   password: 使用しないので固定値
            //   secret_key: UniqueKey(32桁)
            // ====================================================

            log_info('ログイン処理： アカウント登録処理');

            /** @var $Customer \Eccube\Entity\Customer */
            $Customer = $this->customerRepository->newCustomer();

            $secretKey = $this->customerRepository->getUniqueSecretKey();

            /** @var  $CustomerStatus \Eccube\Entity\Master\CustomerStatus */
            $CustomerStatus = $this->customerStatusRepository->find(CustomerStatus::REGULAR);

            $Customer
                ->setName01($getId)
                ->setName02($getName)
                ->setEmail($secretKey.'@test.test')
                ->setStatus($CustomerStatus)
                ->setPassword('aaaaaaaaaaaa')
                ->setSecretKey($secretKey)
                ->setPoint(0);
            $this->entityManager->persist($Customer);
            $this->entityManager->flush();
        }

        log_info('ログイン処理 OK');

        // ========================================================
        // EC-Cubeのtokenにログイン情報を設定してログイン済みにする
        $token = new UsernamePasswordToken($Customer, null, 'customer', ['ROLE_USER']);

        // token設定
        $this->tokenStorage->setToken($token);

        return $this->redirectToRoute('homepage');
    }
}