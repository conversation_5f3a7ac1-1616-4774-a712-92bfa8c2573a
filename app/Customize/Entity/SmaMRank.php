<?php

/*
 * This file is part of EC-CUBE
 *
 * Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.
 *
 * http://www.ec-cube.co.jp/
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Customize\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * SmaMRank
 * 追加テーブル（view）のEntityを追加
 *
 * @ORM\Table(name="sma_m_rank", indexes={
 *     @ORM\Index(name="sma_m_rank", columns={"company_code", "customer_code", "customer_edaban"}),
 * })
 * @ORM\InheritanceType("SINGLE_TABLE")
 * @ORM\DiscriminatorColumn(name="discriminator_type", type="string", length=255)
 * @ORM\HasLifecycleCallbacks()
 * @ORM\Entity(repositoryClass="Customize\Repository\SmaMRankRepository")
 */
class SmaMRank extends \Eccube\Entity\AbstractEntity
{
    /**
     * @var string
     *
     * @ORM\Id
     * @ORM\Column(name="company_code", type="string", length=3, nullable=false)
     */
    private $company_code;

    /**
     * @var string
     *
     * @ORM\Id
     * @ORM\Column(name="customer_code", type="string", length=10, nullable=false)
     */
    private $customer_code;

    /**
     * @var string
     *
     * @ORM\Column(name="customer_edaban", type="string", length=5, nullable=false)
     */
    private $customer_edaban;

    /**
     * @var int
     *
     * @ORM\Column(name="rank_1", type="integer", options={"unsigned":false})
     */
    private $rank_1;

    /**
     * @var int
     *
     * @ORM\Column(name="rank_2", type="integer", options={"unsigned":false})
     */
    private $rank_2;

    /**
     * @var int
     *
     * @ORM\Column(name="rank_3", type="integer", options={"unsigned":false})
     */
    private $rank_3;

    /**
     * @var int
     *
     * @ORM\Column(name="rank_4", type="integer", options={"unsigned":false})
     */
    private $rank_4;

    /**
     * @var int
     *
     * @ORM\Column(name="rank_5", type="integer", options={"unsigned":false})
     */
    private $rank_5;

    /**
     * @var int
     *
     * @ORM\Column(name="rank_6", type="integer", options={"unsigned":false})
     */
    private $rank_6;

    /**
     * @var int
     *
     * @ORM\Column(name="rank_7", type="integer", options={"unsigned":false})
     */
    private $rank_7;

    /**
     * Get customer_code.
     *
     * @return string
     */
    public function getCustomerCode()
    {
        return $this->customer_code;
    }

    /**
     * Get customer_edaban.
     *
     * @return string
     */
    public function getCustomerEdaban()
    {
        return $this->customer_edaban;
    }

    /**
     * Get rank_1.
     *
     * @return string
     */
    public function getRank1()
    {
        return $this->rank_1;
    }

    /**
     * Get rank_2.
     *
     * @return string
     */
    public function getRank2()
    {
        return $this->rank_2;
    }

    /**
     * Get rank_3.
     *
     * @return string
     */
    public function getRank3()
    {
        return $this->rank_3;
    }

    /**
     * Get rank_4.
     *
     * @return string
     */
    public function getRank4()
    {
        return $this->rank_4;
    }

    /**
     * Get rank_5.
     *
     * @return string
     */
    public function getRank5()
    {
        return $this->rank_5;
    }

    /**
     * Get rank_6.
     *
     * @return string
     */
    public function getRank6()
    {
        return $this->rank_6;
    }

    /**
     * Get rank_7.
     *
     * @return string
     */
    public function getRank7()
    {
        return $this->rank_7;
    }
}
