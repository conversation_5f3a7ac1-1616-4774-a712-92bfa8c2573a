<?php

namespace Customize\Twig\Extension;

use Customize\Controller\Admin\AdminHomeController;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Eccube\Entity\Cart;
use Eccube\Entity\Product;
use Eccube\Service\CartService;
use Knp\Bundle\PaginatorBundle\Pagination\SlidingPagination;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

/**
 * Class AdminExtension
 * Twigファイル内で利用できる独自関数を作成
 *　管理画面で使用する
 *
 * @package Customize\Twig\Extension
 */
class AdminExtension extends AbstractExtension
{
    /**
     * @var AdminHomeController
     */
    protected $adminHomeController;

    public function __construct(AdminHomeController $adminHomeController)
    {
        $this->adminHomeController = $adminHomeController;
    }

    public function getFunctions()
    {
        return [
            new TwigFunction('get_user_products', [$this, 'getUserProducts']),
            new TwigFunction('get_demo_products', [$this, 'getDemoProducts']),
        ];
    }

    /**
     * 一般ユーザ商品の個数を取得
     *
     * @return mixed
     */
    public function getUserProducts()
    {
        try {
            return $this->adminHomeController->countUserProducts();
        } catch (NonUniqueResultException $e) {
            return 0;
        }
    }

    /**
     * デモユーザ商品の個数を取得
     *
     * @return mixed
     */
    public function getDemoProducts()
    {
        try {
            return $this->adminHomeController->countDemoProducts();
        } catch (NonUniqueResultException $e) {
            return 0;
        }
    }
}
