<html>
  <head>
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
    <script type="text/javascript">
      google.charts.load('current', {'packages':['corechart']});
      google.charts.setOnLoadCallback(drawChart);

      let visibility = {1: true, 2: true}; // Cột 1 và 2 là các dòng (line) dữ liệu

      function drawChart() {
        const rawData = google.visualization.arrayToDataTable([
          ['Year', 'Sales', 'Expenses'],
          ['2019', 1000, 400],
          ['2020', 1170, 460],
          ['2021', 660, 1120],
          ['2022', 1030, 540]
        ]);

        const view = new google.visualization.DataView(rawData);

        // Tạo các cột hiển thị dựa vào trạng thái visibility
        const columns = [0]; // Cột đầu tiên là nhãn X (Year)
        for (let i = 1; i < rawData.getNumberOfColumns(); i++) {
          if (visibility[i]) {
            columns.push(i);
          } else {
            // Nếu dòng đang bị ẩn, thêm cột null để giữ đúng cấu trúc
            columns.push({
              calc: () => null,
              label: rawData.getColumnLabel(i),
              type: rawData.getColumnType(i)
            });
          }
        }

        view.setColumns(columns);

        const options = {
          title: 'Company Performance',
          legend: { position: 'bottom' }
        };

        const chart = new google.visualization.LineChart(document.getElementById('chart_div'));
        chart.draw(view, options);

        // Bắt sự kiện khi người dùng click vào legend
        google.visualization.events.addListener(chart, 'select', function () {
          const sel = chart.getSelection();
          if (sel.length > 0 && sel[0].column !== null) {
            const colIndex = sel[0].column;
            if (colIndex > 0) {
              // Toggle visibility
              visibility[colIndex] = !visibility[colIndex];
              drawChart(); // Vẽ lại biểu đồ
            }
          }
        });
      }
    </script>
  </head>
  <body>
    <div id="chart_div" style="width: 900px; height: 500px;"></div>
  </body>
</html>
