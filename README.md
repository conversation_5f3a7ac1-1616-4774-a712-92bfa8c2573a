# EC-CUBEの開発環境構築

## 想定環境

* EC-CUBE 4.0.3
* Apache 2.4.x
* PHP 7.3
* MySQL 8.0

## インストール

Dockerを利用してインストールを行う  
本家ドキュメントのインストール手順を参考

### 事前準備

* docker for windowsのインストール
    * 割愛
* node.jsのインストール
    * scssのコンパイルに使用する
    * ターミナルから```$ node -v```と打ってエラーとなればインストールされていない。
    * [こちら](https://nodejs.org/ja/)からダウンロード&インストール
        * LTS版をインストール
* yarnのインストール
    * ターミナルからインストール  
    ```$ npm install -g yarn```
* スマ発のコンテナを起動しておくこと

### 補足
スマ発側と連携するため、ネットワークを共通のものを使用しています。
そのため、先にスマ発コンテナを起動する必要があります

```
$ cd <smart_order_dir>/docker
$ docker-compose up -d
```

なお、開発環境ではポートが異なるため、セッション、Webストレージの共有ができません。

DBのみ参照可能です。

連携の確認は、サーバーのテスト環境にアップして確認してください。

### 初回実行手順

初回ビルド時は小一時間かかります。

1. ワークスペースへ移動
1. Gitからダウンロード  
```$ git clone https://bmc-it.backlog.jp/git/SMHT/smart_order_catalog.git .```
1. docker起動  

```
$ cd smart_order_catalog
# .envファイルを作成
$ cp .env.dist .env
$ docker-compose up -d --build
```

1. EC-CUBEのインストール

```
$ docker-compose exec ec-cube /bin/bash

## 下記はコンテナ内で実行
$ composer install --no-scripts --no-autoloader --no-dev
$ composer dumpautoload -o --apcu --no-dev
$ composer run-script auto-scripts
$ exit 
```

1. パーミッションの変更

```
chmod 707 app app/Plugin app/PluginData/ app/proxy/ app/template/ html var vendor
chmod 606 composer.*
```

1. EC-CUBEの初期設定

```
$ docker-compose exec ec-cube bin/console eccube:install
  ※ 設定はDatabase Url、 Mailer Uｒｌ以外はそのままでEnterキーを押していけばOK

  Database Url [sqlite:///var/eccube.db]:
  > mysql://root:root@db:3306/eccube_db

  Mailer Url [null://localhost]:
  > smtp://mail:1025

  Auth Magic [xxxxxxxxxxxxxxxxx]:
  >

  Is it OK? (yes/no) [yes]:
  >

$ exit
```

1. スマ発のDBを参照させる

スマ発の「d_delivery_result_items」テーブルをViewで参照させる

mysqlサーバにアクセスして、下記を実行

```
# mysqlにログイン
$ mysql -uroot -p

> use <カタログのDB名>
> CREATE VIEW sma_product_base AS SELECT * FROM <スマ発のDB名>.c_product_info_by_base;
> exit;
```


例：

※ smart_order_dbをスマ発のDB名に変更してください

```
# c_product_list, c_product_info_by_base
CREATE OR REPLACE VIEW sma_product AS
    SELECT cpl.product_code,
       cpb.base_id,
       cpl.product_name,
       cpl.product_kana,
       cpl.product_standard AS standard,
       cpl.in_numbers,
       cpl.product_packing  AS packing,
       cpl.unit_name,
       cpl.description1,
       cpl.description2,
       cpl.price_switch_day,
       cpl.price_before,
       cpl.price_after,
       cpl.measurement_division,
       cpl.decimal_point_permission_division,
       cpl.tax_type,
       cpl.tax_rate,
       cpl.is_visible,
       cpb.order_division,
       mc.name as order_division_name,
       cpb.lead_time,
       'smaproduct'         AS discriminator_type
FROM smart_order_db.c_product_list AS cpl
         LEFT JOIN smart_order_db.c_product_info_by_base AS cpb
                   ON cpl.company_code = cpb.company_code
                       AND cpl.product_code = cpb.product_code
         LEFT JOIN (
    SELECT '0' AS code, order_type_0 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '1' AS code, order_type_1 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '2' AS code, order_type_2 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '3' AS code, order_type_3 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '4' AS code, order_type_4 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '5' AS code, order_type_5 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '6' AS code, order_type_6 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '7' AS code, order_type_7 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '8' AS code, order_type_8 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '9' AS code, order_type_9 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
) as mc
                   ON cpb.order_division = mc.code
WHERE cpl.company_code = '001'
  AND cpb.base_id IS NOT NULL;
```

```
# d_orderlist
CREATE OR REPLACE VIEW sma_d_orderlist AS
SELECT customer_code,
       customer_edaban,
       order_code,
       management_product_code AS product_code,
       product_name,
       product_standard AS standard,
       in_numbers,
       unit_name,
       price_switch_day,
       price_before,
       price_after,
       order_division,
       mc.name                 as order_division_name,
       lead_time,
       'smadorderlist'            AS discriminator_type
FROM smart_order_db.d_orderlist as do
         LEFT JOIN (
    SELECT '0' AS code, order_type_0 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '1' AS code, order_type_1 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '2' AS code, order_type_2 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '3' AS code, order_type_3 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '4' AS code, order_type_4 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '5' AS code, order_type_5 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '6' AS code, order_type_6 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '7' AS code, order_type_7 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '8' AS code, order_type_8 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
    UNION ALL
    SELECT '9' AS code, order_type_9 AS name
    FROM smart_order_db.m_companies
    WHERE code = '001'
) as mc
                   ON do.order_division = mc.code
WHERE company_code = '001'
  AND (is_visible = '1' OR is_visible = '8');
```

- ```... exceeded the timeout of 60 seconds.``` のようなエラーが出た場合は、タイムアウト時間の増加処理が必要です。  
下記ファイルの172行目あたりを修正してください。  
```/src/Eccube/Command/InstallerCommand.php```

```
- $process = new Process('bin/console ' . $command);
+ $process = new Process('bin/console ' . $command, null, null, null,6000);
```

- アクセスURLは```http://localhost:8080```
- 管理画面は```http://localhost:8080/admin```

### 2回目以降の起動手順

2回目以降は下記コマンドで起動します。

```
$ docker-compose up -d
```

### 初回インストール時のレイアウトについて

dtb_block_positionの修正
（既存のDBよりデータをコピーする）

### scssコンパイルの方法

windows側から行います  
※node.jsとyarnをインストールしておいてください

- 初回のみ  
 warningが出るのは無視

```
$ yarn
```

- コンパイル

```
$ yarn build
```

- 更新を自動検出する場合

```
$ yarn watch
```


## DBへのアクセス情報
ポート番号は.envで指定した値となります。
デフォルトは下記

* host: localhost
* port: 3307
* user: root
* password: root
* database: eccube_db

## サーバー環境での開発方法について
サーバーのテスト環境での開発方法を記載します。
「ローカルのvscode上でファイルを修正→サーバ側の該当ファイルへ即時反映」のイメージです。

### 手順
1. vscodeの拡張機能から「sftp」をインストールする。
2. vscode上で「Ctrl + Shift + P」を押下する。コマンド入力欄が表示されるので、「SFTP: Config」を入力実行する。
3. プロジェクトルートに.vscode/sftp.jsonが自動作成されるので、下記のように設定する。

```
{
    "name": "eccube_bmc",
    "host": "***************",
    "protocol": "sftp",
    "port": 22,
    "username": <ユーザー名>,
    "password": <パスワード>,
    "remotePath": "/var/www/html/ctl-test/eccube_bmc",
    "uploadOnSave": true,
    "ignore": [
        "**/.vscode/**",
        "**/node_modules/**",
        "**/vendor/**",
        "**/.env"
    ]
}

```
※記述内容は適宜変更してください。

4. vscode上で「ファイル修正→保存」を行い、リモート側のソースが変更されていればOK。

### 補足
 - 実際の開発時に当手法を用いるかは検討が必要。
 - ローカルでのへ変更内容がリモード側へ即時反映されるため、複数人で開発する場合は、作業内容が被らないよう「ctl-test/eccube_bmc_1, ctl-test/eccube_bmc_2, ・・・」 のように人数分のフォルダを用意する必要がある。
 - 参考URL
     - https://qiita.com/mimizq/items/2bfe2ab147544591e0bc
     - https://qiita.com/ishimasar/items/1324af16e19a59b220d3


## ドキュメント

* [EC-CUBE 4.0 開発ドキュメント@doc4.ec-cube.net](http://doc4.ec-cube.net/)


