<?php

/*
 * This file is part of EC-CUBE
 *
 * Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.
 *
 * http://www.ec-cube.co.jp/
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Eccube\Repository;

use Eccube\Entity\Plugin;
use Symfony\Bridge\Doctrine\RegistryInterface;

/**
 * PluginRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class PluginRepository extends AbstractRepository
{
    public function __construct(RegistryInterface $registry)
    {
        parent::__construct($registry, Plugin::class);
    }

    public function findAllEnabled()
    {
        return $this->findBy(['enabled' => '1']);
    }

    /**
     * @param $code string プラグインコード
     *
     * @return Plugin
     */
    public function findByCode($code)
    {
        return $this->findOneBy(['code' => $code]);
    }
}
