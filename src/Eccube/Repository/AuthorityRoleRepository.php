<?php

/*
 * This file is part of EC-CUBE
 *
 * Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.
 *
 * http://www.ec-cube.co.jp/
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Eccube\Repository;

use Eccube\Entity\AuthorityRole;
use Symfony\Bridge\Doctrine\RegistryInterface;

/**
 * AuthorityRoleRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class AuthorityRoleRepository extends AbstractRepository
{
    public function __construct(RegistryInterface $registry)
    {
        parent::__construct($registry, AuthorityRole::class);
    }

    /**
     * 権限、拒否URLでソートする
     *
     * @return array
     */
    public function findAllSort()
    {
        return $this->findBy([], ['Authority' => 'ASC', 'deny_url' => 'ASC']);
    }
}
