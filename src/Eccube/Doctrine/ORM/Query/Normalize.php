<?php

/*
 * This file is part of EC-CUBE
 *
 * Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.
 *
 * http://www.ec-cube.co.jp/
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Eccube\Doctrine\ORM\Query;

use Doctrine\ORM\Query\AST\Functions\FunctionNode;
use Doctrine\ORM\Query\Lexer;
use Doctrine\ORM\Query\Parser;
use Doctrine\ORM\Query\SqlWalker;

class Normalize extends FunctionNode
{
    protected $string;
    const FROM = 'あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわをんがぎぐげござじずぜぞだぢづでどばびぶべぼぱぴぷぺぽぁぃぅぇぉっゃゅょわいえー';
    const TO = 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲンガギグゲゴザジズゼゾダヂヅデドバビブベボパピプペポァィゥェォッャュョヮヰヱー';

    public function parse(Parser $parser)
    {
        $parser->match(Lexer::T_IDENTIFIER);
        $parser->match(Lexer::T_OPEN_PARENTHESIS);
        $this->string = $parser->ArithmeticPrimary();
        $parser->match(Lexer::T_CLOSE_PARENTHESIS);
    }

    public function getSql(SqlWalker $sqlWalker)
    {
        switch ($sqlWalker->getConnection()->getDriver()->getName()) {
            case 'pdo_pgsql':
                $sql = sprintf("LOWER(TRANSLATE(%s, '%s', '%s'))", $this->string->dispatch($sqlWalker), self::FROM, self::TO);
                break;
            case 'pdo_mysql':
                $sql = sprintf('REPLACE(REPLACE(CONVERT(%s USING utf8), "ﾞ", ""), "ﾟ", "") COLLATE utf8_unicode_ci', $this->string->dispatch($sqlWalker));
                //$kana_pattern = "REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(CONVERT(%s USING utf8), 'ｶﾞ','ガ'), 'ｷﾞ','ギ'), 'ｸﾞ','グ'), 'ｹﾞ','ゲ'), 'ｺﾞ','ゴ'),'ｻﾞ','ザ'), 'ｼﾞ','ジ'), 'ｽﾞ','ズ'), 'ｾﾞ','ゼ'), 'ｿﾞ','ゾ'), 'ﾀﾞ','ダ'), 'ﾁﾞ','ヂ'), 'ﾂﾞ','ヅ'), 'ﾃﾞ','デ'),'ﾄﾞ','ド'), 'ﾊﾞ','バ'), 'ﾋﾞ','ビ'), 'ﾌﾞ','ブ'), 'ﾍﾞ','ベ'), 'ﾎﾞ','ボ'), 'ﾊﾟ','パ'), 'ﾋﾟ','ピ'), 'ﾌﾟ','プ'), 'ﾍﾟ','ペ'), 'ﾎﾟ','ポ')";
                //$sql = sprintf($kana_pattern . 'COLLATE utf8_unicode_ci', $this->string->dispatch($sqlWalker));

                break;
            default:
                $sql = sprintf('LOWER(%s)', $this->string->dispatch($sqlWalker));
                break;
        }

        return $sql;
    }
}
