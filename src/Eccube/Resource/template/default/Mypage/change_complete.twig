{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% extends 'default_frame.twig' %}

{% set mypageno = 'change' %}

{% set body_class = 'mypage' %}

{% block main %}
    <div class="ec-layoutRole__main">
        <div class="ec-mypageRole">
            <div class="ec-pageHeader">
                <h1>{{ 'front.mypage.title'|trans }}/{{ 'front.mypage.nav__customer_complete'|trans }}</h1>
            </div>
            {% include 'Mypage/navi.twig' %}
        </div>
        <div class="ec-registerCompleteRole">
            <div class="ec-off2Grid ec-text-ac">
                <div class="ec-off2Grid__cell">
                    <div class="ec-reportHeading">
                        <h2>{{ 'front.mypage.customer_complete_message__title'|trans }}</h2>
                    </div>
                    <p class="ec-reportDescription">{{ 'front.mypage.customer_complete_message__body'|trans }}</p>
                    <div class="ec-off4Grid">
                        <div class="ec-off4Grid__cell"><a class="ec-blockBtn--cancel"
                                                          href="{{ url('homepage') }}">{{ 'common.back'|trans }}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
