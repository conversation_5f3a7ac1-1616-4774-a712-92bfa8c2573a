<!doctype html>
{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
<html lang="{{ eccube_config.locale }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>{{ error_title }}</title>
    <link rel="icon" href="{{ asset('assets/img/common/favicon.ico', 'user_data') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/style.css') }}">
</head>
<body>
<div class="ec-layoutRole">
    <div class="ec-404Role">
        <div class="ec-off4Grid">
            <div class="ec-off4Grid__cell">
                <div style="font-size:100px;text-align:center;">
                    <div class="ec-404Role__icon ec-icon">
                        <img src="{{ asset('assets/icon/exclamation-pale.svg') }}" alt="">
                    </div>
                </div>
                <p class="ec-404Role__title ec-reportHeading">{{ error_title }}</p>
                <p class="ec-404Role__description ec-reportDescription">{{ error_message }}</p>
                <a class="ec-blockBtn--cancel" href="{{ url('homepage') }}">{{ 'common.go_to_top'|trans }}</a>
            </div>
        </div>
    </div>
</div>
</body>
</html>
