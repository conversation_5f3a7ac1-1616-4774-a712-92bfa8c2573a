{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% autoescape false %}
{{ Order.name01 }} {{ Order.name02 }} 様

この度はご注文いただき誠にありがとうございます。下記ご注文内容にお間違えがないかご確認下さい。

************************************************
　ご請求金額
************************************************

ご注文日時：{{ Order.order_date|date_sec }}
ご注文番号：{{ Order.order_no }}
お支払い合計：{{ Order.payment_total|price}}
お支払い方法：{{ Order.payment_method }}
{% if BaseInfo.isOptionPoint and Order.Customer is not null %}
ご利用ポイント：{{ Order.usePoint|number_format }} pt
加算ポイント：{{ Order.addPoint|number_format }} pt
{% endif %}
お問い合わせ：{{ Order.message }}


************************************************
　ご注文商品明細
************************************************

{% for OrderItem in Order.MergedProductOrderItems %}
商品コード：{{ OrderItem.product_code }}
商品名：{{ OrderItem.product_name }}  {{ OrderItem.classcategory_name1 }}  {{ OrderItem.classcategory_name2 }} {{ is_reduced_tax_rate(OrderItem) ? '※' }}
単価：{{ OrderItem.price_inctax|price }}
数量：{{ OrderItem.quantity|number_format }}

{% endfor %}
※は軽減税率対象商品です。
-------------------------------------------------
小　計：{{ Order.subtotal|price }}
手数料：{{ Order.charge|price }}
送　料：{{ Order.delivery_fee_total|price}}
{% if Order.taxable_discount < 0 %}
値引き：{{ Order.taxable_discount|price }}
{% endif %}
-------------------------------------------------
合　計：{{ Order.taxable_total|price }}
    {% for rate, total in Order.taxable_total_by_tax_rate %}
    ({{ rate }} %対象：{{ total|price }})
    {% endfor %}
{% for item in Order.tax_free_discount_items %}
-------------------------------------------------
{{ item.product_name }}：{{ item.total_price|price }}
{% endfor %}
============================================
お支払い合計：{{ Order.payment_total|price }}

************************************************
　ご注文者情報
************************************************
お名前：{{ Order.name01 }} {{ Order.name02 }} 様
お名前(カナ)：{{ Order.kana01 }} {{ Order.kana02 }} 様
{% if Order.company_name %}
会社名：{{ Order.company_name }}
{% endif %}
郵便番号：〒{{ Order.postal_code }}
住所：{{ Order.Pref.name }}{{ Order.addr01 }}{{ Order.addr02 }}
電話番号：{{ Order.phone_number }}
メールアドレス：{{ Order.email }}

************************************************
　配送情報
************************************************

{%  for Shipping in Order.Shippings %}
◎お届け先{% if Order.multiple %}{{ loop.index }}{% endif %}

お名前：{{ Shipping.name01 }} {{ Shipping.name02 }} 様
お名前(カナ)：{{ Shipping.kana01 }} {{ Shipping.kana02 }} 様
{% if Shipping.company_name %}
会社名：{{ Shipping.company_name }}
{% endif %}
郵便番号：〒{{ Shipping.postal_code }}
住所：{{ Shipping.Pref.name }}{{ Shipping.addr01 }}{{ Shipping.addr02 }}
電話番号：{{ Shipping.phone_number }}

配送方法：{{ Shipping.shipping_delivery_name }}
お届け日：{{ Shipping.shipping_delivery_date is empty ? '指定なし' : Shipping.shipping_delivery_date|date_day }}
お届け時間：{{ Shipping.shipping_delivery_time|default('指定なし') }}

{%  for OrderItem in Shipping.productOrderItems %}
商品コード：{{ OrderItem.product_code }}
商品名：{{ OrderItem.product_name }}  {{ OrderItem.classcategory_name1 }}  {{ OrderItem.classcategory_name2 }}
数量：{{ OrderItem.quantity|number_format }}

{% endfor %}
{% endfor %}

{% if Order.complete_mail_message is not empty %}
{{ Order.complete_mail_message }}
{% endif %}

============================================

このメッセージはお客様へのお知らせ専用ですので、
このメッセージへの返信としてご質問をお送りいただいても回答できません。
ご了承ください。
{% endautoescape %}
