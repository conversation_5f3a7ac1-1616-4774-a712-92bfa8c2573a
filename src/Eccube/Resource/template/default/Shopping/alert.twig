{% if error_only is not defined %}
    {% set error_only = false %}
{% endif %}
{% for error in app.session.flashbag.get('eccube.front.error') %}
    <div class="ec-cartRole__error">
        <div class="ec-alert-warning">
            <div class="ec-alert-warning__icon"><img src="{{ asset('assets/icon/exclamation-white.svg') }}"></div>
            <div class="ec-alert-warning__text">
                {{ error|trans|nl2br }}
            </div>
        </div>
    </div>
{% endfor %}
{% if error_only == false %}
{% for error in app.session.flashbag.get('eccube.front.warning') %}
    <div class="ec-cartRole__error">
        <div class="ec-alert-warning">
            <div class="ec-alert-warning__icon"><img src="{{ asset('assets/icon/exclamation-white.svg') }}">
            </div>
            <div class="ec-alert-warning__text">
                {{ error|trans|nl2br }}
            </div>
        </div>
    </div>
{% endfor %}
{% endif %}
