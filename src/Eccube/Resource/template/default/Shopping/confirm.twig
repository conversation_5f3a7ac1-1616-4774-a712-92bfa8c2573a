{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% extends 'default_frame.twig' %}

{% form_theme form 'Form/form_div_layout.twig' %}

{% block main %}

<div class="ec-role">
    <div class="ec-pageHeader">
        <h1>{{ 'front.shopping.confirm_title'|trans }}</h1>
    </div>
</div>

<div class="ec-cartRole">
    <div class="ec-cartRole__progress">
        <ul class="ec-progress">
            {% set step = 1 %}
            <li class="ec-progress__item">
                <div class="ec-progress__number">{{ step }}{% set step = step + 1 %}
                </div>
                <div class="ec-progress__label">{{ 'front.cart.nav__cart_items'|trans }}
                </div>
            </li>
            {% if is_granted('ROLE_USER') == false %}
                <li class="ec-progress__item">
                    <div class="ec-progress__number">{{ step }}{% set step = step + 1 %}
                    </div>
                    <div class="ec-progress__label">{{ 'front.cart.nav__customer_info'|trans }}
                    </div>
                </li>
            {% endif %}
            <li class="ec-progress__item">
                <div class="ec-progress__number">{{ step }}{% set step = step + 1 %}
                </div>
                <div class="ec-progress__label">{{ 'front.cart.nav__order'|trans }}
                </div>
            </li>
            <li class="ec-progress__item is-complete">
                <div class="ec-progress__number">{{ step }}{% set step = step + 1 %}
                </div>
                <div class="ec-progress__label">{{ 'front.cart.nav__confirm'|trans }}
                </div>
            </li>
            <li class="ec-progress__item">
                <div class="ec-progress__number">{{ step }}{% set step = step + 1 %}
                </div>
                <div class="ec-progress__label">{{ 'front.cart.nav__complete'|trans }}
                </div>
            </li>
        </ul>
    </div>
</div>

    <form id="shopping-form" method="post" action="{{ url('shopping_checkout') }}">
    {{ form_widget(form._token) }}
    <div class="ec-orderRole">
        <div class="ec-orderRole__detail">
            <div class="ec-orderAccount">
                <div class="ec-rectHeading">
                    <h2>{{ 'front.shopping.customer_info'|trans }}</h2>
                </div>
                <div class="ec-orderAccount__account">
                    <p class="ec-halfInput">{{ 'common.name.prefix'|trans }}{{ Order.name01 }} {{ Order.name02 }}</span>{{ 'common.name.suffix'|trans }}</p>
                    <p class="ec-halfInput">{{ Order.kana01 }}</span> {{ Order.kana02 }}</p>
                    <p class="ec-input">{{ Order.companyName }}</span></p>
                    <p class="ec-zipInput">{{ 'common.postal_symbol'|trans }}{{ Order.postal_code }}</p>
                    <p class="ec-input">{{ Order.pref }}{{ Order.addr01 }}{{ Order.addr02 }}</p>
                    <p class="ec-telInput">{{ Order.phone_number }}</span></p>
                    <p class="ec-input">{{ Order.email }}</span></p>
                </div>
            </div>
            <div class="ec-orderDelivery">
                <div class="ec-rectHeading">
                    <h2>{{ 'front.shopping.delivery_info'|trans }}</h2>
                </div>
                {% for shipping in Order.shippings %}
                    {% set idx = loop.index0 %}
                    <div class="ec-orderDelivery__item">
                        <ul class="ec-borderedList">
                            {% for orderItem in shipping.productOrderItems %}
                            <li>
                                <div class="ec-imageGrid">
                                    <div class="ec-imageGrid__img"><img src="{{ asset((orderItem.product is null ? null : orderItem.product.MainListImage)|no_image_product, 'save_image') }}" alt="{{ orderItem.productName }}"></div>
                                    <div class="ec-imageGrid__content">
                                        <p>{{ orderItem.productName }}{% if is_reduced_tax_rate(orderItem) %}{{ 'common.reduced_tax_rate_symbol'|trans }}{% endif %}</p>
                                        {% if orderItem.productClass is not null and orderItem.productClass.classCategory1 %}
                                            <p>{{ orderItem.productClass.classCategory1.className.name }}：{{ orderItem.productClass.classCategory1 }}</p>
                                        {% endif %}
                                        {% if orderItem.productClass is not null and orderItem.productClass.classCategory2 %}
                                            <p>{{ orderItem.productClass.classCategory2.className.name }}：{{ orderItem.productClass.classCategory2 }}</p>
                                        {% endif %}
                                        <p>{{ orderItem.priceIncTax|price }} × {{ orderItem.quantity|number_format }}<span>{{ 'common.subtotal__with_separator'|trans }}{{ orderItem.totalPrice|price }}</span></p>
                                    </div>
                                </div>
                            </li>
                            {% endfor %}
                        </ul>
                        <p>{{ 'common.reduced_tax_rate_messeage'|trans }}</p>
                    </div>
                    <div class="ec-orderDelivery__address">
                        <p>{{ 'common.name.prefix'|trans }}{{ shipping.name01 }} {{ shipping.name02 }} ({{ shipping.kana01 }} {{ shipping.kana02 }}){{ 'common.name.suffix'|trans }}</p>
                        <p>{{ 'common.postal_symbol'|trans }}{{ shipping.postal_code }} {{ shipping.pref }}{{ shipping.addr01 }}{{ shipping.addr02 }}</p>
                        <p>{{ shipping.phone_number }}</p>
                    </div>
                    <div class="ec-orderDelivery__actions">
                        <div class="ec-selects">
                            <div class="ec-select">
                                <label>{{ 'front.shopping.delivery_provider'|trans }}</label>
                                {% set delivery_fee = 0 %}
                                {% for item in shipping.order_items if item.isDeliveryFee %}
                                    {% set delivery_fee = item.total_price %}
                                {% endfor %}
                                {{ Order.Shippings[idx].Delivery }}({{ delivery_fee|price }})
                            </div>
                            <div class="ec-select ec-select__delivery">
                                <label>{{ 'front.shopping.delivery_date'|trans }}</label>
                                {{ Order.Shippings[idx].shipping_delivery_date? Order.Shippings[idx].shipping_delivery_date|date_day_with_weekday : 'common.select__unspecified'|trans }}
                            </div>
                            <div class="ec-select ec-select__time">
                                <label>{{ 'front.shopping.delivery_time'|trans }}</label>
                                {{ Order.Shippings[idx].shipping_delivery_time?: 'common.select__unspecified'|trans }}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            <div class="ec-orderPayment">
                <div class="ec-rectHeading">
                    <h2>{{ 'front.shopping.payment_info'|trans }}</h2>
                </div>
                <div class="ec-blockRadio">
                    {% set charge = 0 %}
                    {% for item in Order.order_items if item.isCharge %}
                        {% set charge = item.total_price %}
                    {% endfor %}
                    {{ Order.Payment }}({{ charge|price }})
                </div>
            </div>
            {% if BaseInfo.isOptionPoint and Order.Customer is not null %}
            <div class="ec-orderPayment">
                <div class="ec-rectHeading">
                    <h2>{{ 'front.shopping.point_info'|trans }}</h2>
                </div>
                <div class="ec-blockRadio">
                    {{ Order.use_point|number_format }} pt
                </div>
            </div>
            {% endif %}
            <div class="ec-orderConfirm">
                <div class="ec-rectHeading">
                    <h2>{{ 'front.shopping.message_info'|trans }}</h2>
                </div>
                <div class="ec-input">
                    {{ Order.message|nl2br }}
                </div>
            </div>
        </div>
        <div class="ec-orderRole__summary">
            <div class="ec-totalBox">
                <dl class="ec-totalBox__spec">
                    <dt>{{ 'common.subtotal'|trans }}</dt>
                    <dd class="ec-totalBox__specTotal">{{ Order.subtotal|price }}</dd>
                </dl>
                <dl class="ec-totalBox__spec">
                    <dt>{{ 'common.charge'|trans }}</dt>
                    <dd>{{ Order.charge|price }}</dd>
                </dl>
                <dl class="ec-totalBox__spec">
                    <dt>{{ 'common.delivery_fee'|trans }}</dt>
                    <dd>{{ Order.deliveryFeeTotal|price }}</dd>
                </dl>
                {% if Order.taxable_discount < 0 %}
                <dl class="ec-totalBox__spec">
                    <dt>{{ 'common.discount'|trans }}</dt>
                    <dd>{{ Order.taxable_discount|price }}</dd>
                </dl>
                {% endif %}
                <div class="ec-totalBox__total">{{ 'common.total'|trans }}<span class="ec-totalBox__price">{{ Order.taxable_total|price }}</span><span class="ec-totalBox__taxLabel">{{ 'common.tax_include'|trans }}</span></div>
                {% for rate, total in Order.taxable_total_by_tax_rate %}
                    <dl class="ec-totalBox__taxRate">
                        <dt>{{ 'common.tax_rate_target'|trans({ '%rate%': rate }) }}</dt>
                        <dd>{{ total|price }}</dd>
                    </dl>
                {% endfor %}
                {% for item in Order.tax_free_discount_items %}
                    {% if loop.first %}<div class="ec-totalBox__total"></div>{% endif %}
                    <dl class="ec-totalBox__spec">
                        <dt>{{ item.product_name }}</dt>
                        <dd>{{ item.total_price|price }}</dd>
                    </dl>
                {% endfor %}
                <div class="ec-totalBox__paymentTotal">{{ 'common.payment_total'|trans }}<span class="ec-totalBox__price">{{ Order.payment_total|price }}</span><span class="ec-totalBox__taxLabel">{{ 'common.tax_include'|trans }}</span></div>
                {% if BaseInfo.isOptionPoint and Order.Customer is not null %}
                <div class="ec-totalBox__pointBlock">
                    <dl class="ec-totalBox__spec">
                        <dt>{{ 'front.shopping.use_point'|trans }}</dt>
                        <dd>{{ Order.UsePoint|number_format }} pt</dd>
                    </dl>
                    <dl class="ec-totalBox__spec">
                        <dt><span class="ec-font-bold">{{ 'front.shopping.add_point'|trans }}</span></dt>
                        <dd><span class="ec-font-bold">{{ Order.AddPoint|number_format }} pt</span></dd>
                    </dl>
                </div>
                {% endif %}
                <div class="ec-totalBox__btn">
                    <button type="submit" class="ec-blockBtn--action">{{ 'front.shopping.checkout'|trans }}</button>
                    <a href="{{ url('shopping') }}" class="ec-blockBtn--cancel">{{ 'front.shopping.back_to_order'|trans }}</a>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}
