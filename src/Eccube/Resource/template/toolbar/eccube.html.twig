{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% extends 'WebProfilerBundle:Profiler:layout.html.twig' %}

{% block toolbar %}
    {% set icon %}
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 124.45 105.65"><defs><style>.cls-1{fill:#353a4e;}.cls-2{fill:#fc0;}.cls-3{fill:#f2b50a;}</style></defs><title>eccube_logo_basic</title><path class="cls-1" d="M106.81,15s6.63,49.2-37.4,62.22c-4.09,0-56.94,6.54-58.6-46.42V81.11L69.4,95,110.81,77V16.58Z"/><path class="cls-2" d="M110.81,63.42V77L69.4,95,10.81,81.11V65C4,69.18,0,74.22,0,79.65c0,14.36,27.86,26,62.22,26s62.22-11.64,62.22-26C124.45,73.51,119.33,67.86,110.81,63.42Z"/><path class="cls-2" d="M69.4,0,10.81,25C9,84.13,61.55,78.42,69.4,77.21Z"/><path class="cls-3" d="M106.81,15,69.4,0V77.21C115.22,65.84,106.81,15,106.81,15Z"/></svg>
    <span class="sf-toolbar-value">{{ collector.version }}</span>
    {% endset %}
    {% set text %}
    <script>
        // Minimize to toolbar
        Sfjs.setPreference('toolbar/displayState', 'none');
    </script>
    <style>
        .sf-toolbar-block .sf-toolbar-info-piece.sf-toolbar-plugins .sf-toolbar-status {
            display: block !important;
            float: left;
            margin-right: 4px !important;
            margin-left: 0 !important;
            margin-top: 4px;
        }
        .sf-toolbar-block .sf-toolbar-info-piece.sf-toolbar-plugins > b {
            vertical-align: middle;
        }
    </style>
    <div class="sf-toolbar-info-group">
        <div class="sf-toolbar-info-piece">
            <b>EC-CUBE</b>
            <span><a href="https://www.ec-cube.net">{{ collector.version }}</a></span>
        </div>
        <div class="sf-toolbar-info-piece sf-toolbar-plugins">
            <b>Plugins</b>
            {% for plugin in collector.plugins %}
                <div class="sf-toolbar-status sf-toolbar-status-{{ plugin.enabled ? 'green' : 'red' }}" title="{{ plugin.name }}">
                    {{ plugin.code }}
                </div>
            {% endfor %}
        </div>
    </div>
    <div class="sf-toolbar-info-group">
        <div class="sf-toolbar-info-piece">
            <b>Currency</b>
            <span class="sf-toolbar-status sf-toolbar-status-green">
                <abbr title="Current {% if collector.currencyCode == collector.defaultCurrencyCode %}and default {% endif %}currency">
                    {{ collector.currencyCode|default('Undefined') }}
                </abbr>
            </span>
            {% if collector.currencyCode != collector.defaultCurrencyCode %}
                <span class="sf-toolbar-status">
                    <abbr title="Default locale">
                        {{ collector.defaultCurrencyCode }}
                    </abbr>
                </span>
            {% endif %}
        </div>
        <div class="sf-toolbar-info-piece">
            <b>Locale</b>
            <span class="sf-toolbar-status sf-toolbar-status-green">
                <abbr title="Current {% if collector.localeCode == collector.defaultLocaleCode %}and default {% endif %}locale">
                    {{ collector.localeCode|default('Undefined') }}
                </abbr>
            </span>
            {% if collector.localeCode != collector.defaultLocaleCode %}
                <span class="sf-toolbar-status">
                    <abbr title="Default locale">
                        {{ collector.defaultLocaleCode }}
                    </abbr>
                </span>
            {% endif %}
        </div>
    </div>
    <div class="sf-toolbar-info-group">
        <div class="sf-toolbar-info-piece">
            <b>Resources</b>
            <span><a href="https://doc.ec-cube.net/" rel="help">EC-CUBE Documentation</a></span>
        </div>
    </div>
    {% endset %}

    {% include '@WebProfiler/Profiler/toolbar_item.html.twig' with {'link': false, additional_classes: 'sf-toolbar-block-right'} %}
{% endblock %}
