{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% extends 'frame.twig' %}

{% set current = 5 %}

{% form_theme form _self %}
{% block form_row %}
    <div class="form-group{% if not valid %} has-error{% endif %}">
        <label>{{ label }}
            {% if required %}<span class="required">*</span>{% endif %}
        </label>
        {{ form_widget(form) }}
        {{ form_errors(form) }}
    </div>
{% endblock %}

{% block checkbox_row %}
    <div class="form-group{% if not valid %} has-error{% endif %}">
        {{ form_widget(form) }}
        {{ form_errors(form) }}
    </div>
{% endblock %}

{% block main %}
    <div id="main" class="step6">
        <div class="main_inner">
            <div class="main_content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="page-header">
                                <h1>{{ 'install.database_initalization'|trans }}</h1>
                                <p>{{ 'install.database_do_initialize'|trans|raw }}</p>
                            </div>
                            <form name="form1" id="form1" method="post" action="{{ path('install_step5') }}">
                                {{ form_widget(form._token) }}
                                <div class="note_box">
                                    <p>{{ 'install.database_skip_notice'|trans }}</p>
                                    {{ form_widget(form.no_update) }}

                                    {% for error in app.session.flashbag.get('eccube.front.error')  %}
                                        <div class="message">
                                            <p class="errormsg bg-danger">
                                                <span class="initialism form-error-icon badge badge-danger">ERROR</span>
                                                <span class="form-error-message">{{ error|trans|nl2br }}</span>
                                            </p>
                                        </div>
                                    {% endfor %}
                                </div>

                                <ul class="btn_area">
                                    <li><button type="submit" class="btn btn-primary btn-block btn-lg">{{ 'install.next'|trans }}</button></li>
                                    <li><a href="{{ path('install_step4') }}" class="btn btn-link btn-block btn-lg">{{ 'install.back'|trans }}</a></li>
                                </ul>
                            </form>
                        </div>
                        <!-- /.col -->
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
