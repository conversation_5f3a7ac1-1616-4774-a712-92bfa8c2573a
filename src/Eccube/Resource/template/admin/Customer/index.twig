{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% extends '@admin/default_frame.twig' %}

{% set menus = ['customer', 'customer_master'] %}

{% block title %}{{ 'admin.customer.customer_list'|trans }}{% endblock %}
{% block sub_title %}{{ 'admin.customer.customer_management'|trans }}{% endblock %}

{% form_theme searchForm '@admin/Form/bootstrap_4_horizontal_layout.html.twig' %}

{% block stylesheet %}
    <link rel="stylesheet" href="{{ asset('assets/css/tempusdominus-bootstrap-4.min.css', 'admin') }}">
    <style type="text/css">
        .datepicker-days th.dow:first-child,
        .datepicker-days td:first-child {
            color: #f00;
        }

        .datepicker-days th.dow:last-child,
        .datepicker-days td:last-child {
            color: #00f;
        }
    </style>
{% endblock stylesheet %}

{% block javascript %}
    <script>
        $(function() {
            if ($('[type="date"]').prop('type') != 'date') {
                // input type属性でdateが利用できるかどうか(カレンダー表示できないブラウザ対応)
                $.when(
                    $.getScript("{{ asset('assets/js/vendor/moment.min.js', 'admin') }}"),
                    $.getScript("{{ asset('assets/js/vendor/moment-with-locales.min.js', 'admin') }}"),
                    $.getScript("{{ asset('assets/js/vendor/tempusdominus-bootstrap-4.min.js', 'admin') }}")
                ).done(function() {
                    $('input[id$=_date_start]').datetimepicker({
                        locale: '{{ eccube_config.locale }}',
                        format: 'YYYY-MM-DD',
                        useCurrent: false,
                        buttons: {
                            showToday: true,
                            showClose: true
                        }
                    });

                    $('input[id$=_date_end]').datetimepicker({
                        locale: '{{ eccube_config.locale }}',
                        format: 'YYYY-MM-DD',
                        useCurrent: false,
                        buttons: {
                            showToday: true,
                            showClose: true
                        }
                    });

                    $('#admin_search_customer_birth_start').datetimepicker({
                        locale: '{{ eccube_config.locale }}',
                        format: 'YYYY-MM-DD',
                        useCurrent: false,
                        buttons: {
                            showToday: true,
                            showClose: true
                        }
                    });

                    $('#admin_search_customer_birth_end').datetimepicker({
                        locale: '{{ eccube_config.locale }}',
                        format: 'YYYY-MM-DD',
                        useCurrent: false,
                        buttons: {
                            showToday: true,
                            showClose: true
                        }
                    });

                    $('#admin_search_customer_last_buy_start').datetimepicker({
                        locale: '{{ eccube_config.locale }}',
                        format: 'YYYY-MM-DD',
                        useCurrent: false,
                        buttons: {
                            showToday: true,
                            showClose: true
                        }
                    });

                    $('#admin_search_customer_last_buy_end').datetimepicker({
                        locale: '{{ eccube_config.locale }}',
                        format: 'YYYY-MM-DD',
                        useCurrent: false,
                        buttons: {
                            showToday: true,
                            showClose: true
                        }
                    });
                });
            }

        });

    </script>
{% endblock javascript %}

{% block main %}
    <form name="search_form" id="search_form" method="post" action="">
        {{ form_widget(searchForm._token) }}
        <div class="c-outsideBlock">
            <div class="c-outsideBlock__contents">
                <div class="row justify-content-start">
                    <div class="col-6">
                        <div class="mb-2">
                            <label class="col-form-label" data-tooltip="true" data-placement="top" title="{{ 'tooltip.customer.multi_search_label'|trans }}">{{ 'admin.customer.multi_search_label'|trans }}<i class="fa fa-question-circle fa-lg ml-1"></i></label>
                            {{ form_widget(searchForm.multi) }}
                            {{ form_errors(searchForm.multi) }}
                        </div>
                        <div class="d-inline-block mb-3 collapsed" data-toggle="collapse" href="#searchDetail" aria-expanded="false" aria-controls="searchDetail"><a><i class="fa font-weight-bold mr-1 fa-plus-square-o"></i><span class="font-weight-bold">{{ 'admin.common.search_detail'|trans }}</span></a></div>
                    </div>
                </div>
            </div>
            <div class="c-subContents ec-collapse collapse{{ has_errors ? ' show' }}" id="searchDetail">
                <div class="row mb-2">
                    <div class="col">
                        <div class="form-row">
                            <div class="col-12">
                                <p class="col-form-label">{{ 'admin.customer.customer_status'|trans }}</p>
                                {{ form_widget(searchForm.customer_status, { 'label_attr': { 'class': 'checkbox-inline'}}) }}
                                {{ form_errors(searchForm.customer_status) }}
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div>
                            <label>{{ 'admin.order.purchase_product'|trans }}</label>
                            {{ form_widget(searchForm.buy_product_name) }}
                            {{ form_errors(searchForm.buy_product_name) }}
                        </div>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col">
                        <div class="form-row">
                            <div class="col-12">
                                <p class="col-form-label">{{ 'admin.common.gender'|trans }}</p>
                                {{ form_widget(searchForm.sex, { 'label_attr': { 'class': 'checkbox-inline'}}) }}
                                {{ form_errors(searchForm.sex) }}
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div>
                            <label>{{ 'admin.order.purchase_price'|trans }}</label>
                            <div class="form-row align-items-center">
                                <div class="col">
                                    {{ form_widget(searchForm.buy_total_start) }}
                                    {{ form_errors(searchForm.buy_total_start) }}
                                </div>
                                <div class="col-auto text-center"><span>{{ 'admin.common.separator__range'|trans }}</span></div>
                                <div class="col">
                                    {{ form_widget(searchForm.buy_total_end) }}
                                    {{ form_errors(searchForm.buy_total_end) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col">
                        <div>
                            <label>{{ 'admin.customer.birth_month'|trans }}</label>
                            <div class="row">
                                <div class="col-5">
                                    {{ form_widget(searchForm.birth_month) }}
                                    {{ form_errors(searchForm.birth_month) }}
                                </div>
                                <div class="col-7"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div>
                            <label>{{ 'admin.order.purchase_count'|trans }}</label>
                            <div class="form-row align-items-center">
                                <div class="col">
                                    {{ form_widget(searchForm.buy_times_start) }}
                                    {{ form_errors(searchForm.buy_times_start) }}
                                </div>
                                <div class="col-auto text-center"><span>{{ 'admin.common.separator__range'|trans }}</span></div>
                                <div class="col">
                                    {{ form_widget(searchForm.buy_times_end) }}
                                    {{ form_errors(searchForm.buy_times_end) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col">
                        <div>
                            <label>{{ 'admin.common.birth_day'|trans }}</label>
                            <div class="form-row align-items-center">
                                <div class="col">
                                    {{ form_widget(searchForm.birth_start) }}
                                    {{ form_errors(searchForm.birth_start) }}
                                </div>
                                <div class="col-auto text-center"><span>{{ 'admin.common.separator__range'|trans }}</span></div>
                                <div class="col">
                                    {{ form_widget(searchForm.birth_end) }}
                                    {{ form_errors(searchForm.birth_end) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div>
                            <label>{{ 'admin.common.create_date'|trans }}</label>
                            <div class="form-row align-items-center">
                                <div class="col">
                                    {{ form_widget(searchForm.create_date_start) }}
                                    {{ form_errors(searchForm.create_date_start) }}
                                </div>
                                <div class="col-auto text-center"><span>{{ 'admin.common.separator__range'|trans }}</span></div>
                                <div class="col">
                                    {{ form_widget(searchForm.create_date_end) }}
                                    {{ form_errors(searchForm.create_date_end) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col">
                        <div>
                            <label>{{ 'admin.common.pref'|trans }}</label>
                            <div class="row">
                                <div class="col-5">
                                    {{ form_widget(searchForm.pref) }}
                                    {{ form_errors(searchForm.pref) }}
                                </div>
                                <div class="col-7"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div>
                            <label>{{ 'admin.common.update_date'|trans }}</label>
                            <div class="form-row align-items-center">
                                <div class="col">
                                    {{ form_widget(searchForm.update_date_start) }}
                                    {{ form_errors(searchForm.update_date_start) }}
                                </div>
                                <div class="col-auto text-center"><span>{{ 'admin.common.separator__range'|trans }}</span></div>
                                <div class="col">
                                    {{ form_widget(searchForm.update_date_end) }}
                                    {{ form_errors(searchForm.update_date_end) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col">
                        <div class="mb-3">
                            <label>{{ 'admin.common.phone_number'|trans }}</label>
                            {{ form_widget(searchForm.phone_number) }}
                            {{ form_errors(searchForm.phone_number) }}
                        </div>
                    </div>
                    <div class="col">
                        <div>
                            <label>{{ 'admin.order.last_buy_date'|trans }}</label>
                            <div class="form-row align-items-center">
                                <div class="col">
                                    {{ form_widget(searchForm.last_buy_start) }}
                                    {{ form_errors(searchForm.last_buy_start) }}
                                </div>
                                <div class="col-auto text-center"><span>{{ 'admin.common.separator__range'|trans }}</span></div>
                                <div class="col">
                                    {{ form_widget(searchForm.last_buy_end) }}
                                    {{ form_errors(searchForm.last_buy_end) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {# エンティティ拡張の自動出力 #}
                {% for f in searchForm if f.vars.eccube_form_options.auto_render %}
                    {# TODO 1項目1行になるのを改善 #}
                    <div class="row mb-2">
                        {% if f.vars.eccube_form_options.form_theme %}
                            {% form_theme f f.vars.eccube_form_options.form_theme %}
                            {{ form_row(f) }}
                        {% else %}
                            <div class="col">
                                <div class="mb-3">
                                    <label>{{ f.vars.label|trans }}</label>
                                    {{ form_widget(f) }}
                                    {{ form_errors(f) }}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
        </div>
        <div class="c-outsideBlock__contents mb-5">
            <button type="submit" class="btn btn-ec-conversion px-5">{{ 'admin.common.search'|trans }}</button>
            {% if pagination %}
                <span class="font-weight-bold ml-2">{{ 'admin.common.search_result'|trans({'%count%':pagination.totalItemCount}) }}</span>
            {% endif %}
        </div>
        <div class="c-outsideBlock__contents mb-5">
            {{ include('@admin/search_items.twig', { 'form': searchForm }, ignore_missing = true) }}
        </div>
        <div class="c-contentsArea__cols">
            <div class="c-contentsArea__primaryCol">
                <div class="c-primaryCol">
                    {% if pagination and pagination.totalItemCount %}
                        <div class="row justify-content-between mb-2">
                            <div class="col-6"></div>

                            <div class="col-5 text-right">
                                {#Dropdown page count#}
                                <div class="d-inline-block mr-2">
                                    <select class="custom-select" onchange="location = this.value;">
                                        {% for pageMax in pageMaxis %}
                                            <option {% if pageMax.name == page_count %} selected {% endif %}
                                                    value="{{ path('admin_customer_page', {'page_no': 1, 'page_count': pageMax.name }) }}">
                                                {{ 'admin.common.count'|trans({ '%count%': pageMax.name }) }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="d-inline-block">
                                    <div class="btn-group" role="group">
                                        <a class="btn btn-ec-regular" href="{{ url('admin_customer_export') }}"><i class="fa fa-cloud-download mr-1 text-secondary"></i><span>{{ 'admin.common.csv_download'|trans }}</span></a>
                                        <a class="btn btn-ec-regular" href="{{ url('admin_setting_shop_csv', { id : constant('\\Eccube\\Entity\\Master\\CsvType::CSV_TYPE_CUSTOMER') }) }}"><i class="fa fa-cog mr-1 text-secondary"></i><span>{{ 'admin.setting.shop.csv_setting'|trans }}</span></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card rounded border-0 mb-4 d-block">
                            <div class="card-body p-0">
                                <table class="table">
                                    <thead>
                                    <tr>
                                        <th class="border-top-0 pt-2 pb-3 pl-3 text-nowrap">{{ 'admin.customer.customer_id'|trans }}</th>
                                        <th class="border-top-0 pt-2 pb-3">{{ 'admin.common.name'|trans }}</th>
                                        <th class="border-top-0 pt-2 pb-3">{{ 'admin.common.phone_number'|trans }}</th>
                                        <th class="border-top-0 pt-2 pb-3">{{ 'admin.common.mail_address'|trans }}</th>
                                        <th class="border-top-0 pt-2 pb-3">&nbsp;</th>
                                        <th class="border-top-0 pt-2 pb-3 pr-3">&nbsp;</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for Customer in pagination %}
                                        <tr id="ex-customer-{{ Customer.id }}">
                                            <td class="align-middle pl-3">{{ Customer.id }}</td>
                                            <td class="align-middle"><a href="{{ url('admin_customer_edit', { 'id': Customer.id}) }}">{{ Customer.name01 }}&nbsp;{{ Customer.name02 }}</a></td>
                                            <td class="align-middle">{{ Customer.phone_number }}</td>
                                            <td class="align-middle">{{ Customer.email }}</td>
                                            <td class="align-middle"></td>
                                            <td class="align-middle pr-3">
                                                <div class="text-right">
                                                    {% if Customer.Status.id == constant('Eccube\\Entity\\Master\\CustomerStatus::PROVISIONAL') %}
                                                        <div class="px-1 d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'admin.customer.resend'|trans }}">
                                                            <a class="btn btn-ec-actionIcon" data-toggle="modal" data-target="#discontinuance_cus_{{ Customer.id }}">
                                                                <i class="fa fa-send fa-lg text-secondary" aria-hidden="true"></i>
                                                            </a>
                                                        </div>
                                                        <div class="modal fade" id="discontinuance_cus_{{ Customer.id }}" tabindex="-1" role="dialog" aria-labelledby="discontinuance" aria-hidden="true">
                                                            <div class="modal-dialog" role="document">
                                                                <div class="modal-content">
                                                                    <div class="modal-header">
                                                                        <h5 class="modal-title font-weight-bold">{{ 'admin.customer.resend_confirm_title'|trans }}</h5>
                                                                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                                                                            <span aria-hidden="true">×</span>
                                                                        </button>
                                                                    </div>
                                                                    <div class="modal-body text-left">
                                                                        <p class="text-left">{{ 'admin.customer.resend_confirm_message'|trans }}</p>
                                                                    </div>
                                                                    <div class="modal-footer">
                                                                        <button class="btn btn-ec-sub" type="button" data-dismiss="modal">{{ 'admin.common.cancel'|trans }}</button>
                                                                        {# TODO: CSSの変更は別タスクでやります #}
                                                                        <a class="btn btn-ec-delete" href="{{ url('admin_customer_resend', {'id' : Customer.id}) }}" {{ csrf_token_for_anchor() }} data-method="get" data-confirm="false">
                                                                            {{ 'admin.common.send'|trans }}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    {% endif %}
                                                    <div class="px-1 d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'admin.common.delete'|trans }}">
                                                        <a class="btn btn-ec-actionIcon" data-toggle="modal" data-target="#discontinuance-{{ Customer.id }}">
                                                            <i class="fa fa-close fa-lg text-secondary" aria-hidden="true"></i>
                                                        </a>
                                                    </div>
                                                    <div class="modal fade" id="discontinuance-{{ Customer.id }}" tabindex="-1" role="dialog" aria-labelledby="discontinuance" aria-hidden="true">
                                                        <div class="modal-dialog" role="document">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title font-weight-bold">
                                                                        {{ 'admin.common.delete_modal__title'|trans }}</h5>
                                                                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                                                                        <span aria-hidden="true">×</span>
                                                                    </button>
                                                                </div>
                                                                <div class="modal-body text-left">
                                                                    <p class="text-left">
                                                                        {{ 'admin.common.delete_modal__message'|trans({ '%name%' : Customer.email }) }}</p>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button class="btn btn-ec-sub" type="button" data-dismiss="modal">
                                                                        {{ 'admin.common.cancel'|trans }}
                                                                    </button>
                                                                    <a href="{{ url('admin_customer_delete', {'id' : Customer.id}) }}" class="btn btn-ec-delete"{{ csrf_token_for_anchor() }} data-method="delete" data-confirm="false">
                                                                        {{ 'admin.common.delete'|trans }}
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div><!-- /.text-right -->
                                            </td>
                                        </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                                <div class="row justify-content-md-center mb-4">
                                    {% if pagination.totalItemCount > 0 %}
                                        {% include "@admin/pager.twig" with { 'pages' : pagination.paginationData, 'routes' : 'admin_customer_page' } %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% elseif has_errors %}
                        <div class="card rounded border-0">
                            <div class="card-body p-4">
                                <div class="text-center text-muted mb-4 h5">{{ 'admin.common.search_invalid_condition'|trans }}</div>
                                <div class="text-center text-muted">{{ 'admin.common.search_try_change_condition'|trans }}</div>
                            </div>
                        </div>
                    {% else %}
                        <div class="card rounded border-0">
                            <div class="card-body p-4">
                                <div class="text-center text-muted mb-4 h5">{{ 'admin.common.search_no_result'|trans }}</div>
                                <div class="text-center text-muted">{{ 'admin.common.search_try_change_condition'|trans }}</div>
                                <div class="text-center text-muted">{{ 'admin.common.search_try_advanced_search'|trans }}</div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </form>
{% endblock %}
