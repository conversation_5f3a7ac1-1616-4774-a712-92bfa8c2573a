{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% extends '@admin/default_frame.twig' %}

{% set menus = ['order', 'order_edit'] %}

{% block title %}{{ 'admin.order.shipping_registration'|trans }}{% endblock %}
{% block sub_title %}{{ 'admin.order.order_management'|trans }}{% endblock %}

{% form_theme form '@admin/Form/bootstrap_4_horizontal_layout.html.twig' %}
{% form_theme searchProductModalForm '@admin/Form/bootstrap_4_horizontal_layout.html.twig' %}

{% block javascript %}
    <script src="//yubinbango.github.io/yubinbango/yubinbango.js" charset="UTF-8"></script>

    <script>
        // 商品追加
        $('.addProduct-button').on('click', function() {
            var no = $(this).data('shipping-no');
            $collectionHolder = $('#table-form-field_' + no);
            index = $collectionHolder.find('tbody > tr').length;
            formIdPrefix = '#form_shippings_' + no + '_OrderItems_';
        })

        // その他明細追加
        $('.addOtherProduct-button').on('click', function() {
            var no = $(this).data('shipping-no');
            $collectionHolder = $('#table-form-field_' + no);
            index = $collectionHolder.find('tbody > tr').length;
            formIdPrefix = '#form_shippings_' + no + '_OrderItems_';
        })

        // 商品検索
        $('#searchProductModalButton').on('click', function() {
            var list = $('#searchProductModalList');
            list.children().remove();

            $.ajax({
                url: '{{ url('admin_order_search_product') }}',
                type: 'POST',
                dataType: 'html',
                data: {
                    'id': $('#admin_search_product_id').val(),
                    'category_id': $('#admin_search_product_category_id').val()
                }
            }).done(function(data) {
                $('#searchProductModalList').html(data);
            }).fail(function(data) {
                alert('search product failed.');
            });
        });

        // その他明細
        $('#addOrderItemType').on('show.bs.modal', function(e) {
            var list = $('#searchOrderItemTypeList');
            list.children().remove();

            $.ajax({
                url: '{{ url('admin_order_search_order_item_type') }}',
                type: 'POST',
                dataType: 'html'
            }).done(function(data) {
                $('#searchOrderItemTypeList').html(data);
            }).fail(function(data) {
                alert('search order item type failed.');
            });
        });

        $(document).on('click', '.delete', function(e) {
            // 商品削除
            $(this).parents('tr').remove();
            $("#form1").submit();

            return false;
        });

        $(document).on('click', '.delete-shipping', function(e) {
            // お届け先削除
            $(this).parents('div.card').remove();
            $("#form1").submit();

            return false;
        });

        {# TODO: 商品削除時に更新が走って画面上に行ってしまう。 #}
        // 計算結果の更新が押されたらページ内リンクさせる
        // $('.btn').click(function() {
        //     var data = $(this).data();
        //     if (data.link) {
        //         $('#form1').attr('action', '#' + data.link).submit();
        //         return false;
        //     }
        // });

        // 配送業者選択時にお届け時間を設定
        var times = {{ shippingDeliveryTimes|raw }};

        $("select[id$='_Delivery']").on('change', function() {
            var deliveryId = $(this).val();
            var $shippingDeliveryTime = $(this).parents('.card-body').find("select[id$='_DeliveryTime']");
            $shippingDeliveryTime.find('option').remove();
            $shippingDeliveryTime.append($('<option></option>').val('').text(trans('admin.common.select__unspecified')));
            if (typeof(times[deliveryId]) !== 'undefined') {
                for (var timeId in times[deliveryId]) {
                    timeValue = times[deliveryId][timeId];
                    $shippingDeliveryTime.append($('<option></option>')
                        .val(timeId)
                        .text(timeValue));
                }
            }
        });

        $('#addShipping').on('click', function() {
            $('#form_add_shipping').val("1");
            $("#form1").submit();

            return false;
        })

        // 完了ボタン
        $('#bulkChangeComplete').on('click', function() {
            location.href = '{{ url('admin_shipping_edit', { 'id': Order.id }) }}';
        });

        // PDF出力(単一)
        $('.pdf-print').click(function() {
            window.open(this.href, 'newwin', 'toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=yes, resizable=yes, width=700, height=700');
            return false;
        });

        // モーダル注意文言の削除
        $('#bulkChange').on('click', function() {
            $('.warning-message').css('display', 'none');
        })
    </script>

    {{ include('@admin/Order/confirmationModal_js.twig') }}

{% endblock javascript %}

{% block main %}
    <form name="form1" id="form1" action="{{ url('admin_shipping_edit', {id: Order.id}) }}"
          method="post">
        {{ form_widget(form._token) }}
        {{ form_widget(form.add_shipping) }}
        <!-- 商品明細追加モーダル -->
        <div class="modal fade" id="addProduct" tabindex="-1" role="dialog"
             aria-labelledby="addProduct" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ 'admin.order.add_product_item'|trans }}</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        {{ form_widget(searchProductModalForm.id, { attr : {'class': 'mb-3',  placeholder : 'admin.product.multi_search_label' }}) }}
                        {{ form_widget(searchProductModalForm.category_id) }}
                        <button type="button" id="searchProductModalButton"
                                class="btn btn-ec-conversion px-5 mb-4 mt-2">{{ 'admin.common.search'|trans }}</button>
                        <div id="searchProductModalList"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- その他明細追加モーダル -->
        <div class="modal fade" id="addOrderItemType" tabindex="-1" role="dialog" aria-labelledby="addOrderItemType" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ 'admin.order.add_other_item'|trans }}</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
                    </div>
                    <div class="modal-body">
                        <div id="searchOrderItemTypeList"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 出荷済にするモーダル / 出荷メール送信モーダル -->
        <div class="modal fade" id="sentUpdateModal" tabindex="-1" role="dialog" aria-labelledby="sentUpdateModal" aria-hidden="true" data-keyboard="false" data-backdrop="static">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title font-weight-bold"><!--confirmationModal_js.twig--></h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
                    </div>
                    <div class="modal-body">
                        <p class="mb-4 modal-message"><!--confirmationModal_js.twig--></p>
                        <p class="mb-4 warning-message">{{ 'admin.order.bulk_action__confirm_message'|trans }}</p>
                        <ul id="bulkErrors"></ul>
                        <div id="bulk-options">
                            <div class="font-weight-bold mb-2 notificationMail">{{ 'admin.order.to_shipped__confirm_send_mail'|trans }}</div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="notificationMail">
                                <label class="form-check-label notificationMail">
                                    {{ 'admin.order.to_shipped__confirm_send_mail_in_same_time'|trans }}
                                </label>
                            </div>
                            <div>
                                <div class="d-inline-block" data-toggle="collapse" href="#viewEmail" aria-expanded="false" aria-controls="viewEmail"><a><i class="fa fa-plus-square-o font-weight-bold mr-1"></i><span class="font-weight-bold">{{ 'メール詳細'|trans }}</span></a></div>
                                <div class="collapse bg-light p-4 ec-collapse bg-ec-formGray" id="viewEmail" style="word-wrap: break-word; word-break:break-all">
                                    <pre></pre>
                                </div>
                            </div>
                        </div>
                        <div class="progress" style="display: none">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-ec-sub" type="button" data-dismiss="modal">{{ 'admin.common.cancel'|trans }}</button>
                        <button id="bulkChange" class="btn btn-ec-conversion" type="button"><!--confirmationModal_js.twig--></button>
                        <!--完了ボタン-->
                        <button id="bulkChangeComplete" class="btn btn-ec-regular" style="display: none" type="button">{{ 'admin.common.close'|trans }}</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="c-contentsArea__cols">
            <div class="c-contentsArea__primaryCol">
                <div class="c-primaryCol">
                    {% for shippingForm in form.shippings %}
                        <div class="card rounded border-0 mb-4 h-adr">
                            <span class="p-country-name" style="display:none;">Japan</span>
                            <!-- 出荷情報 -->
                            <div class="card-header">
                                <div class="row">
                                    <div class="col-8">
                                        <div class="d-inline-block">
                                            <span class="card-title">{{ 'admin.order.shipping__card_title'|trans }}({{ loop.index }})</span>
                                        </div>
                                    </div>
                                    <div class="col-4 text-right">
                                        {% if form.shippings|length > 1 %}
                                            <!-- 出荷が2つ以上ある場合は, 出荷の削除ボタンを表示する -->
                                            <a class="btn btn-ec-regular mr-2" data-toggle="modal" data-target="#delete_shipping_{{ shippingForm.vars.id }}" data-tooltip="true" data-placement="top" title="{{ 'admin.common.delete'|trans }}">
                                                <i class="fa fa-close fa-lg text-secondary" aria-hidden="true"></i>
                                                {{ 'admin.order.delete_shipping'|trans }}
                                            </a>
                                            <div class="modal fade" id="delete_shipping_{{ shippingForm.vars.id }}" tabindex="-1" role="dialog" aria-labelledby="delete_shipping_{{ shippingForm.vars.id }}" aria-hidden="true">
                                                <div class="modal-dialog" role="document">
                                                    <div class="modal-content">
                                                        {% if shippingForm.OrderItems|length > 0 %}
                                                            <div class="modal-header">
                                                                <h5 class="modal-title font-weight-bold">{{ 'admin.order.delete_shipping_error__confirm_title'|trans }}</h5>
                                                                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">×</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body text-left">
                                                                <p class="text-left">{{ 'admin.order.delete_shipping_error__confirm_message'|trans }}</p>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button class="btn btn-ec-sub" type="button" data-dismiss="modal">{{ 'admin.common.cancel'|trans }}</button>
                                                            </div>
                                                        {% else %}
                                                            <div class="modal-header">
                                                                <h5 class="modal-title font-weight-bold">{{ 'admin.common.delete_modal__title'|trans }}</h5>
                                                                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">×</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body text-left">
                                                                <p class="text-left">{{ 'admin.order.delete_shipping__confirm_message'|trans({ "%num%" : loop.index }) }}</p>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button class="btn btn-ec-sub" type="button" data-dismiss="modal">{{ 'admin.common.cancel'|trans }}</button>
                                                                <a href="#shipping-product_{{ loop.index0 }}" class="btn delete-shipping btn-ec-delete">{{ 'admin.common.delete'|trans }}</a>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                        {% endif %}
                                        <a data-toggle="collapse" href="#shipmentOverview_{{ loop.index0 }}" aria-expanded="false"
                                           aria-controls="shipmentOverview_{{ loop.index0 }}">
                                            <i class="fa fa-angle-up fa-lg" aria-hidden="true"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="collapse show ec-cardCollapse" id="shipmentOverview_{{ loop.index0 }}">
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            {% if shippingForm.vars.value.id %}
                                                {# ボタンは登録済みの出荷のみ表示する #}
                                                <!-- 納品書を出力ボタン -->
                                                <a class="btn btn-ec-regular pdf-print" href="{{ url('admin_order_export_pdf') }}?ids[]={{ shippingForm.vars.value.id }}">{{ 'admin.order.output_delivery_note'|trans }}</a>
                                                <!-- 出荷メール送信ボタン -->
                                                <button type="button" class="btn btn-ec-regular confirmationModal"
                                                        data-type="mail" data-bulk-update="false"
                                                        data-preview-notify-mail-url="{{ url('admin_shipping_preview_notify_mail', { id: shippingForm.vars.value.id}) }}"
                                                        data-notify-mail-url="{{ url('admin_shipping_notify_mail', { id: shippingForm.vars.value.id}) }}">
                                                    {{ 'admin.order.shipping_mail_send'|trans }}
                                                </button>
                                            {% else %}
                                                {# 未登録の出荷 #}
                                            {% endif %}
                                        </div>
                                    </div>
                                    <!-- お届先情報 -->
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="row form-group">
                                                <label class="col-3 col-form-label">
                                                    {{ 'admin.common.name'|trans }}
                                                    <span class="badge badge-primary ml-1">
                                                        {{ 'admin.common.required'|trans }}
                                                    </span>
                                                </label>
                                                <div class="col">
                                                    <div class="row">
                                                        <div class="col">
                                                            {{ form_widget(shippingForm.name.name01) }}
                                                            {{ form_errors(shippingForm.name.name01) }}
                                                        </div>
                                                        <div class="col">
                                                            {{ form_widget(shippingForm.name.name02) }}
                                                            {{ form_errors(shippingForm.name.name02) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row form-group">
                                                <label class="col-3 col-form-label">
                                                    {{ 'admin.common.kana'|trans }}
                                                    <span class="badge badge-primary ml-1">
                                                        {{ 'admin.common.required'|trans }}
                                                    </span>
                                                </label>
                                                <div class="col">
                                                    <div class="row">
                                                        <div class="col">
                                                            {{ form_widget(shippingForm.kana.kana01) }}
                                                            {{ form_errors(shippingForm.kana.kana01) }}
                                                        </div>
                                                        <div class="col">
                                                            {{ form_widget(shippingForm.kana.kana02) }}
                                                            {{ form_errors(shippingForm.kana.kana02) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row form-group">
                                                <label class="col-3 col-form-label">
                                                    {{ 'admin.common.address'|trans }}
                                                    <span class="badge badge-primary ml-1">
                                                        {{ 'admin.common.required'|trans }}
                                                    </span>
                                                </label>
                                                <div class="col">
                                                    <div class="row mb-3">
                                                        <div class="col form-inline">
                                                            {{ 'admin.common.postal_symbol'|trans }}
                                                            {{ form_widget(shippingForm.postal_code) }}
                                                            {{ form_errors(shippingForm.postal_code) }}
                                                        </div>
                                                    </div>
                                                    <div class="row mb-3">
                                                        <div class="col">
                                                            {{ form_widget(shippingForm.address.pref) }}
                                                            {{ form_errors(shippingForm.address.pref) }}
                                                        </div>
                                                    </div>
                                                    <div class="row mb-3">
                                                        <div class="col">
                                                            {{ form_widget(shippingForm.address.addr01) }}
                                                            {{ form_errors(shippingForm.address.addr01) }}
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col">
                                                            {{ form_widget(shippingForm.address.addr02) }}
                                                            {{ form_errors(shippingForm.address.addr02) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="row form-group">
                                                <label class="col-3 col-form-label">
                                                    {{ 'admin.common.phone_number'|trans }}
                                                    <span class="badge badge-primary ml-1">
                                                        {{ 'admin.common.required'|trans }}
                                                    </span>
                                                </label>
                                                <div class="col">
                                                    {{ form_widget(shippingForm.phone_number) }}
                                                    {{ form_errors(shippingForm.phone_number) }}
                                                </div>
                                            </div>

                                            <div class="row form-group">
                                                <label class="col-3 col-form-label">
                                                    {{ 'admin.common.company_name'|trans }}
                                                </label>
                                                <div class="col">
                                                    {{ form_widget(shippingForm.company_name) }}
                                                    {{ form_errors(shippingForm.company_name) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <hr>
                                    </div>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="row mb-3 form-group">
                                                <label class="col-3 col-form-label">
                                                    {{ 'admin.order.tracking_number'|trans }}
                                                </label>
                                                <div class="col">
                                                    {{ form_widget(shippingForm.tracking_number) }}
                                                    {{ form_errors(shippingForm.tracking_number) }}
                                                </div>
                                            </div>
                                            <div class="row mb-3 form-group">
                                                <label class="col-3 col-form-label">
                                                    {{ 'admin.order.delivery_provider'|trans }}
                                                    <span class="badge badge-primary ml-1">{{ 'admin.common.required'|trans }}
                                                    </span>
                                                </label>
                                                <div class="col">
                                                    {{ form_widget(shippingForm.Delivery) }}
                                                    {{ form_errors(shippingForm.Delivery) }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="row mb-3 form-group">
                                                <label class="col-3 col-form-label">
                                                    <i class="fa fa-calendar-check-o fa-fw mr-1" aria-hidden="true"></i>
                                                    {{ 'admin.order.delivery_date'|trans }}
                                                </label>
                                                <div class="col">
                                                    {{ form_widget(shippingForm.shipping_delivery_date) }}
                                                    {{ form_errors(shippingForm.shipping_delivery_date) }}
                                                </div>
                                            </div>
                                            <div class="row mb-3 form-group">
                                                <label class="col-3 col-form-label">
                                                    <i class="fa fa-clock-o fa-fw mr-1" aria-hidden="true"></i>
                                                    {{ 'admin.order.delivery_date'|trans }}
                                                </label>
                                                <div class="col">
                                                    {{ form_widget(shippingForm.DeliveryTime) }}
                                                    {{ form_errors(shippingForm.DeliveryTime) }}
                                                </div>
                                            </div>
                                            <div class="row mb-3 form-group">
                                                <label class="col-3 col-form-label">
                                                    <i class="fa fa-truck fa-fw mr-1" aria-hidden="true"></i>
                                                    {{ 'admin.order.shipping_date'|trans }}
                                                </label>
                                                <div class="col">
                                                    {% if shippingForm.vars.value.shipping_date %}
                                                        {# 登録済みの出荷で出荷日が設定されている場合は出荷日を表示する #}
                                                        <span>{{ shippingForm.vars.value.shipping_date|date_min }}</span>
                                                    {% elseif shippingForm.vars.value.id %}
                                                        {# 登録済みの出荷で出荷日が設定されていない場合 #}
                                                        <!-- 出荷済にするボタン -->
                                                        <button type="button" class="btn btn-ec-regular mr-2 confirmationModal" href="javascript:;"
                                                                data-id="{{ shippingForm.vars.value.id }}" data-type="status" data-bulk-update="false"
                                                                data-update-status-id="{{ constant('Eccube\\Entity\\Master\\OrderStatus::DELIVERED') }}"
                                                                data-update-status-url="{{ url('admin_shipping_update_order_status', { id: shippingForm.vars.value.id}) }}"
                                                                data-preview-notify-mail-url="{{ url('admin_shipping_preview_notify_mail', { id: shippingForm.vars.value.id}) }}"
                                                                data-tooltip="true" data-placement="top" title data-original-title="{{ 'admin.order.to_shipped'|trans }}">
                                                            <i class="fa fa-check fa-lg text-secondary" aria-hidden="true"></i>
                                                            {{ 'admin.order.to_shipped'|trans }}
                                                        </button>
                                                    {% else %}
                                                        {# 未登録の出荷 #}
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="row mb-3 form-group">
                                                <label class="col-3 col-form-label">
                                                    <i class="fa fa-refresh fa-fw mr-1" aria-hidden="true"></i>
                                                    {{ 'admin.common.last_update_date'|trans }}
                                                </label>
                                                <div class="col">
                                                    <span>{{ shippingForm.vars.value.id ? shippingForm.vars.value.update_date|date_min }}</span>
                                                </div>
                                            </div>
                                            <div class="row mb-2 align-items-center">
                                                <label class="col-3 col-form-label">
                                                    <i class="fa fa-user fa-fw mr-1" aria-hidden="true"></i>
                                                    {{ 'admin.common.last_updater'|trans }}
                                                </label>
                                                <div class="col">
                                                    <span>{{ shippingForm.vars.value.id ? shippingForm.vars.value.creator }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <hr>
                                    </div>
                                    <!-- 明細追加ボタン -->
                                    <div id="shipping-product_{{ loop.index0 }}" class="row justify-content-between mb-2">
                                        <div class="col-6">
                                            <!-- 商品を追加 -->
                                            <button class="btn btn-ec-regular mr-2 addProduct-button" type="button" data-toggle="modal"
                                                    data-target="#addProduct" data-shipping-no="{{ loop.index0 }}">
                                                {{ 'admin.order.add_product_item'|trans }}
                                            </button>
                                            <!-- その他の明細を追加 -->
                                            <a class="btn btn-ec-regular mr-2 addOtherProduct-button" data-toggle="modal" data-target="#addOrderItemType" data-shipping-no="{{ loop.index0 }}">{{ 'admin.order.add_other_item'|trans }}</a>
                                            {# 明細行のエラー表示 #}
                                            {{ form_errors(shippingForm.OrderItemsErrors) }}
                                        </div>
                                    </div>
                                    <!-- 明細 -->
                                    <div class="row">
                                        <table id="table-form-field_{{ loop.index0 }}" class="table table-striped table-sm mb-0"
                                               data-prototype="{% filter escape %}{{ include('@admin/Order/order_item_prototype.twig', {'orderItemForm': shippingForm.OrderItems.vars.prototype}) }}{% endfilter %}">
                                            <thead class="table-active">
                                            <tr class="text-nowrap">
                                                <th class="pt-2 pb-2 pl-3">{{ 'admin.product.product_name_and_code'|trans }}</th>
                                                <th class="pt-2 pb-2">
                                                    <div class="col-8">{{ 'admin.order.amount'|trans }}</div>
                                                </th>
                                                <th class="pt-2 pb-2">
                                                    <div class="col-8">{{ 'admin.order.quantity'|trans }}</div>
                                                </th>
                                                <th class="pt-2 pb-2">
                                                    <div class="col-8">{{ 'admin.order.tax_rate'|trans }}</div>
                                                </th>
                                                <th class="pt-2 pb-2">
                                                    <div class="col-8">{{ 'admin.order.tax_type'|trans }}</div>
                                                </th>
                                                <th class="pt-2 pb-2">
                                                    <div class="col-8">{{ 'admin.order.subtotal'|trans }}</div>
                                                </th>
                                                <th class="pt-2 pb-2 pr-3"></th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            {% set shippingNo = loop.index0 %}
                                            {% for orderItemForm in shippingForm.OrderItems %}
                                                {% set OrderItem = orderItemForm.vars.data %}
                                                <tr>
                                                    {# hidden values #}
                                                    {{ form_widget(orderItemForm.ProductClass) }}
                                                    {{ form_widget(orderItemForm.order_item_type) }}
                                                    {{ form_widget(orderItemForm.tax_type) }}
                                                    {{ form_widget(orderItemForm.tax_rate, {'type': 'hidden'}) }}
                                                    <td class="align-middle w-25 pl-3">
                                                        <p class="mb-0 font-weight-bold">
                                                            {% if OrderItem.OrderItemType.isProduct %}
                                                                {{ OrderItem.product_name }}
                                                                {{ form_widget(orderItemForm.product_name, { 'type': 'hidden' }) }}
                                                            {% else %}
                                                                {{ form_widget(orderItemForm.product_name) }}
                                                            {% endif %}
                                                        </p>
                                                        <span>
                                                            {{ OrderItem.product_code }}
                                                            {% if OrderItem.class_category_name1 is not empty %}
                                                                / (
                                                                {{ OrderItem.class_name1 }}：
                                                                {{ OrderItem.class_category_name1 }}
                                                                {% if OrderItem.class_category_name2 is not empty %}
                                                                    /
                                                                    {{ OrderItem.class_name2 }}：
                                                                    {{ OrderItem.class_category_name2 }}
                                                                {% endif %}
                                                                )
                                                            {% endif %}
                                                        </span>
                                                        {{ form_errors(orderItemForm.product_name) }}
                                                    </td>
                                                    <td class="align-middle">
                                                        <div class="col mt-3">
                                                            {{ form_widget(orderItemForm.price, {'attr': { 'readonly': 'readonly' } }) }}
                                                            <div class="text-right small">({{ OrderItem.TaxDisplayType }})</div>
                                                            {{ form_errors(orderItemForm.price) }}
                                                        </div>
                                                    </td>
                                                    <td class="align-middle">
                                                        <div class="col-12 col-xl-8">
                                                            {{ form_widget(orderItemForm.quantity) }}
                                                            {{ form_errors(orderItemForm.quantity) }}
                                                        </div>
                                                    </td>
                                                    <td class="align-middle">
                                                        <div class="col">
                                                            {{ OrderItem.tax_rate }}%
                                                        </div>
                                                    </td>
                                                    <td class="align-middle">
                                                        <div class="col">
                                                            {{ OrderItem.tax_type }}
                                                        </div>
                                                    </td>
                                                    <td class="align-middle">
                                                        <div class="col">
                                                            <span>{{ OrderItem.total_price|price }}</span>
                                                        </div>
                                                    </td>
                                                    <td class="align-middle text-right pr-3">
                                                        <div class="row justify-content-end">
                                                            <div class="col-auto text-center">
                                                                <div class="d-inline-block mr-3" data-tooltip="true"
                                                                     data-placement="top" title="{{ 'admin.common.delete'|trans }}">
                                                                    <a class="btn btn-ec-actionIcon" data-toggle="modal" data-target="#delete_{{ orderItemForm.vars.id }}">
                                                                        <i class="fa fa-close fa-lg text-secondary" aria-hidden="true"></i>
                                                                    </a>
                                                                </div>
                                                                <!-- 削除確認モーダル -->
                                                                <div class="modal fade" id="delete_{{ orderItemForm.vars.id }}" tabindex="-1" role="dialog" aria-labelledby="delete_{{ orderItemForm.vars.id }}" aria-hidden="true">
                                                                    <div class="modal-dialog" role="document">
                                                                        <div class="modal-content">
                                                                            <div class="modal-header">
                                                                                <h5 class="modal-title font-weight-bold">{{ 'admin.common.delete_modal__title'|trans }}</h5>
                                                                                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                                                                                    <span aria-hidden="true">×</span>
                                                                                </button>
                                                                            </div>
                                                                            <div class="modal-body text-left">
                                                                                <p class="text-left">{{ 'admin.order.delete_item__confirm_message'|trans({ "%name%" : OrderItem.product_name }) }}</p>
                                                                            </div>
                                                                            <div class="modal-footer">
                                                                                <button class="btn btn-ec-sub" type="button" data-dismiss="modal">{{ 'admin.common.cancel'|trans }}</button>
                                                                                <a href="#shipping-product_{{ shippingNo }}" class="btn delete btn-ec-delete">{{ 'admin.common.delete'|trans }}</a>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="col-12">
                                        <hr>
                                    </div>
                                    <!-- 出荷用メモ -->
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="row mb-3 form-group">
                                                <label class="col-3 col-form-label">
                                                    {{ 'admin.order.shop_memo_for_shipped'|trans }}
                                                </label>
                                                <div class="col">
                                                    {{ form_widget(shippingForm.note, { attr: { rows: 8 }}) }}
                                                    {{ form_errors(shippingForm.note) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                    <!-- 出荷先を追加 -->
                    <div class="card rounded border-0 mb-4">
                        <button id="addShipping" type="button" class="btn btn-ec-regular">{{ 'admin.order.add_shipping'|trans }}</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- コンバージョンエリア -->
        <div class="c-conversionArea">
            <div class="c-conversionArea__container">
                <div class="row justify-content-between align-items-center">
                    <!-- 受注一覧 -->
                    <div class="col-6">
                        <div class="c-conversionArea__leftBlockItem">
                            <a class="c-baseLink" href="{{ url("admin_order_edit", {id: Order.id}) }}">
                                <i class="fa fa-backward" aria-hidden="true"></i>
                                <span>{{ 'admin.order.order_registration'|trans }}</span>
                            </a>
                        </div>
                    </div>
                    <!-- 登録 -->
                    <div class="col-6">
                        <div class="row align-items-center justify-content-end">
                            <div class="col-auto">
                                <button type="submit" class="btn btn-ec-conversion px-4" id="btn_save" name="mode" value="register">
                                    {{ 'admin.common.registration'|trans }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
{% endblock %}
