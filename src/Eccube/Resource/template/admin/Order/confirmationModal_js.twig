<script>
    $(function() {
        var updater;
        // モーダルの表示を制御
        $('#bulkSendMail, .confirmationModal').on('click', function (e) {
            var modal = $('#sentUpdateModal');
            modal.modal();
            var eventTarget = $(e.currentTarget);
            var type = eventTarget.data('type');
            switch (type) {
                case 'mail':
                    updater = eventTarget.data('bulk-update') ? new BulkSendMail(modal, eventTarget) : new SimpleSendMail(modal, eventTarget);
                    $('#notificationMail').attr('type', 'hidden');
                    $('.notificationMail').hide();
                    $('#viewEmail').removeClass('collapsed').addClass('collapsed');
                    ;
                    break;
                default:
                case 'status':
                    updater = new SimpleStatusUpdate(modal, eventTarget); // bulk-update is always false
                    $('#notificationMail').attr('type', 'checkbox');
                    $('.notificationMail').show();
                    $('#viewEmail').removeClass('collapsed').addClass('collapsed');
            }
            $.ajax(updater.getPreviewUrl()).done(function (res) {
                $('#viewEmail').html(res);
            });
            $('.modal-title', modal).text(updater.modalTitle);
            $('.modal-body > p.modal-message', modal).text(updater.modalMessage);
            $('#bulkChange')
                .attr({
                    'data-bulk-update': eventTarget.data('bulk-update'),
                    'data-type': eventTarget.data('type'),
                    'data-update-status-url': eventTarget.data('update-status-url'),
                    'data-notify-mail-url': eventTarget.data('notify-mail-url'),
                    'data-update-status-id': eventTarget.data('update-status-id')
                })
                .text(updater.modalButton);
        });
        // プログレスバーの表示を制御
        $('#bulkChange, .progressModal').on('click', function (e) {
            //alert(1119);
            var eventTarget = $(e.currentTarget);
            var type = eventTarget.data('type');
            var modal = $('#sentUpdateModal');
            switch (type) {
                case 'mail':
                    updater = eventTarget.data('bulk-update') ? new BulkSendMail(modal, eventTarget) : new SimpleSendMail(modal, eventTarget);
                    break;
                default:
                case 'status':
                    if (eventTarget.data('bulk-update')) {
                        if ($('#option_bulk_status').val() == '') {
                            alert('対応状況を選択してください');
                            return;
                        }
                        updater = new BulkStatusUpdate(modal, eventTarget);
                        modal.modal();
                    } else {
                        updater = new SimpleStatusUpdate(modal, eventTarget);
                    }
            }
            $('.modal-title', modal).text(updater.modalTitle);
            $('.modal-body > p.modal-message', modal).text("{{ 'admin.order.bulk_action__in_progress_message'|trans }}");
            $('button', modal).hide();
            $('#bulk-options').hide();
            $('.progress', modal).show();
            updater.totalCount = updater.getTotalCount();
            var progress = new $.Deferred();
            progress.progress(function () {
                updater.progress(this, progress);
            }).fail(function () {
                updater.fail(this);
            }).always(function () {
                updater.always(this);
            });
            updater.getPromises(progress);
        });

    });

    /*
     * Super class
     */
    function ConfirmationModal(modal) {
        this.modal = modal;
        this.mailCount = 0;
        this.currentCount = 0;
        this.totalCount = 0;
    }
    ConfirmationModal.prototype = {
        modalTitle: "{{ 'admin.order.to_shipped__confirm_title'|trans }}",
        modalMessage: "{{ 'admin.order.to_shipped__confirm_message'|trans }}",
        modalButton: "{{ 'admin.common.execute'|trans }}",
        getPreviewUrl: function () {
            return null;
        },
        getTotalCount: function () {
            return 1;
        },
        progress: function (result, progress) {
            $('.progress-bar', this.modal).css('width', (++this.currentCount / this.totalCount * 100) + '%');
            if (result['message']) {
                $('<li><span class="badge badge-warning">NOTICE</span> </li>')
                    .append($('<span></span>').text(result['message']))
                    .appendTo('#bulkErrors');
            }
            if (this.currentCount >= this.totalCount) {
                progress.resolve();
            }
        },
        fail: function (result) {
            $('<li><span class="badge badge-danger">ERROR</span> </li>')
                .append($('<span></span>').text("{{ 'admin.common.system_error'|trans }}"))
                .appendTo('#bulkErrors');
        },
        always: function (result) {
            $('.progress', this.modal).hide();
            $('.modal-body > p.modal-message', this.modal).text("{{ 'admin.order.bulk_action__complete_message'|trans }}");
            $('#bulkChangeComplete').show();
        },
        getPromises: function (progress, url, data) {
            if (data == undefined) {
                data = {'notificationMail': $('input#notificationMail:checked').val()};
            }
            return $.ajax({
                'url': url,
                'type': 'PUT',
                'data': data
            })
                .fail(function () {
                    progress.reject();
                    ConfirmationModal.prototype.fail.call(this);
                })
                .always(function (data) {
                    progress.notifyWith(data);
                });
        }
    };
    /*
     * ステータス一括更新
     */
    function BulkStatusUpdate(modal, eventTarget) {
        ConfirmationModal.call(this, modal);
        this.eventTarget = eventTarget;
    }
    // extend super class
    BulkStatusUpdate.prototype = Object.create(ConfirmationModal.prototype, {
        constructor: {
            value: ConfirmationModal
        },
        modalTitle: {
            value: "{{ 'admin.order.change_status'|trans }}"
        },
        getTotalCount: {
            value: function () {
                return $('input[data-id]:checked').length;
            }
        },
        getPromises: {
            value: function (progress) {
                return $('input[data-id]:checked').map(function () {
                    var url = $(this).data('update-status-url');
                    var data = {'order_status': $('#option_bulk_status').val()};
                    return ConfirmationModal.prototype.getPromises.call(this, progress, url, data);
                });
            }
        }
    });
    /*
     * ステータス個別更新
     */
    function SimpleStatusUpdate(modal, eventTarget) {
        ConfirmationModal.call(this, modal);
        this.eventTarget = eventTarget;
        this.notifierCompleteMessage = '';
    }
    // extend super class
    SimpleStatusUpdate.prototype = Object.create(ConfirmationModal.prototype, {
        constructor: {
            value: ConfirmationModal
        },
        getPreviewUrl: {
            value: function () {
                return this.eventTarget.data('preview-notify-mail-url');
            }
        },
        progress: {
            value: function (result, progress) {
                if (result.mail) {
                    this.mailCount++;
                    this.notifierCompleteMessage = '{{ 'admin.order.shipping_mail_send__complete_message'|trans }}'.replace(/%count%/, this.mailCount);
                }
                ConfirmationModal.prototype.progress.call(this, result, progress);
            }
        },
        always: {
            value: function (result) {
                ConfirmationModal.prototype.always.call(this, result);
                $('.modal-body > p.modal-message', this.modal).text("{{ 'admin.order.bulk_action__complete_message'|trans }}" + this.notifierCompleteMessage);
            }
        },
        getPromises: {
            value: function (progress) {
                var url = this.eventTarget.data('update-status-url');
                var data = {
                    'order_status': this.eventTarget.data('update-status-id'),
                    'notificationMail': $('input#notificationMail:checked').val()
                };
                return ConfirmationModal.prototype.getPromises.call(this, progress, url, data);
            }
        }
    });
    /*
     * メール一括送信
     */
    function BulkSendMail(modal) {
        SimpleStatusUpdate.call(this, modal);
    }
    // extend BulkUpdate
    BulkSendMail.prototype = Object.create(SimpleStatusUpdate.prototype, {
        constructor: {
            value: SimpleStatusUpdate
        },
        modalTitle: {
            value: "{{ 'admin.order.shipping_mail_send__confirm_title'|trans }}"
        },
        modalMessage: {
            value: "{{ 'admin.order.shipping_mail_send__confirm_message'|trans }}"
        },
        modalButton: {
            value: "{{ 'admin.common.send'|trans }}"
        },
        getPreviewUrl: {
            value: function () {
                return $('input[data-preview-notify-mail-url]:checked').data('preview-notify-mail-url');
            }
        },
        getTotalCount: {
            value: function () {
                return $('input[data-preview-notify-mail-url]:checked').length;
            }
        },
        getPromises: {
            value: function (progress) {
                return $('input[data-notify-mail-url]:checked').map(function () {
                    var url = $(this).data('notify-mail-url');
                    return ConfirmationModal.prototype.getPromises.call(this, progress, url);
                });
            }
        }
    });
    /*
     * 個別メール送信
     */
    function SimpleSendMail(modal, relatedTarget) {
        SimpleStatusUpdate.call(this, modal, relatedTarget);
    }
    // extends SimpleUpdate
    SimpleSendMail.prototype = Object.create(SimpleStatusUpdate.prototype, {
        constructor: {
            value: SimpleStatusUpdate
        },
        modalTitle: {
            value: "{{ 'admin.order.shipping_mail_send__confirm_title'|trans }}"
        },
        modalMessage: {
            value: "{{ 'admin.order.shipping_mail_send__confirm_message'|trans }}"
        },
        modalButton: {
            value: "{{ 'admin.common.send'|trans }}"
        },
        getPromises: {
            value: function (progress) {
                var url = this.eventTarget.data('notify-mail-url');
                return ConfirmationModal.prototype.getPromises.call(this, progress, url);
            }
        }
    });
</script>
