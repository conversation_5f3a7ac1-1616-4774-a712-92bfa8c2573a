{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% extends '@admin/login_frame.twig' %}

{% form_theme form '@admin/Form/bootstrap_4_horizontal_layout.html.twig' %}

{% block main %}

    <div class="container" style="margin-top: 150px;">
        <div class="row">
            <div class="col-12 col-md-6 offset-md-3 col-lg-4 offset-lg-4">
                <div class="text-center p-5 bg-white">
                    {{ include('@admin/alert.twig') }}
                    <form name="form1" id="form1" method="post" action="{{ path('admin_login') }}">
                        <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">
                        <p><img src="{{ asset('assets/img/logo2.png', 'admin') }}" width="106"></p>
                        <div class="form-group">
                            {{ form_widget(form.login_id, {'id': 'login_id', 'attr': {'placeholder': 'admin.login.login_id', 'autofocus': true}}) }}
                        </div>
                        <div class="form-group">
                            {{ form_widget(form.password, {'attr': {'placeholder': 'admin.login.password'}}) }}
                        </div>
                        {% if error %}
                            <div class="form-group">
                                <span class="text-danger">{{ error.messageKey|trans(error.messageData, 'validators')|nl2br }}</span>
                            </div>
                        {% endif %}
                        <button type="submit" class="btn btn-primary btn-lg btn-block">{{ 'admin.login.login'|trans }}</button>
                        {{ form_rest(form) }}
                    </form>
                </div>
            </div>
            <div class="col-12">
                <p class="text-center mt-3">
                    <small>Copyright &copy; 2000-{{ "now"|date("Y") }} EC-CUBE CO.,LTD. All Rights Reserved.</small>
                </p>
            </div>
        </div>
    </div>

{% endblock %}
