{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% extends '@admin/default_frame.twig' %}

{% set menus = ['setting', 'shop', 'shop_index'] %}

{% block title %}{{ 'admin.setting.shop.shop_setting'|trans }}{% endblock %}
{% block sub_title %}{{ 'admin.setting.shop'|trans }}{% endblock %}

{% form_theme form '@admin/Form/bootstrap_4_horizontal_layout.html.twig' %}

{% block javascript %}
    <script src="//yubinbango.github.io/yubinbango/yubinbango.js" charset="UTF-8"></script>
{% endblock %}

{% block main %}
    <form name="form1" role="form" class="form-horizontal h-adr" id="point_form" method="post" action="">
        <span class="p-country-name" style="display:none;">Japan</span>

        {{ form_widget(form._token) }}

        <div class="c-contentsArea__cols">
            <div class="c-contentsArea__primaryCol">
                <div class="c-primaryCol">
                    <div class="card rounded border-0 mb-4">
                        <div class="card-header"><span>{{ 'admin.setting.shop.shop.base_info'|trans }}</span></div>
                        <div id="ex-shop-basic" class="card-body">
                            <div class="row">
                                <div class="col-3"><span>{{ 'admin.common.company_name'|trans }}</span></div>
                                <div class="col mb-2">
                                    {{ form_widget(form.company_name) }}
                                    {{ form_errors(form.company_name) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-3"><span>{{ 'admin.common.company_name_kana'|trans }}</span></div>
                                <div class="col mb-2">
                                    {{ form_widget(form.company_kana) }}
                                    {{ form_errors(form.company_kana) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-3"><span>{{ 'admin.setting.shop.shop.shop_name'|trans }}</span><span class="badge badge-primary ml-1">{{ 'admin.common.required'|trans }}</span></div>
                                <div class="col mb-2">
                                    {{ form_widget(form.shop_name) }}
                                    {{ form_errors(form.shop_name) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-3"><span>{{ 'admin.setting.shop.shop.shop_name_kana'|trans }}</span></div>
                                <div class="col mb-2">
                                    {{ form_widget(form.shop_kana) }}
                                    {{ form_errors(form.shop_kana) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-3"><span>{{ 'admin.setting.shop.shop.shop_name_en'|trans }}</span></div>
                                <div class="col mb-2">
                                    {{ form_widget(form.shop_name_eng) }}
                                    {{ form_errors(form.shop_name_eng) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-3"><span>{{ 'admin.common.address'|trans }}</span></div>
                                <div class="col mb-2">
                                    <div class="mb-2">
                                        <div class="row justify-content-start">
                                            <div class="col-auto text-center pr-0"><span class="align-middle">{{ 'admin.common.postal_symbol'|trans }}</span></div>
                                            <div class="col-2">
                                                {{ form_widget(form.postal_code) }}
                                            </div>
                                            {{ form_errors(form.postal_code) }}
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <div class="row justify-content-start">
                                            <div class="col-3">
                                                {{ form_widget(form.address.pref) }}
                                                {{ form_errors(form.address.pref) }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        {{ form_widget(form.address.addr01) }}
                                        {{ form_errors(form.address.addr01) }}
                                    </div>
                                    <div class="mb-2">
                                        {{ form_widget(form.address.addr02) }}
                                        {{ form_errors(form.address.addr02) }}
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-3"><span>{{ 'admin.common.phone_number'|trans }}</span></div>
                                <div class="col">
                                    {{ form_widget(form.phone_number) }}
                                    {{ form_errors(form.phone_number) }}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-3"><span>{{ 'admin.setting.shop.shop.business_hour'|trans }}</span></div>
                                <div class="col mb-2">
                                    {{ form_widget(form.business_hour) }}
                                    {{ form_errors(form.business_hour) }}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-3">
                                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.email_from'|trans }}"><span>{{ 'admin.setting.shop.shop.email_from'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i></div>
                                    <span class="badge badge-primary ml-1">{{ 'admin.common.required'|trans }}</span>
                                </div>
                                <div class="col mb-2">
                                    {{ form_widget(form.email01) }}
                                    {{ form_errors(form.email01) }}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-3">
                                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.email_for_inquiries'|trans }}"><span>{{ 'admin.setting.shop.shop.email_for_inquiries'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i></div>
                                    <span class="badge badge-primary ml-1">{{ 'admin.common.required'|trans }}</span>
                                </div>
                                <div class="col mb-2">
                                    {{ form_widget(form.email02) }}
                                    {{ form_errors(form.email02) }}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-3">
                                    <div class="d-inline-block" class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.email_reply_to'|trans }}"><span>{{ 'admin.setting.shop.shop.email_reply_to'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i></div>
                                    <span class="badge badge-primary ml-1">{{ 'admin.common.required'|trans }}</span>
                                </div>
                                <div class="col mb-2">
                                    {{ form_widget(form.email03) }}
                                    {{ form_errors(form.email03) }}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-3">
                                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.email_return_path'|trans }}"><span>{{ 'admin.setting.shop.shop.email_return_path'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i></div>
                                    <span class="badge badge-primary ml-1">{{ 'admin.common.required'|trans }}</span>
                                </div>
                                <div class="col mb-2">
                                    {{ form_widget(form.email04) }}
                                    {{ form_errors(form.email04) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-3">
                                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.good_traded'|trans }}"><span>{{ 'admin.setting.shop.shop.good_traded'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i></div>
                                </div>
                                <div class="col mb-2">
                                    {{ form_widget(form.good_traded) }}
                                    {{ form_errors(form.good_traded) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-3">
                                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.message'|trans }}"><span>{{ 'admin.setting.shop.shop.message'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i></div>
                                </div>
                                <div class="col mb-2">
                                    {{ form_widget(form.message) }}
                                    {{ form_errors(form.message) }}
                                </div>
                            </div>
                            {# エンティティ拡張の自動出力 #}
                            {% for f in form if f.vars.eccube_form_options.auto_render %}
                                {% if f.vars.eccube_form_options.form_theme %}
                                    {% form_theme f f.vars.eccube_form_options.form_theme %}
                                    {{ form_row(f) }}
                                {% else %}
                                    <div class="row">
                                        <div class="col-3"><span>{{ f.vars.label|trans }}</span></div>
                                        <div class="col mb-2">
                                            {{ form_widget(f) }}
                                            {{ form_errors(f) }}
                                        </div>
                                    </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    <div class="card rounded border-0 mb-4">
                        <div class="card-header"><span>{{ 'admin.setting.shop.shop.option_delivery_fee'|trans }}</span></div>
                        <div id="ex-shop-delivery" class="card-body">
                            <div class="row">
                                <div class="col-3">
                                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.option_delivery_fee_free_amount'|trans }}"><span>{{ 'admin.setting.shop.shop.option_delivery_fee_free_amount'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i></div>
                                </div>
                                <div class="col-4 mb-2">
                                    {{ form_widget(form.delivery_free_amount) }}
                                    {{ form_errors(form.delivery_free_amount) }}
                                </div>
                                <div class="col"></div>
                            </div>
                            <div class="row">
                                <div class="col-3">
                                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.option_delivery_fee_free_quantity'|trans }}"><span>{{ 'admin.setting.shop.shop.option_delivery_fee_free_quantity'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i></div>
                                </div>
                                <div class="col-4 mb-2">
                                    {{ form_widget(form.delivery_free_quantity) }}
                                    {{ form_errors(form.delivery_free_quantity) }}
                                </div>
                                <div class="col"></div>
                            </div>
                            <div class="row">
                                <div class="col-3">
                                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.option_delivery_fee_by_product'|trans }}"><span>{{ 'admin.setting.shop.shop.option_delivery_fee_by_product'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i></div>
                                </div>
                                <div class="col mb-2">
                                    {{ form_widget(form.option_product_delivery_fee) }}
                                    {{ form_errors(form.option_product_delivery_fee) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card rounded border-0 mb-4">
                        <div class="card-header"><span>{{ 'admin.setting.shop.shop.option_customer'|trans }}</span></div>
                        <div id="ex-shop-customer" class="card-body">
                            <div class="row">
                                <div class="col-3">
                                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.option_customer_activate'|trans }}"><span>{{ 'admin.setting.shop.shop.option_customer_activate'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i></div>
                                </div>
                                <div class="col mb-2">
                                    {{ form_widget(form.option_customer_activate) }}
                                    {{ form_errors(form.option_customer_activate) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-3">
                                    <span>{{ 'admin.setting.shop.shop.option_mypage_order_status_display'|trans }}</span>
                                </div>
                                <div class="col mb-2">
                                    {{ form_widget(form.option_mypage_order_status_display) }}
                                    {{ form_errors(form.option_mypage_order_status_display) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-3">
                                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.option_favorite_product'|trans }}"><span>{{ 'admin.setting.shop.shop.option_favorite_product'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i></div>
                                </div>
                                <div class="col mb-2">
                                    {{ form_widget(form.option_favorite_product) }}
                                    {{ form_errors(form.option_favorite_product) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-3">
                                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.option_remember_me'|trans }}"><span>{{ 'admin.setting.shop.shop.option_remember_me'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i></div>
                                </div>
                                <div class="col mb-2">
                                    {{ form_widget(form.option_remember_me) }}
                                    {{ form_errors(form.option_remember_me) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card rounded border-0 mb-4">
                        <div class="card-header"><span>{{ 'admin.setting.shop.shop.option_product'|trans }}</span></div>
                        <div id="ex-shop-product" class="card-body">
                            <div class="row">
                                <div class="col-3"><span>{{ 'admin.setting.shop.shop.nostock_hidden'|trans }}</span></div>
                                <div class="col mb-2">
                                    {{ form_widget(form.option_nostock_hidden) }}
                                    {{ form_errors(form.option_nostock_hidden) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card rounded border-0 mb-4">
                        <div class="card-header"><span>{{ 'admin.setting.shop.shop.option_tax'|trans }}</span></div>
                        <div id="ex-shop-tax" class="card-body">
                            <div class="row">
                                <div class="col-3">
                                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.option_product_tax'|trans }}"><span>{{ 'admin.setting.shop.shop.option_product_tax'|trans }}</span></div>
                                </div>
                                <div class="col mb-2">
                                    {{ form_widget(form.option_product_tax_rule) }}
                                    {{ form_errors(form.option_product_tax_rule) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card rounded border-0 mb-4">
                        <div class="card-header"><span>{{ 'admin.setting.shop.shop.option_point'|trans }}</span></div>
                        <div id="ex-shop-point" class="card-body">
                            <div class="row">
                                <div class="col-3">
                                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.option_point_enabled'|trans }}"><span>{{ 'admin.setting.shop.shop.option_point_enabled'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i></div>
                                </div>
                                <div class="col mb-2">
                                    {{ form_widget(form.option_point) }}
                                    {{ form_errors(form.option_point) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-3">
                                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.option_point_rate'|trans }}"><span>{{ 'admin.setting.shop.shop.option_point_rate'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i></div>
                                </div>
                                <div class="col mb-2">
                                    <div class="input-group col-2 pl-0">
                                        <div class="input-group">
                                            {{ form_widget(form.basic_point_rate) }}
                                            <div class="input-group-append">
                                                <span class="input-group-text">%</span>
                                            </div>
                                            {{ form_errors(form.basic_point_rate) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-3">
                                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.shop.shop.option_point_conversion_rate'|trans }}"><span>{{ 'admin.setting.shop.shop.option_point_conversion_rate'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i></div>
                                </div>
                                <div class="col mb-2">
                                    <div class="input-group col-2 pl-0">
                                        <div class="input-group">
                                            {{ form_widget(form.point_conversion_rate) }}
                                            {{ form_errors(form.point_conversion_rate) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="c-conversionArea">
            <div class="c-conversionArea__container">
                <div class="row justify-content-between align-items-center">
                    <div class="col-6">
                        <div class="c-conversionArea__leftBlockItem">
                        </div>
                    </div>
                    <div id="ex-conversion-action" class="col-6">
                        <div class="row align-items-center justify-content-end">
                            <div class="col-auto">
                                <button class="btn btn-ec-conversion px-5" type="submit">{{ 'admin.common.registration'|trans }}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
{% endblock %}