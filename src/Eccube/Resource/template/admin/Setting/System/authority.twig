{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% extends '@admin/default_frame.twig' %}

{% set menus = ['setting', 'system', 'authority'] %}

{% block title %}{{ 'admin.setting.system.authority_management'|trans }}{% endblock %}
{% block sub_title %}{{ 'admin.setting.system'|trans }}{% endblock %}

{% form_theme form '@admin/Form/bootstrap_4_layout.html.twig' %}

{% block javascript %}
    <script>
        $(function() {

            var $collectionHolder = $('#table-authority');
            var index = $collectionHolder.find('.authority-rule').length;

            $('.add').click(function() {
                var prototype = $collectionHolder.data('prototype');
                index++;
                var newForm = prototype.replace(/__name__/g, index);
                var $lastRow = $('#table-authority tbody > tr:last')
                $lastRow.after(newForm);
            });

            $(document).on('click', '.delete', function() {
                $(this).parent('td').parent('tr').remove();
                var idx = $collectionHolder.find('.authority-rule').length;
                if (idx == 0) {
                    var prototype = $collectionHolder.data('prototype');
                    var newForm = prototype.replace(/__name__/g, idx);
                    var $lastRow = $('#table-authority tbody');
                    $lastRow.append(newForm);
                }
            });

        });
    </script>
{% endblock javascript %}

{% block main %}
    <div class="c-contentsArea__cols">
        <div class="c-contentsArea__primaryCol">
            <div class="c-primaryCol">
                <form name="form1" method="post" action="{{ url('admin_setting_system_authority') }}">
                    {{ form_widget(form._token) }}
                    <div class="card rounded border-0 mb-4">
                        <div class="card-header">
                            <span class="card-title">{{ 'admin.setting.system.authority__card_title'|trans }}</span>
                        </div>
                        <div class="card-body">
                            <p>{{ 'admin.setting.system.authority.description'|trans({'%url%':app.request.baseUrl ~ '/' ~ eccube_config.eccube_admin_route}) }}</p>
                            <p>{{ 'admin.setting.system.authority.example'|trans|raw }}</p>
                            <div class="table-responsive">
                                <table id="table-authority" class="table table-striped table-condensed with-border"
                                       data-prototype="{% filter escape %}{{ include('@admin/Setting/System/authority_prototype.twig', {'form': form.AuthorityRoles.vars.prototype}) }}{% endfilter %}">
                                    <thead>
                                    <tr>
                                        <th style="width: 210px;">{{ 'admin.setting.system.authority.authority'|trans }}</th>
                                        <th>{{ 'admin.setting.system.authority.deny_url'|trans }}</th>
                                        <th style="width: 80px;"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for form in form.AuthorityRoles %}
                                        {{ include('@admin/Setting/System/authority_prototype.twig', {'form': form }) }}
                                    {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <p>
                                <button type="button" class="btn btn-ec-regular add">{{ 'admin.setting.system.authority.add_row'|trans }}</button>
                            </p>

                        </div>
                    </div>
                    <div class="c-conversionArea">
                        <div class="c-conversionArea__container">
                            <div class="row justify-content-between align-items-center">
                                <div class="col-6">
                                    <div class="c-conversionArea__leftBlockItem"></div>
                                </div>
                                <div class="col-6">
                                    <div id="ex-conversion-action" class="row align-items-center justify-content-end">
                                        <div class="col-auto">
                                            <button class="btn btn-ec-conversion px-5" type="submit">{{ 'admin.common.registration'|trans }}</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endblock %}
