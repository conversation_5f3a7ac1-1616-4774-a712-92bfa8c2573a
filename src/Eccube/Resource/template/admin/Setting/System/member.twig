{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% extends '@admin/default_frame.twig' %}

{% set menus = ['setting', 'system', 'member'] %}

{% block title %}{{ 'admin.setting.system.member_management'|trans }}{% endblock %}
{% block sub_title %}{{ 'admin.setting.system'|trans }}{% endblock %}

{% form_theme form 'Form/bootstrap_4_layout.html.twig' %}

{% block javascript %}
    <script>
        $('.action-down, .action-up').on('click', function(e) {
            var self = $(this),
                current = $(this).parents('tr');
            $('body').append($('<div class="modal-backdrop show"></div>'));
            $.ajax({
                url: self.attr('href'),
                method: self.data('method'),
            }).done(function() {
                if (self.hasClass('action-down')) {
                    current.next().after(current);
                } else {
                    current.prev().before(current);
                }
            }).always(function() {
                $('.modal-backdrop').remove();
                $('.action-up, .action-down').removeClass('disabled');
                $('.action-up').first().addClass('disabled');
                $('.action-down').last().addClass('disabled');
            });

            return false;
        });
    </script>
{% endblock %}

{% block main %}
    <div class="c-contentsArea__cols">
        <div class="c-contentsArea__primaryCol">
            <div class="c-primaryCol">
                <div id="ex-member-new" class="d-block mb-3">
                    <a class="btn btn-ec-regular" href="{{ url('admin_setting_system_member_new') }}">{{ 'admin.common.registration__new'|trans }}</a>
                </div>
                <div class="card rounded border-0 mb-4">
                    <div class="card-body p-0">
                        <form name="form1" id="form1" method="post" action="">
                            <table class="table table-sm">
                                <thead>
                                <tr>
                                    <th class="border-top-0 pt-2 pb-2 text-center">
                                        {{ 'admin.setting.system.member.name'|trans }}
                                    </th>
                                    <th class="border-top-0 pt-2 pb-2 text-center">
                                        {{ 'admin.setting.system.member.department'|trans }}
                                    </th>
                                    <th class="border-top-0 pt-2 pb-2 text-center">
                                        {{ 'admin.common.authority'|trans }}
                                    </th>
                                    <th class="border-top-0 pt-2 pb-2 text-center">
                                        {{ 'admin.setting.system.member.work'|trans }}
                                    </th>
                                    <th class="border-top-0 pt-2 pb-2 text-center"></th>
                                </tr>
                                </thead>
                                <tbody>
                                {% for Member in Members %}
                                    <tr id="ex-member-{{ Member.id }}">
                                        <td class="align-middle text-center pl-3">
                                            {{ Member.name }}
                                        </td>
                                        <td class="align-middle text-center">
                                            {{ Member.department }}
                                        </td>
                                        <td class="align-middle text-center">
                                            {{ Member.Authority.name }}
                                        </td>
                                        <td class="align-middle text-center">
                                            {{ Member.Work.name }}
                                        </td>
                                        <td class="align-middle pr-3">
                                            <div class="text-right">
                                                <div class="px-1 d-inline-block">
                                                    <a class="btn btn-ec-actionIcon action-edit"
                                                       href="{{ url('admin_setting_system_member_edit', { 'id' : Member.id }) }}"
                                                       data-tooltip="true"
                                                       data-placement="top"
                                                       data-original-title="{{ 'admin.common.edit'|trans }}">
                                                        <i class="fa fa-pencil fa-lg text-secondary" aria-hidden="true"></i>
                                                    </a>
                                                </div>
                                                <div class="px-1 d-inline-block">
                                                    <a class="btn btn-ec-actionIcon action-up {% if loop.first %} disabled {% endif %}"
                                                       href="{{ url('admin_setting_system_member_up', {id: Member.id}) }}"
                                                       data-tooltip="true"
                                                       data-method="put"
                                                       data-placement="top"
                                                       data-original-title="{{ 'admin.common.up'|trans }}">
                                                        <i class="fa fa-arrow-up fa-lg text-secondary" aria-hidden="true"></i>
                                                    </a>
                                                </div>
                                                <div class="px-1 d-inline-block">
                                                    <a class="btn btn-ec-actionIcon action-down {% if loop.last %} disabled {% endif %}"
                                                       href="{{ url('admin_setting_system_member_down', {id: Member.id}) }}"
                                                       data-tooltip="true"
                                                       data-method="put"
                                                       data-placement="top"
                                                       data-original-title="{{ 'admin.common.down'|trans }}">
                                                        <i class="fa fa-arrow-down fa-lg text-secondary" aria-hidden="true"></i>
                                                    </a>
                                                </div>
                                                <div class="px-1 d-inline-block">
                                                    {% if Member.id == app.user.id %}
                                                        <a class="btn btn-ec-actionIcon action-delete mr-2 disabled"
                                                            data-tooltip="true" data-placement="top"
                                                            title="{{ 'admin.common.delete'|trans }}">
                                                            <i class="fa fa-close fa-lg text-secondary" aria-hidden="true"></i>
                                                        </a>
                                                    {% else %}
                                                        <div class="d-inline-block mr-2" data-tooltip="true" data-placement="top"
                                                             title="{{ 'admin.common.delete'|trans }}">
                                                            <a class="btn btn-ec-actionIcon action-delete"
                                                               data-toggle="modal"
                                                               data-target="#member_delete_{{ Member.id }}">
                                                                <i class="fa fa-close fa-lg text-secondary" aria-hidden="true"></i>
                                                            </a>
                                                        </div>
                                                        <div class="modal fade" id="member_delete_{{ Member.id }}" tabindex="-1"
                                                             role="dialog"
                                                             aria-labelledby="member_delete_{{ Member.id }}" aria-hidden="true">
                                                            <div class="modal-dialog" role="document">
                                                                <div class="modal-content">
                                                                    <div class="modal-header">
                                                                        <h5 class="modal-title font-weight-bold">
                                                                            {{ 'admin.setting.system.member.delete__confirm_title'|trans }}</h5>
                                                                        <button class="close" type="button"
                                                                                data-dismiss="modal"
                                                                                aria-label="Close"><span
                                                                                    aria-hidden="true">×</span>
                                                                        </button>
                                                                    </div>
                                                                    <div class="modal-body text-left">
                                                                        <p class="text-left">
                                                                            {{ 'admin.setting.system.member.delete__confirm_message'|trans }}</p>
                                                                    </div>
                                                                    <div class="modal-footer">
                                                                        <button class="btn btn-ec-sub" type="button" data-dismiss="modal">{{ 'admin.common.cancel'|trans }}
                                                                        </button>
                                                                        <a class="btn btn-ec-delete"
                                                                           href="{{ url('admin_setting_system_member_delete', {id: Member.id}) }}"
                                                                                {{ csrf_token_for_anchor() }}
                                                                           data-method="delete" data-confirm="false">
                                                                            {{ 'admin.common.delete'|trans }}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}