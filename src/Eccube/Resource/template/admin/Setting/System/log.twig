{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}

{% extends '@admin/default_frame.twig' %}

{% set menus = ['setting', 'system', 'log'] %}

{% block title %}{{ 'admin.setting.system.log_display'|trans }}{% endblock %}
{% block sub_title %}{{ 'admin.setting.system'|trans }}{% endblock %}

{% block main %}
    <div class="c-outsideBlock">
        <div class="c-outsideBlock__contents">
            <div class="card rounded border-0 mb-4">
                <div class="card-header">
                    <div class="d-inline-block" data-tooltip="true" data-placement="top" title="{{ 'tooltip.setting.system.log_display'|trans }}">
                        <span class="card-title">{{ 'admin.setting.system.log_display'|trans }}</span><i class="fa fa-question-circle fa-lg ml-1"></i>
                    </div>
                </div>
                <div class="card-body">
                    <form name="form1" id="form1" method="post">
                        {{ form_widget(form._token) }}
                        <div class="form-inline form-group">
                            <div class="form-group pr-3">
                                {{ form_widget(form.files) }}
                            </div>
                            <div class="form-group pr-3">
                                {{ form_widget(form.line_max) }}
                                <span class="ml-2">{{ 'admin.setting.system.log.line_number'|trans }}</span>
                            </div>
                            <div class="form-group pr-3">
                                <button type="submit"
                                        class="btn btn-ec-conversion">{{ 'admin.setting.system.log.read'|trans }}</button>
                            </div>
                            {{ form_errors(form.line_max) }}
                        </div>
                    </form>
                    <div>
<textarea class="form-control" rows="20" wrap="off" readonly>
{% for line in log %}
    {{ line }}
{% endfor %}
</textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
