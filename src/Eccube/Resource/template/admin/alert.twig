{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% for message in app.flashes('eccube.admin.info') %}
    <div class="alert alert-primary alert-dismissible fade show m-3" role="alert">
        <i class="fa fa-comment fa-lg mr-2"></i>
        <span class="font-weight-bold">{{ message|trans }}</span>
        <button class="close" type="button" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">×</span>
        </button>
    </div>
{% endfor %}

{% for message in app.flashes('eccube.admin.success') %}
    <div class="alert alert-success alert-dismissible fade show m-3" role="alert">
        <i class="fa fa-check fa-lg mr-2"></i>
        <span class="font-weight-bold">{{ message|trans }}</span>
        <button class="close" type="button" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">×</span>
        </button>
    </div>
{% endfor %}

{% for message in app.flashes('eccube.admin.danger') %}
    <div class="alert alert-danger alert-dismissible fade show m-3" role="alert">
        <i class="fa fa-warning fa-lg mr-2"></i>
        <span class="font-weight-bold">{{ message|trans }}</span>
        <button class="close" type="button" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">×</span>
        </button>
    </div>
{% endfor %}

{% for message in app.flashes('eccube.admin.error') %}
    <div class="alert alert-danger alert-dismissible fade show m-3" role="alert">
        <i class="fa fa-warning fa-lg mr-2"></i>
        <span class="font-weight-bold">{{ message|trans }}</span>
        <button class="close" type="button" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">×</span>
        </button>
    </div>
{% endfor %}

{% for message in app.flashes('eccube.admin.warning') %}
    <div class="alert alert-warning alert-dismissible fade show m-3" role="alert">
        <i class="fa fa-warning fa-lg mr-2"></i>
        <span class="font-weight-bold">{{ message|trans }}</span>
        <button class="close" type="button" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">×</span>
        </button>
    </div>
{% endfor %}
