"""core URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from django.contrib.auth import views as auth_views
from django.views.generic import RedirectView
from myapp import views
from myapp.views import CustomLoginView
from myapp.views import CorporateMasterCSVUploadView
from myapp.views import ProjectCorporateMasterCSVUploadView
from myapp.views import BasicAnalyticsCSVUploadView
from myapp.views import corporate_list
from myapp.views import project_list
from django.conf import settings
from django.conf.urls.static import static
from myapp.views import CorporateMasterDeleteView
from myapp.views import ProjectDeleteView
from myapp.views import CorporateListAPI
from myapp.views import ProjectListAPI
from myapp.views import analyticsTopPage
from myapp.views import ProjectListByCompanyIdAPI
from myapp.views import analyticsDashboard
from myapp.views import analyticsExportCsv
from myapp.views import analyticsAnalogSignal
from myapp.views import exportAnalyticsAnalogSignal
urlpatterns = [
    path('login/', CustomLoginView.as_view(), name='login'),
    path('logout/', auth_views.LogoutView.as_view(next_page='login'), name='logout'),
    path('dashboard/', views.dashboard, name='dashboard'),
    # Redirect root URL to login page if not authenticated, otherwise to import-product-items-master
    path('', RedirectView.as_view(url='dashboard/'), name='root'),
    # API endpoints
    path('api/upload/corporate/', CorporateMasterCSVUploadView.as_view(), name='upload_corporate_csv'),
    path('api/upload/project/', ProjectCorporateMasterCSVUploadView.as_view(), name='upload_project_csv'),
    path('api/upload/analytics/', BasicAnalyticsCSVUploadView.as_view(), name='upload_analytics_csv'),
    path('corporates/', corporate_list, name='corporate_list'),
    path('api/corporates/', CorporateListAPI.as_view(), name='corporate_list_api'),
    path('api/projects/', ProjectListAPI.as_view(), name='project_list_api'),
    path('projects/', project_list, name='project_list'),
    path('corporate/<int:company_id>/delete/', CorporateMasterDeleteView.as_view(), name='corporate-delete'),
    path('projects/<int:system_id>/delete/', ProjectDeleteView.as_view(), name='project-delete'),
    path('analytics/', analyticsTopPage, name='analytics_top_page'),
    path('analytics-dashboard/', analyticsDashboard, name='analytics_dashboard'),
    path('api/projects-by-company-id/', ProjectListByCompanyIdAPI.as_view(), name='project_list_by_company_id_api'),
    path('analytics-dashboard/<int:project_id>', analyticsDashboard, name='analytics_dashboard'),
    path('analytics-analog-signal/<int:project_id>', analyticsAnalogSignal, name='analytics_dashboard'),
    path('export-csv-analytics/<int:project_id>', analyticsExportCsv, name='export_csv_analytics'),
    path('export-analytics-analog-signal/<int:project_id>', exportAnalyticsAnalogSignal, name='export_csv_projects'),
]
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATICFILES_DIRS[0])
