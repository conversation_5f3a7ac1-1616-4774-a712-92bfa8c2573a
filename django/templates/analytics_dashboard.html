{% extends "layout/base.html" %}
{% block content %}
<div class="container-xxl flex-grow-1 container-p-y">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-style1">
            <li class="breadcrumb-item">
                <a href="{% url 'analytics_top_page' %}">基礎分析</a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'project_list' %}?company_id={{company_id}}">{{company_name}}</a>
            </li>
            <li class="breadcrumb-item active">
                <a href="{% url 'analytics_top_page' %}">
                    {{system_name}}
                </a>
            </li>
        </ol>
    </nav>
    <h3 class="fw-bold">基礎分析 </h3>
    <div class="nav-align-top mb-4">
        <ul class="nav nav-tabs" role="tablist">
            <li class="nav-item">
                <button type="button" class="nav-link active" role="tab" data-bs-toggle="tab"
                        data-bs-target="#navs-top-home" aria-controls="navs-top-home" aria-selected="true">
                    日別・温度帯別
                </button>
            </li>
            <li class="nav-item">
                <button type="button" class="nav-link" onClick="window.location.href = '/analytics-analog-signal/{{project_id}}';" role="tab">
                    類似波形
                </button>
            </li>
        </ul>
        <div class="tab-content">
            <div class="tab-pane fade active show" id="navs-top-home" role="tabpanel">
                <div class="card shadow-none">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 col-lg-4 order-2 mb-4 "><label>系統名: <b>{{system_name}} </b> </label></div>
                            <div class="col-md-6 col-lg-4 order-2 mb-4 "><label>型番: <b>{{outdoor_model}}</b>  </label></div>
                            <div class="col-md-6 col-lg-4 order-2 mb-4 "><label>定格消費電力: <b>{{rated_power}}</b> </label>
                            </div>
                        </div>
                        <div class="row justify-content-center">
                            <div class="col-md-6 col-lg-4  order-2 mb-4">
                                <div class="card " style="min-height: 50px;">
                                    <div class="card-body d-flex align-items-center justify-content-center">
                                        <p class="mb-0 text-center">削減率 : <b> {{reductionRate|floatformat:2}}%</b></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-lg-6 order-2 mb-4 ">
                                <div class="card " style="min-height: 50px;">
                                    <div class="card-body">
                                        <div  class="text-center fw-bold ">設置前</div>
                                        <div class="mt-1">消費電力: <b>{{averagePowerConsumptionNotInstall|floatformat:1}}  kw</b> </div>
                                        <div class="mt-1">負荷率: <b>{{averageLoadRateNotInstall|floatformat:2}}%</b></div>
                                        <div class="mt-1">データ数 :<b> {{resultCountNotInstall|floatformat:1 }} </b></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-6 order-2 mb-4 ">
                                <div class="card" style="min-height: 50px;">
                                    <div class="card-body">
                                        <div   class="text-center fw-bold ">設置後</div>
                                        <div class="mt-1">消費電力 :<b>{{averagePowerConsumptionAfterInstall|floatformat:1}} kw</b> </div>
                                        <div class="mt-1">負荷率 : <b>{{averageLoadRateAfterInstall|floatformat:2}}%</b> </div>
                                        <div class="mt-1">データ数 :<b> {{resultCountInstall|floatformat:1 }} </b></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <form id="formSearch">
                            <div class="row g-3 align-items-end">
                                <div class="col-md-8 d-flex align-items-center">
                                    <input name="start_date" type="text" class="form-control" id="start_date"
                                           placeholder="期間" autoComplete="off"
                                           value="{{ start_date_raw }}">
                                    <span class="mx-2">~</span>
                                    <input name="end_date" type="text" class="form-control" id="end_date"
                                           placeholder="期間" autoComplete="off"
                                           value="{{ end_date_raw }}">
                                </div>

                                <div class="col-md-3">
                                    <input type="text" name="result_count" class="form-control" id="result_count"
                                           placeholder="データ数" value="{{ result_count }}">
                                </div>
                            </div>
                            <div class="row mt-4">
                                <div class="col-md-4">
                                    <p>
                                        外気温 : <span id="outdoor_temperature">{{ outdoor_temperature_start|floatformat:1|default:min_outdoor_temperature|floatformat:1 }} - {{outdoor_temperature_end|floatformat:1|default:max_outdoor_temperature|floatformat:1}}</span>
                                    </p>
                                    <div id="outdoor_temperature_slider_range"></div>
                                    <input type="hidden" name="outdoor_temperature_start"
                                           value="{{outdoor_temperature_start|default:min_outdoor_temperature}}">
                                    <input type="hidden" name="outdoor_temperature_end"
                                           value="{{outdoor_temperature_end|default:max_outdoor_temperature}}">
                                </div>
                                <div class="col-md-4">
                                    <p>
                                        消費電力 : <span id="power_consumption">{{power_consumption_start|floatformat:1|default:min_power_consumption|floatformat:1 }} - {{power_consumption_end|floatformat:1|default:max_power_consumption|floatformat:1}}</span>
                                    </p>
                                    <div id="power_consumption_slider_range"></div>
                                    <input type="hidden" name="power_consumption_start"
                                           value="{{power_consumption_start|default:min_power_consumption}}">
                                    <input type="hidden" name="power_consumption_end"
                                           value="{{power_consumption_end|default:max_power_consumption}}">
                                </div>
                                <div class="col-md-4 mt-3 ms-auto d-flex gap-2">
                                    <button id="resetBtn" type="button" class="btn btn-primary w-100">クリア</button>
                                    <button type="submit" class="btn btn-primary w-100">検索</button>

                                </div>
                            </div>
                            <div class="row mt-2 ">
                                <div class="col-md-2 mt-3 ms-auto ">
                                    <button type="button" class=" btn btn-primary w-100"
                                            onclick="downloadCSV({{project_id}})">エクスポート
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <div>
                                <div class="row g-2">
                                    <div class="col-12 col-sm-auto">
                                        <button class="btn btn-primary w-100" onclick="downloadChartPowerAsImage()">📥
                                            画像をダウンロード
                                        </button>
                                    </div>

                                    <div class="col-12 col-sm-auto">
                                        <select id="selectChartDate" class="form-select" onchange="updateChartDate()">
                                            <option value="avg" selected>平均</option>
                                            <option value="sum">合計</option>
                                        </select>
                                    </div>

                                </div>
                                <div id="chart_power" style="width: 100%; height: 500px;"></div>
                            </div>
                            <div>
                                <div class="row g-2">
                                    <div class="col-12 col-sm-auto">
                                        <button class="btn btn-primary w-100" onclick="downloadChartTempAsImage()">📥
                                            画像をダウンロード
                                        </button>
                                    </div>

                                    <div class="col-12 col-sm-auto">
                                        <select id="selectChartTemp" class="form-select" onchange="updateChartTemp()">
                                            <option value="avg" selected>平均</option>
                                            <option value="sum">合計</option>
                                        </select>
                                    </div>


                                </div>
                                <div id="chart_temp" style="width: 100%; height: 500px;"></div>
                            </div>
                            <div>
                                <div class="row g-2">
                                    <div class="col-12 col-sm-auto">
                                        <button class="btn btn-primary w-100" onClick="downloadChartScatterAsImage()">📥
                                            画像をダウンロード
                                        </button>
                                    </div>
                                </div>
                                <div id="chart_scatter" style="width: 100%; height: 500px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="navs-top-profile" role="tabpanel">

            </div>
        </div>
    </div>

</div>

<!-- end extra Large Modal analytics-->
{% endblock %}
{% block extra_js%}
{{ min_outdoor_temperature|json_script:"min_outdoor_temperature" }}
{{ max_outdoor_temperature|json_script:"max_outdoor_temperature" }}
{{ outdoor_temperature_start|json_script:"outdoor_temperature_start" }}
{{ outdoor_temperature_end|json_script:"outdoor_temperature_end" }}
{{ min_power_consumption|json_script:"min_power_consumption" }}
{{ max_power_consumption|json_script:"max_power_consumption" }}
{{ power_consumption_start|json_script:"power_consumption_start" }}
{{ power_consumption_end|json_script:"power_consumption_end" }}
{{ chartTemp|json_script:"chartTemp" }}
{{ chartDataDateAvg|json_script:"chartDataDateAvg" }}
{{ chartDataDateSum|json_script:"chartDataDateSum" }}
{{ chartTempAvg|json_script:"chartTempAvg" }}
{{ chartTempSum|json_script:"chartTempSum" }}
{{ dataChartScatter|json_script:"dataChartScatter" }}
{{ min_time|json_script:"min_time" }}
{{ max_time|json_script:"max_time" }}
<script>
    $(document).ready(function () {
        let minDate = new Date(JSON.parse($("#min_time").text()));
        let maxDate = new Date(JSON.parse($("#max_time").text()));
        // set select date search
        const startPicker = flatpickr("#start_date", {
            dateFormat: "Y-m-d",
            locale: "ja",
            minDate: minDate,
            maxDate: maxDate,
            allowInput: true,
            onChange: function (selectedDates, dateStr, instance) {
                endPicker.set('minDate', dateStr);
            }
        });
        const endPicker = flatpickr("#end_date", {
            dateFormat: "Y-m-d",
            locale: "ja",
            minDate: minDate,
            maxDate: maxDate,
            allowInput: true,
            onChange: function (selectedDates, dateStr, instance) {
                startPicker.set('maxDate', dateStr);
            }
        });

        // end select date search
        // set slider range temp and power consumption
        let minOutdoorTemperature = JSON.parse($("#min_outdoor_temperature").text());
        let maxOutdoorTemperature = JSON.parse($("#max_outdoor_temperature").text());
        let outdoorTemperatureStart = JSON.parse($("#outdoor_temperature_start").text());
        let outdoorTemperatureEnd = JSON.parse($("#outdoor_temperature_end").text());
        let minPowerConsumption = JSON.parse($("#min_power_consumption").text());
        let maxPowerConsumption = JSON.parse($("#max_power_consumption").text());
        let powerConsumptionStart = JSON.parse($("#power_consumption_start").text());
        let powerConsumptionEnd = JSON.parse($("#power_consumption_end").text());

        $("#outdoor_temperature_slider_range").slider({
            range: true,
            min: Number(minOutdoorTemperature),
            max: Number(maxOutdoorTemperature),
            values: (Number(outdoorTemperatureStart) === 0 && Number(outdoorTemperatureEnd) === 0) ? [Number(minOutdoorTemperature), Number(maxOutdoorTemperature)] : [Number(outdoorTemperatureStart), Number(outdoorTemperatureEnd)],
            slide: function (event, ui) {
                $("#outdoor_temperature").text(ui.values[0] + "- " + ui.values[1]);
                $('input[name="outdoor_temperature_start"]').val(ui.values[0]);
                $('input[name="outdoor_temperature_end"]').val(ui.values[1]);
            }
        });
        $("#power_consumption_slider_range").slider({
            range: true,
            step: 0.1,
            min: Number(minPowerConsumption),
            max: Number(maxPowerConsumption),
            values: (Number(powerConsumptionStart) === 0 && Number(powerConsumptionEnd) === 0) ? [Number(minPowerConsumption), Number(maxPowerConsumption)] : [Number(powerConsumptionStart), Number(powerConsumptionEnd)],

            slide: function (event, ui) {
                $("#power_consumption").text(ui.values[0] + "- " + ui.values[1] + "");
                $('input[name="power_consumption_start"]').val(ui.values[0]);
                $('input[name="power_consumption_end"]').val(ui.values[1]);
            }
        });
        //end set slider range temp and power consumption
        document.getElementById("formSearch").addEventListener("reset", function () {



        });
        $("#resetBtn").on("click", function (e) {
            console.log(maxPowerConsumption);
            const form = document.querySelector('form');
            form.querySelectorAll('input').forEach(input => input.value = '');
                        setTimeout(() => {
                $("#outdoor_temperature_slider_range").slider("values", [Number(minOutdoorTemperature), Number(maxOutdoorTemperature)]);
                $("#power_consumption_slider_range").slider("values", [Number(minPowerConsumption), Number(maxPowerConsumption)]);

                $('input[name="outdoor_temperature_start"]').val(Number(minOutdoorTemperature));
                $('input[name="outdoor_temperature_end"]').val(Number(maxOutdoorTemperature));
                $("#outdoor_temperature").text(Number(minOutdoorTemperature) + "- " + Number(maxOutdoorTemperature) + "");

                $('input[name="power_consumption_start"]').val(Number(minPowerConsumption));
                $('input[name="power_consumption_end"]').val(Number(maxPowerConsumption));
                $("#power_consumption").text(Number(minPowerConsumption) + "- " + Number(maxPowerConsumption) + "");

                startPicker.clear(); // xóa giá trị trong flatpickr
                endPicker.clear(); // xóa giá trị trong flatpickr // Dùng timeout để đảm bảo chạy sau khi input được reset
            }, 0);
        });
        // set input number
        $('#result_count').on('input', function () {
           this.value = this.value.replace(/\D/g, '');
        });
    });

    // chart google js
    var chartDate;
    var chartTemp;
    var chartScatter;
    let visibilityChartDate = {1: true, 4: true, 6: true, 8: true};
    let visibilityChartTemp = {1: true, 4: true};
    let visibilityChartScatter = {1: true, 2: true};
    google.charts.load('current', {
        packages: ['corechart']
    });
    google.charts.setOnLoadCallback(drawChartPower);
    google.charts.setOnLoadCallback(drawChartTemp);
    google.charts.setOnLoadCallback(drawChartScatter);

    function drawChartPower() {
        chartDate = new google.visualization.ComboChart(document.getElementById('chart_power'));
        updateChartDate()
    }

    function updateChartDate() {
        const selected = document.getElementById('selectChartDate').value;
        var dataChartDate = [['日付', '消費電力 (kW)', {
            type: 'string',
            role: 'tooltip',
            p: {html: true}
        }, {role: 'style'},
            '外気温 (°C)', {type: 'string', role: 'tooltip', p: {html: true}},
            '吸込温度（℃）', {type: 'string', role: 'tooltip', p: {html: true}},
            '吹出温度（℃）', {type: 'string', role: 'tooltip', p: {html: true}}
            ]
        ]
        if (selected === 'avg') {
            dataChartDate.push(...JSON.parse($("#chartDataDateAvg").text()));
        }
        if (selected === 'sum') {
            dataChartDate.push(...JSON.parse($("#chartDataDateSum").text()));
        }
        if (dataChartDate.length === 1) {
            return;
        }
        dataChartDate = dataChartDate.map(row => {
          return row.map(cell => cell === null ? NaN : cell);
        });
        var data = google.visualization.arrayToDataTable(
            dataChartDate
        );
        const view = new google.visualization.DataView(data);
        const columns = [0];
        colors = ['#007BFF', 'red', 'blue', 'green'];
        for (let i = 1; i < data.getNumberOfColumns(); i++) {
            if (visibilityChartDate[i] === false) {
                columns.push({
                    calc: () => null,
                    label: data.getColumnLabel(i),
                    type: data.getColumnType(i)
                });
                if (i === 1) {
                   colors[0] = 'black';
                }
                if (i === 4){
                    colors[1] = 'black';
                }
                if (i === 6){
                    colors[2] = 'black';
                }if (i === 8){
                    colors[3] = 'black';
                }
            } else {
                columns.push(i);
            }
        }
        view.setColumns(columns);

        var options = {
            vAxes: {
                0: {title: '電力消費量（kW)', minValue: 0, titleTextStyle: {italic: false}},
                1: {title: '温度(°C)', minValue: 0, titleTextStyle: {italic: false}}
            },
            hAxis: {title: '日付', titleTextStyle: {italic: false}},
            seriesType: 'bars',
            series: {
                0: {type: 'bars', targetAxisIndex: 0, color: colors[0]},
                1: {type: 'line', targetAxisIndex: 1, color: colors[1]},
                2: {type: 'line', targetAxisIndex: 1, color: colors[2]},
                3 : {type: 'line', targetAxisIndex: 1, color: colors[3]},
            },
            legend: {position: 'top', alignment: 'center'},
            chartArea: {width: '75%', height: '70%'},
            tooltip: {isHtml: true},
        };
        chartDate.draw(view, options);
        google.visualization.events.addListener(chartDate, 'select', function () {
            const sel = chartDate.getSelection();
            if (sel.length > 0) {
                const selection = sel[0];
                if (selection.row === null && selection.column > 0) {
                    visibilityChartDate[selection.column] = !visibilityChartDate[selection.column];
                    drawChartPower();
                }
            }
        });
    }

    function downloadChartPowerAsImage() {
        if (!chartDate) {
            alert('チャートが読み込まれていません。しばらく待ってから再試行してください。');
            return;
        }

        const imageURI = chartDate.getImageURI();
        downloadChartAsJPEG(imageURI, filename = 'chart_date.jpeg')

    }

    function drawChartTemp() {
        chartTemp = new google.visualization.ColumnChart(document.getElementById("chart_temp"));
        updateChartTemp()
    }

    function updateChartTemp() {
        const selected = document.getElementById('selectChartTemp').value;
        let dataChartTemp = [['外気温', '設置前(kW)', {
            type: 'string',
            role: 'tooltip',
            p: {html: true}
        }, {role: 'style'}, '設置後(kW)', {
            type: 'string',
            role: 'tooltip',
            p: {html: true}
        }]]
        var min = 0;
        var max = 0;
        if (selected === 'avg') {
            dataChartTemp.push(...JSON.parse($("#chartTempAvg").text()));
            const firstValues = JSON.parse($("#chartTempSum").text()).map(item => item[0]);
            // set min và max
            min = Math.min(...firstValues);
            max = Math.max(...firstValues);


        }

        if (selected === 'sum') {
            dataChartTemp.push(...JSON.parse($("#chartTempSum").text()));
            const firstValues = JSON.parse($("#chartTempSum").text()).map(item => item[0]);
            // set min và max
            min = Math.min(...firstValues);
            max = Math.max(...firstValues);

        }
        if (dataChartTemp.length === 1) {
            return;
        }
        dataChartTemp = dataChartTemp.map(row => {
          return row.map(cell => cell === null ? NaN : cell);
        });
        var data = google.visualization.arrayToDataTable(
            dataChartTemp
        );
        const view = new google.visualization.DataView(data);
        const columns = [0];
        colors = ['#a4a6a8', '#2684FC'];
        for (let i = 1; i < data.getNumberOfColumns(); i++) {
            if (visibilityChartTemp[i] === false) {
                columns.push({
                    calc: () => null,
                    label: data.getColumnLabel(i),
                    type: data.getColumnType(i)
                });
                if (i === 1) {
                   colors[0] = 'black';
                }
                if (i === 4) {
                   colors[1] = 'black';
                }
            } else {
                columns.push(i);
            }
        }
        view.setColumns(columns);

        var ticks = []
        for (var i = min; i <= max; i++) {
            ticks.push(i);
        }
        var options = {
            tooltip: {isHtml: true},
            hAxis: {
                title: '外気温 (°C)',
                titleTextStyle: {italic: false},
                viewWindow: {min: min - 1, max: max + 1},
                ticks: ticks,
            },
            vAxis: {
                title: '電力消費量（kW)',
                titleTextStyle: {italic: false},
            },
            legend: {position: 'top', alignment: 'center'},
            series: {
                0: {color: colors[0]},
                1: {color: colors[1]}
            },
            bar: {groupWidth: '75%'},
        };
        chartTemp.draw(view, options);
        google.visualization.events.addListener(chartTemp, 'select', function () {
            const sel = chartTemp.getSelection();
            if (sel.length > 0) {
                const selection = sel[0];
                if (selection.row === null && selection.column > 0) {
                    visibilityChartTemp[selection.column] = !visibilityChartTemp[selection.column];
                    drawChartTemp();
                }
            }
        });
    }

    function downloadChartTempAsImage() {
        if (!chartTemp) {
            alert('チャートが読み込まれていません。しばらく待ってから再試行してください。');
            return;
        }

        const imageURI = chartTemp.getImageURI();
        downloadChartAsJPEG(imageURI, filename = 'chart_temp.jpeg')

    }

    function drawChartScatter() {
        var dataChartScatter = [['外温度（°C）', 'α-HTなし', 'α-HTあり']];
        dataChartScatter.push(...JSON.parse($("#dataChartScatter").text()));
        dataChartScatter = dataChartScatter.map(row => {
          return row.map(cell => cell === null ? NaN : cell);
        });
        var data = google.visualization.arrayToDataTable(dataChartScatter);
        const view = new google.visualization.DataView(data);
        const columns = [0];
        colors = ['#a4a6a8', '#007BFF'];
        for (let i = 1; i < data.getNumberOfColumns(); i++) {
            if (visibilityChartScatter[i] === false) {
                columns.push({
                    calc: () => null,
                    label: data.getColumnLabel(i),
                    type: data.getColumnType(i)
                });
                if (i === 1) {
                   colors[0] = 'black';
                }
                if (i === 2) {
                   colors[1] = 'black';
                }
            } else {
                columns.push(i);
            }
        }
        view.setColumns(columns);
        var ticks = []
        min = JSON.parse($("#min_outdoor_temperature").text());
        max = JSON.parse($("#max_outdoor_temperature").text());
        for (var i = min; i <= max; i++) {
            ticks.push(Number(i));
        }
        var options = {
            hAxis: {
                title: '外温度（°C）',
                titleTextStyle: {italic: false},
                ticks: ticks,
                format: '0'
            },
            vAxis: {
                title: '電力消費量（kW)',
                titleTextStyle: {italic: false},
            },
            legend: {position: 'top', alignment: 'center'},
            series: {
                0: {color:  colors[0], pointSize: 5},     // Trước lắp đặt
                1: {color: colors[1], pointSize: 5}      // Sau lắp đặt
            },
            trendlines: {
                0: {
                    type: 'linear',
                    color: colors[0],
                    titleTextStyle: {italic: false},
                    labelInLegend: '近似線（設置前）',
                    visibleInLegend: true
                },
                1: {
                    type: 'linear',
                    color: colors[1],
                    titleTextStyle: {italic: false},
                    labelInLegend: '近似線（設置後）',
                    visibleInLegend: true
                }
            },
            chartArea: {width: '75%', height: '70%'}
        };
        if (dataChartScatter.length === 1) {
            return;
        }
        chartScatter = new google.visualization.ScatterChart(document.getElementById('chart_scatter'));
        chartScatter.draw(view, options);
        google.visualization.events.addListener(chartScatter, 'select', function () {
            const sel = chartScatter.getSelection();
            if (sel.length > 0) {
                const selection = sel[0];
                if (selection.row === null && selection.column > 0) {
                    visibilityChartScatter[selection.column] = !visibilityChartScatter[selection.column];
                    drawChartScatter();
                }
            }
        });
    }

    function downloadChartScatterAsImage() {
        if (!chartScatter) {
            alert('チャートが読み込まれていません。しばらく待ってから再試行してください。');
            return;
        }

        const imageURI = chartScatter.getImageURI();
        downloadChartAsJPEG(imageURI, filename = 'chart_scatter.jpeg')

    }

    function downloadCSV(project_id) {
        const url = `/export-csv-analytics/${project_id}?${window.location.search.substring(1)}`;
        const link = document.createElement('a');
        link.href = url;
        // link.download = ''; // nếu backend gửi Content-Disposition: attachment thì không cần tên file
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    function downloadChartAsJPEG(imageURI, filename = 'chart.jpeg') {
        const img = new Image();
        img.crossOrigin = 'Anonymous';  // Tránh lỗi CORS nếu cần
        img.onload = function () {
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;

            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#ffffff'; // Nền trắng cho JPEG (tránh nền trong suốt)
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            ctx.drawImage(img, 0, 0);

            const jpegURI = canvas.toDataURL('image/jpeg', 0.95);  // 0.95 = chất lượng

            const a = document.createElement('a');
            a.href = jpegURI;
            a.download = filename;
            a.click();
        };
        img.src = imageURI;
    }

</script>
{% endblock %}