{% extends "layout/base.html" %}
{% block content %}
<div class="container-xxl flex-grow-1 container-p-y">

    <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-style1">
            <li class="breadcrumb-item">
                <a href="{% url 'corporate_list' %}">企業一覧</a>
            </li>
            <li class="breadcrumb-item">
                系統一覧
            </li>

        </ol>
    </nav>
    <h3 class="fw-bold">系統一覧</h3>
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3" id="searchForm">
                <div class="col-md-6">
                    <label for="search" class="form-label fs-4 ">検索</label>
                    <input type="text" class="form-control" id="search" name="search" autocomplete="off"
                           placeholder="系統名、セット名など..." value="{{ search_query }}">
                </div>

                <div class="col-md-2 d-flex align-items-end">
                    <a href="{% url 'project_list' %}?company_id={{company_id}}" class="btn btn-primary w-100 ">クリア</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Data Table -->
    <div class="card">
        <div class="table-responsive text-nowrap">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th class="sortable" data-key="system_id">系統ID <span class="sort-icon cursor-pointer "></span></th>
                        <th class="sortable" data-key="system_name">系統名<span class="sort-icon cursor-pointer "></span></th>
                        <th>室外機型番</th>
                        <th>セット台数</th>
                        <th>室外機台数</th>
                        <th>室内機台数</th>
                        <th>メーカー</th>

                        <th class="sortable" data-key="created_at">登録日時<span class="sort-icon cursor-pointer "></span></th>
                          <th>アクション</th>
                    </tr>
                </thead>
                <tbody id="corporateTableBody"  class="table-border-bottom-0">

                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="card-footer" id="paginationWrapper">
        </div>
    </div>
    <!--    modal delete -->
    <div class="modal fade " id="modalDelete" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header text-center align-content-center">
                    <h5 class="">本当に削除してもよろしいですか。</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        キャンセル
                    </button>
                    <button id="btnConfirmDelete" type="button" class="btn btn-primary">削除する</button>
                </div>
            </div>
        </div>
    </div>
    <!--    end modal delete-->
</div>
{% endblock %}
{% block extra_js%}
<script>
    let idDelete = '';
    $(document).on('click', '.btnOpenModalDelete', function () {
        idDelete = $(this).data('id')
        $('#modalDelete').modal('show');
    });
    $(document).on('click', '#btnConfirmDelete', function () {
        $.ajax({
            url: `/projects/${idDelete}/delete/`,
            type: 'DELETE',
            headers: {
                'X-CSRFToken': $('input[name="csrfmiddlewaretoken"]').val(),
            },
            success: function (response) {
                location.reload();
            },
            error: function (xhr) {
                alert('データの削除に失敗しました。再度お試しください。');
            }
        });
    })

    const input = document.getElementById('search');
        const form = document.getElementById('searchForm');
        let debounceTimer = null;
        const params = new URLSearchParams(window.location.search);
        const company_id = params.get('company_id') ;
        input.focus();
        input.setSelectionRange(input.value.length, input.value.length);
        input.addEventListener('input', function () {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(function () {
                fetchCorporatePage(company_id,1)
            }, 700); // 0.5 giây
        });

        function fetchCorporatePage(company_id,page = 1, currentSortKey='updated_at', currentSortOrder='desc') {
            $.ajax({
                url: '/api/projects/',
                type: 'GET',
                data: {
                    search: input.value.trim(),
                    page: page,
                    company_id: company_id,
                    sort_by: currentSortKey,
                    sort_order: currentSortOrder,
                },
                success: function (response) {
                    let html = '';

                    if (response.results.length === 0) {
                        html = `
                        <tr>
                            <td colspan="7" class="text-center">データがありません</td>
                        </tr>`;
                    } else {
                        response.results.forEach(function (project) {
                            html += `
                            <tr>
                                <td>${project.system_id }</td>
                                <td>${project.system_name}</td>
                                <td>${ project.outdoor_model}</td>
                                <td>${ project.num_sets}</td>
                                <td>${project.num_outdoor_units}</td>
                                <td>${project.num_indoor_units}</td>
                                <td>${project.manufacturer}</td>
                                <td>${project.created_at_tokyo }</td>
                                <td>
                                     <a href="/analytics-dashboard/${project.system_id}"><button class="btn btn-primary m-2" type="button">基礎分析</button> </a>
                                    <button data-id="${project.system_id}" class="btn btn-primary m-2 btnOpenModalDelete" type="button">削除</button>
                                </td>
                            </tr>`;
                        });
                        updateURL(company_id,input.value.trim(), page, currentSortKey, currentSortOrder)
                    }

                    $('#corporateTableBody').html(html);
                    if(response.results.length === 0){
                        $('#paginationWrapper').html('');
                    }else   {
                       updatePagination(company_id,response.pagination.page, response.pagination.num_pages, response.pagination);
                    }
                },
                error: function () {
                    $('#corporateTableBody').html('<tr><td colspan="7" class="text-danger text-center">エラーが発生しました。</td></tr>');
                }
            });

        }

        function updatePagination(company_id,currentPage, totalPages, pagination) {
            let paginationHtml = '';
            if (pagination.has_previous) {
                paginationHtml += `<li class="page-item">
                        <a class="page-link"
                           onclick="fetchCorporatePage(company_id,1)"
                           aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link"
                           onclick="fetchCorporatePage(company_id,${pagination.page - 1})"
                           aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>`;
            } else {
                paginationHtml += `      <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>`;
            }
            for (let i = 1; i <= totalPages; i++) {
                if (Math.abs(currentPage - i) < 3 || i === 1 || i === totalPages) {
                    paginationHtml += `
                <li class="page-item ${currentPage === i ? 'active' : ''}">
                    <a href="#" class="page-link pagination-link" data-page="${i}">${i}</a>
                </li>`;
                }
            }
            if (pagination.has_next) {
                paginationHtml += `<li class="page-item">
                    <a class="page-link"
                       onclick="fetchCorporatePage(company_id,${pagination.page + 1})"
                       aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link"
                       onclick="fetchCorporatePage(company_id,${pagination.num_pages})"
                       aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                </li>`;
            } else {
                paginationHtml += `<li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>`;
            }

            $('#paginationWrapper').html(`<ul class="pagination justify-content-center mb-0">${paginationHtml}</ul>`);
        }

        $(document).on('click', '.pagination-link', function (e) {
            e.preventDefault();
            const page = $(this).data('page');
            const params = new URLSearchParams(window.location.search);
            const company_id = params.get('company_id') ;
            fetchCorporatePage(company_id,page);
        });


        $(document).ready(function () {
            const params = new URLSearchParams(window.location.search);
            const page = params.get('page') ? params.get('page') : 1;
            const company_id = params.get('company_id') ;
             $('.sortable .sort-icon').text('▼▲'); // Reset tất cả
            $(`.sortable[data-key="${currentSortKey}"] .sort-icon`).text('▼'); //
             fetchCorporatePage(company_id,page);
        });

        let currentSortKey = 'created_at';
        let currentSortOrder = 'desc';

    $(document).on('click', '.sortable', function () {
    const key = $(this).data('key');

    // Toggle asc/desc
    if (currentSortKey === key) {
        currentSortOrder = currentSortOrder === 'asc' ? 'desc' : 'asc';
    } else {
        currentSortKey = key;
        currentSortOrder = 'asc';
    }

    $('.sortable .sort-icon').text('▼▲');

    const icon = currentSortOrder === 'asc' ? '▲' : '▼';
    $(this).find('.sort-icon').text(icon);
    const params = new URLSearchParams(window.location.search);
    const company_id = params.get('company_id') ;
    fetchCorporatePage(company_id,1, currentSortKey, currentSortOrder);
});
    function updateURL(company_id,search, page, sort_by, sort_order) {
    const params = new URLSearchParams();

    if (search) params.set('search', search);
    if (company_id) params.set('company_id', company_id);
    if (page) params.set('page', page);
    if (sort_by) params.set('sort_by', sort_by);
    if (sort_order) params.set('sort_order', sort_order);

    const newUrl = `${window.location.pathname}?${params.toString()}`;

    history.pushState({}, '', newUrl);
}
</script>
{% endblock %}