{% extends "layout/base.html" %}
{% block content %}
<div class="container-xxl flex-grow-1 container-p-y">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-style1">
            <li class="breadcrumb-item">
                <a href="">CSVアップロード</a>
            </li>
            <li class="breadcrumb-item">
            </li>

        </ol>
    </nav>
    <h3 class="fw-bold ">CSVアップロード</h3>
    <div class="row">
        <div class="col-md-6 col-lg-4 order-2 mb-4 cursor-pointer" id="corporateMaster">
            <div class="card h-100" style="min-height: 250px;">
                <div class="card-header d-flex align-items-center justify-content-between">
                </div>
                <div class="card-body d-flex align-items-center justify-content-center">
                    <p class="mb-0 text-center">企業情報をアップロードする</p>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 order-2 mb-4 cursor-pointer" id="projectCorporateMaster">
            <div class="card h-100" style="min-height: 250px;">
                <div class="card-header d-flex align-items-center justify-content-between">
                </div>
                <div class="card-body d-flex align-items-center justify-content-center">
                    <p class="mb-0 text-center">系統情報をアップロードする</p>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 order-2 mb-4 cursor-pointer" id="dataAnalytics">
            <div class="card h-100" style="min-height: 250px;">
                <div class="card-header d-flex align-items-center justify-content-between">
                </div>
                <div class="card-body d-flex align-items-center justify-content-center">
                    <p class="mb-0 text-center">計測データをアップロードする</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Extra Large Modal corporate Master-->
<div class="modal fade" id="corporateMasterModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel4">CSVアップロード＞企業情報</h5>
                <button
                        type="button"
                        class="btn-close"
                        data-bs-dismiss="modal"
                        aria-label="Close"
                ></button>
            </div>
            <div class="modal-body">
                <p>CSVアップロード</p>
                <p >企業情報マスタをCSVファイルから取り込んで作成できます。</p>
                <div class="border p-3 rounded mt-3 text-center bg-light areaUploadFile">

                    <label for="csvFileInput" class="form-label">
                                  ドラッグアンドドロップか
                                  <span class="text-primary" style="cursor: pointer;">ファイルを選択</span>
                                  してください
                                </label>
                    <form id="uploadForm">
                        <input class="form-control" type="file" id="csvFileInput" accept=".csv" hidden>
                        <input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">
                    </form>
                    <p class="small mt-2">対応ファイル： CSV </p>
                    <p id="filenameCorp" class="small text-primary"></p>
                </div>
                <p></p>

            </div>
            <div class="modal-footer d-flex justify-content-between">
                <button id="cancelUploadCorporateMaster" type="button" class="btn btn-link text-secondary">キャンセル
                </button>
                <button id="uploadCorporateMaster" type="button" class="btn btn-primary">確認</button>
            </div>
        </div>
    </div>
</div>
<!-- end extra Large Modal corporate Master-->
<!-- Extra Large Modal corporate Master project -->
<div class="modal fade" id="projectCorporateMasterModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" >CSVアップロード＞系統情報</h5>
                <button
                        type="button"
                        class="btn-close"
                        data-bs-dismiss="modal"
                        aria-label="Close"
                ></button>
            </div>
            <div class="modal-body">
                <p>CSVアップロード</p>
                <p >系統情報マスタをCSVファイルから取り込んで作成できます。</p>
                <div class="border p-3 rounded mt-3 text-center bg-light areaUploadFile" >
                    <label for="csvFileInputProject" class="form-label">
                       ドラッグアンドドロップか
                       <span class="text-primary" style="cursor: pointer;">ファイルを選択</span>
                       してください
                    </label>
                    <form id="uploadFormProject">
                        <input class="form-control" type="file" id="csvFileInputProject" accept=".csv" hidden>
                        <input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">
                    </form>
                    <p class="small mt-2">対応ファイル： CSV </p>
                    <p id="filenameProject" class="small text-primary"></p>
                </div>
                <p></p>

            </div>
            <div class="modal-footer d-flex justify-content-between">
                <button id="cancelUploadProjectCorporateMaster" type="button" class="btn btn-link text-secondary">キャンセル
                </button>
                <button id="uploadProjectCorporateMaster" type="button" class="btn btn-primary">確認</button>
            </div>
        </div>
    </div>
</div>
<!-- end extra Large Modal corporate Master project-->
<!-- Extra Large Modal analytics -->
<div class="modal fade" id="analyticsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" >CSVアップロード＞計測データ</h5>
                <button
                        type="button"
                        class="btn-close"
                        data-bs-dismiss="modal"
                        aria-label="Close"
                ></button>
            </div>
            <div class="modal-body">
                <p>CSVアップロード</p>
                <p >計測データをCSVファイルから取り込んで作成できます。</p>
                <div class="border p-3 rounded mt-3 text-center bg-light areaUploadFile" >

                    <label for="csvFileInputAnalytics" class="form-label">
                       ドラッグアンドドロップか
                       <span class="text-primary" style="cursor: pointer;">ファイルを選択</span>
                       してください
                    </label>
                    <form id="uploadFormAnalytics">
                        <input class="form-control" type="file" id="csvFileInputAnalytics" accept=".csv" hidden>
                        <input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">
                    </form>
                    <p class="small mt-2">対応ファイル： CSV </p>
                    <p id="filenameAnalytics" class="small text-primary"></p>
                </div>
                <p></p>

            </div>
            <div class="modal-footer d-flex justify-content-between">
                <button id="cancelUploadAnalytics" type="button" class="btn btn-link text-secondary">キャンセル
                </button>
                <button id="uploadAnalytics" type="button" class="btn btn-primary">確認</button>
            </div>
        </div>
    </div>
</div>

<!-- end extra Large Modal analytics-->
{% endblock %}
{% block extra_js%}
<script>
    const dropzone = $('.areaUploadFile');

    $(document).on('dragover drop', function (e) {
        e.preventDefault();
        e.stopPropagation();
    });

    dropzone.on('dragover', function () {
        $(this).css('border-color', '#00aaff');
    });

    dropzone.on('dragleave', function () {
        $(this).css('border-color', '#ccc');
    });

    dropzone.on('drop', function (e) {
        e.preventDefault();
        $(this).css('border-color', '#ccc');
        const files = e.originalEvent.dataTransfer.files;

        if (files.length > 0) {
            const fileList = new DataTransfer();
            fileList.items.add(files[0]);
            const fileInput = $(this).find('input[type="file"]');
            console.log($(this));
            fileInput[0].files = fileList.files;

            fileInput.trigger('change');
        }
    });
    $('#csvFileInput').on('change', function () {
        const fileName = $(this).val().split('\\').pop();
        $('#filenameCorp').html(fileName);
    });
    $('#csvFileInputProject').on('change', function () {
        const fileName = $(this).val().split('\\').pop();
        $('#filenameProject').html(fileName);
    });
    $('#csvFileInputAnalytics').on('change', function () {
        const fileName = $(this).val().split('\\').pop();
        $('#filenameAnalytics').html(fileName);
    });

    $(document).ready(function () {

        // click open modal
        $('#corporateMaster').click(function () {
            $('#corporateMasterModal').modal('show');
            $('#uploadForm')[0].reset();
           $('#filenameCorp').html('');

        });
        $('#projectCorporateMaster').click(function () {
            $('#projectCorporateMasterModal').modal('show');
            $('#uploadFormProject')[0].reset();
            $('#filenameProject').html('');
        });
        $('#dataAnalytics').click(function () {
            $('#analyticsModal').modal('show');
            $('#uploadFormAnalytics')[0].reset();
            $('#filenameProject').html('');

        });
    });

    $("#cancelUploadCorporateMaster").on('click', function () {
        $('#uploadForm')[0].reset();
        $('#corporateMasterModal').modal('hide');
    });
    $("#uploadCorporateMaster").on('click', function () {
        const fileInput = $('#csvFileInput')[0];
        if (fileInput.files.length === 0) {
            alert('ファイルを選択してください');
            return;
        }

        const file = fileInput.files[0];

        // Check if it's a CSV file
        if (!file.name.endsWith('.csv')) {
            alert('CSVファイルを選択してください');
            return;
        }

        // Create FormData object
        const formData = new FormData();
        formData.append('csv_file', file);

        // Show loading state
        $(this).prop('disabled', true).text('アップロード中...');

        // Send AJAX request
        $.ajax({
            url: '/api/upload/corporate/',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRFToken': $('input[name="csrfmiddlewaretoken"]').val(),
            },
            success: function (response) {
                alert('インポートが完了しました。');
                $('#corporateMasterModal').modal('hide');
                // Reset form
                $('#csvFileInput').val('');
                $('#filenameCorp').html('');
            },
            error: function (xhr) {
                let errorMessage = 'インポート処理中にエラーが発生しました。';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage += ': ' + xhr.responseJSON.error;
                }
                alert(errorMessage);
            },
            complete: function () {
                // Reset button state
                $('#uploadCorporateMaster').prop('disabled', false).text('確認');
            }
        });
    });

    // Handle project CSV upload
    $("#uploadProjectCorporateMaster").on('click', function () {
        const fileInput = $('#csvFileInputProject')[0];

        if (fileInput.files.length === 0) {
            alert('ファイルを選択してください');
            return;
        }

        const file = fileInput.files[0];

        // Check if it's a CSV file
        if (!file.name.endsWith('.csv')) {
            alert('CSVファイルを選択してください');
            return;
        }

        // Create FormData object
        const formData = new FormData();
        formData.append('csv_file', file);

        // Show loading state
        $(this).prop('disabled', true).text('アップロード中...');

        // Send AJAX request
        $.ajax({
            url: '/api/upload/project/',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRFToken': $('input[name="csrfmiddlewaretoken"]').val(),
            },
            success: function (response) {
                alert('インポートが完了しました。' );
                $('#projectCorporateMasterModal').modal('hide');
                // Reset form
                $('#projectCsvFileInput').val('');
                $('#projectSelectedFileName').text('');
            },
            error: function (xhr) {
                let errorMessage = 'インポート処理中にエラーが発生しました。';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    if(xhr.responseJSON.details){
                        errorMessage += ': \n' + xhr.responseJSON.details.join('\n');
                    }else {
                        errorMessage += ': ' + xhr.responseJSON.error;
                    }
                }
                $('#projectCorporateMasterModal').modal('hide');
                alert(errorMessage);
            },
            complete: function () {
                // Reset button state
                $('#uploadProjectCorporateMaster').prop('disabled', false).text('確認');
                $('#projectCorporateMasterModal').modal('hide');
            }
        });
    });

    $("#cancelUploadProjectCorporateMaster").on('click', function () {
        $('#uploadFormProject')[0].reset();
        $('#projectCorporateMasterModal').modal('hide');
    });

    // Handle analytics modal
    $('#dataAnalytics').click(function(){
        $('#analyticsModal').modal('show');
    });

    // Handle analytics file selection
    $('#analyticsCsvFileInput').change(function(){
        const fileName = $(this).val().split('\\').pop();
        $('#analyticsSelectedFileName').text(fileName ? `Selected file: ${fileName}` : '');
    });

    // Make the analytics "ファイルを選択" text trigger the file input
    $('.analytics-file-select').click(function(){
        $('#analyticsCsvFileInput').click();
    });

    // Handle analytics CSV upload
    $("#uploadAnalytics").on('click', function(){
        const fileInput = $('#csvFileInputAnalytics')[0];

        if(fileInput.files.length === 0){
            alert('ファイルを選択してください');
            return;
        }

        const file = fileInput.files[0];

        // Check if it's a CSV file
        if(!file.name.endsWith('.csv')){
            alert('CSVファイルを選択してください');
            return;
        }

        // Create FormData object
        const formData = new FormData();
        formData.append('csv_file', file);

        // Show loading state
        $(this).prop('disabled', true).text('アップロード中...');

        // Send AJAX request
        $.ajax({
            url: '/api/upload/analytics/',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRFToken': $('input[name="csrfmiddlewaretoken"]').val(),
            },
            success: function(response){
                alert('インポートが完了しました。');
                $('#analyticsModal').modal('hide');
                // Reset form
                $('#uploadFormAnalytics')[0].reset();
            },
            error: function(xhr){
                let errorMessage = 'インポート処理中にエラーが発生しました。';
                if(xhr.responseJSON && xhr.responseJSON.error){
                    errorMessage += ': ' + xhr.responseJSON.error;
                }
                alert(errorMessage);
            },
            complete: function(){
                // Reset button state
                $('#uploadAnalytics').prop('disabled', false).text('確認');
            }
        });
    });

    $("#cancelUploadAnalytics").on('click', function(){
        $('#uploadFormAnalytics')[0].reset();
        $('#analyticsModal').modal('hide');
    });
</script>
{% endblock %}