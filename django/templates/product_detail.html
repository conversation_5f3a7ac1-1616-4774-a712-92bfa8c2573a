{% extends "layout/base.html" %}
{% load static %}
{% block content %}
<div class="container mt-4">
    <div class="card mb-4">
        <div class="card-header">
            <h5><a href="{% url 'product_list' %}">卸BD－一覧ページ</a>＞食品詳細ページ</h5>
            <h5 class="mb-0  ">食品DB - 詳細ページ</h5>
        </div>
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-start">
                <table class="table table-bordered w-50 mb-4">
                    <tbody>
                    <tr>
                        <th scope="row">商品コード</th>
                        <td>{{ product.product_code }}</td>
                    </tr>
                    <tr>
                        <th scope="row">商品名</th>
                        <td>{{ product.product_name }}</td>
                    </tr>
                    <tr>
                        <th scope="row">登録日時</th>
                        <td>{{ product.created_at|date:"Y-m-d H:i" }}</td>
                    </tr>
                    {% if product.sync_product_code %}
                    <tr>
                        <th scope="row">突合済み</th>
                        <td>{{productItemMaster.product_name }}</td>
                    </tr>
                    {% endif %}
                    </tbody>
                </table>
                <!-- Ảnh bên phải -->
                <div class="d-flex flex-wrap gap-2 ms-4">
                    <img src="{% static 'image/fake1.png' %}" class="d-block rounded" width="100">
                    <img src="{% static 'image/fake2.png' %}" class="d-block rounded" width="100">
                    <img src="{% static 'image/fake3.png' %}" class="d-block rounded" width="100">
                    <img src="{% static 'image/fake4.png' %}" class="d-block rounded" width="100">
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="card mb-4">
        <div class="card-header">
            <h4 class="mb-0  ">▼食品DBマスタ </h4>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">

                <div class="row mb-3">
                    <div class="col-md-6">
                    <input
                            name="maker_name"
                            class="form-control"
                            list="datalistOptions"
                            id="exampleDataList"
                            placeholder="メーカー名を入力する"
                            value="{{ maker_name }}"
                    />
                    <datalist id="datalistOptions">
                        {% for maker in maker_list %}
                        <option value="{{maker}}"></option>
                        {% endfor %}
                    </datalist>
                </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" disabled
                                   placeholder="商品検索－商品名・JANコード" value="{{ search_query }}">
                        </div>
                    </div>
                    <div class="col-md-3 mt-3 mt-md-0 d-flex align-items-end">
                    <button class="btn btn-outline-secondary" id="searchDetailList-btn">検索</button>
                </div>
                </div>


            </form>
            <div class="table-responsive {% if not search_query and not maker_name %}d-none{% endif %}">
<!--            <div class="table-responsive ">-->
                <table class="table table-hover ">
                    <thead>
                    <tr>
                        <th>メーカー名</th>
                        <th>JANコード</th>
                        <th>規格</th>
                        <th>容量</th>
                        <th>保存 (温度帯 - 冷蔵・冷凍)</th>
                        <th>商品名</th>
                        <th class="text-center">アクション</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for item in products %}
                    <tr>
                        <td>{{ item.manufacturer_name }}</td>
                        <td>
                            <div class="cursor-pointer text-primary openModalInfo"
                            data-item='{
                                        "product_name": "{{ item.product_name|escapejs }}",
                                        "capacity": "{{ item.capacity|escapejs }}"
                                        "unit": "{{ item.unit|escapejs }}"
                                        "number_of_units_per_pack": "{{ item.number_of_units_per_pack|escapejs }}"
                                        "input_classification_name": "{{ item.input_classification_name|escapejs }}"
                                        "manufacturer_name": "{{ item.manufacturer_name|escapejs }}"
                                        "tax_name": "{{ item.tax_name|escapejs }}"
                                        "individual_item_jan_code": "{{ item.individual_item_jan_code|escapejs }}"
                                        "itf_code": "{{ item.itf_code|escapejs }}"
                                        "sdp_code": "{{ item.sdp_code|escapejs }}"
                                        "weight_per_case": "{{ item.weight_per_case|escapejs }}"
                                        "individual_item_weight": "{{ item.individual_item_weight|escapejs }}"
                                        "warehouse_classification_name": "{{ item.warehouse_classification_name|escapejs }}"
                                        "storage_classification_name": "{{ item.storage_classification_name|escapejs }}"
                                        "production_area": "{{ item.production_area|escapejs }}"
                                        "brand": "{{ item.brand|escapejs }}"
                                      }'
                            >{{ item.individual_item_jan_code }}</div>
                            <div>{{ item.product_name }}</div>
                        </td>
                        <td>{{ item.product_group_name }}</td>
                        <td>{{ item.capacity }}</td>
                        <td>{{ item.storage_classification_name }}</td>
                        <td>
                            <img
                                src="{% static 'image/fake4.png' %}"
                                class="cursor-pointer"
                                data-bs-toggle="modal"
                                data-bs-target="#imagePreviewModal"
                                data-image-url="{% static 'image/fake4.png' %}" alt="" width="55">
                        </td>

                        <td class="text-center">
                            {% if item.product_code == product.sync_product_code %}
                            <button class="btn btn-dark"> 突合済み</button>
                            {% else %}
                            <button
                                    data-id="{{ item.product_code }}"
                                    class="btn btn-outline-dark btnOpenModalSyns">
                                手動突合
                            </button>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center">データがありません</td>
                    </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if products.has_other_pages %}
            <div class="card-footer {% if not search_query and not maker_name %}d-none{% endif %}">
                <div class="d-flex justify-content-between align-items-center flex-wrap gap-2">
                    <form method="get" class="d-flex align-items-center">
                        <select name="page_size" id="page_size" class="form-select " onchange="this.form.submit()">
                            {% for size in sizes %}
                            <option value="{{ size }}" {% if page_size == size %}selected{% endif %}>{{ size }}</option>
                            {% endfor %}
                        </select>

                        <!-- Preserve other query params -->
                        {% if search_query %}
                        <input type="hidden" name="search" value="{{ search_query }}">
                        {% endif %}
                        {% if maker_name %}
                        <input type="hidden" name="maker_name" value="{{ maker_name }}">
                        {% endif %}
                        {% if sort_by %}
                        <input type="hidden" name="sort" value="{{ sort_by }}">
                        {% endif %}
                        {% if products.number %}
                        <input type="hidden" name="page" value="{{ products.number }}">
                        {% endif %}
                    </form>
                    {% if products.has_other_pages %}
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                        </div>
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center mb-0">
                                {% if products.has_previous %}
                                <li class="page-item">
                                    <a class="page-link"
                                       href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if maker_filter %}&maker={{ maker_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if page_size %}&page_size={{ page_size }}{% endif %}"
                                       aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link"
                                       href="?page={{ products.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if maker_filter %}&maker={{ maker_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if page_size %}&page_size={{ page_size }}{% endif %}"
                                       aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}

                                {% for i in page_range %}
                                {% if products.number == i %}
                                <li class="page-item active">
                                    <a class="page-link" href="#">{{ i }}</a>
                                </li>
                                {% else %}
                                <li class="page-item">
                                    <a class="page-link"
                                       href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if maker_filter %}&maker={{ maker_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if page_size %}&page_size={{ page_size }}{% endif %}">{{ i }}
                                    </a>
                                </li>
                                {% endif %}
                                {% endfor %}

                                {% if products.has_next %}
                                <li class="page-item">
                                    <a class="page-link"
                                       href="?page={{ products.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if maker_filter %}&maker={{ maker_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if page_size %}&page_size={{ page_size }}{% endif %}"
                                       aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link"
                                       href="?page={{ products.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if maker_filter %}&maker={{ maker_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if page_size %}&page_size={{ page_size }}{% endif %}"
                                       aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                        <div>
                            <!-- Empty div for flex alignment -->
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    <div class="modal fade" id="imagePreviewModal" tabindex="-1" aria-labelledby="imagePreviewModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="閉じる"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="previewImage" src="" alt="プレビュー画像" class="img-fluid rounded">
                </div>
            </div>
        </div>
    </div>
    <!--    modal-confirm-syns -->
    <div class="modal fade " id="modalSyns" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header text-center align-content-center">
                    <h5 class="">この商品と突合を実行してもよろしいですか。</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        キャンセル
                    </button>
                    <button id="btnConfirmSyns" type="button" class="btn btn-primary">突合する</button>
                </div>
            </div>
        </div>
    </div>
    <!--    end modal-confirm-syns -->
    <!--    modal-confirm-syns -->
    <div class="modal fade " id="modalDataMaster" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header text-center align-content-center">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <p class="fw-bold">商品名</p>
                        </div>

                        <div class="col-md-6 mb-3">
                            <p class="fw-bold">容量</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <p class="fw-bold">単位</p>
                        </div>

                        <div class="col-md-6 mb-3">
                            <p class="fw-bold">入数</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <p class="fw-bold">分類名</p>
                        </div>

                        <div class="col-md-6 mb-3">
                            <p class="fw-bold">ﾒｰｶｰ名</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <p class="fw-bold">税務名</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <p class="fw-bold">ﾊﾞﾗ JAN</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <p class="fw-bold">ITF ｺｰﾄﾞ</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <p class="fw-bold">SDP ｺｰﾄﾞﾞ</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6  mb-3">
                            <p class="fw-bold">ｹｰｽ当り 重量</p>
                        </div>
                        <div class="col-md-6  mb-3">
                            <p class="fw-bold">ﾊﾞﾗ 重量</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <p class="fw-bold">新棟旧棟</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <p class="fw-bold">保管</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <p class="fw-bold">産地</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <p class="fw-bold">ブランド</p>
                        </div>
                    </div>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        キャンセル
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!--    end modal-confirm-syns -->
</div>
{% endblock %}
{% block extra_js%}
<script>
    window.onload = function()
    {
        if($('input[name="maker_name"]').val()!==''){
            $('input[name="search"]').prop('disabled', false);;
        }
    };
    $('input[name="maker_name"]').on('input', function () {
        if($('input[name="maker_name"]').val()!==''){
            $('input[name="search"]').prop('disabled', false);
        }else {
            $('input[name="search"]').prop('disabled', true);
        }
    });
    document.addEventListener('DOMContentLoaded', function () {
      const modal = document.getElementById('imagePreviewModal');
      modal.addEventListener('show.bs.modal', function (event) {
        const trigger = event.relatedTarget;
        const imageUrl = trigger.getAttribute('data-image-url');
        const modalImage = document.getElementById('previewImage');
        modalImage.src = imageUrl;
      });
    });
    let idSyns = '';

    $('.btnOpenModalSyns').click(function () {
        idSyns = $(this).data('id');
        $('#modalSyns').modal('show');
    });
    $('#btnConfirmSyns').click(function () {
        var path = window.location.pathname;
        var segments = path.split("/").filter(Boolean);
        var id = segments[segments.length - 1];
        $.ajax({
            url: `/api/products/${id}/sync-code/`,
            data: {
                sync_product_code: idSyns
            },
            type: 'patch',
            headers: {
                'X-CSRFToken': $('input[name="csrfmiddlewaretoken"]').val(),
            },
            success: function (response) {
                 location.reload();
            },
            error: function (xhr) {
                alert('データの削除に失敗しました。再度お試しください');
            }
        });
    })
    $('.openModalInfo').click(function () {
        $('#modalDataMaster').modal('show');
        
    })
</script>
{% endblock %}