{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0"/>
    <title>Dashboard - Analytics </title>
<!--    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">-->
    <link rel="stylesheet" href="{% static 'css/boxicons.css' %}?v={{ timestamp }}" />
    <!-- Core CSS -->
    <link rel="stylesheet" href="{% static 'css/core.css' %}?v={{ timestamp }}"/>
    <link rel="stylesheet" href="{% static 'css/theme-default.css' %}?v={{ timestamp }}"/>
    <link rel="stylesheet" href="{% static 'css/base.css' %}?v={{ timestamp }}"/>
    <link rel="stylesheet" href="{% static 'css/perfect-scrollbar.css' %}?v={{ timestamp }}"/>
    <link rel="stylesheet" href="{% static 'css/select2.min.css' %}?v={{ timestamp }}"/>
    <link rel="stylesheet" href="{% static 'css/select2.min.css' %}?v={{ timestamp }}"/>
    <script src=" {% static 'js/helpers.js' %}?v={{ timestamp }}"></script>

    {% block extra_css %}{% endblock %}
</head>
<body>
<!-- Layout wrapper -->
    <div class="layout-wrapper layout-content-navbar">
        <div class="layout-container">
            <!-- Menu -->
            <aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">
          <div class="app-brand base">
            <a href="/" class="app-brand-link">
              <span class="app-brand-text base menu-text fw-bolder ms-2">DDe_BIツール</span>
            </a>
            <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto d-block d-xl-none">
              <i class="bx bx-chevron-left bx-sm align-middle"></i>
            </a>
          </div>

          <div class="menu-inner-shadow"></div>

          <ul class="menu-inner py-1">
            <!-- Dashboard -->
            <li class="menu-item ">
              <a href="{% url 'analytics_top_page' %}"  class="menu-link">
                <i class="menu-icon tf-icons bx bx-home-circle"></i>
                <div data-i18n="Analytics">基礎分析</div>
              </a>
            </li>

            <!-- Corporates -->
            <li class="menu-item {% if request.path == '/corporates/' %}active{% endif %}">
              <a href="/corporates/" class="menu-link">
                <i class="menu-icon tf-icons bx bx-layout"></i>
                <div data-i18n="Layouts">企業一覧</div>
              </a>
            </li>
            <!-- CSV Upload -->
            <li class="menu-item {% if request.path == '/' or request.path == '/dashboard/' %}active{% endif %}">
              <a href="/"  class="menu-link">
                <i class="menu-icon tf-icons bx bx-file"></i>
                <div data-i18n="Account Settings">CSVアップロード</div>
              </a>
            </li>
          </ul>
        </aside>
            <!-- / Menu -->

            <!-- Layout container -->
            <div class="layout-page">
                <!-- Navbar -->
                <nav class="layout-navbar container-xxl navbar navbar-expand-xl navbar-detached align-items-center bg-navbar-theme" id="layout-navbar">
                    <div class="layout-menu-toggle navbar-nav align-items-xl-center me-3 me-xl-0 d-xl-none">
                        <a class="nav-item nav-link px-0 me-xl-4" href="javascript:void(0)">
                            <i class="bx bx-menu bx-sm"></i>
                        </a>
                    </div>
                    <div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse"></div>
                </nav>
                    <!-- / Navbar -->
           <!-- Content wrapper -->
            <div class="content-wrapper">
                {% block content %}{% endblock %}
            </div>
            </div>

        </div>
    </div>
    <script src=" {% static 'js/jquery.js' %}?v={{ timestamp }}"></script>
    <script src=" {% static 'js/bootstrap.js' %}?v={{ timestamp }}"></script>
    <script src=" {% static 'js/menu.js' %}?v={{ timestamp }}"></script>
    <script src=" {% static 'js/main.js' %}?v={{ timestamp }}"></script>
    <script src=" {% static 'js/select2.min.js' %}?v={{ timestamp }}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>