{% extends "layout/base.html" %}
{% block content %}
<div class="container-xxl flex-grow-1 container-p-y">
    <p><span class="fw-light">基礎分析 ＞</span></p>
    <h3 class="fw-bold "><span class="fw-light">基礎分析</span></h3>
    <div class="row">
        <div class="card mb-4 card-min-300">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                    <div class="input-group">
                        <select class="form-control mySelect" id="corporateSelect" style="width: 100%">
                            <option value="">企業名を入力</option>
                            {% for item in corporate_lists %}
                            <option value="{{item.company_id}}">{{item.company_name}}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                    <div class="col-md-6">
                    <div class="input-group">
                        <select  class="form-control mySelect" id="projectSelect" style="width: 100%">

                        </select>
                    </div>
                </div>
                </div>

            </div>
        </div>
    </div>
</div>


<!-- end extra Large Modal analytics-->
{% endblock %}
{% block extra_js%}
<script>
    $(document).ready(function () {
        $('.mySelect').select2({
            placeholder: '企業名を入力',
            allowClear: true,
            width: '100%',
            language: {
                noResults: function () {
                    return "";
                }
            }
        });
        $('#corporateSelect').on('change', function () {
            if ($(this).val() !== '') {
                $.ajax({
                    url: '/api/projects-by-company-id/',
                    type: 'GET',
                    data: {
                        company_id: $(this).val(),
                    },
                    success: function (response) {
                         let html = '';
                         if(response.results.length === 0){
                             html = `
                             <option value="">データがありません</option>`;
                         }else {
                             response.results.forEach(function (project) {
                                 html += `
                                 <option value="${project.system_id}">${project.system_name}</option>`;
                             });
                         }
                        $('#projectSelect').html(html);
                    },
                    error: function () {
                    }
                });
            } else {

            }

        })

    });
</script>
{% endblock %}