{% extends "layout/base.html" %}
{% block content %}
<div class="container-xxl flex-grow-1 container-p-y">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-style1">
            <li class="breadcrumb-item">
                <a href="">基礎分析</a>
            </li>
            <li class="breadcrumb-item">
            </li>

        </ol>
    </nav>
    <h3 class="fw-bold ">基礎分析</h3>
    <div class="row">
        <div class="card mb-4 card-min-400">
            <div class="card-body d-flex flex-column">
                <div class="row">
                    <div class="col-md-6">
                    <div class="input-group">
                        <select class="form-control mySelect" id="corporateSelect" style="width: 100%">
                            <option value="">企業名を入力</option>
                            {% for item in corporate_lists %}
                            <option value="{{item.company_id}}">{{item.company_name}}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                    <div class="col-md-6">
                    <div class="input-group">
                        <select  class="form-control mySelect" id="projectSelect" style="width: 100%">
                            <option value="">系統名を入力</option>
                        </select>
                    </div>
                </div>
                </div>
                <div class="mt-auto pt-3">
                    <div class="d-flex justify-content-center gap-2 mt-2">
                      <button class="btn btn-primary">キャンセル</button>
                      <button class="btn btn-primary" onclick="gotoAnalyticsDashboardChart()">決定する</button>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>


<!-- end extra Large Modal analytics-->
{% endblock %}
{% block extra_js%}
<script>
    $(document).ready(function () {
        $('.mySelect').select2({
            placeholder: '企業名を入力',
            allowClear: true,
            width: '100%',
            language: {
                noResults: function () {
                    return "";
                }
            }
        });
        $('#projectSelect').select2({
            placeholder: '系統名を入力',
            allowClear: true,
            width: '100%',
            language: {
                noResults: function () {
                    return "";
                }
            }
        });
        $('#corporateSelect').on('change', function () {
            if ($(this).val() !== '') {
                $.ajax({
                    url: '/api/projects-by-company-id/',
                    type: 'GET',
                    data: {
                        company_id: $(this).val(),
                    },
                    success: function (response) {
                        let html = '';
                        if (response.results.length === 0) {
                            html = `
                             <option value="">系統名を入力</option>`;
                        } else {
                            response.results.forEach(function (project) {
                                html += `
                                 <option value="${project.system_id}">${project.system_name}</option>`;
                            });
                        }
                        $('#projectSelect').html(html);
                    },
                    error: function () {
                        $('#projectSelect').html('');
                    }
                });
            } else {

            }
        })

    });

    function gotoAnalyticsDashboardChart() {
        let system_id = $('#projectSelect').val();
        if (system_id !== '') {
            window.location.href = '/analytics-dashboard/' + system_id;
        }
    }
</script>
{% endblock %}