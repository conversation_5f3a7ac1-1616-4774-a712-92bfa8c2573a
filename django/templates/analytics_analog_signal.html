{% extends "layout/base.html" %}
{% load custom_tags %}
{% block content %}
{% load custom_tags %}
<div class="container-xxl flex-grow-1 container-p-y">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-style1">
            <li class="breadcrumb-item">
                <a href="{% url 'analytics_top_page' %}">基礎分析</a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'project_list' %}?company_id={{company_id}}">{{company_name}}</a>
            </li>
            <li class="breadcrumb-item active">
                <a href="{% url 'analytics_top_page' %}">
                     {{system_name}}
                </a>
            </li>
        </ol>
    </nav>
    <h3 class="fw-bold ">基礎分析 </h3>
    <div class="nav-align-top mb-4">
        <ul class="nav nav-tabs" role="tablist">
            <li class="nav-item">
                <button type="button" class="nav-link" onClick="window.location.href = '/analytics-dashboard/{{project_id}}';" role="tab">
                    日別・温度帯別
                </button>
            </li>
            <li class="nav-item">
                <button type="button" class="nav-link active" role="tab" data-bs-toggle="tab"
                        data-bs-target="#navs-top-profile" aria-controls="navs-top-profile" aria-selected="true">
                    類似波形
                </button>
            </li>
        </ul>
         <div class="tab-content">
            <div class="tab-pane fade " id="navs-top-home" role="tabpanel">
            </div>
            <div class="tab-pane fade active show" id="navs-top-profile" role="tabpanel">
                <div class="card shadow-none">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 col-lg-4 order-2 mb-4 "><label>系統名: <b>{{system_name}}</b> </label></div>
                            <div class="col-md-6 col-lg-4 order-2 mb-4 "><label>型番: <b>{{outdoor_model}}</b> </label></div>
                            <div class="col-md-6 col-lg-4 order-2 mb-4 "><label>定格消費電力: <b>{{rated_power}}</b></label></div>
                        </div>
                        <form id="formSearch">
                            <div class="row g-3 align-items-end">
                                <div class="col-md-4">
                                    <input type="text" id="correlation_coefficient"
                                           value="{{pearson_correlation_temp}}" name="pearson_correlation_temp"
                                           class="form-control pearson_correlation" placeholder="相関係数">
                                </div>
                                <div class="col-md-4">
                                    <input type="text" value="{{maximum_temp}}"
                                           name="maximum_temp" class="form-control temp" placeholder="最高気温差">
                                </div>
                            </div>
                                <div class="row g-3 mt-3 align-items-end">
                                    <div class="col-md-4">
                                        <input type="text" value="{{minimum_temp}}"
                                               name="minimum_temp" class="form-control temp" placeholder="最低気温差">
                                    </div>
                                    <div class="col-md-4">
                                        <input type="text" value="{{avg_temp}}" name="avg_temp"
                                               class="form-control temp" placeholder="平均気温差">
                                    </div>
                                    <div class="col-md-4 ms-auto d-flex gap-2">
                                        <button id="resetBtn" type="button" class="btn btn-primary w-100" onclick="resetForm()">クリア</button>
                                        <button type="submit" class="btn btn-primary w-100">類似条件を探す</button>
                                    </div>
                                </div>

                        </form>
                        <div class="table-responsive mt-3">
                            <table class="table table-hover">
                                <thead>
                                <tr>
                                    <th>日付1</th>
                                    <th>日付2</th>
                                    <th>相関係数</th>
                                    <th>最高気温</th>
                                    <th>最低気温</th>
                                    <th>平均気温</th>
                                    <th>削減率</th>
                                    <th class="text-center"></th>
                                </tr>
                                </thead>
                                <tbody>
                                {% for item in results.data %}
                                <tr>
                                    <td>{{ item.dateNotInstall }}</td>
                                    <td>{{ item.dateInstall }}</td>
                                    <td>{{ item.checkPearsonCorrelationCoefficientTemp }}</td>
                                    <td>{{ item.maxmum_temp_not_install }}-{{ item.maximum_temp_install }}</td>
                                    <td>{{ item.minimum_temp_not_install }}-{{ item.minimum_temp_install }}</td>
                                    <td>{{ item.avg_temp_not_install }}-{{ item.avg_temp_install }}</td>
                                    <td>{{ item.cutting_performance|floatformat:2 }} %</td>
                                    <td class="text-center">
                                        <button type="button" class="btn btn-primary btn-list-item"
                                                data-item='{"list_times": "{{ item.list_times|escapejs }}","itemTempNotInstalls": {{ item.itemTempNotInstalls|escapejs }},"itemTempInstalls": {{ item.itemTempInstalls|escapejs }},"itemPowerNotInstalls": {{ item.itemPowerNotInstalls|escapejs }},"itemPowerInstalls": {{ item.itemPowerInstalls|escapejs }},"dateNotInstall": "{{ item.dateNotInstall|escapejs }}","dateInstall":"{{ item.dateInstall|escapejs }}"}'
                                                onclick="updateChart(this)">グラフ表示
                                        </button>
                                    </td>

                                </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <nav>
                            <ul class="pagination justify-content-center mt-3">
                                <!-- First Page -->
                                {% if results.page > 1 %}
                                <li class="page-item">
                                    <a class="page-link"
                                       href="?pearson_correlation_temp={{ request.GET.pearson_correlation_temp }}&maximum_temp={{ request.GET.maximum_temp }}&minimum_temp={{ request.GET.minimum_temp }}&avg_temp={{ request.GET.avg_temp }}&pages=1">&laquo;</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">&laquo;</span>
                                </li>
                                {% endif %}

                                <!-- Previous -->
                                {% if results.page > 1 %}
                                <li class="page-item">
                                    <a class="page-link" href="{% pagination_url request results.page|add:'-1' %}">&lsaquo;</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">&lsaquo;</span>
                                </li>
                                {% endif %}

                                <!-- Page Numbers -->
                                {% for p in results.page_range %}
                                {% if p == '...' %}
                                <li class="page-item disabled">
                                    <span class="page-link">{{ p }}</span>
                                </li>
                                {% elif p == results.page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ p }}</span>
                                </li>
                                {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="{% pagination_url request p %}">{{ p }}</a>
                                </li>
                                {% endif %}
                                {% endfor %}

                                <!-- Next -->
                                {% if results.page < results.total_pages %}
                                <li class="page-item">
                                    <a class="page-link" href="{% pagination_url request results.page|add:'1' %}">&rsaquo;</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">&rsaquo;</span>
                                </li>
                                {% endif %}

                                <!-- Last Page -->
                                {% if results.page < results.total_pages %}
                                <li class="page-item">
                                    <a class="page-link"
                                       href="{% pagination_url request results.total_pages %}">&raquo;</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">&raquo;</span>
                                </li>
                                {% endif %}

                            </ul>
                        </nav>
                        <div class="mb-5">
                            <div class="row g-2">
                                <div class="col-12 col-sm-auto">
                                    <button class="btn btn-primary w-100" onclick="downloadChartImage()">📥 画像をダウンロード</button>
                                </div>
                                <div class="col-12 col-sm-auto">
                                    <button class="btn btn-primary w-100" onclick="downloadCSV({{project_id}})">エクスポート</button>
                                </div>
                                <div class="col-12 d-flex align-items-center justify-content-center">
                                    <div id="time-chart"></div>
                                </div>
                            </div>
                            <div id="chart_analog_signal" style="width: 100%; height: 500px;"></div>
                        </div>
                    </div>
                </div>
         </div>
    </div>
</div>
</div>
{% endblock %}
{% block extra_js%}

<script>
    $(document).ready(function () {

        $('input.pearson_correlation').on('input', function () {
            const val = $(this).val();
            if (val === '' || val === '-' || val === '0.' || val === '1.' || val === '-0.' || val === '-1.') {
                return;
            }
            const regex = /^-?([01](\.\d{1,2})?|0?\.\d{1,2})$/;

            if (!regex.test(val)) {
                $(this).val(val.slice(0, -1));
                return;
            }
            const num = parseFloat(val);
            if (!isNaN(num) && (num > 1 || num < -1)) {
                $(this).val(val.slice(0, -1));
            }
        });

        $('input.pearson_correlation').on('keypress', function (e) {
            const char = String.fromCharCode(e.which);
            const currentVal = $(this).val();
            const cursorPos = this.selectionStart;

            if (e.which === 8 || e.which === 9 || e.which === 13 || e.which === 46) {
                return;
            }
            if (!/[0-9.\-]/.test(char)) {
                e.preventDefault();
                return;
            }
            if (char === '-' && cursorPos !== 0) {
                e.preventDefault();
                return;
            }
            if (char === '-' && currentVal.includes('-')) {
                e.preventDefault();
                return;
            }
            if (char === '.' && currentVal.includes('.')) {
                e.preventDefault();
                return;
            }
        });

        $('input.temp').on('input', function () {
        const val = $(this).val();
        // Cho phép các trạng thái đang gõ: '-', '-0.', '0.', '12.'
        const allowTemporary = /^-?$|^-?\d+\.?$/.test(val);

        // Giá trị hoàn chỉnh (số âm hoặc dương, tối đa 1 chữ số sau dấu chấm)
        const regex = /^-?\d+(\.\d{0,1})?$/;

        if (!regex.test(val) && !allowTemporary && val !== '') {
            $(this).val(val.slice(0, -1));
        }
    });

    });
    var chart;
    var dateInstall;
    var dateNotInstall;
    let visibilityChart = {1: true, 2: true,3: true,4: true};

    google.charts.load('current', {packages: ['corechart']});
    google.charts.setOnLoadCallback(drawChart);

    function drawChart() {
        chart = new google.visualization.ComboChart(document.getElementById('chart_analog_signal'));
        updateChart()
    }

    function updateChart(buttonElement = undefined, hiddenData= false) {
        let dataItem = "";
        if (buttonElement === undefined) {
            if (!hiddenData) {
                return;
            }
            if (hiddenData) {
                dataItem = $('.btn-list-item.btn-dark').data("item");
            }
        } else {
            $('.btn-list-item.btn-dark').toggleClass('btn-primary');
            $('.btn-list-item').removeClass('btn-dark');
            $(buttonElement).removeClass('btn-primary');
            $(buttonElement).toggleClass('btn-dark');
            dataItem = JSON.parse(buttonElement.getAttribute("data-item"));
        }
        dateInstall = dataItem.dateInstall;
        dateNotInstall = dataItem.dateNotInstall;
        $('#time-chart').text( dataItem.dateNotInstall + ' - ' + dataItem.dateInstall);
        const resultItem = JSON.parse(dataItem.list_times).map((time, i) => [
            time,
            dataItem.itemPowerNotInstalls[i],
                        dataItem.itemPowerInstalls[i],
            dataItem.itemTempInstalls[i],
            dataItem.itemTempNotInstalls[i],

        ]);
        const dataChart = [['日付', '消費電力(kW)', '消費電力(kW)', '外気温(℃)', '外気温(℃)']];
        dataChart.push(...resultItem);

        const data = google.visualization.arrayToDataTable(dataChart);
        const view = new google.visualization.DataView(data);
        const columns = [0];
        const colors = ['#a4a6a8', '#2684FC', '#2684FC','#a4a6a8'];
        for (let i = 1; i < data.getNumberOfColumns(); i++) {
            if (visibilityChart[i] === false) {
                columns.push({
                    calc: () => null,
                    label: data.getColumnLabel(i),
                    type: data.getColumnType(i)
                });
                if(i===1){
                    colors[0] = 'black';
                }
                if(i===2){
                    colors[1] = 'black';
                }
                if(i===3){
                    colors[2] = 'black';
                }
                if(i===4){
                    colors[3] = 'black';
                }
            } else {
                columns.push(i);
            }
        }

        view.setColumns(columns);
        const options = {
            title: '',
            chartArea: {width: '75%', height: '70%'},
            hAxis: {
                title: '時間',
                titleTextStyle: {italic: false},
                slantedText: true,
                slantedTextAngle: 45
            },
            vAxes: {
                0: {title: '消費電力（kW）',minValue: 0,  titleTextStyle: {italic: false}},
                1: {title: '外気温（°C）', minValue: 0, titleTextStyle: {italic: false}}
            },
            seriesType: 'bars',
            series: {
                0: {type: 'bars', targetAxisIndex: 0, color: colors[0]},
                1: {type: 'bars', targetAxisIndex: 0, color: colors[1]},
                2: {type: 'line', targetAxisIndex: 1, color: colors[2]},
                3: {type: 'line', targetAxisIndex: 1, color: colors[3]}
            },
            legend: {position: 'top'}
        };
        chart.draw(view, options);
        google.visualization.events.addListener(chart, 'select', function () {
            const sel = chart.getSelection();
            if (sel.length > 0) {
                const selection = sel[0];
                if (selection.row === null && selection.column > 0) {
                    visibilityChart[selection.column] = !visibilityChart[selection.column];
                    updateChart(undefined, true);
                }
            }
        });
    }

    function downloadChartImage() {
        if (!chart) {
            alert('チャートが読み込まれていません。しばらく待ってから再試行してください。');
            return;
        }
        const imageURI = chart.getImageURI();

        downloadChartAsJPEG(imageURI, 'chart_analytics_analog_signal.jpeg');
    }

    function downloadChartAsJPEG(imageURI, filename = 'chart.jpeg') {
        const img = new Image();
        img.crossOrigin = 'Anonymous';  // Tránh lỗi CORS nếu cần
        img.onload = function () {
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;

            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#ffffff'; // Nền trắng cho JPEG (tránh nền trong suốt)
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            ctx.drawImage(img, 0, 0);

            const jpegURI = canvas.toDataURL('image/jpeg', 0.95);  // 0.95 = chất lượng

            const a = document.createElement('a');
            a.href = jpegURI;
            a.download = filename;
            a.click();
        };
        img.src = imageURI;
    }
    function downloadCSV(project_id) {
        if (dateInstall === undefined || dateNotInstall === undefined) {
            alert('日付を選択してください');
            return;
        }
      const url = `/export-analytics-analog-signal/${project_id}?date_install=${dateInstall}&date_not_install=${dateNotInstall}`;
      const link = document.createElement('a');
      link.href = url;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    function resetForm() {
        const form = document.querySelector('form');
        console.log(form);
        form.querySelectorAll('input').forEach(input => input.value = '');
    }
</script>
{% endblock %}