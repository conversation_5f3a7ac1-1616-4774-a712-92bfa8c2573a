from django.utils import timezone
from django.db import models
import csv
from django.db import transaction

class CorporateMaster(models.Model):
    company_id = models.BigAutoField(primary_key=True, verbose_name="法人ID")
    company_name = models.CharField(max_length=255, verbose_name="法人名")
    industry_type = models.CharField(max_length=100, verbose_name="業務")
    annual_revenue = models.CharField(max_length=50, verbose_name="企業規模")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="登録日時")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新日時")
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name="削除日時")

    class Meta:
        db_table = 'corporate_master'
        verbose_name_plural = 'CorporateMaster'
        indexes = [
            models.Index(fields=['company_name'], name='company_name_idx'),
            models.Index(fields=['industry_type'], name='industry_type_idx'),
            models.Index(fields=['annual_revenue'], name='annual_revenue_idx'),
            models.Index(fields=['created_at'], name='created_at_idx'),
        ]

    def __str__(self):
        return str(self.company_id)


class ProjectCorporateMaster(models.Model):
    system_id = models.BigAutoField(primary_key=True, verbose_name="系統ID")
    company = models.ForeignKey(CorporateMaster, on_delete=models.CASCADE, verbose_name="法人ID")
    system_name = models.CharField(max_length=100, verbose_name="系統名")
    set_name = models.CharField(max_length=100, null=True, blank=True, verbose_name="セット名")
    set_model = models.CharField(max_length=100, null=True, blank=True, verbose_name="セット型式")
    outdoor_model = models.CharField(max_length=100, verbose_name="室外機型番")
    num_sets = models.IntegerField(default=1, verbose_name="セット台数")
    num_outdoor_units = models.IntegerField(default=1, verbose_name="室外機台数")
    num_indoor_units = models.IntegerField(default=1, verbose_name="室内機台数")
    room_type = models.CharField(max_length=50, verbose_name="部屋タイプ")
    operation_mode = models.CharField(max_length=50, verbose_name="運転手別")
    monthly_operating_days = models.IntegerField(verbose_name="月間稼働日数")
    daily_operating_hours = models.DecimalField(max_digits=4, decimal_places=1, verbose_name="一日稼働時間")
    temp_summer = models.DecimalField(max_digits=4, decimal_places=1, verbose_name="設定温度夏")
    temp_winter = models.DecimalField(max_digits=4, decimal_places=1, verbose_name="設定温度冬")
    manufacturer = models.CharField(max_length=100, verbose_name="メーカー")
    compressor_output = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True,
                                            verbose_name="圧縮機出力")
    rated_power = models.DecimalField(max_digits=6, decimal_places=2, verbose_name="定格消費電力")
    refrigerant_type = models.CharField(max_length=50, verbose_name="冷媒種")
    piping_diameter = models.CharField(max_length=50, verbose_name="配管経")
    installation_date = models.DateField(verbose_name="導入年月")
    piping_length = models.DecimalField(max_digits=6, decimal_places=1, verbose_name="配管長")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="登録日時")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新日時")
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name="削除日時")

    class Meta:
        db_table = 'project_corporate_master'
        verbose_name_plural = 'ProjectCorporateMaster'
        indexes = [
            models.Index(fields=['company'], name='pcm_company_idx'),
            models.Index(fields=['system_name'], name='pcm_system_name_idx'),
            models.Index(fields=['manufacturer'], name='pcm_manufacturer_idx'),
            models.Index(fields=['installation_date'], name='pcm_install_date_idx'),
            models.Index(fields=['created_at'], name='pcm_created_at_idx'),
            models.Index(fields=['deleted_at'], name='pcm_deleted_at_idx'),
        ]
    def __str__(self):
        return self.name

class BasicAnalytics(models.Model):
    measurement_id = models.BigAutoField(primary_key=True, verbose_name="計測ID")
    system = models.ForeignKey(ProjectCorporateMaster, on_delete=models.CASCADE, verbose_name="系統ID")
    measurement_time = models.DateTimeField(verbose_name="計測日時")
    measurement_interval = models.IntegerField(verbose_name="測定間隔（分）")
    power_consumption = models.DecimalField(max_digits=6, decimal_places=2, verbose_name="消費電力(kW)")
    load_rate = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="負荷率(%)")
    outdoor_temp = models.DecimalField(max_digits=4, decimal_places=1, verbose_name="外気温(℃)")
    suction_temp = models.DecimalField(max_digits=4, decimal_places=1, verbose_name="吸込温度(℃)")
    discharge_temp = models.DecimalField(max_digits=4, decimal_places=1, verbose_name="吹出温度(℃)")
    is_anomaly = models.BooleanField(default=False, verbose_name="異常フラグ")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="登録日時")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新日時")
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name="削除日時")
    is_installed = models.BooleanField(default=False, verbose_name="異常フラグ")

    class Meta:
        db_table = 'basic_analytics'
        verbose_name_plural = 'ProductItemMapping'

    def __str__(self):
        return str(self.measurement_id)

