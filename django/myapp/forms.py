from django import forms

class UploadCSVForm(forms.Form):
    csv_file = forms.FileField(
        label='Select a CSV file',
        help_text='Max. 20 megabytes',
        widget=forms.ClearableFileInput(attrs={'accept': '.csv'})
    )
    
    def clean_csv_file(self):
        csv_file = self.cleaned_data.get('csv_file')
        
        if csv_file:
            # Check file size
            if csv_file.size > 20 * 1024 * 1024:  # 10 MB limit
                raise forms.ValidationError('File size must be under 20 MB')
                
            # Check file extension
            if not csv_file.name.endswith('.csv'):
                raise forms.ValidationError('File must be a CSV file')
                
        return csv_file