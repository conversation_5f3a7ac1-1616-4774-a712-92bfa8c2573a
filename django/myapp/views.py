import csv
import io
import json
from collections import defaultdict
from statistics import mean

import pytz
from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.views import LoginView
from django.http import HttpResponseRedirect, HttpResponse
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import transaction
from .models import CorporateMaster, ProjectCorporateMaster, BasicAnalytics
from .serializers import CSVUploadSerializer, ProjectCSVUploadSerializer, AnalyticsCSVUploadSerializer
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.utils.timezone import now, localtime
from rest_framework.permissions import IsAuthenticated
from django.db.models import <PERSON>, <PERSON>, <PERSON>, Avg, <PERSON>, <PERSON><PERSON>, <PERSON>, ExpressionWrapper, Float<PERSON>ield
from django.db.models.functions import TruncDate, Floor
from datetime import datetime, date, timedelta
from decimal import Decimal, ROUND_HALF_UP
from dateutil import parser
import scipy.stats
import csv
class CustomLoginView(LoginView):
    template_name = 'login.html'

    def form_valid(self, form):
        # Call the parent class's form_valid method to authenticate the user
        response = super().form_valid(form)

        # Check if the "remember me" checkbox is checked
        remember_me = self.request.POST.get('remember_me', False)

        if not remember_me:
            # If not checked, set session to expire when browser closes
            self.request.session.set_expiry(0)
        else:
            # If checked, set session to expire according to SESSION_COOKIE_AGE in settings
            # Default is 2 weeks (1209600 seconds)
            self.request.session.set_expiry(1209600)

        return response

@login_required
def dashboard(request):
    return render(request, 'dashboard.html',{'timestamp': int(now().timestamp())})

class CorporateMasterCSVUploadView(APIView):
    def post(self, request, *args, **kwargs):
        serializer = CSVUploadSerializer(data=request.data)
        if serializer.is_valid():
            csv_file = serializer.validated_data['csv_file']

            # Check file size
            if csv_file.size > 20 * 1024 * 1024:  # 20 MB limit
                return Response(
                    {"error": "File size must be under 20 MB"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check file extension
            if not csv_file.name.endswith('.csv'):
                return Response(
                    {"error": "File must be a CSV file"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Process CSV file
            try:
                decoded_file = csv_file.read().decode('utf-8-sig')
                io_string = io.StringIO(decoded_file)
                reader = csv.reader(io_string)

                # Skip header row
                header = next(reader)

                # Expected columns
                expected_columns = ['company_name', 'industry_type', 'annual_revenue']
                # Validate header
                if not all(col in header for col in expected_columns):
                    return Response(
                        {"error": f"必須項目を含むCSVをアップロードしてください。"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Get column indices
                col_indices = {col: header.index(col) for col in expected_columns if col in header}

                # Process rows
                with transaction.atomic():
                    created_count = 0
                    for row in reader:
                        if len(row) >= len(expected_columns):
                            CorporateMaster.objects.create(
                                company_name=row[col_indices['company_name']],
                                industry_type=row[col_indices['industry_type']],
                                annual_revenue=row[col_indices['annual_revenue']]
                            )
                            created_count += 1

                return Response(
                    {"success": f"Successfully imported {created_count} companies"},
                    status=status.HTTP_201_CREATED
                )

            except Exception as e:
                return Response(
                    {"error": f"必須項目を含むCSVをアップロードしてください。"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ProjectCorporateMasterCSVUploadView(APIView):
    def post(self, request, *args, **kwargs):
        serializer = ProjectCSVUploadSerializer(data=request.data)
        if serializer.is_valid():
            csv_file = serializer.validated_data['csv_file']

            # check file size
            if csv_file.size > 20 * 1024 * 1024:
                return Response(
                    {"error": f"ファイルサイズは20MB以下である必要があります。"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check file extension
            if not csv_file.name.endswith('.csv'):
                return Response(
                    {"error": f"ファイル形式はCSVである必要があります。"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Process CSV file
            try:
                decoded_file = csv_file.read().decode('utf-8-sig')
                io_string = io.StringIO(decoded_file)
                reader = csv.reader(io_string)

                # Skip header row
                header = next(reader)

                # Expected columns - adjust these based on your CSV structure
                expected_columns = [
                    'company_id', 'system_name', 'set_name', 'set_model',
                    'outdoor_model', 'num_sets', 'num_outdoor_units',
                    'num_indoor_units', 'room_type', 'operation_mode',
                    'monthly_operating_days', 'daily_operating_hours',
                    'temp_summer', 'temp_winter', 'manufacturer',
                    'compressor_output', 'rated_power', 'refrigerant_type',
                    'piping_diameter', 'installation_date', 'piping_length'
                ]

                # Validate header
                if not all(col in header for col in expected_columns):
                    return Response(
                        {"error": f"必須項目を含むCSVをアップロードしてください。"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Get column indices
                col_indices = {col: header.index(col) for col in expected_columns}
                errors = []
                created_count = 0

                # Process rows
                with transaction.atomic():
                    for i, row in enumerate(reader, start=2):  # dòng bắt đầu từ 2 (bỏ header)
                        if len(row) < len(expected_columns):
                            errors.append(f"{i}行目：列が不足しています。")
                            continue

                        try:
                            company_id = row[col_indices['company_id']].strip()
                            try:
                                company = CorporateMaster.objects.get(company_id=company_id)
                            except CorporateMaster.DoesNotExist:
                                errors.append(f"{i}行目：会社ID '{company_id}'が存在しません。")
                                continue
                            try:
                                installation_date = parser.parse(row[col_indices['installation_date']].strip()).date()
                            except ValueError:
                                errors.append(f" {i}行目：「installation_date」の日付形式が無効です。")
                                continue

                            # Tạo bản ghi
                            ProjectCorporateMaster.objects.create(
                                company=company,
                                system_name=row[col_indices['system_name']].strip(),
                                set_name=row[col_indices['set_name']].strip(),
                                set_model=row[col_indices['set_model']].strip(),
                                outdoor_model=row[col_indices['outdoor_model']].strip(),
                                num_sets=int(row[col_indices['num_sets']]),
                                num_outdoor_units=int(row[col_indices['num_outdoor_units']]),
                                num_indoor_units=int(row[col_indices['num_indoor_units']]),
                                room_type=row[col_indices['room_type']].strip(),
                                operation_mode=row[col_indices['operation_mode']].strip(),
                                monthly_operating_days=int(row[col_indices['monthly_operating_days']]),
                                daily_operating_hours=float(row[col_indices['daily_operating_hours']]),
                                temp_summer=float(row[col_indices['temp_summer']]),
                                temp_winter=float(row[col_indices['temp_winter']]),
                                manufacturer=row[col_indices['manufacturer']].strip(),
                                compressor_output=float(row[col_indices['compressor_output']]) if row[
                                    col_indices['compressor_output']].strip() else None,
                                rated_power=float(row[col_indices['rated_power']]),
                                refrigerant_type=row[col_indices['refrigerant_type']].strip(),
                                piping_diameter=row[col_indices['piping_diameter']].strip(),
                                installation_date= installation_date,
                                piping_length=float(row[col_indices['piping_length']])
                            )
                            created_count += 1

                        except ValueError as ve:
                            errors.append(f" {i}行目：無効な値です。 - {str(ve)}")
                        except Exception as e:
                            errors.append(f" {i}行目: 予期しないエラーが発生しました。 - {str(e)}")

                    # Rollback nếu có lỗi
                    if errors:
                        transaction.set_rollback(True)
                        return Response(
                            {"error": f"CSVファイルにエラーが見つかりました。", "details": errors},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                return Response(
                    {"success": f"{created_count} 件を正常にインポートしました。."},
                    status=status.HTTP_201_CREATED
                )

            except Exception as e:
                return Response(
                    {"error": f"CSVファイルにエラーが見つかりました。: {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class BasicAnalyticsCSVUploadView(APIView):
    def post(self, request, *args, **kwargs):
        serializer = AnalyticsCSVUploadSerializer(data=request.data)
        if serializer.is_valid():
            csv_file = serializer.validated_data['csv_file']

            # Check file size
            if csv_file.size > 20 * 1024 * 1024:  # 20 MB limit
                return Response(
                    {"error": "File size must be under 20 MB"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check file extension
            if not csv_file.name.endswith('.csv'):
                return Response(
                    {"error": "File must be a CSV file"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Process CSV file
            try:
                decoded_file = csv_file.read().decode('utf-8-sig')
                io_string = io.StringIO(decoded_file)
                reader = csv.reader(io_string)

                # Skip header row
                header = next(reader)

                # Expected columns
                expected_columns = [
                    'system_id', 'measurement_time', 'measurement_interval',
                    'power_consumption', 'load_rate', 'outdoor_temp',
                    'suction_temp', 'discharge_temp','is_anomaly','is_installed'
                ]

                # Validate header
                if not all(col in header for col in expected_columns):
                    return Response(
                        {"error": f"CSV must contain columns: {', '.join(expected_columns)}"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Get column indices
                col_indices = {col: header.index(col) for col in expected_columns if col in header}

                # Process rows
                with transaction.atomic():
                    created_count = 0
                    for row in reader:
                        if len(row) >= len(expected_columns):
                            try:
                                # Get the system instance
                                system = ProjectCorporateMaster.objects.get(system_id=row[col_indices['system_id']])

                                # Parse datetime
                                measurement_time = row[col_indices['measurement_time']].strip()
                                naive_dt = parser.parse(measurement_time)

                                # Chuyển sang datetime có timezone
                                measurement_time = timezone.make_aware(naive_dt)
                                # Create analytics record
                                BasicAnalytics.objects.create(
                                    system=system,
                                    measurement_time=measurement_time,
                                    measurement_interval=int(row[col_indices['measurement_interval']]),
                                    power_consumption=float(row[col_indices['power_consumption']]),
                                    load_rate=float(row[col_indices['load_rate']]),
                                    outdoor_temp=float(row[col_indices['outdoor_temp']]),
                                    suction_temp=float(row[col_indices['suction_temp']]),
                                    discharge_temp=float(row[col_indices['discharge_temp']]),
                                    is_anomaly=bool(row[col_indices['is_anomaly']]) , # Default value, can be updated later
                                    is_installed=bool(row[col_indices['is_installed']])  # Default value, can be updated later
                                )
                                created_count += 1
                            except ProjectCorporateMaster.DoesNotExist:
                                return Response(
                                    {"error": f"System ID {row[col_indices['system_id']]} does not exist"},
                                    status=status.HTTP_400_BAD_REQUEST
                                )
                            except ValueError as e:
                                return Response(
                                    {"error": f"Invalid data format: {str(e)}"},
                                    status=status.HTTP_400_BAD_REQUEST
                                )

                return Response(
                    {"success": f"Successfully imported {created_count} measurement records"},
                    status=status.HTTP_201_CREATED
                )

            except Exception as e:
                return Response(
                    {"error": f"Error processing CSV file: {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@login_required
def corporate_list(request):
    # Get unique industry types for filter dropdown
    industry_types = CorporateMaster.objects.values_list('industry_type', flat=True).distinct()

    context = {
        'search_query': request.GET.get('search', ''),
        'industry_types': industry_types,
        'timestamp': int(now().timestamp())
    }

    return render(request, 'corporate_list.html', context)
class CorporateListAPI(APIView):
    def get(self, request):
        permission_classes = [IsAuthenticated]
        corporate_list = CorporateMaster.objects.filter(deleted_at__isnull=True)

        # Search
        search_query = request.GET.get('search', '')
        if search_query:
            corporate_list = corporate_list.filter(
                models.Q(company_name__icontains=search_query) |
                models.Q(industry_type__icontains=search_query) |
                models.Q(company_id__icontains=search_query) |
                models.Q(annual_revenue=search_query)
            )
        # 🔽 Sort
        sort_by = request.GET.get('sort_by','created_at')  # Mặc định sort theo created_at
        sort_order = request.GET.get('sort_order', 'desc')
            # Áp dụng sắp xếp
        order_field = f'-{sort_by}' if sort_order == 'desc' else sort_by
        corporate_list = corporate_list.order_by(order_field)

        # Pagination
        paginator = Paginator(corporate_list, 10)
        page = request.GET.get('page')
        try:
            corporates = paginator.page(page)
        except PageNotAnInteger:
            corporates = paginator.page(1)
        except EmptyPage:
            corporates = paginator.page(paginator.num_pages)

        # Format data
        tokyo_tz = pytz.timezone("Asia/Tokyo")
        data = []
        for corp in corporates:
            data.append({
                "company_id": corp.company_id,
                "company_name": corp.company_name,
                "industry_type": corp.industry_type,
                "annual_revenue": corp.annual_revenue,
                "project_count": corp.projectcorporatemaster_set.filter(deleted_at__isnull=True).count(),
                "created_at": corp.created_at.astimezone(tokyo_tz).strftime("%Y/%m/%d %H:%M"),
            })

        return Response({
            "results": data,
            "pagination": {
                "page": corporates.number,
                "num_pages": paginator.num_pages,
                "has_next": corporates.has_next(),
                "has_previous": corporates.has_previous(),
                "next_page_number": corporates.next_page_number() if corporates.has_next() else None,
                "previous_page_number": corporates.previous_page_number() if corporates.has_previous() else None,
            }
        }, status=status.HTTP_200_OK)
@login_required
def project_list(request):
    company_id = request.GET.get('company_id', '')
    search_query = request.GET.get('search', '')
    manufacturers = ProjectCorporateMaster.objects.values_list('manufacturer', flat=True).distinct()
    context = {
        'company_id': company_id,
        'search_query': search_query,
        'manufacturers': manufacturers,
        'timestamp': int(now().timestamp())
    }

    return render(request, 'project_list.html', context)
class ProjectListAPI(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        project_list = ProjectCorporateMaster.objects.filter(deleted_at__isnull=True)

        # Search
        search_query = request.GET.get('search', '')
        if search_query:
            project_list = project_list.filter(
                models.Q(system_id__icontains=search_query) |
                models.Q(system_name__icontains=search_query) |
                models.Q(manufacturer__icontains=search_query)
            )
        company_id = request.GET.get('company_id', '')
        if company_id:
            project_list = project_list.filter(company_id=company_id)
        # 🔽 Sort
        sort_by = request.GET.get('sort_by','created_at')
        sort_order = request.GET.get('sort_order', 'desc')
            # Áp dụng sắp xếp
        order_field = f'-{sort_by}' if sort_order == 'desc' else sort_by
        project_list = project_list.order_by(order_field)

        # Pagination
        paginator = Paginator(project_list, 20)
        page = request.GET.get('page')
        try:
            projects = paginator.page(page)
        except PageNotAnInteger:
            projects = paginator.page(1)
        except EmptyPage:
            projects = paginator.page(paginator.num_pages)

        # Format data
        tokyo_tz = pytz.timezone("Asia/Tokyo")
        data = []
        for corp in projects:
            data.append({
                "system_id": corp.system_id,
                "system_name": corp.system_name,
                "set_model": corp.set_model,
                "outdoor_model": corp.outdoor_model,
                "num_sets": corp.num_sets,
                "num_outdoor_units": corp.num_outdoor_units,
                "manufacturer": corp.manufacturer,
                "num_indoor_units": corp.num_indoor_units,
                "company_id": corp.company_id,
                "created_at_tokyo": corp.created_at.astimezone(tokyo_tz).strftime("%Y/%m/%d %H:%M"),
            })

        return Response({
            "results": data,
            "pagination": {
                "page": projects.number,
                "num_pages": paginator.num_pages,
                "has_next": projects.has_next(),
                "has_previous": projects.has_previous(),
                "next_page_number": projects.next_page_number() if projects.has_next() else None,
                "previous_page_number": projects.previous_page_number() if projects.has_previous() else None,
            }
        }, status=status.HTTP_200_OK)
class CorporateMasterDeleteView(APIView):
    def delete(self, request, company_id):
        permission_classes = [IsAuthenticated]
        try:
            corporate = CorporateMaster.objects.get(company_id=company_id, deleted_at__isnull=True)
            corporate.deleted_at = now()
            corporate.save()
            return Response({'detail': 'Deleted successfully (soft delete).'}, status=status.HTTP_200_OK)
        except CorporateMaster.DoesNotExist:
            return Response({'detail': 'Not found or already deleted.'}, status=status.HTTP_404_NOT_FOUND)

class ProjectDeleteView(APIView):
    permission_classes = [IsAuthenticated]
    def delete(self, request,system_id):
        try:
            project = ProjectCorporateMaster.objects.get(system_id=system_id, deleted_at__isnull=True)
            project.deleted_at = now()
            project.save()
            return Response({'detail': 'Deleted successfully (soft delete).'}, status=status.HTTP_200_OK)
        except ProjectCorporateMaster.DoesNotExist:
            return Response({'detail': 'Not found or already deleted.'}, status=status.HTTP_404_NOT_FOUND)
def analyticsTopPage(request):
    corporate_lists =CorporateMaster.objects.filter(
        projectcorporatemaster__basicanalytics__isnull=False,
        projectcorporatemaster__deleted_at__isnull=True,
        deleted_at__isnull=True,
    ).distinct()
    return render(request, 'analytics_top_page.html',
                  {'timestamp': int(now().timestamp()),
                   'corporate_lists': corporate_lists
                   }
                  )
class ProjectListByCompanyIdAPI(APIView):
    def get(self, request):
        project_list = ProjectCorporateMaster.objects.filter(
            basicanalytics__isnull=False,
            deleted_at__isnull=True
        ).distinct()

        company_id = request.GET.get('company_id', '')
        if company_id:
            project_list = project_list.filter(company_id=company_id)
        data = []
        for corp in project_list:
            data.append({
                "system_id": corp.system_id,
                "system_name": corp.system_name,
            })

        return Response({
            "results": data,
        }, status=status.HTTP_200_OK)


def analyticsDashboard(request, project_id):
    analytics = BasicAnalytics.objects.filter(system_id=project_id, deleted_at__isnull=True)
    projectCorporateMaster= ProjectCorporateMaster.objects.filter(system_id=project_id).values_list('company_id','rated_power','outdoor_model','system_name').first()
    corporation = CorporateMaster.objects.filter(company_id=projectCorporateMaster[0]).values_list('company_name').first()
    company_name = corporation[0]
    rated_power = projectCorporateMaster[1]
    outdoor_model = projectCorporateMaster[2]
    system_name = projectCorporateMaster[3]
    # get value default form search
    measurement_time = analytics.aggregate(
        min_time=Min('measurement_time'),
        max_time=Max('measurement_time')
    )
    min_time = localtime(measurement_time['min_time']).date()
    max_time = localtime(measurement_time['max_time']).date()

    outdoor_temperature = analytics.aggregate(
        min_temp=Min('outdoor_temp'),
        max_temp=Max('outdoor_temp')
    )
    power_consumption = analytics.aggregate(
        min_power=Min('power_consumption'),
        max_power=Max('power_consumption')
    )
    start_date_raw = request.GET.get('start_date', '')
    end_date_raw = request.GET.get('end_date', '')

    if start_date_raw:
        start_date = datetime.strptime(start_date_raw, '%Y-%m-%d').date()
    else:
        start_date = min_time

    if end_date_raw:
        end_date = datetime.strptime(end_date_raw, '%Y-%m-%d').date()
    else:
        end_date = max_time

    outdoor_temperature_start = request.GET.get('outdoor_temperature_start', '')
    outdoor_temperature_end = request.GET.get('outdoor_temperature_end', '')
    power_consumption_start = request.GET.get('power_consumption_start', '')
    power_consumption_end = request.GET.get('power_consumption_end', '')
    result_count = request.GET.get('result_count', '')

    # search query data url
    analytics = analytics.filter(
        measurement_time__gte=start_date,
        measurement_time__lt=end_date + timedelta(days=1)
    )
    if outdoor_temperature_start:
        analytics = analytics.filter(outdoor_temp__gte=outdoor_temperature_start)
    if outdoor_temperature_end:
        analytics = analytics.filter(outdoor_temp__lte=outdoor_temperature_end)
    if power_consumption_start:
        analytics = analytics.filter(power_consumption__gte=power_consumption_start)
    if power_consumption_end:
        analytics = analytics.filter(power_consumption__lte=power_consumption_end)
    # end search query data url
    averagePowerConsumptionNotInstall = analytics.filter(is_installed=0).aggregate(avg_power=Avg('power_consumption'))[
                                            'avg_power'] or 0
    averagePowerConsumptionAfterInstall = analytics.filter(is_installed=1).aggregate(avg_power=Avg('power_consumption'))['avg_power'] or 0
    countNotInstall = analytics.filter(is_installed=0).count()
    countInstall = analytics.filter(is_installed=1).count()
    first_analytics = analytics.first()
    measurement_interval = first_analytics.measurement_interval if first_analytics else None
    if countNotInstall > 0 and measurement_interval:
        resultCountNotInstall = countNotInstall * 60 / measurement_interval
    else:
        resultCountNotInstall = 0
    if countInstall > 0 and measurement_interval:
        resultCountInstall = countInstall * 60 / measurement_interval
    else:
        resultCountInstall = 0
    averagePowerConsumption = analytics.aggregate(avg_power=Avg('power_consumption'))['avg_power'] or 0

    if rated_power > 0:
        loadfactor = averagePowerConsumption / rated_power
    else:
        loadfactor = 0
    if result_count:
        analytics = (
            analytics
            .annotate(measurement_date=TruncDate('measurement_time'))
            .values('measurement_date', 'is_installed', 'measurement_interval')
            .annotate(
                avg_power=Avg('power_consumption'),
                avg_temp=Avg('outdoor_temp'),
                sum_power=Sum('power_consumption'),
                sum_temp=Sum('outdoor_temp'),
                avg_suction_temp=Avg('suction_temp'),
                sum_suction_temp=Sum('suction_temp'),
                avg_discharge_temp=Avg('discharge_temp'),
                sum_discharge_temp=Sum('discharge_temp'),
                total_record=Count('measurement_id')
            )
            .annotate(
                expected_count=ExpressionWrapper(
                    F('total_record') * 60.0 / F('measurement_interval'),
                    output_field=FloatField()
                )
            )
            .filter(expected_count__gte=result_count)
            .order_by('measurement_date')
        )
    else:
        analytics = (analytics.annotate(measurement_date=TruncDate('measurement_time'))
                 .values('measurement_date', 'is_installed','measurement_interval')
                 .annotate(avg_power=Avg('power_consumption'),
                           avg_temp=Avg('outdoor_temp'),
                           sum_power=Sum('power_consumption'),
                           sum_temp=Sum('outdoor_temp'),
                           avg_suction_temp=Avg('suction_temp'),
                           sum_suction_temp=Sum('suction_temp'),
                           avg_discharge_temp=Avg('discharge_temp'),
                           sum_discharge_temp=Sum('discharge_temp'),
                           total_record= Count('measurement_id'))
                 .order_by('measurement_date'))
    chartDataDateAvg = []
    chartDataDateSum = []
    chartDataDateSuctionAvg = []
    chartDataDateSuctionSum = []
    chartDataDateDischargeAvg = []
    chartDataDateDischargeSum = []
    for analytics_data in analytics:
        if analytics_data['is_installed'] == 0:
            total = analytics_data['total_record'] * 60 / analytics_data['measurement_interval']
            chartDataDateAvg.append([
                analytics_data['measurement_date'].strftime('%Y/%m/%d'),
                safe_float(round(analytics_data['avg_power'],2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                 <b> データ数: </b> {int(total)}<br>
                                  <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                  <b> 電力消費量:</b> {safe_float(round(analytics_data['avg_power'], 2))} kW<br>
                                  <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_temp'], 2))}℃
                                </div>''',
                'color: #a4a6a8',
                safe_float(round(analytics_data['avg_temp'],2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                     <b> データ数: </b> {int(total)}<br>
                      <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                      <b> 電力消費量:</b> {safe_float(round(analytics_data['avg_power'], 2))} kW<br>
                      <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_temp'], 2))}℃
                    </div>'''            ])
            chartDataDateSum.append([
                analytics_data['measurement_date'].strftime('%Y/%m/%d'),
                safe_float(round(analytics_data['sum_power'],2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                 <b> データ数: </b> {int(total)}<br>
                                  <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                  <b> 電力消費量:</b> {safe_float(round(analytics_data['sum_power'], 2))} kW<br>
                                  <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_temp'], 2))}℃
                                </div>''',
                'color: #a4a6a8;',
                safe_float(round(analytics_data['avg_temp'],2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                     <b> データ数: </b> {int(total)}<br>
                      <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                      <b> 電力消費量:</b> {safe_float(round(analytics_data['sum_power'], 2))} kW<br>
                      <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_temp'], 2))}℃
                    </div>'''            ])
            chartDataDateSuctionAvg.append([
                analytics_data['measurement_date'].strftime('%Y/%m/%d'),
                safe_float(round(analytics_data['avg_power'],2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                 <b> データ数: </b> {int(total)}<br>
                                  <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                  <b> 電力消費量:</b> {safe_float(round(analytics_data['avg_power'], 2))} kW<br>
                                  <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_suction_temp'], 2))}℃
                                </div>''',
                'color: #a4a6a8',
                safe_float(round(analytics_data['avg_suction_temp'],2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                     <b> データ数: </b> {int(total)}<br>
                      <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                      <b> 電力消費量:</b> {safe_float(round(analytics_data['avg_power'], 2))} kW<br>
                      <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_suction_temp'], 2))}℃
                    </div>'''            ])
            chartDataDateSuctionSum.append([
                analytics_data['measurement_date'].strftime('%Y/%m/%d'),
                safe_float(round(analytics_data['sum_power'],2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                 <b> データ数: </b> {int(total)}<br>
                                  <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                  <b> 電力消費量:</b> {safe_float(round(analytics_data['sum_power'], 2))} kW<br>
                                  <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_suction_temp'], 2))}℃
                                </div>''',
                'color: #a4a6a8;',
                safe_float(round(analytics_data['avg_suction_temp'],2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                     <b> データ数: </b> {int(total)}<br>
                      <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                      <b> 電力消費量:</b> {safe_float(round(analytics_data['sum_power'], 2))} kW<br>
                      <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_suction_temp'], 2))}℃
                    </div>'''            ])
            chartDataDateDischargeAvg.append([
                analytics_data['measurement_date'].strftime('%Y/%m/%d'),
                safe_float(round(analytics_data['avg_power'],2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                 <b> データ数: </b> {int(total)}<br>
                                  <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                  <b> 電力消費量:</b> {safe_float(round(analytics_data['avg_power'], 2))} kW<br>
                                  <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_discharge_temp'], 2))}℃
                                </div>''',
                'color: #a4a6a8',
                safe_float(round(analytics_data['avg_discharge_temp'],2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                     <b> データ数: </b> {int(total)}<br>
                      <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                      <b> 電力消費量:</b> {safe_float(round(analytics_data['avg_power'], 2))} kW<br>
                      <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_discharge_temp'], 2))}℃
                    </div>'''            ])
            chartDataDateDischargeSum.append([
                analytics_data['measurement_date'].strftime('%Y/%m/%d'),
                safe_float(round(analytics_data['sum_power'],2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                 <b> データ数: </b> {int(total)}<br>
                                  <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                  <b> 電力消費量:</b> {safe_float(round(analytics_data['sum_power'], 2))} kW<br>
                                  <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_discharge_temp'], 2))}℃
                                </div>''',
                'color: #a4a6a8;',
                safe_float(round(analytics_data['avg_discharge_temp'],2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                     <b> データ数: </b> {int(total)}<br>
                      <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                      <b> 電力消費量:</b> {safe_float(round(analytics_data['sum_power'], 2))} kW<br>
                      <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_discharge_temp'], 2))}℃
                    </div>'''])
        else:
            total = analytics_data['total_record'] * 60 / analytics_data['measurement_interval']
            chartDataDateAvg.append([
                analytics_data['measurement_date'].strftime('%Y/%m/%d'),
                safe_float(round(analytics_data['avg_power'],2)),
                f'''<div style="width: 170px; font-size: 14px; margin-left: 10px;">
                                 <b> データ数: </b> {int(total)}<br>
                                  <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                  <b> 電力消費量:</b> {safe_float(round(analytics_data['avg_power'], 2))} kW<br>
                                  <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_temp'], 2))}℃
                                </div>''',
                'color: #007BFF',
                safe_float(round(analytics_data['avg_temp'],2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                     <b> データ数: </b> {int(total)}<br>
                                      <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                      <b> 電力消費量:</b> {safe_float(round(analytics_data['avg_power'], 2))} kW<br>
                                      <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_temp'], 2))}℃
                                    </div>'''
            ])
            chartDataDateSum.append([
                analytics_data['measurement_date'].strftime('%Y/%m/%d'),
                safe_float(round(analytics_data['sum_power'],2)),
                f'''<div style="width: 170px; font-size: 14px; margin-left: 10px;">
                                 <b> データ数: </b> {int(total)}<br>
                                  <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                  <b> 電力消費量:</b> {safe_float(round(analytics_data['sum_power'], 2))} kW<br>
                                  <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_temp'], 2))}℃
                                </div>''',
                'color: #007BFF',
                safe_float(round(analytics_data['avg_temp'],2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                     <b> データ数: </b> {int(total)}<br>
                                      <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                      <b> 電力消費量:</b> {safe_float(round(analytics_data['sum_power'], 2))} kW<br>
                                      <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_temp'], 2))}℃
                                    </div>'''
            ])
            chartDataDateSuctionAvg.append([
                analytics_data['measurement_date'].strftime('%Y/%m/%d'),
                safe_float(round(analytics_data['avg_power'], 2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                             <b> データ数: </b> {int(total)}<br>
                                              <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                              <b> 電力消費量:</b> {safe_float(round(analytics_data['avg_power'], 2))} kW<br>
                                              <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_suction_temp'], 2))}℃
                                            </div>''',
                'color: #007BFF',
                safe_float(round(analytics_data['avg_suction_temp'], 2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                 <b> データ数: </b> {int(total)}<br>
                                  <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                  <b> 電力消費量:</b> {safe_float(round(analytics_data['avg_power'], 2))} kW<br>
                                  <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_suction_temp'], 2))}℃
                                </div>'''])
            chartDataDateSuctionSum.append([
                analytics_data['measurement_date'].strftime('%Y/%m/%d'),
                safe_float(round(analytics_data['sum_power'], 2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                             <b> データ数: </b> {int(total)}<br>
                                              <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                              <b> 電力消費量:</b> {safe_float(round(analytics_data['sum_power'], 2))} kW<br>
                                              <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_suction_temp'], 2))}℃
                                            </div>''',
                'color: #007BFF;',
                safe_float(round(analytics_data['avg_suction_temp'], 2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                 <b> データ数: </b> {int(total)}<br>
                                  <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                  <b> 電力消費量:</b> {safe_float(round(analytics_data['sum_power'], 2))} kW<br>
                                  <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_suction_temp'], 2))}℃
                                </div>'''])
            chartDataDateDischargeAvg.append([
                analytics_data['measurement_date'].strftime('%Y/%m/%d'),
                safe_float(round(analytics_data['avg_power'], 2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                             <b> データ数: </b> {int(total)}<br>
                                              <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                              <b> 電力消費量:</b> {safe_float(round(analytics_data['avg_power'], 2))} kW<br>
                                              <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_discharge_temp'], 2))}℃
                                            </div>''',
                'color: #007BFF',
                safe_float(round(analytics_data['avg_discharge_temp'], 2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                 <b> データ数: </b> {int(total)}<br>
                                  <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                  <b> 電力消費量:</b> {safe_float(round(analytics_data['avg_power'], 2))} kW<br>
                                  <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_discharge_temp'], 2))}℃
                                </div>'''])
            chartDataDateDischargeSum.append([
                analytics_data['measurement_date'].strftime('%Y/%m/%d'),
                safe_float(round(analytics_data['sum_power'], 2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                             <b> データ数: </b> {int(total)}<br>
                                              <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                              <b> 電力消費量:</b> {safe_float(round(analytics_data['sum_power'], 2))} kW<br>
                                              <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_discharge_temp'], 2))}℃
                                            </div>''',
                'color: #007BFF;',
                safe_float(round(analytics_data['avg_discharge_temp'], 2)),
                f'''<div style="width: 150px; font-size: 14px;margin-left: 10px;">
                                 <b> データ数: </b> {int(total)}<br>
                                  <b> 期間:</b> {analytics_data['measurement_date'].strftime('%Y/%m/%d')} <br>
                                  <b> 電力消費量:</b> {safe_float(round(analytics_data['sum_power'], 2))} kW<br>
                                  <b> 外気温度:</b>  {safe_float(round(analytics_data['avg_discharge_temp'], 2))}℃
                                </div>'''])
    if result_count:
        analytics_by_temp = (
            analytics.annotate(temp_floor=Floor('outdoor_temp'))
            .values('temp_floor', 'is_installed', 'measurement_interval')
            .annotate(
                avg_power=Avg('power_consumption'),
                sum_power=Sum('power_consumption'),
                total_record=Count('measurement_id')
            )
            .annotate(
                expected_count=ExpressionWrapper(
                    F('total_record') * 60.0 / F('measurement_interval'),
                    output_field=FloatField()
                )
            )
            .filter(expected_count__gte=result_count)
            .order_by('temp_floor', 'is_installed')
        )
    else:
        analytics_by_temp = (analytics.annotate(temp_floor=Floor('outdoor_temp'))
                             .values('temp_floor', 'is_installed', 'measurement_interval')
                             .annotate(avg_power=Avg('power_consumption'), sum_power=Sum('power_consumption'),
                                       total_record=Count('measurement_id'))
                             .order_by('temp_floor', 'is_installed'))

    result = defaultdict(dict)
    for row in analytics_by_temp:
        temp = int(row['temp_floor'])
        key_power_avg = 'avg_power_installed' if row['is_installed'] == 1 else 'avg_power_not_installed'
        key_power_sum = 'sum_power_installed' if row['is_installed'] == 1 else 'sum_power_not_installed'
        key_count = 'count_installed' if row['is_installed'] == 1 else 'count_not_installed'

        result[temp]['temp_floor'] = temp
        result[temp][key_power_avg] = round(row['avg_power'], 1)
        result[temp][key_power_sum] = round(row['sum_power'], 1)
        result[temp][key_count] = round(row['total_record'],
                                        2) * 60 / measurement_interval  # bạn có thể làm tròn đến 0 nếu muốn

    for temp_row in result.values():
        temp_row.setdefault('avg_power_installed', 0)
        temp_row.setdefault('avg_power_not_installed', 0)
        temp_row.setdefault('sum_power_installed', 0)
        temp_row.setdefault('sum_power_not_installed', 0)
        temp_row.setdefault('count_installed', 0)
        temp_row.setdefault('count_not_installed', 0)
    chartTempAvg = []
    chartTempSum = []
    for temp_row in result.values():
        chartTempAvg.append([
            temp_row['temp_floor'],
            safe_float(temp_row['avg_power_not_installed']),
            f'''<div style="width: 200px; font-size: 14px;margin-left: 10px;">
                    <b> データ数: </b> {safe_float(temp_row['count_not_installed'])}<br>
                    <b> 期間:</b> {start_date.strftime('%Y/%m/%d')}-{end_date.strftime('%Y/%m/%d')} <br> 
                    <b> 電力消費量:</b> {safe_float(round(temp_row['avg_power_not_installed'], 2))} kW<br>
                </div>''',
            'color: #a4a6a8; ',
            safe_float(temp_row['avg_power_installed']),
            f'''<div style="width: 200px; font-size: 14px;margin-left: 10px;">
                    <b> データ数: </b> {safe_float(temp_row['count_installed'])}<br>
                    <b> 期間:</b> {start_date.strftime('%Y/%m/%d')}-{end_date.strftime('%Y/%m/%d')} <br> 
                    <b> 電力消費量:</b> {safe_float(round(temp_row['avg_power_installed'], 2))} kW<br>
                </div>''',
        ])
        chartTempSum.append([
            temp_row['temp_floor'],
            safe_float(temp_row['sum_power_not_installed']),
            f'''<div style="width: 200px; font-size: 14px;margin-left: 10px;">
                    <b> データ数: </b> {safe_float(temp_row['count_not_installed'])}<br>
                    <b> 期間:</b> {start_date.strftime('%Y/%m/%d')}-{end_date.strftime('%Y/%m/%d')} <br> 
                    <b> 電力消費量:</b> {safe_float(round(temp_row['sum_power_not_installed'], 2))} kW<br>
                </div>''',
            'color: #a4a6a8;  ',
            safe_float(temp_row['sum_power_installed']),
            f'''<div style="width: 200px; font-size: 14px;margin-left: 10px;">
                    <b> データ数: </b> {safe_float(temp_row['count_installed'])}<br>
                    <b> 期間:</b> {start_date.strftime('%Y/%m/%d')}-{end_date.strftime('%Y/%m/%d')} <br> 
                    <b> 電力消費量:</b> {safe_float(round(temp_row['sum_power_installed'], 2))} kW<br>
                </div>''',
        ])

    # data scatter
    analytics_scatter = (
            analytics.annotate(temp_floor=Floor('outdoor_temp'))
            .values('temp_floor', 'outdoor_temp', 'power_consumption', 'is_installed', 'measurement_interval')
            .order_by('outdoor_temp')
        )
    dataChartScatter = []

    for item in analytics_scatter:
        if item['is_installed'] == 0:
            dataChartScatter.append([
                int(item['temp_floor']),
                safe_float(round(item['power_consumption'], 2)),
                0
            ])
        else:
            dataChartScatter.append([
                int(item['temp_floor']),
                0,
                safe_float(round(item['power_consumption'], 2))
            ])
    reductionRate = (
        (averagePowerConsumptionNotInstall - averagePowerConsumptionAfterInstall) * 100 / averagePowerConsumptionNotInstall
        if averagePowerConsumptionNotInstall else 0
    )
    return render(request, 'analytics_dashboard.html',
                  {
                      'project_id': project_id,
                      'timestamp': int(now().timestamp()),
                      'averagePowerConsumptionNotInstall': averagePowerConsumptionNotInstall,
                      'resultCountNotInstall': resultCountNotInstall,
                      'averagePowerConsumptionAfterInstall': averagePowerConsumptionAfterInstall,
                      'resultCountInstall': resultCountInstall,
                      'loadfactor': loadfactor,
                      'reductionRate': reductionRate,
                      'min_outdoor_temperature': outdoor_temperature['min_temp'],
                      'max_outdoor_temperature': outdoor_temperature['max_temp'],
                      'max_power_consumption': power_consumption['max_power'],
                      'min_power_consumption': power_consumption['min_power'],
                      'start_date': start_date.strftime('%Y-%m-%d'),
                      'end_date': end_date.strftime('%Y-%m-%d'),
                      'result_count': result_count,
                      'outdoor_temperature_start': outdoor_temperature_start,
                      'outdoor_temperature_end': outdoor_temperature_end,
                      'power_consumption_start': power_consumption_start,
                      'power_consumption_end': power_consumption_end,
                      'chartDataDateAvg': chartDataDateAvg,
                      'chartDataDateSum': chartDataDateSum,
                      'chartDataDateSuctionAvg':chartDataDateSuctionAvg,
                      'chartDataDateSuctionSum':chartDataDateSuctionSum,
                      'chartDataDateDischargeAvg':chartDataDateDischargeAvg,
                      'chartDataDateDischargeSum':chartDataDateDischargeSum,
                      'chartTempAvg': chartTempAvg,
                      'chartTempSum': chartTempSum,
                      'min_time': min_time,
                      'max_time': max_time,
                      'start_date_raw': start_date_raw,
                      'end_date_raw': end_date_raw,
                      'dataChartScatter': dataChartScatter,
                      'rated_power' :rated_power,
                      'outdoor_model' : outdoor_model,
                      'system_name' :system_name ,
                      'company_name' :company_name,
                  }
                  )
def safe_float(val):
    if isinstance(val, Decimal):
        return float(val)
    if isinstance(val, list):
        return [safe_float(x) for x in val]
    if isinstance(val, dict):
        return {k: safe_float(v) for k, v in val.items()}
    return val

def analyticsExportCsv(request, project_id):
    analytics = (BasicAnalytics.objects.filter(system_id=project_id, deleted_at__isnull=True).values('system_id','measurement_id','measurement_time','outdoor_temp','power_consumption','is_anomaly', 'is_installed', 'measurement_interval','created_at', 'updated_at'))

    # get value default form search
    measurement_time = analytics.aggregate(
        min_time=Min('measurement_time'),
        max_time=Max('measurement_time')
    )
    min_time = localtime(measurement_time['min_time']).date()
    max_time = localtime(measurement_time['max_time']).date()

    start_date_raw = request.GET.get('start_date', '')
    end_date_raw = request.GET.get('end_date', '')
    if start_date_raw:
        start_date = datetime.strptime(start_date_raw, '%Y-%m-%d').date()
    else:
        start_date = min_time

    if end_date_raw:
        end_date = datetime.strptime(end_date_raw, '%Y-%m-%d').date()
    else:
        end_date = max_time

    outdoor_temperature_start = request.GET.get('outdoor_temperature_start', '')
    outdoor_temperature_end = request.GET.get('outdoor_temperature_end', '')
    power_consumption_start = request.GET.get('power_consumption_start', '')
    power_consumption_end = request.GET.get('power_consumption_end', '')

    # search query data url
    analytics = analytics.filter(
        measurement_time__gte=start_date,
        measurement_time__lt=end_date + timedelta(days=1)
    )
    if outdoor_temperature_start:
        analytics = analytics.filter(outdoor_temp__gte=outdoor_temperature_start)
    if outdoor_temperature_end:
        analytics = analytics.filter(outdoor_temp__lte=outdoor_temperature_end)
    if power_consumption_start:
        analytics = analytics.filter(power_consumption__gte=power_consumption_start)
    if power_consumption_end:
        analytics = analytics.filter(power_consumption__lte=power_consumption_end)

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="analytics.csv"'

    writer = csv.writer(response)
    writer.writerow(['system_id','measurement_id','measurement_time', 'measurement_interval', 'power_consumption', 'outdoor_temp','is_anomaly', 'is_installed','created_at', 'updated_at'])
    for row in analytics:
        writer.writerow([row['system_id'], row['measurement_id'], row['measurement_time'].strftime('%Y-%m-%d %H:%M:%S'), row['measurement_interval'], row['power_consumption'], row['outdoor_temp'],int(row['is_anomaly']), int(row['is_installed']),row['updated_at'].strftime('%Y-%m-%d %H:%M:%S'),row['created_at'].strftime('%Y-%m-%d %H:%M:%S')])

    return response
def analyticsAnalogSignal(request, project_id):
    projectCorporateMaster = (ProjectCorporateMaster.objects.
                              filter(system_id=project_id).values_list('rated_power','outdoor_model','system_name','company_id').first())
    rated_power = projectCorporateMaster[0]
    outdoor_model = projectCorporateMaster[1]
    system_name = projectCorporateMaster[2]
    corporation = CorporateMaster.objects.filter(company_id=projectCorporateMaster[3]).values_list(
        'company_name').first()
    company_name = corporation[0]
    maximum_temp = request.GET.get('maximum_temp', '')
    minimum_temp = request.GET.get('minimum_temp', '')
    avg_temp = request.GET.get('avg_temp', '')
    pearson_correlation_temp = request.GET.get('pearson_correlation_temp', '')
    pages= request.GET.get('pages', 1)
    analytics = (BasicAnalytics.objects.filter(system_id=project_id, deleted_at__isnull=True)
                 .values('measurement_time','outdoor_temp','power_consumption', 'is_installed')
                .order_by('is_installed','measurement_time' ))
    grouped_data_install = defaultdict(list)
    grouped_data_not_install = defaultdict(list)
    for item in analytics:
        measurementDate = item['measurement_time'].strftime('%Y/%m/%d')
        if item['is_installed']:
             grouped_data_install[measurementDate].append(item)
        else:
            grouped_data_not_install[measurementDate].append(item)
    for date, items in grouped_data_install.items():
        grouped_data_install[date] = getDataByDate(items)

    for date, items in grouped_data_not_install.items():
        grouped_data_not_install[date] = getDataByDate(items)

    dataAnalogSignal =[]
    for dateNotInstall, itemNotInstalls in grouped_data_not_install.items():
        for dateInstall,  itemInstalls  in grouped_data_install.items():
            checkTempMaxResult = checkTemp(maximum_temp, itemNotInstalls['outdoor_max'], itemInstalls['outdoor_max'])
            checkTempMinResult = checkTemp(minimum_temp, itemNotInstalls['outdoor_min'], itemInstalls['outdoor_min'])
            checkTempAvgResult = checkTemp(avg_temp, itemNotInstalls['outdoor_avg'], itemInstalls['outdoor_avg'])
            checkPearsonTemp = checkPearsonCorrelationCoefficientTemp(pearson_correlation_temp, itemNotInstalls['list_temps'], itemInstalls['list_temps'])
            if checkTempMaxResult['status'] and checkTempMinResult['status'] and checkTempAvgResult['status'] and checkPearsonTemp['status']:
                dataAnalogSignal.append({
                    'dateNotInstall': dateNotInstall,
                    'dateInstall': dateInstall,
                    'itemTempNotInstalls': itemNotInstalls['list_temps'],
                    'list_times': json.dumps(itemNotInstalls['list_times']),
                    'itemTempInstalls': itemInstalls['list_temps'],
                    'itemPowerNotInstalls': itemNotInstalls['list_powers'],
                    'itemPowerInstalls': itemInstalls['list_powers'],
                    'maximum_temperature_variation':checkTempMaxResult['value'],
                    'minimum_temperature_variation':checkTempMinResult['value'],
                    'avg_temperature_variation':checkTempAvgResult['value'],
                    'checkPearsonCorrelationCoefficientTemp':checkPearsonTemp['value'],
                    'minimum_temp_not_install':itemNotInstalls['outdoor_min'],
                    'minimum_temp_install':itemInstalls['outdoor_min'],
                    'maxmum_temp_not_install':itemNotInstalls['outdoor_max'],
                    'maximum_temp_install':itemInstalls['outdoor_max'],
                    'avg_temp_not_install':itemNotInstalls['outdoor_avg'],
                    'avg_temp_install':itemInstalls['outdoor_avg'],
                })
    dataAnalogSignal = sorted(
        dataAnalogSignal,
        key=lambda x: x['checkPearsonCorrelationCoefficientTemp'] or 0,
        reverse=True
    )
    paged_data = paginate_list(dataAnalogSignal, page=int(pages), page_size=5)

    # Create smart pagination with ellipsis
    current_page = paged_data['page']
    total_pages = paged_data['total_pages']

    # Calculate page range with ellipsis
    page_range = []
    if total_pages <= 7:
        # If total pages <= 7, show all pages
        page_range = list(range(1, total_pages + 1))
    else:
        # Always show first page
        page_range.append(1)

        # Calculate start and end of middle range
        start = max(2, current_page - 2)
        end = min(total_pages - 1, current_page + 2)

        # Add ellipsis before middle range if needed
        if start > 2:
            page_range.append('...')

        # Add middle range
        for i in range(start, end + 1):
            if i != 1 and i != total_pages:  # Don't duplicate first/last
                page_range.append(i)

        # Add ellipsis after middle range if needed
        if end < total_pages - 1:
            page_range.append('...')

        # Always show last page
        if total_pages > 1:
            page_range.append(total_pages)

    paged_data['page_range'] = page_range
    return render(request, 'analytics_analog_signal.html',
                  {
                      "results": paged_data,
                      "minimum_temp": minimum_temp,
                      "maximum_temp": maximum_temp,
                      "avg_temp": avg_temp,
                      "pearson_correlation_temp": pearson_correlation_temp,
                      'rated_power': rated_power,
                      'outdoor_model': outdoor_model,
                      'system_name': system_name,
                      'company_name':company_name,
                      'project_id':project_id,
                      'timestamp': int(now().timestamp())
                  }
                  )
def dataByGroupHour(dataByDate):

    grouped = defaultdict(list)
    for item in dataByDate:
        hour_key = item['measurement_time'].replace(minute=0, second=0, microsecond=0)
        grouped[hour_key].append(item)

    # list result
    hours = []
    avg_outdoor_temps = []
    avg_power_consumptions = []

    # Tính avg power , temp by hour
    for hour, items in sorted(grouped.items()):
        total_temp = sum(i['outdoor_temp'] for i in items)
        total_power = sum(i['power_consumption'] for i in items)
        count = len(items)

        avg_temp = (total_temp / count).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        avg_power = (total_power / count).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

        hours.append(hour.strftime('%H:%M'))
        avg_outdoor_temps.append(float(avg_temp))
        avg_power_consumptions.append(float(avg_power))
    return {
            'hours': hours,
            'avg_outdoor_temps': avg_outdoor_temps,
            'avg_power_consumptions': avg_power_consumptions
        }

def getDataByDate(dataByDate):
    temp_values = [float(r['outdoor_temp']) for r in dataByDate]
    listByTime = dataByGroupHour(dataByDate)
    return {'item': dataByDate,
            'count': len(dataByDate),
            'outdoor_avg': round(mean(temp_values), 1),
            'outdoor_min': round(min(temp_values), 1),
            'outdoor_max': round(max(temp_values), 1),
            'list_times': listByTime['hours'],
            'list_temps': listByTime['avg_outdoor_temps'],
            'list_powers': listByTime['avg_power_consumptions'],
            }
def checkTemp(temp_request, temp_not_install, temp_install):
    div_temp = float(temp_not_install) - float(temp_install)
    if temp_request:
        if div_temp < float(temp_request):
            return {'status': True, 'value': round(div_temp, 1)}
        else:
            return {'status': False}
    return {'status': True, 'value': round(div_temp, 1)}

def checkPearsonCorrelationCoefficientTemp( correlation_coefficient_request, list_temp_not_install, list_temp_install):
    min_len = min(len(list_temp_not_install), len(list_temp_install))
    if min_len < 2:
        return {'status':False}
    temps1 = list_temp_not_install[:min_len]
    temps2 = list_temp_install[:min_len]
    correlation, _ = scipy.stats.pearsonr(temps1, temps2)
    if correlation_coefficient_request:
        if correlation > float(correlation_coefficient_request):
            return  {'status':True, 'value': round(correlation, 1)}
        else:
            return {'status':False}
    return {'status': True, 'value': round(correlation, 1)}

def paginate_list(data, page=1, page_size=10):
    total_items = len(data)
    total_pages = (total_items + page_size - 1) // page_size
    start = (page - 1) * page_size
    end = start + page_size
    return {
        'page': page,
        'page_size': page_size,
        'total_pages': total_pages,
        'total_items': total_items,
        'data': data[start:end]
    }
def exportAnalyticsAnalogSignal(request, project_id):
    dateInstall = request.GET.get('date_install', '')
    dateNotInstall = request.GET.get('date_not_install', '')
    if not dateInstall or not dateNotInstall:
        return HttpResponse(status=400, content="date_install and date_not_install are required")
    dateInstall = datetime.strptime(dateInstall, "%Y/%m/%d").strftime("%Y-%m-%d")
    dateNotInstall = datetime.strptime(dateNotInstall, "%Y/%m/%d").strftime("%Y-%m-%d")
    analytics = (BasicAnalytics.objects.filter(system_id=project_id, deleted_at__isnull=True).filter(
        Q(measurement_time__date=dateNotInstall, is_installed=False) |
        Q(measurement_time__date=dateInstall, is_installed=True)
    ).values('system_id','measurement_id','measurement_time','measurement_interval','power_consumption','load_rate','outdoor_temp','suction_temp','discharge_temp','is_anomaly', 'is_installed','created_at', 'updated_at')
    .order_by('measurement_time' ))
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="analytics-analog-signal.csv"'

    writer = csv.writer(response)
    writer.writerow(
        ['system_id','measurement_id','measurement_time','measurement_interval','power_consumption','load_rate','outdoor_temp','suction_temp','discharge_temp','is_anomaly', 'is_installed', 'created_at', 'updated_at'])
    for row in analytics:
        writer.writerow(
            [ row['system_id'],
              row['measurement_id'],
              row['measurement_time'].strftime('%Y-%m-%d %H:%M:%S'),
              row['measurement_interval'],
              row['power_consumption'],
              row['load_rate'],
              row['outdoor_temp'],
              row['suction_temp'],
              row['discharge_temp'],
              int(row['is_anomaly']),
              int(row['is_installed']),
              row['created_at'].strftime('%Y-%m-%d %H:%M:%S'),
              row['updated_at'].strftime('%Y-%m-%d %H:%M:%S')])

    return response