import pytz
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.core.paginator import <PERSON><PERSON><PERSON>, EmptyPage, PageNotAnInteger
from django.db.models import Q
from .models import ProductItem, ProductItemMasters
from .forms import UploadCSVForm
import tempfile
from django.contrib import messages
import os
from rest_framework.views import APIView
from .serializers import SyncProductCodeSerializer
from rest_framework import status
from rest_framework.response import Response
from django.utils import timezone
from django.shortcuts import redirect
from django.contrib.auth import login
import base64
import json
import time
from django.http import HttpResponse, HttpResponseRedirect
from django.views.decorators.csrf import csrf_exempt
from Crypto.Cipher import AES

from .sso_user import DummyUser


@login_required
def import_product_items(request):
    if request.method == 'POST':
        form = UploadCSVForm(request.POST, request.FILES)
        if form.is_valid():
            # Save the uploaded file to a temporary location
            csv_file = request.FILES['csv_file']
            with tempfile.NamedTemporaryFile(delete=False, suffix='.csv') as temp_file:
                temp_file_path = temp_file.name
                for chunk in csv_file.chunks():
                    temp_file.write(chunk)

            # Process the CSV file
            try:
                success_count, error_count, error_messages = ProductItem.import_from_csv(temp_file_path)

                # Display results
                messages.success(
                    request,
                    f'Successfully imported {success_count} product items'
                )

                if error_count > 0:
                    messages.warning(
                        request,
                        f'Failed to import {error_count} product items'
                    )

                    for error in error_messages[:10]:  # Show first 10 errors
                        messages.error(request, error)

                    if len(error_messages) > 10:
                        messages.error(
                            request,
                            f'... and {len(error_messages) - 10} more errors'
                        )

            finally:
                # Clean up the temporary file
                os.unlink(temp_file_path)

            return redirect('import_product_items')
    else:
        form = UploadCSVForm()

    return render(request, 'import_product_items.html', {'form': form})
def import_product_item_masters(request):
    if request.method == 'POST':
        form = UploadCSVForm(request.POST, request.FILES)
        if form.is_valid():
            # Save the uploaded file to a temporary location
            csv_file = request.FILES['csv_file']
            with tempfile.NamedTemporaryFile(delete=False, suffix='.csv') as temp_file:
                temp_file_path = temp_file.name
                for chunk in csv_file.chunks():
                    temp_file.write(chunk)

            # Process the CSV file
            try:
                success_count, error_count, error_messages = ProductItemMasters.import_from_csv(temp_file_path)

                # Display results
                messages.success(
                    request,
                    f'Successfully imported {success_count} product item masters'
                )

                if error_count > 0:
                    messages.warning(
                        request,
                        f'Failed to import {error_count} product item masters'
                    )

                    for error in error_messages[:10]:  # Show first 10 errors
                        messages.error(request, error)

                    if len(error_messages) > 10:
                        messages.error(
                            request,
                            f'... and {len(error_messages) - 10} more errors'
                        )

            finally:
                # Clean up the temporary file
                os.unlink(temp_file_path)

            return redirect('import_product_item_masters')
    else:
        form = UploadCSVForm()

    return render(request, 'import_product_item_masters.html', {'form': form})


@login_required
def product_list(request):
    """
    View to display a paginated list of products with search functionality
    """
    # Get query parameters
    search_query = request.GET.get('search', '')
    is_check_sync = request.GET.get('is_check_sync', '')
    sort_by = request.GET.get('sort', 'product_code')
    page_size = int(request.GET.get('page_size', 20))  # Default 20 items per page

    products = ProductItem.objects
    # Apply search filter if provided
    if search_query:
        products = products.filter(
            Q(product_code__icontains=search_query) |
            Q(product_name_search__icontains=search_query) |
            Q(product_kana_search__icontains=search_query)
        )

    # Apply maker filter if provided
    if is_check_sync:
        products = products.filter(is_check_sync=is_check_sync)

    # Get list of all makers for the filter dropdown

    # Apply sorting
    if sort_by.startswith('-'):
        products = products.order_by(sort_by)
    else:
        products = products.order_by(sort_by)
    # Paginate the results
    page = request.GET.get('page', 1)
    paginator = Paginator(products, page_size)

    try:
        products_page = paginator.page(page)
    except PageNotAnInteger:
        products_page = paginator.page(1)
    except EmptyPage:
        products_page = paginator.page(paginator.num_pages)
    # Calculate page range for pagination display
    page_range = get_page_range(paginator, products_page.number)
    sizes = [20, 50, 100]
    tokyo_tz = pytz.timezone("Asia/Tokyo")
    for pro in products_page:
        if pro.check_sync_at is not None:
            pro.check_sync_time_jp = pro.check_sync_at.astimezone(tokyo_tz).strftime("%Y/%m/%d %H:%M")
        pro.created_at_jp = pro.created_at.astimezone(tokyo_tz).strftime("%Y/%m/%d %H:%M")
    context = {
        'products': products_page,
        'search_query': search_query,
        'is_check_sync': is_check_sync,
        'sort_by': sort_by,
        'page_obj': products_page,
        'page_range': page_range,
        'page_size': page_size,
        'total_count': paginator.count,
        'sizes': sizes,
    }

    return render(request, 'product_list.html', context)


def get_page_range(paginator, current_page, window=5):
    """
    Returns a range of page numbers to display in pagination
    with the current page in the center of a window of pages
    """
    total_pages = paginator.num_pages

    # If total pages is less than window, show all pages
    if total_pages <= window:
        return range(1, total_pages + 1)

    # Calculate the window of pages to show
    half_window = window // 2

    # If current page is close to the beginning
    if current_page <= half_window:
        return range(1, window + 1)

    # If current page is close to the end
    if current_page >= total_pages - half_window:
        return range(total_pages - window + 1, total_pages + 1)

    # Current page is in the middle
    return range(current_page - half_window, current_page + half_window + 1)


@login_required
def product_detail(request, product_id):
    """
    View to display product details
    """
    product = get_object_or_404(ProductItem, id=product_id)
    if product.sync_product_code :
        productItemMaster = get_object_or_404(ProductItemMasters, product_code=product.sync_product_code)
    else:
        productItemMaster = None
    # Get query parameters
    search_query = request.GET.get('search', '')
    maker_name = request.GET.get('maker_name', '')
    sort_by = request.GET.get('sort', 'product_code')
    page_size = int(request.GET.get('page_size', 20))  # Default 20 items per page
    products = ProductItemMasters.objects
    # Apply search filter if provided
    if search_query:
        products = products.filter(
            Q(individual_item_jan_code=search_query) |
            Q(product_name_search__icontains=search_query) |
            Q(product_kana_search__icontains=search_query)
        )

    # Apply maker filter if provided
    if maker_name:
        products = products.filter(manufacturer_name=maker_name)

    # Get list of all makers for the filter dropdown

    # Apply sorting
    if sort_by.startswith('-'):
        products = products.order_by(sort_by)
    else:
        products = products.order_by(sort_by)
    # Paginate the results
    page = request.GET.get('page', 1)
    paginator = Paginator(products, page_size)

    try:
        products_page = paginator.page(page)
    except PageNotAnInteger:
        products_page = paginator.page(1)
    except EmptyPage:
        products_page = paginator.page(paginator.num_pages)
    # Calculate page range for pagination display
    # Calculate page range for pagination display
    page_range = get_page_range(paginator, products_page.number)
    maker_list = ProductItemMasters.objects.values_list('manufacturer_name', flat=True).distinct()
    sizes = [20, 50, 100]
    context = {
        'products': products_page,
        'search_query': search_query,
        'maker_name': maker_name,
        'sort_by': sort_by,
        'page_obj': products_page,
        'page_range': page_range,
        'page_size': page_size,
        'total_count': paginator.count,
        'product': product,
        'maker_list': maker_list,
        'sizes': sizes,
        'productItemMaster': productItemMaster,
    }

    return render(request, 'product_detail.html', context)


class UpdateSyncProductCodeAPIView(APIView):
    def patch(self, request, id):
        product = get_object_or_404(ProductItem, id=id)
        data = request.data.copy()
        data['is_check_sync'] = '1'
        data['check_sync_at'] = timezone.now()
        serializer = SyncProductCodeSerializer(product, data=data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({'message': 'Sync product code updated successfully'})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
def sso_login(request):
    SECRET_KEY = b'Q9mxJrKRC-h1IlL4ST0grXYHddv_bHoJvWyFR95EasA='
    token = request.GET.get('sso_token')
    if not token:
        return HttpResponse("❌ Thiếu token", status=400)
    try:
        raw = base64.b64decode(token)
        iv = raw[:16]
        ciphertext = raw[16:]

        cipher = AES.new(SECRET_KEY, AES.MODE_CBC, iv)
        decrypted = cipher.decrypt(ciphertext)

        # Bỏ padding (PKCS7)
        pad_len = decrypted[-1]
        decrypted = decrypted[:-pad_len]

        user_data = json.loads(decrypted)
    except Exception as e:
        return HttpResponse(f"❌ Giải mã thất bại: {str(e)}", status=400)

        # Kiểm tra hết hạn
    if user_data.get("exp", 0) < time.time():
        return HttpResponse("❌ Token hết hạn", status=403)

    user_data = {
        "id": 123,
        "email": "<EMAIL>",
        "exp": time.time() + 300,
    }

    # Tạo user tạm thời
    fake_user = DummyUser(user_data['id'], user_data['email'])

    # Gắn backend giả
    request.session['_auth_user_backend'] = 'django.contrib.auth.backends.ModelBackend'
    request.session['_auth_user_id'] = user_data['id']
    request.user = SimpleLazyObject(lambda: fake_user)

    # Tùy chọn: thêm thông tin vào session
    request.session['sso_email'] = user_data['email']

    return HttpResponseRedirect("/")