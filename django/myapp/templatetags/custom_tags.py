# analytics/templatetags/custom_tags.py

from django import template
from urllib.parse import urlencode

register = template.Library()

@register.filter
def get_range(start, end):
    try:
        start = int(start)
        end = int(end)
    except (ValueError, TypeError):
        return []
    return range(start, end + 1)

@register.simple_tag
def pagination_url(request, page_num):
    """Build pagination URL with all current query parameters"""
    params = request.GET.copy()
    params['pages'] = page_num
    return '?' + urlencode(params)
