from django.contrib import admin

# Register your models here.
import requests
from django.forms.models import model_to_dict
from django.conf import settings
import os
def load_list_image_product(productCode):
    prefix = settings.AWS_S3_ENDPOINT_URL
    countImage = settings.COUNT_IMAGE
    list_images = [f"{prefix}{productCode}_{i}.jpg" for i in range(1, countImage)]

    valid_images = [img for img in list_images if check_url_image_product(img)]

    return valid_images

def check_url_image_product(url, timeout=5):
    try:
        response = requests.head(url, allow_redirects=True, timeout=timeout)
        if response.status_code == 200:
            return True
        # Một số server trả về 403/401 cho HEAD, thử GET nhẹ
        if response.status_code in (401, 403):
            response = requests.get(url, stream=True, timeout=timeout)
            return response.status_code == 200
        return False
    except requests.RequestException:
        return False
        