# Generated by Django 5.2.1 on 2025-05-28 03:03

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('myapp', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AirConditioningSystem',
            fields=[
                ('system_id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='系統ID')),
                ('system_name', models.CharField(max_length=100, verbose_name='系統名')),
                ('set_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='セット名')),
                ('set_model', models.CharField(blank=True, max_length=100, null=True, verbose_name='セット型式')),
                ('outdoor_model', models.CharField(max_length=100, verbose_name='室外機型番')),
                ('num_sets', models.IntegerField(default=1, verbose_name='セット台数')),
                ('num_outdoor_units', models.IntegerField(default=1, verbose_name='室外機台数')),
                ('num_indoor_units', models.IntegerField(default=1, verbose_name='室内機台数')),
                ('room_type', models.CharField(max_length=50, verbose_name='部屋タイプ')),
                ('operation_mode', models.CharField(max_length=50, verbose_name='運転手別')),
                ('monthly_operating_days', models.IntegerField(verbose_name='月間稼働日数')),
                ('daily_operating_hours', models.DecimalField(decimal_places=1, max_digits=4, verbose_name='一日稼働時間')),
                ('temp_summer', models.DecimalField(decimal_places=1, max_digits=4, verbose_name='設定温度夏')),
                ('temp_winter', models.DecimalField(decimal_places=1, max_digits=4, verbose_name='設定温度冬')),
                ('manufacturer', models.CharField(max_length=100, verbose_name='メーカー')),
                ('compressor_output', models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True, verbose_name='圧縮機出力')),
                ('rated_power', models.DecimalField(decimal_places=2, max_digits=6, verbose_name='定格消費電力')),
                ('refrigerant_type', models.CharField(max_length=50, verbose_name='冷媒種')),
                ('piping_diameter', models.CharField(max_length=50, verbose_name='配管経')),
                ('installation_date', models.DateField(verbose_name='導入年月')),
                ('piping_length', models.DecimalField(decimal_places=1, max_digits=6, verbose_name='配管長')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='登録日時')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新日時')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='削除日時')),
            ],
            options={
                'verbose_name_plural': 'AirConditioningSystem',
                'db_table': 'air_conditioning_systems',
            },
        ),
        migrations.CreateModel(
            name='BasicAnalytics',
            fields=[
                ('measurement_id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='計測ID')),
                ('measurement_time', models.DateTimeField(verbose_name='計測日時')),
                ('measurement_interval', models.IntegerField(verbose_name='測定間隔（分）')),
                ('power_consumption', models.DecimalField(decimal_places=2, max_digits=6, verbose_name='消費電力(kW)')),
                ('load_rate', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='負荷率(%)')),
                ('outdoor_temp', models.DecimalField(decimal_places=1, max_digits=4, verbose_name='外気温(℃)')),
                ('suction_temp', models.DecimalField(decimal_places=1, max_digits=4, verbose_name='吸込温度(℃)')),
                ('discharge_temp', models.DecimalField(decimal_places=1, max_digits=4, verbose_name='吹出温度(℃)')),
                ('is_anomaly', models.BooleanField(default=False, verbose_name='異常フラグ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='登録日時')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新日時')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='削除日時')),
                ('system', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='myapp.airconditioningsystem', verbose_name='系統ID')),
            ],
            options={
                'verbose_name_plural': 'ProductItemMapping',
                'db_table': 'basic_analytics',
            },
        ),
        migrations.CreateModel(
            name='CorporateMaster',
            fields=[
                ('company_id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='法人ID')),
                ('company_name', models.CharField(max_length=255, verbose_name='法人名')),
                ('industry_type', models.CharField(max_length=100, verbose_name='業務')),
                ('annual_revenue', models.CharField(max_length=50, verbose_name='企業規模')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='登録日時')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新日時')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='削除日時')),
            ],
            options={
                'verbose_name_plural': 'CorporateMaster',
                'db_table': 'corporate_master',
            },
        ),
        migrations.DeleteModel(
            name='Category',
        ),
        migrations.DeleteModel(
            name='ProductItem',
        ),
        migrations.DeleteModel(
            name='ProductItemMapping',
        ),
        migrations.DeleteModel(
            name='ProductItemMaster',
        ),
        migrations.AddField(
            model_name='airconditioningsystem',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='myapp.corporatemaster', verbose_name='法人ID'),
        ),
    ]
