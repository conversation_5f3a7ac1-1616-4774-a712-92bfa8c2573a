# Generated by Django 5.2.1 on 2025-06-04 03:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('myapp', '0003_rename_airconditioningsystem_projectcorporatemaster_and_more'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='corporatemaster',
            index=models.Index(fields=['company_name'], name='company_name_idx'),
        ),
        migrations.AddIndex(
            model_name='corporatemaster',
            index=models.Index(fields=['industry_type'], name='industry_type_idx'),
        ),
        migrations.AddIndex(
            model_name='corporatemaster',
            index=models.Index(fields=['annual_revenue'], name='annual_revenue_idx'),
        ),
        migrations.AddIndex(
            model_name='corporatemaster',
            index=models.Index(fields=['created_at'], name='created_at_idx'),
        ),
        migrations.AddIndex(
            model_name='projectcorporatemaster',
            index=models.Index(fields=['company'], name='pcm_company_idx'),
        ),
        migrations.AddIndex(
            model_name='projectcorporatemaster',
            index=models.Index(fields=['system_name'], name='pcm_system_name_idx'),
        ),
        migrations.AddIndex(
            model_name='projectcorporatemaster',
            index=models.Index(fields=['manufacturer'], name='pcm_manufacturer_idx'),
        ),
        migrations.AddIndex(
            model_name='projectcorporatemaster',
            index=models.Index(fields=['installation_date'], name='pcm_install_date_idx'),
        ),
        migrations.AddIndex(
            model_name='projectcorporatemaster',
            index=models.Index(fields=['created_at'], name='pcm_created_at_idx'),
        ),
        migrations.AddIndex(
            model_name='projectcorporatemaster',
            index=models.Index(fields=['deleted_at'], name='pcm_deleted_at_idx'),
        ),
    ]
