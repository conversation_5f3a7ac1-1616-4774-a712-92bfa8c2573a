from rest_framework import serializers
from .models import CorporateMaster
from .models import ProjectCorporateMaster
from .models import BasicAnalytics

class CorporateMasterSerializer(serializers.ModelSerializer):
    class Meta:
        model = CorporateMaster
        fields = ['company_id', 'company_name', 'industry_type', 'annual_revenue']

class CSVUploadSerializer(serializers.Serializer):
    csv_file = serializers.FileField()


class ProjectCSVUploadSerializer(serializers.Serializer):
    csv_file = serializers.FileField()


class ProjectCorporateMasterSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProjectCorporateMaster
        fields = '__all__'


class AnalyticsCSVUploadSerializer(serializers.Serializer):
    csv_file = serializers.FileField()


class BasicAnalyticsSerializer(serializers.ModelSerializer):
    class Meta:
        model = BasicAnalytics
        fields = '__all__'