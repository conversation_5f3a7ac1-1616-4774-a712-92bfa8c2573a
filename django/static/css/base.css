/*
* base.css
* File include item base only specific css only
******************************************************************************/

.menu .app-brand.base {
  height: 64px;
  margin-top: 12px;
}

.app-brand-logo.base svg {
  width: 22px;
  height: 38px;
}

.app-brand-text.base {
  font-size: 1.75rem;
  letter-spacing: -0.5px;
}

/* ! For .layout-navbar-fixed added fix padding top tpo .layout-page */
/* Detached navbar */
.layout-navbar-fixed .layout-wrapper:not(.layout-horizontal):not(.layout-without-menu) .layout-page {
  padding-top: 76px !important;
}
/* Default navbar */
.layout-navbar-fixed .layout-wrapper:not(.layout-without-menu) .layout-page {
  padding-top: 64px !important;
}

/* Navbar page z-index issue solution */
.content-wrapper .navbar {
  z-index: auto;
}

/*
* Content
******************************************************************************/

.base-blocks > * {
  display: block !important;
}

.base-inline-spacing > * {
  margin: 1rem 0.375rem 0 0 !important;
}

/* ? .base-vertical-spacing class is used to have vertical margins between elements. To remove margin-top from the first-child, use .base-only-element class with .base-vertical-spacing class. For example, we have used this class in forms-input-groups.html file. */
.base-vertical-spacing > * {
  margin-top: 1rem !important;
  margin-bottom: 0 !important;
}
.base-vertical-spacing.base-only-element > :first-child {
  margin-top: 0 !important;
}

.base-vertical-spacing-lg > * {
  margin-top: 1.875rem !important;
  margin-bottom: 0 !important;
}
.base-vertical-spacing-lg.base-only-element > :first-child {
  margin-top: 0 !important;
}

.base-vertical-spacing-xl > * {
  margin-top: 5rem !important;
  margin-bottom: 0 !important;
}
.base-vertical-spacing-xl.base-only-element > :first-child {
  margin-top: 0 !important;
}

.rtl-only {
  display: none !important;
  text-align: left !important;
  direction: ltr !important;
}

[dir='rtl'] .rtl-only {
  display: block !important;
}

/*
* Layout base
******************************************************************************/

.layout-base-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: 1rem;
}
.layout-base-placeholder img {
  width: 900px;
}
.layout-base-info {
  text-align: center;
  margin-top: 1rem;
}
/* Điều chỉnh chiều cao cho Select2 */
.select2-container .select2-selection--single {
    height: 38px !important; /* cùng chiều cao với input của Bootstrap 5 */
    padding: 6px 12px;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.375rem; /* bo góc như Bootstrap 5 */
}

/* Điều chỉnh dòng chữ bên trong */
.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 24px;  /* Đảm bảo chữ nằm giữa */
}

/* Mũi tên dropdown */
.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 100%;
}
.card-min-400 {
    min-height: 400px;
  }
.ui-slider-range.ui-corner-all.ui-widget-header {
  background: #007bff; /* Màu xanh Bootstrap */
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 37px !important;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 24px !important;
}

#chart_div {
    width: 100%;
    height: 60vh; /* responsive chiều cao theo màn hình */
}