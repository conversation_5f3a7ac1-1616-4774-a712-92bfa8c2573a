services:
  nginx:
    build: ./nginx
    image: nginx
    container_name: "nginx_bi_tool"
    ports:
      - 80:80
      - 443:443
    volumes:
      - ./nginx/certs:/etc/letsencrypt
      - ./nginx/certbot-www:/var/www/certbot
    depends_on:
      - django_bi_tool
    restart: unless-stopped

  certbot:
    image: certbot/certbot
    container_name: "certbot_bi_tool"
    volumes:
      - ./nginx/certs:/etc/letsencrypt
      - ./nginx/certbot-www:/var/www/certbot
    command: certonly --webroot --webroot-path=/var/www/certbot --email <EMAIL> --agree-tos --no-eff-email -d dde.co.jp -d www.dde.co.jp
    depends_on:
      - nginx
  django_bi_tool:
    image: django_app
    build:
      context: ./django
    container_name: "django_bi_tool"
    volumes:
      - ./django:/django
      - ./django/static:/django/static
    ports:
      - 8000:8000
    command: sh -c "python manage.py makemigrations &&
           python manage.py migrate &&
           gunicorn core.wsgi --bind 0.0.0.0:8000"
    env_file:
      - "./django/.env"
    restart: on-failure
    depends_on:
      - db
  db:
    image: mysql
    container_name: "db_bi_tool"
    ports:
      - 3306:3306
    environment:
      - MYSQL_ROOT_PASSWORD=bi_password
      - MYSQL_USER=bi_tool_user
      - MYSQL_PASSWORD=bi_password
      - MYSQL_DATABASE=bi_tool_db
    volumes:
      - ./data/mysql/db:/var/lib/mysql
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "localhost" ]
      interval: 10s
      timeout: 5s
      retries: 5