
services:
  ec-cube:
    build:
      context: .
    environment:
      VIRTUAL_HOST: ${CATALOG_URL}
    volumes:
      - ".:/var/www/html:cached"
    ports:
      - "${HTTP_PORT}:80"
      - "${HTTPS_PORT}:443"
    stdin_open: true
    tty: true
    depends_on:
      - db
      - mail
    links:
      - db
      - mail
    networks:
      - my_network

  db:
    image: mysql:${MYSQL_VERSION}
    environment:
      - MYSQL_DATABASE=${MYSQL_DATABASE}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - TZ=${MYSQL_TIMEZONE}
    volumes:
      - mysql-database:/var/lib/mysql
      - ./dockerbuild/mysql/conf.d:/etc/mysql/conf.d
    ports:
      - "${MYSQL_PORT}:3306"
    networks:
      - my_network


  mail:
    image: schickling/mailcatcher
    ports:
      - "1080:1080"
      - "1025:1025"
    networks:
      - my_network

volumes:
  mysql-database:
    driver: local
  mailcatcher-data:
    driver: local

networks:
  my_network:
    name: ${SMART_ORDER_POJECT_NAME}_default
    external: false
