version: '3.8'

services:
  nginx:
    build: ./nginx
    image: nginx_bi_tool
    container_name: "nginx_bi_tool"
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/certs:/etc/letsencrypt:ro
      - ./nginx/certbot-www:/var/www/certbot:ro
      - ./django/static:/django/static:ro
      - ./django/media:/django/media:ro
    depends_on:
      - django_bi_tool
    restart: unless-stopped
    networks:
      - bi_tool_network

  certbot:
    image: certbot/certbot:latest
    container_name: "certbot_bi_tool"
    volumes:
      - ./nginx/certs:/etc/letsencrypt
      - ./nginx/certbot-www:/var/www/certbot
    # Command sẽ được override bởi script init-letsencrypt.sh
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"
    networks:
      - bi_tool_network
  django_bi_tool:
    image: django_app
    build:
      context: ./django
    container_name: "django_bi_tool"
    volumes:
      - ./django:/django
      - ./django/static:/django/static
      - ./django/media:/django/media
    ports:
      - "8000:8000"
    command: sh -c "python manage.py collectstatic --noinput &&
           python manage.py makemigrations &&
           python manage.py migrate &&
           gunicorn core.wsgi --bind 0.0.0.0:8000 --workers 3 --timeout 120"
    env_file:
      - "./django/.env"
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
    networks:
      - bi_tool_network

  db:
    image: mysql
    container_name: "db_bi_tool"
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=bi_password
      - MYSQL_USER=bi_tool_user
      - MYSQL_PASSWORD=bi_password
      - MYSQL_DATABASE=bi_tool_db
    volumes:
      - ./data/mysql/db:/var/lib/mysql
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - bi_tool_network

networks:
  bi_tool_network:
    driver: bridge