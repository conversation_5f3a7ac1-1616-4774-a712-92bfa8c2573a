# スマ発/カタログ開発環境 設定手順

# １．概要

スマート発注の開発環境を作成するDocker環境です。

- スマ発とカタログの連携も行うことができます。
- スマ発のみの開発にも使用できます。

# ２．環境について

- Windowsで開発する場合を記載しています。
- 実行環境
    - OS: Windows10
    - ターミナル: power shell
- サーバー環境
    - Apache 2.4.x
    - PHP 7.3.x
    - Mysql 8.0.x
        - ※CakePHP3.7がMysql 8の暗号化方式（caching_sha2_password）に対応していないため、5.7以前のもの（mysql_native_password）に変更する必要がある
- フレームワーク
    - CakePHP 3.7
        - laravel-mix
            - sass, javascriptファイルのビルド、圧縮のために追加
    - EC-CUBE 4.0.1
- ツールなど
    - Docker Desktop for Windows 
        - WSL2版は未調査です 
        - WSL2版だと動作が速くなると思います（特にカタログ）
    - Git for Windows
        - PowerShellでGitを使うときに便利なプラグイン[Posh-Git（こちらを参照）](https://git-scm.com/book/ja/v2/Appendix-A%3A-%E3%81%9D%E3%81%AE%E4%BB%96%E3%81%AE%E7%92%B0%E5%A2%83%E3%81%A7%E3%81%AEGit-Powershell%E3%81%A7Git%E3%82%92%E4%BD%BF%E3%81%86)
    - エディタ/IDE
        - VSCode
    - DB確認ツール
        - A5:SQLなど




# ３．補足

記号の説明

- コマンド説明の先頭記号について
    - //：コメント
    - $：dockerコンテナ内で実行
    - \>：PowerShellから実行

ディレクトリ構造について

- Webサーバ内
    - web root: /var/www/html
    - smart_order: /var/www/html/smart_order/cake
    - catalog: /var/www/html/catalog

閲覧用URLについて

- スマート発注
    - http://localhost/001/login
- スマート発注管理画面
    - http://localhost/001/admin/login
- カタログ管理画面
    - http://localhost/catalog/admin

# ４．必要ファイルの設置

## ディレクトリ構造

スマ発、カタログのディレクトリと同じ階層においてください。    
ディレクトリ名は.envで設定できるため、任意の名称でも構いません。    
※任意の名称に変更した場合は、下記説明を置き換えてください。

```
<任意のフォルダ>
－ smart_order_docker # このプロジェクト
        － user_dir # 画像や取り込み用csvファイルを設置するディレクトリ
        － ...
－ smart_order # スマート発注プロジェクト
        － cake
        － docker # 使用しません
        － ...
－ smart_order_catalog # カタログプロジェクト
        － app
        － dockerbuild # 使用しません
        － ...
```

## 各プロジェクトのダウンロード

BacklogのGitリポジトリから各プロジェクトをダウンロードします。

```bash
> cd <任意のフォルダ>
> git clone https://bmc-it.backlog.jp/git/SMHT/smart_order_docker.git smart_order_docker
> git clone https://bmc-it.backlog.jp/git/SMHT/smart_order.git smart_order
> git clone https://bmc-it.backlog.jp/git/SMHT/smart_order_catalog.git smart_order_catalog
```

## 各プロジェクトの.envの修正

### smart_order_docker（dockerプロジェクト）の修正

「.env.sample」からコピーして「.env」を作成する。

.envファイルを修正し、スマ発、カタログのディレクトリ名を設定する    
（gitからcloneする場合はデフォルトで構いません。）

```dotenv
// スマ発
DIR_NAME_SMART=smart_order

// カタログ
DIR_NAME_CATALOG=smart_order_catalog
```

### smart_order（スマ発プロジェクト）の修正

「smart_order/cake/config/.env.develop」を「smart_order/cake/config/.env」にコピーする    
DBとメールの設定が下記となっていることを確認する

```dotenv
# DB設定
export DB_HOST="db"    # DBコンテナ名を指定
export DB_SCHEMA="smart_order"
export DB_USERNAME="testuser"
export DB_PASSWORD="passwd"

# メール設定
export EMAIL_HOST="mail"
export EMAIL_PORT="1025"
export EMAIL_ADDRESS="<EMAIL>"
export EMAIL_PASSWORD=""
```

### smart_order_catalog（カタログ）の修正

「smart_order_catalog/.env.develop」を「smart_order_catalog/.env」にコピーする    
DBとメールの設定が下記となっていることを確認する

```dotenv
DATABASE_URL=mysql://testuser:passwd@db:3306/smart_order_catalog
DATABASE_SERVER_VERSION=8.0

MAILER_URL=sendmail://mail:1025
```

# ５．開発環境立ち上げ


## 補足：Dockerの基本操作（docker-composeを利用した操作）

docker-composeコマンドは「Docker Desktop for Windows」のインストール時に設定されます。    
docker-composeコマンドは```docker-compose.yml```ファイルのあるフォルダで実行してください。

### 1. 起動方法

ビルド（初回のみ）→ 起動の流れとなります。    

ビルド（※初回のみ実行）
```
> docker-compose build
```

起動
```
> docker-compose up -d
```


### 2. 停止方法
起動したコンテナと同じフォルダで実行してください
```
> docker-compose down
```

※注意    
起動したままの状態では他のプロジェクトのdockerは起動できません。    
（ポートが被る場合）    
作業が終わったら必ず停止するようにしましょう。

### 3. サーバの中にアクセスする方法

Webサーバへログイン

```
> docker-compose exec web /bin/bash
```

### 4. DBへの接続方法

A5:SQLでの接続方法    
※デフォルト状態

- ホスト: localhost
- Port: 33306
- User: testuser
- password: passwd
- スキーマ: smart_order, smart_order_catalog

# ６．初期セットアップ

CakePHPやEC-CUBEのセットアップを行います。

## 1. Docker環境の立ち上げ

ビルド（※初回のみ実行）
```
> docker-compose build
```

起動
```
> docker-compose up -d
```

サーバーイメージのダウンロードや必要なパッケージのインストールで時間がかかります。    
気長に待ちましょう。

### ※備考：セットアップ完了確認方法    

初回起動時は、環境が立ち上がった後もDBが出来ていないことがあります。    
DB設定に時間がかかるためです。（5分くらい？） 

DB設定の完了は、DBに接続することで確認できます。

確認方法
```
// DBコンテナに接続
> docker-compose exec db /bin/bash
```

セットアップ中の場合

```
// DBに接続
$ mysql -utestuser -ppasswd

ERROR 2002 (HY000): Can't connect to local MySQL server through socket '/var/run/mysqld/mysqld.sock' (2)
// ↑ エラーメッセージが表示されます
```

セットアップ完了後

```
$ mysql -utestuser -ppasswd

...

Type 'help;' or '\h' for help. Type '\c' to clear the current input statement.

mysql>

// ↑ Mysqlのプロンプトが表示されます

// DB:smart_order, smart_order_catalogが作成されていることが確認できます。
mysql> show databases;
+---------------------+
| Database                        |
+---------------------+
| information_schema    |
| smart_order                 |
| smart_order_catalog |
+---------------------+
3 <USER> <GROUP> set (0.03 sec)

mysql>
```

## 2. スマート発注プロジェクトの設定

Webサーバに接続して操作します。    
Docker開発環境が起動していることと、.envファイルが作成されていることが前提となります。    
<smart_order_docker>フォルダ内で操作してください。

```
// Webサーバにログイン
> docker-comose exec web /bin/bash
```

下記はWebサーバ内で操作

```
// CakePHP内に移動
$ cd /var/www/html/smart_order/cake

// 関連するライブラリをインストール
$ composer install
$ yarn

// CSS、javascriptのコンパイル
$ yarn prod

// DBの初期化（空データのテーブルを作成する）
$ php bin/cake.php migrations migrate

$ exit
```

基本データの登録

```
// DBコンテナにログイン
> docker-compose exec db /bin/bash
```


```
// マスタデータの登録
$ mysql -utestuser -ppasswd smart_order < /root/tmp/01_smart_insert_m_codes.sql
$ mysql -utestuser -ppasswd smart_order < /root/tmp/02_smart_insart_m_plans.sql 
$ mysql -utestuser -ppasswd smart_order < /root/tmp/03_smart_insert_m_companies.sql 
$ mysql -utestuser -ppasswd smart_order < /root/tmp/04_smart_insert_m_admins.sql 
```

ここまでの操作で管理画面へのログインが可能となります。    
管理画面から「企業情報」「取引先情報」「オーダーリスト」「ユーザ」等を更新してください。


## 3. カタログプロジェクトの設定

Webサーバに接続して操作します。    
Docker開発環境が起動していることと、.envファイルが作成されていることが前提となります。    
<smart_order_docker>に移動して操作してください。

アクセス権の設定を行います。

Webサーバにログイン

```
> docker-comose exec web /bin/bash
```

下記はWebサーバ内で操作

```
// catalog内に移動
$ cd /var/www/html/catalog

// webサーバからアクセス可能とする
$ chown -R www-data:www-data *

// ファイルパーミッションを変更(ファイル、ディレクトリを一括で変更)
$ find . -type d | xargs chmod 705
$ find . -type f | xargs chmod 604
$ chmod 707 eccube_root/ app/ app/Plugin/ app/PluginData/ app/proxy/ app/template/ html/ var/ vendor/
$ chmod 606 composer.json composer.lock
$ chmod 705 bin/console

// PHPモジュールの初期設定
$ composer install --no-scripts --no-autoloader --no-dev
```

設定ファイルの作成    
※```bin/console eccube:install```コマンドでエラーが出ると思いますが、無視してください。    
次の処理で対応します。

```
$ bin/console eccube:install
    // 設定は基本的にEnterキーを押していけばOK

    Database Url [mysql://testuser:passwd@db:3306/smart_order_catalog]:
    > 

    Mailer Url [sendmail://mail:1025]:
    > 

    Auth Magic [xxxxxxxxxxxxxxxxx]:
    >

    Is it OK? (yes/no) [yes]:
    >
```

一度ログアウト

```
$ exit
```

DBに初期データを登録します

```
// DBサーバにログイン
> docker-compose exec db /bin/bash

// DBにテーブル作成＆初期データ登録
$ mysql -utestuser -ppasswd smart_order_catalog < /root/tmp/11_catalog_insert_testdata.sql
$ mysql -utestuser -ppasswd smart_order_catalog < /root/tmp/12_catalog_create_view_sma_product.sql
$ mysql -utestuser -ppasswd smart_order_catalog < /root/tmp/13_catalog_create_view_sma_d_orderlist.sql

// ログアウト
$ exit
```

Webサーバにログイン

```
> docker-comose exec web /bin/bash
```

PHP側の初期化

```
// catalog内に移動
$ cd /var/www/html/catalog

// PHPモジュールの初期設定
$ composer install --no-scripts --no-autoloader --no-dev
$ php bin/console cache:clear --no-warmup 
$ composer dumpautoload -o --apcu --no-dev
$ composer run-script auto-scripts
```

CSSをビルド

```
$ yarn
$ yarn build
```

## 4. カタログの管理者パスワードの更新

初期データ登録時は管理者パスワードが正しく設定されていません。    
ソースコードを変更しながら、初期設定を行います。

### 1. パスワードの暗号化方法をPLAINに変更

カタログは以下の「/app/config/eccube/packages/eccube.yaml」を編集します。

eccube.yamlの27行目あたりの「eccube_auth_type: HMAC」を「eccube_auth_type: PLAIN」に変更してください。

```
24|        eccube.theme: '%env(ECCUBE_TEMPLATE_CODE)%'
25|        eccube_theme_code: '%eccube.theme%'
26|        eccube_auth_magic: '%env(ECCUBE_AUTH_MAGIC)%'
27|        eccube_auth_type: HMAC     -> PLAINに変更
28|        eccube_password_hash_algos: SHA256
29|        eccube_theme_app_dir: '%kernel.project_dir%/app/template'
```

### 2. DBからログインID/パスワードを取得して管理画面ログイン

DBから「smart_order_catalog.dtb_member」を確認します。    
テーブル内のlogin_id, passwordをカタログ管理画面のログイン画面に入力し、ログインしてください。    
URL：http://localhost/catalog/admin

DBの初期設定が終わった段階では、login_idは「admin」、passwordは「02ef38...」となっています。

### 3.    パスワードの暗号化方法をHMACに戻す

「/app/config/eccube/packages/eccube.yaml」を編集します。

```
24|        eccube.theme: '%env(ECCUBE_TEMPLATE_CODE)%'
25|        eccube_theme_code: '%eccube.theme%'
26|        eccube_auth_magic: '%env(ECCUBE_AUTH_MAGIC)%'
27|        eccube_auth_type: PLAIN     -> HMACに戻す
28|        eccube_password_hash_algos: SHA256
29|        eccube_theme_app_dir: '%kernel.project_dir%/app/template'
```

ファイルを編集しただけでは、PLAINの状態がキャッシュされているかもしれないため、キャッシュクリアを行います。    
管理画面からはログインせず、ターミナルからキャッシュクリアを行ってください。

Webサーバにログイン

```
> docker-comose exec web /bin/bash
```

```
// catalog内に移動
$ cd /var/www/html/catalog

// キャッシュクリア
$ php bin/console cache:clear --no-warmup
```


### 4. 新規パスワードを登録する

「/src/Eccube/Form/Type/Admin/ChangePasswordType.php」から、52行目あたりの「new UserPassword()」をコメントアウトします。

```
45|        public function buildForm(FormBuilderInterface $builder, array $options)
46|        {
47|                $builder
48|                        ->add('current_password', PasswordType::class, [
49|                                'label' => 'changepassword.label.current_pass',
50|                                'constraints' => [
51|                                        new Assert\NotBlank(),
52|                                        // new UserPassword(),     <- コメントアウト
53|                                ],
54|                        ])
55|                        ->add('change_password', RepeatedType::class, [
56|                                'first_options' => [
```

管理画面の右上の「管理者 様」となっているところをクリックし、「パスワード変更」ボタンを押してください。

現在のパスワード欄にDBからコピーしたパスワード、
新しいパスワード欄に希望するパスワードを入れて登録します。


### 5. 設定を戻す

「/src/Eccube/Form/Type/Admin/ChangePasswordType.php」から、「4.」で行ったコメントアウトを外します。

### 6. 確認
ログアウトして、新しく登録したパスワードでログインしてください。

# ７．スマ発のデータ登録

スマート発注管理画面から、企業情報、取引先、ユーザの更新、オーダーリスト登録、営業担当者を行ってください。
（取引先はオーダーリストを登録することでも追加できます）

※カタログを利用する場合、オーダーリストと同じ営業担当者を登録しておく必要があります
（拠点IDと営業担当者が紐づくため）
# ８．カタログのデータ登録

カタログ管理画面より、カテゴリCSV登録を行ってください。

バッチ処理でカタログデータ（catalog, base_info）を登録してください。

# ９．バッチ処理について

バッチ処理の動作確認を行う場合はcsvファイル（zipで固めたもの）を、  
ローカルの「smart_order_docker\usr_dir」以下の該当フォルダに設置してください。

テストデータは「smart_order_docker\usr_dir\test_data」フォルダに保存してあります