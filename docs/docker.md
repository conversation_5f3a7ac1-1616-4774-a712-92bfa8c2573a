# Dockerの設定について

## 用語について
- docker
    - コンテナ型アプリケーション実行環境
        - 詳しく知りたい方はWebで検索
    - VMを動かすよりも負荷が少ない
        - が、WindowsやMacだとVM上のLinuxでdockerを動かしているため本家ほどではない
- コンテナ
    - dockerで動かすサービスの単位
    - linuxOSを元に、必要なサービスをインストールしたもの
- ホスト
    - dockerを実行するマシン（windowsPC）
- シェル
    - コマンドラインで操作する端末
    - Powershell、GitBashなど
    - Windows版を使う場合はPowershellを使うのが無難
        - 余計な不具合対応が減るため
- docker-compose
    - 複数のdockerコンテナを操作するツール
    - docker-compose.yml で設定する

## 設定など

1. Docker desktop for Windowsのインストール
    1. WindowsのHyper-Vをオンにする
        - Hyper-Vの設定が必要なため設定する  
        https://docs.microsoft.com/ja-jp/virtualization/hyper-v-on-windows/quick-start/enable-hyper-v  
        一番下の[設定]で。。。の項目が分かりやすい。
        - PCによっては、BIOSの設定が必要な場合も有り
            - CPU Configuration > Intel Virtualization Technology
    1. インストール
        - 公式サイトよりダウンロードして実行  
        https://docs.docker.com/docker-for-windows/install/
        - アカウント登録が必要なため、インストーラーを下記の共有ドライブに保存してあります
            - J:\Group\ITS\r.oda\files
        - 特に設定は必要にないので、OKを押していくだけで良い。
    1. 設定
        1. インストール後、dockerの設定画面を開く
        1. Generalタブの「Expose daemon...」のチェックをONにする
        1. Shared Drivesタブ内で、docker-compose.ymlを作成するドライブ（プロジェクトフォルダを作成するドライブ）を選択する  
            チェック後、Applyボタンを押すとパスワードを聞かれるので、ログインパスワードを入力
1. 環境に合わせて.envファイルを作成する
    - env.sampleをリネームして修正
    
    ```sh
    mv env.sample .env
    ```
    
### 実行について
1. dockerディレクトリ（docker-compose.ymlのある場所）に移動
1. dockerの起動

    ```
    $ docker-compose up -d
    ```
    
    - 初回はビルドが必要なため--buildオプションを付けて実行する
    
    ```
    $ docker-compose up --build -d
    ```
    
1. dockerコンテナの中に移動  
    例：phpコンテナの中に入って操作する場合
    ```
    $ docker-compose exec php /bin/bash
    ```
    - コンテナの中に入るとLinuxと同じように操作できます 
1. dockerの停止
    ```
    $ docker-compose down
    ```

### 設定時の注意点、Tips

1. .envの「COMPOSE_PROJECT_NAME」をホスト内で一意の文字列に設定する。
    - （他プロジェクトのコンテナを上書きしないため）
2. ユーザーIDをホスト側と合わせる（ホストがLinuxの場合）
    - docker側で操作するユーザとホストユーザのIDが異なると、docker側で作成したファイルをホストから操作できなくなるため
3. データ保存用ディレクトリ
    - docker再起動の際もデータを保存させるため
    - dockerの volumes で作成した場合、中身がわからないため
        - 最悪、ディレクトリごとコピペすればよい
4. 各ミドルウェアのバージョンを指定する
    - 環境を合わせる意味でも、バージョンの統一化は必要
    - マイナーアップデートは許容する？