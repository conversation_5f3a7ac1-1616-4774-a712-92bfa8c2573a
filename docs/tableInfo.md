# DB登録データの説明

## 標準のEC-CUBEから変更したテーブルについて

DBの構造は、ビューを追加した以外はテーブル構造を変えていません。

## １．データを変更したテーブル

### mtb_product_status

商品公開状態の項目名称を変更

商品の公開状態が「公開、非公開、廃止」となっているのを、「公開、デモ端末、非公開」に変更

### mtb_product_list_order_by

並び順の選択肢  
下記に設定

```
4,商品コード順,1,productlistorderby
5,登録順,0,productlistorderby
```

## ２．ビューの追加

下記ページをご確認ください。
[[【カタログ】02.スマ発連携用のビュー作成方法]]

## ３．トランザクションデータの初期化方法

カタログ用DBから、商品データや顧客データを削除する方法を示します。

※ 運用開始後は行わないでください。

データ数が多い場合 DELETE だと遅くなるため、truncateでテーブルを空にします。

DBサーバーからDBにログインして操作します。

```
## 外部キーを一時的に無効化

SET FOREIGN_KEY_CHECKS = 0;

## 受注データ削除（正常運用ではデータは存在しないはず）

TRUNCATE TABLE dtb_order_item;
TRUNCATE TABLE dtb_mail_history;
TRUNCATE TABLE dtb_shipping;
TRUNCATE TABLE dtb_order;
ALTER TABLE dtb_order AUTO_INCREMENT = 1;

## 商品データ削除

TRUNCATE TABLE dtb_cart_item;
TRUNCATE TABLE dtb_cart;
TRUNCATE TABLE dtb_customer_favorite_product;
TRUNCATE TABLE dtb_product_tag;
TRUNCATE TABLE dtb_product_stock;
TRUNCATE TABLE dtb_product_image;
TRUNCATE TABLE dtb_product_class;
TRUNCATE TABLE dtb_product_category;
TRUNCATE TABLE dtb_product;

TRUNCATE TABLE dtb_class_category;
TRUNCATE TABLE dtb_class_name;
TRUNCATE TABLE dtb_category;
TRUNCATE TABLE dtb_tag;

ALTER TABLE dtb_product AUTO_INCREMENT = 1;
ALTER TABLE dtb_class_category AUTO_INCREMENT = 1;
ALTER TABLE dtb_class_name AUTO_INCREMENT = 1;
ALTER TABLE dtb_category AUTO_INCREMENT = 1;
ALTER TABLE dtb_tag AUTO_INCREMENT = 1;

## 顧客（取引先）データ削除

TRUNCATE TABLE dtb_customer;
ALTER TABLE dtb_customer AUTO_INCREMENT = 1;

## 外部キーを有効化

SET FOREIGN_KEY_CHECKS = 1;
```

```
# スマ発側のテーブルを削除する
> use ...

# テーブル内のデータを全削除する

TRUNCATE TABLE c_product_info_by_base;
TRUNCATE TABLE c_product_list;
```



## 補足：外部キー制約を付けたまま商品の追加/削除を行う方法

- 商品追加順序
    1. dtb_product
    2. dtb_product_class
    3. dtb_product_stock
    4. dtb_product_category
- 商品削除時
    - product_id, product_class_idを取得
    1. 下記テーブルからデータを削除
        1. dtb_cart_item
        1. dtb_order_item
        1. dtb_product_stock
        1. dtb_tax_rule
        1. dtb_customer_favorite_product
    1. dtb_product_category
    1. dtb_product_image
    1. dtb_product_class
    1. dtb_product


