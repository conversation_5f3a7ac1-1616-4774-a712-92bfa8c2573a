# GITの使い方について

## インストール
  - WindowsではGitBashをインストールする
  - ※WindowsだとCRLF形式の改行コードが使用されるため、コミット時に改行コードをLFに変換する設定にする
    - GitBushインストール時に設定できるが、気にせずインストールした場合は、GitBash上で下記コマンドを実行
    
      ```
      $ git config --global core.autocrlf input
      ```
## コミットの粒度
- 基本1タスク単位
- pushする前ならamendオプションで前のコミットを修正出来ます
    - 頻繁にコミットしてもログは1つのみにすることが可能

## ブランチ作成について
- 複数人で同時に作業を行うと競合が発生したり、作業内容が見えづらくなるため、作業ごとにブランチを作成してください。
- 一通りの画面が完了するまでは、画面名をブランチ名とします
    - 例：Admin/OrderListControllerを作成（管理画面：発注情報管理）
    
        ```
        $ git checkout -b feature/admin_order_list
        ```
- 画面開発が一段落（顧客確認/修正フェーズ）後は課題番号をブランチ名とします

        ```
        $ git checkout -b feature/SMHT-12
        ```
