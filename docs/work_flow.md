# 開発作業の進め方

## 目次
1. 環境構築
1. 作業ブランチ切り替え
1. 画面の開発
1. DBの操作

## 1. 環境構築
Readme.mdの「環境構築」を参照してください
1. Backlogから最新のソース（developブランチ）をダウンロード
1. dockerの初期設定 & 起動
1. cakephp、nodeの初期設定

## 2. Gitを使った作業の流れ
※ リポジトリからcloneして初期設定済みの前提で説明します

### 2-1. 作業ブランチの切り替え（チェックアウト）
- 作業を行う前に、今のリポジトリから作業用リポジトリを作成します

```
$ git checkout -b feature/branche_name
```
- -bオプションはチェックアウト時にブランチの作成もしてくれます。  
すでに作成済みブランチに切り替える場合は、"-b"オプションは外してください

### 2-2. ローカルの更新をコミット
- ソースコードの変更分をローカルリポジトリに反映  
※コミットだけではサーバーのリポジトリは更新されません

```php
$ git commit -m "コメント"
```
- 直前のコミットを上書きする場合は"--amend"オプションを付けて実行してください  
    - コメントを間違えた
    - 修正漏れがあったなど

### 2-3. 作業中にdevelopリポジトリが更新されている場合
サーバーにアップする前に、最新のソースコードに対してテストする必要があるため、サーバーの最新情報を反映させます

- git pullコマンドを使用して更新してください

```
$ git pull origin develop
```

※1 競合が発生したら修正してください。
※2 成功したらコミットしてください。


- Gitツリーの枝をシンプルにしたい場合。

"--rebase"オプションを使うことでツリーが整理されます。
ただし、ブランチ作成後にマスタブランチの変更が多かった場合、競合の解消が面倒です。

```
$ git pull --rebase origin develop
```

※ 通常は、pullのみ利用してください。

### 2-4. ブランチの更新分をサーバーにPush

- git pushコマンドでサーバーに反映させる

```
$ git push
```

### 2-5. プルリクエストを送る
プルリクエスト：ブランチ更新分をdevelopブランチにマージさせるためのレビュー依頼  
プルリクエストはBacklog上で行ってください  
参考： https://backlog.com/ja/git-tutorial/pull-request/pull-request1_1.html

### その他
- 現在のブランチを確認するコマンド

```
$ git branch
```


## 3. 画面の開発

### 3-1. 画面を追加する
- テーブル名に沿ったCRUD機能を作成するにはbakeコマンドでスケルトンを作成できます

    ```
    $ bin/cake.ps1 bake all MUsers
    ```
- Controller、Viewはbakeコマンドを使わずに作成しても大丈夫です（他の画面からコピペ）
- Modelは、DBからカラムなどをリバース生成してくれるため、bakeコマンドで作成したほうが楽です
    - DBのテーブル名を元に作成します
    - bakeコマンドを使うことでDBのカラム情報を元に作成できます

    ```
    $ bin/cake.ps1 bake model MUsers
    ```


### 3-2. サブディレクトリ配下のコントローラ/画面を作成する方法
- 例として、「https://domain/admin/login」の画面を追加する方法を説明します
1. config/routes.phpに下記を追加する  
URLとコントローラの対応付けを行います（リポジトリ上ではすでに作成済み）

    ```php
    // 管理者画面(admin)
    Router::prefix('admin', function (RouteBuilder $routes) {
        // 標準のフォールバックルートを接続
        $routes->fallbacks(DashedRoute::class);
    }); 
    ```
1. コントローラ、Viewの作成
    - パスの修正が面倒なのでbakeコマンドは使わないほうが良いかも
    - Admin/loginController.phpをコピーしてください

## 4. DBの設定

※下記に記載するコマンドは「<project_dir>/docker」ディレクトリで実行してください  
テスト用のデータベースを設定します

- 初期の仕様が固まるまでは、migrationファイルを直接操作します
- migrationファイルが更新されるとmigration時にエラーとなるので、  
「テーブル全削除→再度migration→seedでテストデータ入力」の流れでコマンドを実行します
- 初期仕様決定後は、カラム追加/削除用のマイグレーションファイルを追加する方針に切り替えます

### DB再構築手順

1. テーブルデータ全削除  
    A5などのクライアントから下記SQL文を実行 ※初回マイグレーション時は必要なし  
    drop table if exists phinxlog;  
    drop table if exists t_logs;  
    drop table if exists t_order_items;  
    ...  
      
    ※コマンドは「cake/config/Migrations/00000000000000_dropAllTables.sql」に記載してあります。  
  SQLツール（A5:SQLなど）にコピペして実行してください。
    
1. マイグレーションの実行

    ```
    $ bin/cake.ps1 migrations migrate
    ```

※ 特定のMigrationファイルを実行する場合

    ```
    $ bin/cake migrations status
    該当するタイムスタンプを指定
    $ bin/cake migrations migrate -t 20150103081132
    ```        

1. データベースの初期データ投入
    1. 「cake/config/Seeds」ディレクトリにseedファイルを作成してください
    1. 下記コマンドを実行するとSeedファイルがすべて読み込まれます

    ```
    $ bin/cake.ps1 migrations seed
    ```

※ スクリプトを使わない、Cakephp正規のやり方は下記です

    ```
    $ bin/cake.ps1 migrations seed
    ※1つだけ実行するには---seedオプションを使用する
    $ bin/cake.ps1 migrations seed --seed ArticlesSeed
    ```


### Bake時の注意点

複合キーはbake時に反映されないのでEntityに追記が必要。
具体的には、Entityのaccessibleに複合キーとなっているカラムを追記する。

http://thebaker.hatenablog.com/entry/2018/04/24/154427
https://noarts.net/archives/667

### キャッシュクリア

- DBやビューなどを更新したときに、変更が反映されない場合はキャッシュクリアを試してください。

```
$ bin/cake cache clear default
```
