# This file is a "template" of which env vars needs to be defined in your configuration or in an .env file
# Set variables here that may be different on each deployment target of the app, e.g. development, staging, production.
# https://symfony.com/doc/current/best_practices/configuration.html#infrastructure-related-configuration

###> symfony/framework-bundle ###
APP_ENV=dev
APP_DEBUG=1
#TRUSTED_PROXIES=127.0.0.1,*********
#TRUSTED_HOSTS=localhost,example.com
###< symfony/framework-bundle ###

###> doctrine/doctrine-bundle ###
# Format described at http://docs.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# For a sqlite database, use: "sqlite:///%kernel.project_dir%/var/data.db"
# Set "serverVersion" to your server version to avoid edge-case exceptions and extra database calls
DATABASE_URL=sqlite:///var/eccube.db
# The version of your database engine
DATABASE_SERVER_VERSION=3
###< doctrine/doctrine-bundle ###

###> symfony/swiftmailer-bundle ###
# For Gmail as a transport, use: "gmail://username:password@localhost"
# For a generic SMTP server, use: "smtp://localhost:25?encryption=&auth_mode="
# Delivery is disabled by default via "null://localhost"
MAILER_URL=null://localhost
###< symfony/swiftmailer-bundle ###

###> APPLICATION CONFIG ###
# EC-CUBE Configs. The default value is defined in app/config/packages/eccube.yaml.
# Please remove commented out and enable it if you want to change.

#ECCUBE_LOCALE=ja
#ECCUBE_TIMEZONE=Asia/Tokyo
#ECCUBE_CURRENCY=JPY
#ECCUBE_ADMIN_ROUTE=admin
#ECCUBE_USER_DATA_ROUTE=user_data
#ECCUBE_ADMIN_ALLOW_HOSTS=[]
#ECCUBE_FORCE_SSL=false
#ECCUBE_TEMPLATE_CODE=default
#ECCUBE_AUTH_MAGIC=<change.me>
#ECCUBE_COOKIE_NAME=eccube
#ECCUBE_COOKIE_PATH=/
#ECCUBE_COOKIE_LIFETIME=0
#ECCUBE_GC_MAXLIFETIME=1440

###< APPLICATION CONFIG ###
