CREATE OR REPLACE VIEW sma_d_orderlist AS
SELECT
  customer_code,
  customer_edaban,
  order_code,
  management_product_code AS product_code,
  product_name,
  product_standard AS standard,
  in_numbers,
  unit_name,
  price_switch_day,
  price_before,
  price_after,
  order_division,
  mc.name as order_division_name,
  lead_time,
  is_visible,
  'smadorderlist' AS discriminator_type
FROM
  smart_order.d_orderlist as do
  LEFT JOIN (
    SELECT
      '0' AS code,
      order_type_0 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '1' AS code,
      order_type_1 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '2' AS code,
      order_type_2 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '3' AS code,
      order_type_3 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '4' AS code,
      order_type_4 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '5' AS code,
      order_type_5 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '6' AS code,
      order_type_6 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '7' AS code,
      order_type_7 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '8' AS code,
      order_type_8 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '9' AS code,
      order_type_9 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
  ) as mc ON do.order_division = mc.code
WHERE
  company_code = '999'
  AND (
    is_visible = '1'
    OR is_visible = '8'
  );