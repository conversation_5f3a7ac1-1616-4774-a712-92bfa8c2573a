CREATE OR REPLACE VIEW sma_product AS
SELECT
  cpl.product_code,
  cpb.base_id,
  cpl.product_name,
  cpl.product_kana,
  cpl.product_standard AS standard,
  cpl.in_numbers,
  cpl.product_packing AS packing,
  cpl.unit_name,
  cpl.description1,
  cpl.description2,
  cpl.price_switch_day,
  cpl.price_before,
  cpl.price_after,
  cpl.measurement_division,
  cpl.decimal_point_permission_division,
  cpl.tax_type,
  cpl.tax_rate,
  cpl.is_visible,
  cpb.order_division,
  mc.name as order_division_name,
  cpb.lead_time,
  'smaproduct' AS discriminator_type
FROM
  smart_order.c_product_list AS cpl
  LEFT JOIN smart_order.c_product_info_by_base AS cpb ON cpl.company_code = cpb.company_code
  AND cpl.product_code = cpb.product_code
  LEFT JOIN (
    SELECT
      '0' AS code,
      order_type_0 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '1' AS code,
      order_type_1 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '2' AS code,
      order_type_2 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '3' AS code,
      order_type_3 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '4' AS code,
      order_type_4 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '5' AS code,
      order_type_5 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '6' AS code,
      order_type_6 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '7' AS code,
      order_type_7 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '8' AS code,
      order_type_8 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
    UNION ALL
    SELECT
      '9' AS code,
      order_type_9 AS name
    FROM
      smart_order.m_companies
    WHERE
      code = '999'
  ) as mc ON cpb.order_division = mc.code
WHERE
  cpl.company_code = '999'
  AND cpb.base_id IS NOT NULL;