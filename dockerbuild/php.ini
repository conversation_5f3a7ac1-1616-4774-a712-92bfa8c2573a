; Optimizations for Symfony, as documented on http://symfony.com/doc/current/performance.html
opcache.max_accelerated_files = 20000
opcache.memory_consumption=256
realpath_cache_size = 4096K
realpath_cache_ttl = 600

;file_uploads = On
;memory_limit = -1
;upload_max_filesize = -1
;post_max_size = -1
max_execution_time = 600
;auto_detect_line_endings = On
default_socket_timeout = 600
max_input_time = 600

date.timezone = Asia/Tokyo

[xdebug]
xdebug.remote_connect_back=0
xdebug.remote_enable=1
xdebug.remote_autostart=1
xdebug.remote_port=9001

xdebug.remote_host = host.docker.internal
xdebug.remote_log = /tmp/xdebug.log
xdebug.idekey=xdebug
