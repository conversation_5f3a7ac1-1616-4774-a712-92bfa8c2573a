#!/bin/bash

# Script to initialize SSL certificate from Let's Encrypt
# Use for first time HTTPS setup

domains=(dde.co.jp www.dde.co.jp)
rsa_key_size=4096
data_path="./nginx/certs"
email="<EMAIL>" # Email for Let's Encrypt registration

# Màu sắc cho output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Detect docker-compose command
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    echo -e "${RED}Error: Neither 'docker-compose' nor 'docker compose' found${NC}"
    echo -e "${YELLOW}Please install Docker Compose${NC}"
    exit 1
fi

echo -e "${GREEN}Using: $DOCKER_COMPOSE${NC}"

echo -e "${GREEN}=== Let's Encrypt SSL Setup Script ===${NC}"
echo -e "${YELLOW}Domains: ${domains[*]}${NC}"
echo -e "${YELLOW}Email: $email${NC}"
echo ""

if [ -d "$data_path" ]; then
  read -p "Existing data found for $domains. Continue and replace existing certificate? (y/N) " decision
  if [ "$decision" != "Y" ] && [ "$decision" != "y" ]; then
    echo -e "${RED}Aborted.${NC}"
    exit
  fi
fi

echo -e "${YELLOW}### Downloading recommended TLS parameters...${NC}"
mkdir -p "$data_path/conf"
curl -s https://raw.githubusercontent.com/certbot/certbot/master/certbot-nginx/certbot_nginx/_internal/tls_configs/options-ssl-nginx.conf > "$data_path/conf/options-ssl-nginx.conf"
curl -s https://raw.githubusercontent.com/certbot/certbot/master/certbot/certbot/ssl-dhparams.pem > "$data_path/conf/ssl-dhparams.pem"
echo -e "${GREEN}✓ TLS parameters downloaded${NC}"

echo -e "${YELLOW}### Creating dummy certificate for ${domains[0]} ...${NC}"
path="/etc/letsencrypt/live/${domains[0]}"
mkdir -p "$data_path/conf"
mkdir -p "$data_path/live/${domains[0]}"
$DOCKER_COMPOSE run --rm --entrypoint "\
  openssl req -x509 -nodes -newkey rsa:$rsa_key_size -days 1\
    -keyout '$path/privkey.pem' \
    -out '$path/fullchain.pem' \
    -subj '/CN=localhost'" certbot
echo -e "${GREEN}✓ Dummy certificate created${NC}"

echo -e "${YELLOW}### Starting nginx ...${NC}"
$DOCKER_COMPOSE up --force-recreate -d nginx
echo -e "${GREEN}✓ Nginx started${NC}"

echo -e "${YELLOW}### Deleting dummy certificate for ${domains[0]} ...${NC}"
$DOCKER_COMPOSE run --rm --entrypoint "\
  rm -Rf /etc/letsencrypt/live/${domains[0]} && \
  rm -Rf /etc/letsencrypt/archive/${domains[0]} && \
  rm -Rf /etc/letsencrypt/renewal/${domains[0]}.conf" certbot
echo -e "${GREEN}✓ Dummy certificate deleted${NC}"

echo -e "${YELLOW}### Requesting Let's Encrypt certificate for ${domains[*]} ...${NC}"
# Join $domains to -d args
domain_args=""
for domain in "${domains[@]}"; do
  domain_args="$domain_args -d $domain"
done

# Select appropriate email arg
case "$email" in
  "") email_arg="--register-unsafely-without-email" ;;
  *) email_arg="--email $email" ;;
esac

$DOCKER_COMPOSE run --rm --entrypoint "\
  certbot certonly --webroot -w /var/www/certbot \
    $email_arg \
    $domain_args \
    --rsa-key-size $rsa_key_size \
    --agree-tos \
    --force-renewal" certbot

if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ SSL certificate obtained successfully${NC}"
else
  echo -e "${RED}✗ Failed to obtain SSL certificate${NC}"
  exit 1
fi

echo -e "${YELLOW}### Reloading nginx ...${NC}"
$DOCKER_COMPOSE exec nginx nginx -s reload
echo -e "${GREEN}✓ Nginx reloaded${NC}"

echo ""
echo -e "${GREEN}=== SSL Setup Complete! ===${NC}"
echo -e "${GREEN}Your site should now be available at:${NC}"
echo -e "${GREEN}  https://dde.co.jp${NC}"
echo -e "${GREEN}  https://www.dde.co.jp${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo -e "${YELLOW}1. Test your SSL: https://www.ssllabs.com/ssltest/${NC}"
echo -e "${YELLOW}2. Set up auto-renewal with crontab${NC}"
echo -e "${YELLOW}3. Update Django ALLOWED_HOSTS if needed${NC}"
