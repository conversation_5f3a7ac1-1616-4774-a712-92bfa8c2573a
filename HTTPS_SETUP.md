# 🔒 HTTPS Deployment Guide with Let's Encrypt

## ✅ Prerequisites

1. **Domain pointing to EC2 IP**: Ensure `dde.co.jp` and `www.dde.co.jp` point to IP `**************`
   ```bash
   # Check DNS
   nslookup dde.co.jp
   nslookup www.dde.co.jp
   ```

2. **Ports 80 and 443 are open**: Check EC2 Security Group
   - Port 80 (HTTP) - Source: 0.0.0.0/0
   - Port 443 (HTTPS) - Source: 0.0.0.0/0

3. **Docker and Docker Compose installed**
   ```bash
   docker --version
   docker-compose --version
   ```

## 🚀 Deployment Steps

### Step 1: Stop current services (if running)

```bash
docker-compose down
```

### Step 2: Rebuild images

```bash
docker-compose build --no-cache
```

### Step 3: Run SSL certificate initialization script

```bash
./init-letsencrypt.sh
```

This script will:
- ✅ Download TLS parameters from Let's Encrypt
- ✅ Create temporary dummy certificate
- ✅ Start nginx
- ✅ Delete dummy certificate
- ✅ Get real certificate from Let's Encrypt
- ✅ Reload nginx with new certificate

### Step 4: Check results

```bash
# Check SSL status
./check-ssl.sh

# Check running services
docker-compose ps

# Test HTTPS
curl -I https://dde.co.jp
```

### Step 5: Access website

- 🌐 **HTTPS**: https://dde.co.jp/dashboard
- 🌐 **HTTPS**: https://www.dde.co.jp/dashboard
- ↩️ **HTTP redirect**: http://dde.co.jp → https://dde.co.jp

## 🔄 SSL Certificate Auto-renewal

### Setup crontab

```bash
# Open crontab
crontab -e

# Add this line to run renewal daily at 2:00 AM
0 2 * * * /home/<USER>/bi_tool/renew-ssl.sh >> /var/log/letsencrypt-renew.log 2>&1
```

### Test manual renewal

```bash
./renew-ssl.sh
```

## 🛠️ Available Scripts

1. **`init-letsencrypt.sh`** - Initialize SSL certificate for first time
2. **`renew-ssl.sh`** - Renew SSL certificate
3. **`check-ssl.sh`** - Check SSL status

## 🔍 Troubleshooting

### Common errors:

1. **Domain not pointing to server**:
   ```bash
   # Check DNS
   dig dde.co.jp
   # Should return **************
   ```

2. **Port blocked**:
   ```bash
   # Test port 80
   telnet ************** 80
   # Test port 443
   telnet ************** 443
   ```

3. **Certificate validation failed**:
   ```bash
   # Check nginx logs
   docker-compose logs nginx

   # Check certbot logs
   docker-compose logs certbot
   ```

### Useful commands:

```bash
# View all logs
docker-compose logs

# Restart all services
docker-compose restart

# View certificate info
docker-compose run --rm certbot certificates

# Test nginx config
docker-compose exec nginx nginx -t

# Reload nginx
docker-compose exec nginx nginx -s reload
```

## 📁 File structure after setup:

```
bi_tool/
├── docker-compose.yml          # ✅ Updated with HTTPS support
├── init-letsencrypt.sh         # ✅ SSL initialization script
├── renew-ssl.sh               # ✅ SSL renewal script
├── check-ssl.sh               # ✅ SSL status check script
├── HTTPS_SETUP.md             # ✅ This guide
├── nginx/
│   ├── default.conf           # ✅ Updated with HTTPS config
│   ├── Dockerfile
│   ├── certs/                 # 📁 SSL certificates
│   │   ├── live/dde.co.jp/
│   │   └── conf/
│   └── certbot-www/           # 📁 ACME challenge
└── django/
    ├── core/settings.py       # ✅ Updated with HTTPS settings
    └── ...
```

## 🎯 Expected results:

- ✅ HTTPS working with valid certificate
- ✅ HTTP automatically redirects to HTTPS
- ✅ Security headers configured
- ✅ Auto-renewal configured
- ✅ Django settings compatible with HTTPS
