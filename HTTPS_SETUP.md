# 🔒 HTTPS Deployment Guide with Let's Encrypt

## ✅ Prerequisites

1. **Domain pointing to EC2 IP**: Ensure `dde.co.jp` and `www.dde.co.jp` point to IP `**************`
   ```bash
   # Check DNS
   nslookup dde.co.jp
   nslookup www.dde.co.jp
   ```

2. **Ports 80 and 443 are open**: Check EC2 Security Group
   - Port 80 (HTTP) - Source: 0.0.0.0/0
   - Port 443 (HTTPS) - Source: 0.0.0.0/0

3. **Docker and Docker Compose installed**
   ```bash
   docker --version
   docker-compose --version
   ```

## 🚀 Các bước triển khai

### Bước 1: Dừng services hiện tại (nếu đang chạy)

```bash
docker-compose down
```

### Bước 2: Build lại images

```bash
docker-compose build --no-cache
```

### Bước 3: Chạy script khởi tạo SSL certificate

```bash
./init-letsencrypt.sh
```

Script này sẽ:
- ✅ Tải TLS parameters từ Let's Encrypt
- ✅ Tạo dummy certificate tạm thời
- ✅ Khởi động nginx
- ✅ Xóa dummy certificate
- ✅ Lấy certificate thật từ Let's Encrypt
- ✅ Reload nginx với certificate mới

### Bước 4: Kiểm tra kết quả

```bash
# Kiểm tra SSL status
./check-ssl.sh

# Kiểm tra services đang chạy
docker-compose ps

# Test HTTPS
curl -I https://dde.co.jp
```

### Bước 5: Truy cập website

- 🌐 **HTTPS**: https://dde.co.jp/dashboard
- 🌐 **HTTPS**: https://www.dde.co.jp/dashboard
- ↩️ **HTTP redirect**: http://dde.co.jp → https://dde.co.jp

## 🔄 Auto-renewal SSL Certificate

### Thiết lập crontab

```bash
# Mở crontab
crontab -e

# Thêm dòng này để chạy renew mỗi ngày lúc 2:00 AM
0 2 * * * /home/<USER>/bi_tool/renew-ssl.sh >> /var/log/letsencrypt-renew.log 2>&1
```

### Test renewal thủ công

```bash
./renew-ssl.sh
```

## 🛠️ Scripts có sẵn

1. **`init-letsencrypt.sh`** - Khởi tạo SSL certificate lần đầu
2. **`renew-ssl.sh`** - Renew SSL certificate
3. **`check-ssl.sh`** - Kiểm tra trạng thái SSL

## 🔍 Troubleshooting

### Lỗi thường gặp:

1. **Domain chưa trỏ về server**:
   ```bash
   # Kiểm tra DNS
   dig dde.co.jp
   # Phải trả về **************
   ```

2. **Port bị block**:
   ```bash
   # Test port 80
   telnet ************** 80
   # Test port 443
   telnet ************** 443
   ```

3. **Certificate validation failed**:
   ```bash
   # Kiểm tra nginx logs
   docker-compose logs nginx
   
   # Kiểm tra certbot logs
   docker-compose logs certbot
   ```

### Lệnh hữu ích:

```bash
# Xem tất cả logs
docker-compose logs

# Restart toàn bộ services
docker-compose restart

# Xem certificate info
docker-compose run --rm certbot certificates

# Test nginx config
docker-compose exec nginx nginx -t

# Reload nginx
docker-compose exec nginx nginx -s reload
```

## 📁 Cấu trúc file sau khi setup:

```
bi_tool/
├── docker-compose.yml          # ✅ Updated với HTTPS support
├── init-letsencrypt.sh         # ✅ Script khởi tạo SSL
├── renew-ssl.sh               # ✅ Script renew SSL
├── check-ssl.sh               # ✅ Script kiểm tra SSL
├── HTTPS_SETUP.md             # ✅ Hướng dẫn này
├── nginx/
│   ├── default.conf           # ✅ Updated với HTTPS config
│   ├── Dockerfile
│   ├── certs/                 # 📁 SSL certificates
│   │   ├── live/dde.co.jp/
│   │   └── conf/
│   └── certbot-www/           # 📁 ACME challenge
└── django/
    ├── core/settings.py       # ✅ Updated với HTTPS settings
    └── ...
```

## 🎯 Kết quả mong đợi:

- ✅ HTTPS hoạt động với certificate hợp lệ
- ✅ HTTP tự động redirect sang HTTPS
- ✅ Security headers được thiết lập
- ✅ Auto-renewal được cấu hình
- ✅ Django settings tương thích với HTTPS
