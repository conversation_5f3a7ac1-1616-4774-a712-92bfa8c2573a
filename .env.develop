# This file is a "template" of which env vars needs to be defined in your configuration or in an .env file
# Set variables here that may be different on each deployment target of the app, e.g. development, staging, production.
# https://symfony.com/doc/current/best_practices/configuration.html#infrastructure-related-configuration

###> symfony/framework-bundle ###
APP_ENV=dev
APP_DEBUG=0
#TRUSTED_PROXIES=127.0.0.1,*********
#TRUSTED_HOSTS=localhost,example.com
###< symfony/framework-bundle ###

###> doctrine/doctrine-bundle ###
# Format described at http://docs.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# For a sqlite database, use: "sqlite:///%kernel.project_dir%/var/data.db"
# Set "serverVersion" to your server version to avoid edge-case exceptions and extra database calls
DATABASE_URL=mysql://testuser:passwd@db:3306/smart_order_catalog
#DATABASE_URL=mysql://root:root@db:3306/eccube_db
# The version of your database engine
DATABASE_SERVER_VERSION=8.0.19
###< doctrine/doctrine-bundle ###

###> symfony/swiftmailer-bundle ###
# For Gmail as a transport, use: "gmail://username:password@localhost"
# For a generic SMTP server, use: "smtp://localhost:25?encryption=&auth_mode="
# For a debug SMTP server, use: "smtp://mailcatcher:1025"
# Delivery is disabled by default via "null://localhost"
MAILER_URL=sendmail://mail:1025
#MAILER_URL=smtp://mail:1025
###< symfony/swiftmailer-bundle ###

###> APPLICATION CONFIG ###
# EC-CUBE Configs. The default value is defined in app/config/packages/eccube.yaml.
# Please remove commented out and enable it if you want to change.

#ECCUBE_LOCALE=ja
#ECCUBE_TIMEZONE=Asia/Tokyo
#ECCUBE_CURRENCY=JPY
#ECCUBE_ADMIN_ROUTE=admin
#ECCUBE_USER_DATA_ROUTE=user_data
#ECCUBE_ADMIN_ALLOW_HOSTS=[]
#ECCUBE_FORCE_SSL=false
#ECCUBE_TEMPLATE_CODE=default
#ECCUBE_AUTH_MAGIC=<change.me>
#ECCUBE_COOKIE_NAME=eccube
#ECCUBE_COOKIE_PATH=/
#ECCUBE_COOKIE_LIFETIME=0
#ECCUBE_GC_MAXLIFETIME=1440

ECCUBE_SHOP_NAME="ACT NETORDER"
###< APPLICATION CONFIG ###

###> API URL ###
# 認証先のスマ発注URL
SMART_ORDER_URL=http://localhost
SMART_ORDER_CATALOG_LOGIN_PAGE=/001/login/catalog
SMART_ORDER_CONFIRM_PAGE=/001/order_confirm
CATALOG_LOGIN_PAGE=/catalog/catalog/login
CATALOG_CART_PAGE=/catalog/cart
SYSTEM_ROOT_PATH=/var/www/html/
COMPANY_CODE=001
###< ###

ECCUBE_AUTH_MAGIC=
ECCUBE_ADMIN_ROUTE=admin
ECCUBE_TEMPLATE_CODE=default
ECCUBE_LOCALE=ja