#!/bin/bash

# Script to check SSL certificate status

# <PERSON><PERSON>u sắc cho output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Detect docker-compose command
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    echo -e "${RED}Error: Neither 'docker-compose' nor 'docker compose' found${NC}"
    exit 1
fi

echo -e "${BLUE}=== SSL Certificate Status Check ===${NC}"
echo ""

# Check certificate files
echo -e "${YELLOW}### Checking certificate files...${NC}"
if [ -f "./nginx/certs/live/bi.dde.co.jp/fullchain.pem" ]; then
    echo -e "${GREEN}✓ Certificate file exists${NC}"
    
    # Display certificate information
    echo -e "${YELLOW}### Certificate Information:${NC}"
    $DOCKER_COMPOSE run --rm --entrypoint "\
        openssl x509 -in /etc/letsencrypt/live/bi.dde.co.jp/fullchain.pem -text -noout | grep -E '(Subject:|Issuer:|Not Before:|Not After:)'" certbot
else
    echo -e "${RED}✗ Certificate file not found${NC}"
fi

echo ""

# Check expiry date
echo -e "${YELLOW}### Certificate Expiry:${NC}"
$DOCKER_COMPOSE run --rm certbot certificates

echo ""

# Test HTTPS connection
echo -e "${YELLOW}### Testing HTTPS connection...${NC}"
if command -v curl &> /dev/null; then
    response=$(curl -s -o /dev/null -w "%{http_code}" https://bi.dde.co.jp --max-time 10)
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}✓ HTTPS connection successful (HTTP $response)${NC}"
    else
        echo -e "${RED}✗ HTTPS connection failed (HTTP $response)${NC}"
    fi
else
    echo -e "${YELLOW}curl not available, skipping connection test${NC}"
fi

echo ""

# Check nginx status
echo -e "${YELLOW}### Nginx Status:${NC}"
if $DOCKER_COMPOSE ps nginx | grep -q "Up"; then
    echo -e "${GREEN}✓ Nginx is running${NC}"
else
    echo -e "${RED}✗ Nginx is not running${NC}"
fi

echo ""
echo -e "${BLUE}=== Check Complete ===${NC}"
