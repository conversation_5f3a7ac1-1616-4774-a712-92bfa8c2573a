#!/bin/bash

# Script để kiểm tra trạng thái SSL certificate

# <PERSON><PERSON>u sắc cho output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== SSL Certificate Status Check ===${NC}"
echo ""

# Kiểm tra certificate files
echo -e "${YELLOW}### Checking certificate files...${NC}"
if [ -f "./nginx/certs/live/dde.co.jp/fullchain.pem" ]; then
    echo -e "${GREEN}✓ Certificate file exists${NC}"
    
    # Hiển thị thông tin certificate
    echo -e "${YELLOW}### Certificate Information:${NC}"
    docker-compose run --rm --entrypoint "\
        openssl x509 -in /etc/letsencrypt/live/dde.co.jp/fullchain.pem -text -noout | grep -E '(Subject:|Issuer:|Not Before:|Not After:)'" certbot
else
    echo -e "${RED}✗ Certificate file not found${NC}"
fi

echo ""

# Kiểm tra expiry date
echo -e "${YELLOW}### Certificate Expiry:${NC}"
docker-compose run --rm certbot certificates

echo ""

# Test HTTPS connection
echo -e "${YELLOW}### Testing HTTPS connection...${NC}"
if command -v curl &> /dev/null; then
    response=$(curl -s -o /dev/null -w "%{http_code}" https://dde.co.jp --max-time 10)
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}✓ HTTPS connection successful (HTTP $response)${NC}"
    else
        echo -e "${RED}✗ HTTPS connection failed (HTTP $response)${NC}"
    fi
else
    echo -e "${YELLOW}curl not available, skipping connection test${NC}"
fi

echo ""

# Kiểm tra nginx status
echo -e "${YELLOW}### Nginx Status:${NC}"
if docker-compose ps nginx | grep -q "Up"; then
    echo -e "${GREEN}✓ Nginx is running${NC}"
else
    echo -e "${RED}✗ Nginx is not running${NC}"
fi

echo ""
echo -e "${BLUE}=== Check Complete ===${NC}"
