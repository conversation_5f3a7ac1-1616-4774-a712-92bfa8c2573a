#!/bin/bash

# Script để quản lý AWS Parameter Store
# Usage: ./manage-parameters.sh [get|set|list|delete] [parameter-name] [value]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
ENV=${ENV:-"dev"}
PROJECT_NAME=${PROJECT_NAME:-"smart-catalog"}
REGION=${AWS_REGION:-"ap-northeast-1"}

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [PARAMETER_NAME] [VALUE]"
    echo ""
    echo "Commands:"
    echo "  get     - Get a parameter value"
    echo "  set     - Set a parameter value"
    echo "  list    - List all parameters for the project"
    echo "  delete  - Delete a parameter"
    echo ""
    echo "Examples:"
    echo "  $0 get db_host"
    echo "  $0 set db_host localhost"
    echo "  $0 set db_pass mypassword --secure"
    echo "  $0 list"
    echo "  $0 delete db_host"
    echo ""
    echo "Environment variables:"
    echo "  ENV          - Environment name (default: dev)"
    echo "  PROJECT_NAME - Project name (default: smart-catalog)"
    echo "  AWS_REGION   - AWS region (default: ap-northeast-1)"
}

# Function to get parameter path
get_parameter_path() {
    local param_name=$1
    echo "/${ENV}/${PROJECT_NAME}/${param_name}"
}

# Function to get parameter
get_parameter() {
    local param_name=$1
    local param_path=$(get_parameter_path $param_name)
    
    print_info "Getting parameter: $param_path"
    
    if aws ssm get-parameter --name "$param_path" --region "$REGION" --with-decryption >/dev/null 2>&1; then
        aws ssm get-parameter --name "$param_path" --region "$REGION" --with-decryption --query 'Parameter.Value' --output text
    else
        print_error "Parameter $param_path not found"
        exit 1
    fi
}

# Function to set parameter
set_parameter() {
    local param_name=$1
    local param_value=$2
    local param_path=$(get_parameter_path $param_name)
    local param_type="String"
    
    # Check if --secure flag is provided
    if [[ "$3" == "--secure" ]]; then
        param_type="SecureString"
        print_info "Setting secure parameter: $param_path"
    else
        print_info "Setting parameter: $param_path"
    fi
    
    aws ssm put-parameter \
        --name "$param_path" \
        --value "$param_value" \
        --type "$param_type" \
        --region "$REGION" \
        --overwrite
    
    print_info "Parameter $param_path set successfully"
}

# Function to list parameters
list_parameters() {
    local base_path="/${ENV}/${PROJECT_NAME}"
    
    print_info "Listing parameters for path: $base_path"
    
    if aws ssm get-parameters-by-path --path "$base_path" --region "$REGION" --recursive --query 'Parameters[].Name' --output table; then
        print_info "Parameters listed successfully"
    else
        print_error "Failed to list parameters"
        exit 1
    fi
}

# Function to delete parameter
delete_parameter() {
    local param_name=$1
    local param_path=$(get_parameter_path $param_name)
    
    print_warning "Deleting parameter: $param_path"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        aws ssm delete-parameter --name "$param_path" --region "$REGION"
        print_info "Parameter $param_path deleted successfully"
    else
        print_info "Deletion cancelled"
    fi
}

# Main script logic
case "$1" in
    "get")
        if [[ -z "$2" ]]; then
            print_error "Parameter name is required"
            show_usage
            exit 1
        fi
        get_parameter "$2"
        ;;
    "set")
        if [[ -z "$2" ]] || [[ -z "$3" ]]; then
            print_error "Parameter name and value are required"
            show_usage
            exit 1
        fi
        set_parameter "$2" "$3" "$4"
        ;;
    "list")
        list_parameters
        ;;
    "delete")
        if [[ -z "$2" ]]; then
            print_error "Parameter name is required"
            show_usage
            exit 1
        fi
        delete_parameter "$2"
        ;;
    *)
        print_error "Invalid command: $1"
        show_usage
        exit 1
        ;;
esac 