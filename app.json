{"name": "EC-CUBE4", "description": "EC-CUBE 4 on Heroku", "website": "https://github.com/EC-CUBE/ec-cube", "repository": "https://github.com/EC-CUBE/ec-cube", "keywords": ["php", "ec", "e-commerce", "ec-cube"], "addons": [{"plan": "heroku-postgresql", "options": {"version": "9.6"}}], "env": {"APP_ENV": {"description": "prod|dev", "value": "prod"}, "APP_DEBUG": {"description": "1|0", "value": "0"}, "DATABASE_SERVER_VERSION": {"description": "Database server version.", "value": "9.6"}, "ECCUBE_AUTH_MAGIC": {"description": "Secret key for login password.", "generator": "secret"}, "ECCUBE_ADMIN_USER": {"description": "The login id of admin user.", "value": "admin"}, "ECCUBE_ADMIN_PASS": {"description": "The password of admin user."}}, "buildpacks": [{"url": "heroku/php"}]}