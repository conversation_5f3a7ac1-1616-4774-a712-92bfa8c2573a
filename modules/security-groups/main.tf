resource "aws_security_group" "alb" {
  name        = "${var.env}-${var.project_name}-alb-sg"
  vpc_id      = var.vpc_id
  description = "Allow HTTP/HTTPS"
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "ecs" {
  name        = "${var.env}-${var.project_name}-ecs-sg"
  vpc_id      = var.vpc_id
  description = "Allow traffic from ALB and EFS access"
  ingress {
    from_port       = 0
    to_port         = 65535
    protocol        = "tcp"
    security_groups = [aws_security_group.alb.id]
  }
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "rds" {
  name        = "${var.env}-${var.project_name}-rds-sg"
  vpc_id      = var.vpc_id
  description = "Allow DB access from ECS and optionally public"
  ingress {
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = [aws_security_group.ecs.id]
  }
  # Public access (chỉ nên dùng cho dev/test, đổi IP cho production)
  ingress {
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"] # Đổi thành IP của bạn, ví dụ: "***********/32"
    description = "Allow public access to RDS (for dev only!)"
  }
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "lambda" {
  name        = "${var.env}-${var.project_name}-lambda-sg"
  vpc_id      = var.vpc_id
  description = "Allow Lambda access to RDS"
  ingress {
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.rds.id]
  }
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
} 