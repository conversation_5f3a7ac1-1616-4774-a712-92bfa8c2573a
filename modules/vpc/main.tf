resource "aws_vpc" "this" {
  cidr_block           = var.cidr_block
  enable_dns_support   = true
  enable_dns_hostnames = true
  tags = {
    Name = var.name
    Environment = var.env
    Project = var.project_name
  }
}

resource "aws_subnet" "public" {
  count                    = length(var.public_subnet_cidrs)
  vpc_id                   = aws_vpc.this.id
  cidr_block               = var.public_subnet_cidrs[count.index]
  map_public_ip_on_launch  = true
  availability_zone        = var.public_subnet_azs[count.index]
  tags = { Name = "${var.env}-${var.project_name}-public-${count.index}" }
}

resource "aws_subnet" "private" {
  count             = length(var.private_subnet_cidrs)
  vpc_id            = aws_vpc.this.id
  cidr_block        = var.private_subnet_cidrs[count.index]
  availability_zone = var.private_subnet_azs[count.index]
  tags = { Name = "${var.env}-${var.project_name}-private-${count.index}" }
}

resource "aws_internet_gateway" "this" {
  vpc_id = aws_vpc.this.id
  tags = { Name = "${var.env}-${var.project_name}-igw" }
}

resource "aws_route_table" "public" {
  vpc_id = aws_vpc.this.id
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.this.id
  }
  tags = { Name = "${var.env}-${var.project_name}-rtb-public" }
}

resource "aws_route_table_association" "public" {
  count          = length(aws_subnet.public)
  subnet_id      = aws_subnet.public[count.index].id
  route_table_id = aws_route_table.public.id
} 