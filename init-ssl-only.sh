#!/bin/bash

# Script đơn giản để lấy SSL certificate mà không cần database
# Chỉ chạy nginx và certbot

domains=(dde.co.jp www.dde.co.jp)
rsa_key_size=4096
data_path="./nginx/certs"
email="<EMAIL>"

# Màu sắc cho output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Detect docker-compose command
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    echo -e "${RED}Error: Neither 'docker-compose' nor 'docker compose' found${NC}"
    exit 1
fi

echo -e "${GREEN}=== Simple SSL Setup (Nginx Only) ===${NC}"
echo -e "${YELLOW}Domains: ${domains[*]}${NC}"
echo -e "${YELLOW}Email: $email${NC}"
echo ""

# Dừng tất cả containers trước
echo -e "${YELLOW}### Stopping all containers...${NC}"
$DOCKER_COMPOSE down

# Tạo thư mục cần thiết
echo -e "${YELLOW}### Creating directories...${NC}"
mkdir -p "$data_path/conf"
mkdir -p "$data_path/live/${domains[0]}"
mkdir -p "./nginx/certbot-www"

# Tải TLS parameters
echo -e "${YELLOW}### Downloading TLS parameters...${NC}"
curl -s https://raw.githubusercontent.com/certbot/certbot/master/certbot-nginx/certbot_nginx/_internal/tls_configs/options-ssl-nginx.conf > "$data_path/conf/options-ssl-nginx.conf"
curl -s https://raw.githubusercontent.com/certbot/certbot/master/certbot/certbot/ssl-dhparams.pem > "$data_path/conf/ssl-dhparams.pem"
echo -e "${GREEN}✓ TLS parameters downloaded${NC}"

# Tạo dummy certificate
echo -e "${YELLOW}### Creating dummy certificate...${NC}"
path="/etc/letsencrypt/live/${domains[0]}"
$DOCKER_COMPOSE run --rm --entrypoint "\
  openssl req -x509 -nodes -newkey rsa:$rsa_key_size -days 1\
    -keyout '$path/privkey.pem' \
    -out '$path/fullchain.pem' \
    -subj '/CN=localhost'" certbot
echo -e "${GREEN}✓ Dummy certificate created${NC}"

# Tạo docker-compose tạm thời chỉ với nginx
cat > docker-compose-ssl.yml << EOF
version: '3.8'

services:
  nginx:
    build: ./nginx
    image: nginx_bi_tool
    container_name: "nginx_bi_tool"
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/certs:/etc/letsencrypt:ro
      - ./nginx/certbot-www:/var/www/certbot:ro
    restart: unless-stopped

  certbot:
    image: certbot/certbot:latest
    container_name: "certbot_bi_tool"
    volumes:
      - ./nginx/certs:/etc/letsencrypt
      - ./nginx/certbot-www:/var/www/certbot
EOF

# Khởi động nginx
echo -e "${YELLOW}### Starting nginx...${NC}"
docker compose -f docker-compose-ssl.yml up -d nginx
sleep 5

# Kiểm tra nginx có chạy không
if ! docker compose -f docker-compose-ssl.yml ps nginx | grep -q "Up"; then
    echo -e "${RED}✗ Nginx failed to start${NC}"
    docker compose -f docker-compose-ssl.yml logs nginx
    exit 1
fi
echo -e "${GREEN}✓ Nginx started${NC}"

# Test HTTP connection
echo -e "${YELLOW}### Testing HTTP connection...${NC}"
if curl -s -o /dev/null -w "%{http_code}" http://dde.co.jp/.well-known/acme-challenge/test --max-time 10 | grep -q "404"; then
    echo -e "${GREEN}✓ HTTP connection working (404 expected)${NC}"
else
    echo -e "${RED}✗ HTTP connection failed${NC}"
    echo -e "${YELLOW}Checking nginx logs:${NC}"
    docker compose -f docker-compose-ssl.yml logs nginx
    exit 1
fi

# Xóa dummy certificate
echo -e "${YELLOW}### Deleting dummy certificate...${NC}"
docker compose -f docker-compose-ssl.yml run --rm --entrypoint "\
  rm -Rf /etc/letsencrypt/live/${domains[0]} && \
  rm -Rf /etc/letsencrypt/archive/${domains[0]} && \
  rm -Rf /etc/letsencrypt/renewal/${domains[0]}.conf" certbot
echo -e "${GREEN}✓ Dummy certificate deleted${NC}"

# Lấy certificate thật
echo -e "${YELLOW}### Requesting real SSL certificate...${NC}"
domain_args=""
for domain in "${domains[@]}"; do
  domain_args="$domain_args -d $domain"
done

docker compose -f docker-compose-ssl.yml run --rm --entrypoint "\
  certbot certonly --webroot -w /var/www/certbot \
    --email $email \
    $domain_args \
    --rsa-key-size $rsa_key_size \
    --agree-tos \
    --force-renewal" certbot

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ SSL certificate obtained successfully${NC}"
    
    # Reload nginx
    echo -e "${YELLOW}### Reloading nginx...${NC}"
    docker compose -f docker-compose-ssl.yml exec nginx nginx -s reload
    echo -e "${GREEN}✓ Nginx reloaded${NC}"
    
    echo ""
    echo -e "${GREEN}=== SSL Setup Complete! ===${NC}"
    echo -e "${GREEN}Test your SSL: https://dde.co.jp${NC}"
    echo ""
    echo -e "${YELLOW}Next: Start full application with:${NC}"
    echo -e "${YELLOW}docker compose -f docker-compose-ssl.yml down${NC}"
    echo -e "${YELLOW}docker compose up -d${NC}"
    
else
    echo -e "${RED}✗ Failed to obtain SSL certificate${NC}"
    echo -e "${YELLOW}Checking certbot logs:${NC}"
    docker compose -f docker-compose-ssl.yml logs certbot
    exit 1
fi

# Cleanup
rm -f docker-compose-ssl.yml
