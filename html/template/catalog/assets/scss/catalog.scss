@import "./mixins/variables";
@import "./mixins/media";

$white: #f8f9fa !default;
$black: #495057 !default;


// 受発注区分名称のバッチ
@mixin badge_gradation($textColor, $mainColor, $subColor) {
  color: $textColor;
  background-color: $mainColor; /* ～IE9（グラデーション無し） */
  //background: linear-gradient(to bottom, $subColor, $mainColor); /* IE10 */
  //background: -webkit-gradient(linear, center top, center bottom, from($subColor), to($mainColor));

  padding: .2em .6em;
}

.order-division {
  &--0 {
    @include badge_gradation($white, #00cc00, #66ff66);
  }

  &--1 {
    @include badge_gradation($white, #F15A24, #FBB03B);
  }

  &--2 {
    @include badge_gradation($white, #0071BC, #29ABE2);
  }

  &--3 {
    @include badge_gradation($white, #E229AB, #EA69C4);
  }

  &--4 {
    @include badge_gradation($white, #710FFB, #9B57FC);
  }

  &--5 {
    @include badge_gradation($white, #6E4C0A, #998153);
  }

  &--6 {
    @include badge_gradation($white, #326600, #6F934C);
  }

  &--7 {
    @include badge_gradation($white, #C40000, #D54C4C);
  }

  &--8 {
    @include badge_gradation($white, #3442C2, #707AD4);
  }

  &--9 {
    @include badge_gradation($black, #EEB500, #F3CB4C);
  }
}

.in-smart-order {
  @include badge_gradation($white, #ea888d, #c47489);
  margin-right: 3px;
}

.flex-right {
  margin-left: auto;
  display: flex;
  font-size: 0.7em;

  @include media_desktop {
    font-size: 1em;
  }
}
