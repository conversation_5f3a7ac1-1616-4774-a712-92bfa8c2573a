{"version": 3, "sources": ["admin/assets/scss/bootstrap.css", "admin/assets/scss/component/_bootstrapOriginal.scss", "../../node_modules/bootstrap/scss/bootstrap.scss", "../../node_modules/bootstrap/scss/_root.scss", "../../node_modules/bootstrap/scss/_reboot.scss", "admin/assets/scss/library/_variable.scss", "../../node_modules/bootstrap/scss/_variables.scss", "../../node_modules/bootstrap/scss/vendor/_rfs.scss", "../../node_modules/bootstrap/scss/mixins/_hover.scss", "../../node_modules/bootstrap/scss/_type.scss", "../../node_modules/bootstrap/scss/mixins/_lists.scss", "../../node_modules/bootstrap/scss/_images.scss", "../../node_modules/bootstrap/scss/mixins/_image.scss", "../../node_modules/bootstrap/scss/mixins/_border-radius.scss", "../../node_modules/bootstrap/scss/_code.scss", "../../node_modules/bootstrap/scss/_grid.scss", "../../node_modules/bootstrap/scss/mixins/_grid.scss", "../../node_modules/bootstrap/scss/mixins/_grid-framework.scss", "../../node_modules/bootstrap/scss/_tables.scss", "../../node_modules/bootstrap/scss/mixins/_table-row.scss", "../../node_modules/bootstrap/scss/_functions.scss", "../../node_modules/bootstrap/scss/_forms.scss", "../../node_modules/bootstrap/scss/mixins/_transition.scss", "../../node_modules/bootstrap/scss/mixins/_forms.scss", "../../node_modules/bootstrap/scss/mixins/_gradients.scss", "../../node_modules/bootstrap/scss/_buttons.scss", "../../node_modules/bootstrap/scss/mixins/_buttons.scss", "../../node_modules/bootstrap/scss/_transitions.scss", "../../node_modules/bootstrap/scss/_dropdown.scss", "../../node_modules/bootstrap/scss/mixins/_caret.scss", "../../node_modules/bootstrap/scss/mixins/_nav-divider.scss", "../../node_modules/bootstrap/scss/_button-group.scss", "../../node_modules/bootstrap/scss/_input-group.scss", "../../node_modules/bootstrap/scss/_custom-forms.scss", "../../node_modules/bootstrap/scss/_nav.scss", "../../node_modules/bootstrap/scss/_navbar.scss", "../../node_modules/bootstrap/scss/_card.scss", "../../node_modules/bootstrap/scss/_breadcrumb.scss", "../../node_modules/bootstrap/scss/_pagination.scss", "../../node_modules/bootstrap/scss/mixins/_pagination.scss", "../../node_modules/bootstrap/scss/_badge.scss", "../../node_modules/bootstrap/scss/mixins/_badge.scss", "../../node_modules/bootstrap/scss/_jumbotron.scss", "../../node_modules/bootstrap/scss/_alert.scss", "../../node_modules/bootstrap/scss/mixins/_alert.scss", "../../node_modules/bootstrap/scss/_progress.scss", "../../node_modules/bootstrap/scss/_media.scss", "../../node_modules/bootstrap/scss/_list-group.scss", "../../node_modules/bootstrap/scss/mixins/_list-group.scss", "../../node_modules/bootstrap/scss/_close.scss", "../../node_modules/bootstrap/scss/_toasts.scss", "../../node_modules/bootstrap/scss/_modal.scss", "../../node_modules/bootstrap/scss/_tooltip.scss", "../../node_modules/bootstrap/scss/mixins/_reset-text.scss", "../../node_modules/bootstrap/scss/_popover.scss", "../../node_modules/bootstrap/scss/_carousel.scss", "../../node_modules/bootstrap/scss/mixins/_clearfix.scss", "../../node_modules/bootstrap/scss/_spinners.scss", "../../node_modules/bootstrap/scss/utilities/_align.scss", "../../node_modules/bootstrap/scss/mixins/_background-variant.scss", "../../node_modules/bootstrap/scss/utilities/_background.scss", "../../node_modules/bootstrap/scss/utilities/_borders.scss", "../../node_modules/bootstrap/scss/utilities/_display.scss", "../../node_modules/bootstrap/scss/utilities/_embed.scss", "../../node_modules/bootstrap/scss/utilities/_flex.scss", "../../node_modules/bootstrap/scss/utilities/_float.scss", "../../node_modules/bootstrap/scss/utilities/_overflow.scss", "../../node_modules/bootstrap/scss/utilities/_position.scss", "../../node_modules/bootstrap/scss/utilities/_screenreaders.scss", "../../node_modules/bootstrap/scss/mixins/_screen-reader.scss", "../../node_modules/bootstrap/scss/utilities/_shadows.scss", "../../node_modules/bootstrap/scss/utilities/_sizing.scss", "../../node_modules/bootstrap/scss/utilities/_stretched-link.scss", "../../node_modules/bootstrap/scss/utilities/_spacing.scss", "../../node_modules/bootstrap/scss/utilities/_text.scss", "../../node_modules/bootstrap/scss/mixins/_text-truncate.scss", "../../node_modules/bootstrap/scss/mixins/_text-emphasis.scss", "../../node_modules/bootstrap/scss/mixins/_text-hide.scss", "../../node_modules/bootstrap/scss/utilities/_visibility.scss", "../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "../../node_modules/bootstrap/scss/_print.scss"], "names": [], "mappings": "AAAA,gBAAgB;ACoBhB;EACE,yBAAyB,EAAA;;ACrB3B;;;;;EFSE;AGRF;EAGI,eAAc;EAAd,iBAAc;EAAd,iBAAc;EAAd,eAAc;EAAd,cAAc;EAAd,iBAAc;EAAd,iBAAc;EAAd,gBAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,eAAc;EAAd,oBAAc;EAId,kBAAc;EAAd,oBAAc;EAAd,kBAAc;EAAd,eAAc;EAAd,kBAAc;EAAd,iBAAc;EAAd,gBAAc;EAAd,eAAc;EAId,kBAAiC;EAAjC,sBAAiC;EAAjC,sBAAiC;EAAjC,sBAAiC;EAAjC,uBAAiC;EAKnC,+MAAyB;EACzB,6GAAwB,EAAA;;ACA1B;;;EAGE,sBAAsB,EAAA;;AAGxB;EACE,uBAAuB;EACvB,iBAAiB;EACjB,8BAA8B;EAC9B,6CCfU,EAAA;;ADqBZ;EACE,cAAc,EAAA;;AAUhB;EACE,SAAS;EACT,kMEyOiN;ECzJ7M,eAtCY;EHxChB,gBEkP+B;EFjP/B,gBEsP+B;EFrP/B,cEnCgB;EFoChB,gBAAgB;EAChB,sBC7BU,EAAA;;ALwCZ;EIEE,qBAAqB,EAAA;;AASvB;EACE,uBAAuB;EACvB,SAAS;EACT,iBAAiB,EAAA;;AAanB;EACE,aAAa;EACb,qBEoNuC,EAAA;;AF7MzC;EACE,aAAa;EACb,mBEuF8B,EAAA;;AF5EhC;;EAEE,0BAA0B;EAC1B,yCAAiC;UAAjC,iCAAiC;EACjC,YAAY;EACZ,gBAAgB;EAChB,sCAA8B;UAA9B,8BAA8B,EAAA;;AAGhC;EACE,mBAAmB;EACnB,kBAAkB;EAClB,oBAAoB,EAAA;;AAGtB;;;EAGE,aAAa;EACb,mBAAmB,EAAA;;AAGrB;;;;EAIE,gBAAgB,EAAA;;AAGlB;EACE,gBEqJ+B,EAAA;;AFlJjC;EACE,oBAAoB;EACpB,cAAc,EAAA;;AAGhB;EACE,gBAAgB,EAAA;;AAGlB;;EAEE,mBEwIkC,EAAA;;AFrIpC;EGxFI,cAAW,EAAA;;AHiGf;;EAEE,kBAAkB;EGnGhB,cAAW;EHqGb,cAAc;EACd,wBAAwB,EAAA;;AAG1B;EAAM,cAAc,EAAA;;AACpB;EAAM,UAAU,EAAA;;AAOhB;EACE,cHtLqB;EGuLrB,qBER4C;EFS5C,6BAA6B,EAAA;EIhL7B;IJmLE,cEX8D;IFY9D,0BEX+C,EAAA;;AFoBnD;EACE,cAAc;EACd,qBAAqB,EAAA;EI/LrB;IJkME,cAAc;IACd,qBAAqB,EAAA;;AASzB;;;;EAIE,iGE6DgH;ECjN9G,cAAW,EAAA;;AHwJf;EAEE,aAAa;EAEb,mBAAmB;EAEnB,cAAc,EAAA;;AAQhB;EAEE,gBAAgB,EAAA;;AAQlB;EACE,sBAAsB;EACtB,kBAAkB,EAAA;;AAGpB;EAGE,gBAAgB;EAChB,sBAAsB,EAAA;;AAQxB;EACE,yBAAyB,EAAA;;AAG3B;EACE,oBEoFkC;EFnFlC,uBEmFkC;EFlFlC,cEnQgB;EFoQhB,gBAAgB;EAChB,oBAAoB,EAAA;;AAGtB;EAGE,mBAAmB,EAAA;;AAQrB;EAEE,qBAAqB;EACrB,qBEqK2C,EAAA;;AF/J7C;EAEE,gBAAgB,EAAA;;AAOlB;EACE,mBAAmB;EACnB,0CAA0C,EAAA;;AAG5C;;;;;EAKE,SAAS;EACT,oBAAoB;EGrPlB,kBAAW;EHuPb,oBAAoB,EAAA;;AAGtB;;EAEE,iBAAiB,EAAA;;AAGnB;;EAEE,oBAAoB,EAAA;;AAMtB;EACE,iBAAiB,EAAA;;AAOnB;;;;EAIE,0BAA0B,EAAA;;AAK1B;;;;EAKI,eAAe,EAAA;;AAMrB;;;;EAIE,UAAU;EACV,kBAAkB,EAAA;;AAGpB;;EAEE,sBAAsB;EACtB,UAAU,EAAA;;AAIZ;;;;EASE,2BAA2B,EAAA;;AAG7B;EACE,cAAc;EAEd,gBAAgB,EAAA;;AAGlB;EAME,YAAY;EAEZ,UAAU;EACV,SAAS;EACT,SAAS,EAAA;;AAKX;EACE,cAAc;EACd,WAAW;EACX,eAAe;EACf,UAAU;EACV,oBAAoB;EGjShB,iBAtCY;EHyUhB,oBAAoB;EACpB,cAAc;EACd,mBAAmB,EAAA;;AAGrB;EACE,wBAAwB,EAAA;;AJxJ1B;;EI8JE,YAAY,EAAA;;AJ1Jd;EIkKE,oBAAoB;EACpB,wBAAwB,EAAA;;AJ/J1B;EIuKE,wBAAwB,EAAA;;AAQ1B;EACE,aAAa;EACb,0BAA0B,EAAA;;AAO5B;EACE,qBAAqB,EAAA;;AAGvB;EACE,kBAAkB;EAClB,eAAe,EAAA;;AAGjB;EACE,aAAa,EAAA;;AJjLf;EIuLE,wBAAwB,EAAA;;AK1d1B;;EAEE,qBHySuC;EGvSvC,gBHyS+B;EGxS/B,gBHyS+B,EAAA;;AGrSjC;EFgHM,iBAtCY,EAAA;;AEzElB;EF+GM,eAtCY,EAAA;;AExElB;EF8GM,kBAtCY,EAAA;;AEvElB;EF6GM,iBAtCY,EAAA;;AEtElB;EF4GM,kBAtCY,EAAA;;AErElB;EF2GM,eAtCY,EAAA;;AEnElB;EFyGM,kBAtCY;EEjEhB,gBH2S+B,EAAA;;AGvSjC;EFmGM,eAtCY;EE3DhB,gBH8R+B;EG7R/B,gBHqR+B,EAAA;;AGnRjC;EF8FM,iBAtCY;EEtDhB,gBH0R+B;EGzR/B,gBHgR+B,EAAA;;AG9QjC;EFyFM,iBAtCY;EEjDhB,gBHsR+B;EGrR/B,gBH2Q+B,EAAA;;AGzQjC;EFoFM,iBAtCY;EE5ChB,gBHkR+B;EGjR/B,gBHsQ+B,EAAA;;AFzOjC;EKpBE,gBHiFW;EGhFX,mBHgFW;EG/EX,SAAS;EACT,wCJ7CU,EAAA;;AIqDZ;;EFMI,cAAW;EEHb,gBH8N+B,EAAA;;AG3NjC;;EAEE,cHsQgC;EGrQhC,yBH8QmC,EAAA;;AGtQrC;EC/EE,eAAe;EACf,gBAAgB,EAAA;;ADmFlB;ECpFE,eAAe;EACf,gBAAgB,EAAA;;ADsFlB;EACE,qBAAqB,EAAA;EADvB;IAII,oBHwP+B,EAAA;;AG9OnC;EFjCI,cAAW;EEmCb,yBAAyB,EAAA;;AAI3B;EACE,mBHwBW;ECTP,kBAtCY,EAAA;;AE2BlB;EACE,cAAc;EF7CZ,cAAW;EE+Cb,cH1GgB,EAAA;EGuGlB;IAMI,qBAAqB,EAAA;;AEnHzB;ECIE,eAAe;EAGf,YAAY,EAAA;;ADDd;EACE,gBLigCwC;EKhgCxC,sBNSU;EMRV,yBLNgB;EOLd,sBP6OgC;EMvOlC,eAAe;EAGf,YAAY,EAAA;;ADcd;EAEE,qBAAqB,EAAA;;AAGvB;EACE,qBAA0B;EAC1B,cAAc,EAAA;;AAGhB;EJkCI,cAAW;EIhCb,cL3BgB,EAAA;;AQZlB;EPuEI,gBAAW;EOrEb,cRoCe;EQnCf,qBAAqB,EAAA;EAGrB;IACE,cAAc,EAAA;;AAKlB;EACE,sBRqlCuC;EC3hCrC,gBAAW;EOxDb,WTQU;ESPV,yBRDgB;EOXd,qBP+O+B,EAAA;EQvOnC;IASI,UAAU;IPkDV,eAAW;IOhDX,gBR4Q6B,EAAA;;AFpEjC;EUjME,cAAc;EPyCZ,gBAAW;EOvCb,cRjBgB,EAAA;EQclB;IP0CI,kBAAW;IOlCX,cAAc;IACd,kBAAkB,EAAA;;AAKtB;EACE,iBR4jCuC;EQ3jCvC,kBAAkB,EAAA;;ACxClB;ECDA,WAAW;EACX,mBAA0B;EAC1B,kBAAyB;EACzB,kBAAkB;EAClB,iBAAiB,EAAA;;ADGjB;ECPA,WAAW;EACX,mBAA0B;EAC1B,kBAAyB;EACzB,kBAAkB;EAClB,iBAAiB,EAAA;;ADmCjB;ECrBA,oBAAa;EAAb,aAAa;EACb,eAAe;EACf,mBAA0B;EAC1B,kBAAyB,EAAA;;ADwBzB;EACE,eAAe;EACf,cAAc,EAAA;EAFhB;;IAMI,gBAAgB;IAChB,eAAe,EAAA;;AElDnB;;;;;;EACE,kBAAkB;EAClB,WAAW;EACX,mBAA0B;EAC1B,kBAAyB,EAAA;;AAmBvB;EACE,aAAa;EACb,mBAAY;UAAZ,YAAY;EACZ,eAAe,EAAA;;AAIf;ED4BJ,mBAAuB;UAAvB,cAAuB;EACvB,eAAwB,EAAA;;AC7BpB;ED4BJ,mBAAuB;UAAvB,aAAuB;EACvB,cAAwB,EAAA;;AC7BpB;ED4BJ,mBAAuB;UAAvB,mBAAuB;EACvB,oBAAwB,EAAA;;AC7BpB;ED4BJ,mBAAuB;UAAvB,aAAuB;EACvB,cAAwB,EAAA;;AC7BpB;ED4BJ,mBAAuB;UAAvB,aAAuB;EACvB,cAAwB,EAAA;;AC7BpB;ED4BJ,mBAAuB;UAAvB,mBAAuB;EACvB,oBAAwB,EAAA;;ACxBtB;EDMJ,mBAAc;UAAd,cAAc;EACd,WAAW;EACX,eAAe,EAAA;;ACHT;EDPN,mBAAsC;UAAtC,kBAAsC;EAItC,mBAAuC,EAAA;;ACGjC;EDPN,mBAAsC;UAAtC,mBAAsC;EAItC,oBAAuC,EAAA;;ACGjC;EDPN,mBAAsC;UAAtC,aAAsC;EAItC,cAAuC,EAAA;;ACGjC;EDPN,mBAAsC;UAAtC,mBAAsC;EAItC,oBAAuC,EAAA;;ACGjC;EDPN,mBAAsC;UAAtC,mBAAsC;EAItC,oBAAuC,EAAA;;ACGjC;EDPN,mBAAsC;UAAtC,aAAsC;EAItC,cAAuC,EAAA;;ACGjC;EDPN,mBAAsC;UAAtC,mBAAsC;EAItC,oBAAuC,EAAA;;ACGjC;EDPN,mBAAsC;UAAtC,mBAAsC;EAItC,oBAAuC,EAAA;;ACGjC;EDPN,mBAAsC;UAAtC,aAAsC;EAItC,cAAuC,EAAA;;ACGjC;EDPN,mBAAsC;UAAtC,mBAAsC;EAItC,oBAAuC,EAAA;;ACGjC;EDPN,mBAAsC;UAAtC,mBAAsC;EAItC,oBAAuC,EAAA;;ACGjC;EDPN,mBAAsC;UAAtC,cAAsC;EAItC,eAAuC,EAAA;;ACQnC;EAAwB,4BAAS;UAAT,SAAS,EAAA;;AAEjC;EAAuB,6BX6KG;UW7KH,SX6KG,EAAA;;AW1KxB;EAAwB,4BADZ;UACY,QADZ,EAAA;;AACZ;EAAwB,4BADZ;UACY,QADZ,EAAA;;AACZ;EAAwB,4BADZ;UACY,QADZ,EAAA;;AACZ;EAAwB,4BADZ;UACY,QADZ,EAAA;;AACZ;EAAwB,4BADZ;UACY,QADZ,EAAA;;AACZ;EAAwB,4BADZ;UACY,QADZ,EAAA;;AACZ;EAAwB,4BADZ;UACY,QADZ,EAAA;;AACZ;EAAwB,4BADZ;UACY,QADZ,EAAA;;AACZ;EAAwB,4BADZ;UACY,QADZ,EAAA;;AACZ;EAAwB,6BADZ;UACY,QADZ,EAAA;;AACZ;EAAwB,6BADZ;UACY,SADZ,EAAA;;AACZ;EAAwB,6BADZ;UACY,SADZ,EAAA;;AACZ;EAAwB,6BADZ;UACY,SADZ,EAAA;;AAOV;EDRR,qBAA8C,EAAA;;ACQtC;EDRR,sBAA8C,EAAA;;ACQtC;EDRR,gBAA8C,EAAA;;ACQtC;EDRR,sBAA8C,EAAA;;ACQtC;EDRR,sBAA8C,EAAA;;ACQtC;EDRR,gBAA8C,EAAA;;ACQtC;EDRR,sBAA8C,EAAA;;ACQtC;EDRR,sBAA8C,EAAA;;ACQtC;EDRR,gBAA8C,EAAA;;ACQtC;EDRR,sBAA8C,EAAA;;ACQtC;EDRR,sBAA8C,EAAA;;AEnDhD;EACE,WAAW;EACX,mBZkIW;EYjIX,cZSgB,EAAA;EYZlB;;IAQI,gBZsVgC;IYrVhC,mBAAmB;IACnB,0BjBGqB,EAAA;EiBbzB;IAcI,sBAAsB;IACtB,6BjBFqB,EAAA;EiBbzB;IAmBI,0BjBNqB,EAAA;;AiBezB;;EAGI,eZgU+B,EAAA;;AYvTnC;EACE,sBjB5BuB,EAAA;EiB2BzB;;IAKI,sBjBhCqB,EAAA;EiB2BzB;;IAWM,wBAA4C,EAAA;;AAKlD;;;;EAKI,SAAS,EAAA;;AAQb;EAEI,yBjBzDqB,EAAA;;AONvB;EU2EI,cZvEY;EYwEZ,sCb3EM,EAAA;;AcRV;;;EAII,yBCsF4D,EAAA;;AD1FhE;;;;EAYM,qBC8E0D,EAAA;;AZnFhE;EWiBM,yBAJsC,EAAA;EAD5C;;IASQ,yBARoC,EAAA;;AApB5C;;;EAII,yBCsF4D,EAAA;;AD1FhE;;;;EAYM,qBC8E0D,EAAA;;AZnFhE;EWiBM,yBAJsC,EAAA;EAD5C;;IASQ,yBARoC,EAAA;;AApB5C;;;EAII,yBCsF4D,EAAA;;AD1FhE;;;;EAYM,qBC8E0D,EAAA;;AZnFhE;EWiBM,yBAJsC,EAAA;EAD5C;;IASQ,yBARoC,EAAA;;AApB5C;;;EAII,yBCsF4D,EAAA;;AD1FhE;;;;EAYM,qBC8E0D,EAAA;;AZnFhE;EWiBM,yBAJsC,EAAA;EAD5C;;IASQ,yBARoC,EAAA;;AApB5C;;;EAII,yBCsF4D,EAAA;;AD1FhE;;;;EAYM,qBC8E0D,EAAA;;AZnFhE;EWiBM,yBAJsC,EAAA;EAD5C;;IASQ,yBARoC,EAAA;;AApB5C;;;EAII,yBCsF4D,EAAA;;AD1FhE;;;;EAYM,qBC8E0D,EAAA;;AZnFhE;EWiBM,yBAJsC,EAAA;EAD5C;;IASQ,yBARoC,EAAA;;AApB5C;;;EAII,yBCsF4D,EAAA;;AD1FhE;;;;EAYM,qBC8E0D,EAAA;;AZnFhE;EWiBM,yBAJsC,EAAA;EAD5C;;IASQ,yBARoC,EAAA;;AApB5C;;;EAII,yBCsF4D,EAAA;;AD1FhE;;;;EAYM,sBC8E0D,EAAA;;AZnFhE;EWiBM,yBAJsC,EAAA;EAD5C;;IASQ,yBARoC,EAAA;;AApB5C;;;EAII,yBlBUmB,EAAA;;AOPvB;EWiBM,yBAJsC,EAAA;EAD5C;;IASQ,yBARoC,EAAA;;ADsF9C;EAGM,Wb1FM;Ea2FN,yBZpGY;EYqGZ,qBZmQqD,EAAA;;AYxQ3D;EAWM,cZ5GY;EY6GZ,yBZlHY;EYmHZ,kBjB3GmB,EAAA;;AiBgHzB;EACE,Wb1GU;Ea2GV,yBZpHgB,EAAA;EYkHlB;;;IAOI,qBZ+OuD,EAAA;EYtP3D;IAWI,SAAS,EAAA;EAXb;IAgBM,2CbzHM,EAAA;EGZV;IU4IM,WbhII;IaiIJ,4CbjII,EAAA;;Aa6IZ;EAOQ,cAAc;EACd,WAAW;EACX,gBAAgB;EAChB,iCAAiC,EAAA;EAVzC;IAcU,SAAS,EAAA;;AG7KnB;EACE,cAAc;EACd,WAAW;EACX,mCDuG8D;ECtG9D,yBf4XkC;ECvQ9B,eAtCY;Ec5EhB,gBfsR+B;EerR/B,gBf0R+B;EezR/B,cfDgB;EeEhB,sBhBQU;EgBPV,4BAA4B;EAC5B,yBfPgB;EONd,sBP6OgC;EgB5O9B,gFhBof4F;EgBpf5F,wEhBof4F,EAAA;EepflG;IAsBI,6BAA6B;IAC7B,SAAS,EAAA;EAvBb;IA4BI,kBAAkB;IAClB,0BfrBc,EAAA;EiBDhB;IACE,cjBAc;IiBCd,sBlBSQ;IkBRR,qBjBwdsE;IiBvdtE,UAAU;IAKR,iDtBlBiB,EAAA;EoBEvB;IAqCI,cf9Bc;IegCd,UAAU,EAAA;EAvCd;IAqCI,cf9Bc;IegCd,UAAU,EAAA;EAvCd;IAqCI,cf9Bc;IegCd,UAAU,EAAA;EAvCd;IAqCI,cf9Bc;IegCd,UAAU,EAAA;EAvCd;IAqCI,cf9Bc;IegCd,UAAU,EAAA;EAvCd;IAiDI,yBf9Cc;IegDd,UAAU,EAAA;;AAId;EAOI,cftDc;EeuDd,sBhB7CQ,EAAA;;AgBkDZ;;EAEE,cAAc;EACd,WAAW,EAAA;;AAUb;EACE,iCDwB8D;ECvB9D,oCDuB8D;ECtB9D,gBAAgB;EdlBd,kBAAW;EcoBb,gBf4M+B,EAAA;;AezMjC;EACE,+BDgB8D;ECf9D,kCDe8D;Ebe1D,kBAtCY;EcUhB,gBfyI+B,EAAA;;AetIjC;EACE,gCDS8D;ECR9D,mCDQ8D;Ebe1D,mBAtCY;EciBhB,gBfmI+B,EAAA;;Ae1HjC;EACE,cAAc;EACd,WAAW;EACX,mBAA2B;EAC3B,gBAAgB;EdQZ,eAtCY;EcgChB,gBf+K+B;Ee9K/B,cf1GgB;Ee2GhB,6BAA6B;EAC7B,yBAAyB;EACzB,mBAAmC,EAAA;EAVrC;IAcI,gBAAgB;IAChB,eAAe,EAAA;;AAYnB;EACE,kCD/B8D;ECgC9D,uBfgQiC;ECjR7B,mBAtCY;EcyDhB,gBf2F+B;EOxO7B,qBP+O+B,EAAA;;Ae9FnC;EACE,gCDvC8D;ECwC9D,oBf6PgC;ECtR5B,kBAtCY;EciEhB,gBfkF+B;EOvO7B,qBP8O+B,EAAA;;AepFnC;EAGI,YAAY,EAAA;;AAIhB;EACE,YAAY,EAAA;;AAQd;EACE,mBfsV0C,EAAA;;AenV5C;EACE,cAAc;EACd,mBfuU4C,EAAA;;Ae/T9C;EACE,oBAAa;EAAb,aAAa;EACb,eAAe;EACf,kBAA0C;EAC1C,iBAAyC,EAAA;EAJ3C;;IAQI,kBAA0C;IAC1C,iBAAyC,EAAA;;AAS7C;EACE,kBAAkB;EAClB,cAAc;EACd,qBf4S6C,EAAA;;AezS/C;EACE,kBAAkB;EAClB,kBfwS2C;EevS3C,qBfsS6C,EAAA;EezS/C;;IAQI,cfhNc,EAAA;;AeoNlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,2BAAoB;EAApB,oBAAoB;EACpB,yBAAmB;UAAnB,mBAAmB;EACnB,eAAe;EACf,qBfyR4C,EAAA;Ee7R9C;IAQI,gBAAgB;IAChB,aAAa;IACb,uBfoR4C;IenR5C,cAAc,EAAA;;AEpMhB;EACE,aAAa;EACb,WAAW;EACX,mBjB6c0C;ECpb1C,cAAW;EgBvBX,ctB3CmB,EAAA;;AsB8CrB;EACE,kBAAkB;EAClB,SAAS;EACT,UAAU;EACV,aAAa;EACb,eAAe;EACf,uBjBmyBqC;EiBlyBrC,iBAAiB;EhBoEf,mBAtCY;EgB5Bd,gBjB2O6B;EiB1O7B,WlBtCQ;EkBuCR,yCtBzDmB;EYDnB,sBP6OgC,EAAA;;AiBlNhC;;;;EAsCE,cAAc,EAAA;;AAtChB;EA4CE,qBtBtEiB;EsByEf,oCHiCwD;EGhCxD,iRHpCmI;EGqCnI,4BAA4B;EAC5B,2DAA6D;EAC7D,gEH6BwD,EAAA;EGhF5D;IAuDI,qBtBjFe;IsBkFf,iDtBlFe,EAAA;;AsB0BnB;EAiEI,oCHewD;EGdxD,kFHcwD,EAAA;;AGhF5D;EAyEE,qBtBnGiB;EsBsGf,uCHIwD;EGHxD,ujBAA8J,EAAA;EA7ElK;IAiFI,qBtB3Ge;IsB4Gf,iDtB5Ge,EAAA;;AsB0BnB;EA0FI,ctBpHe,EAAA;;AsB0BnB;;;EA+FI,cAAc,EAAA;;AA/FlB;EAuGI,ctBjIe,EAAA;EsB0BnB;IA0GM,qBtBpIa,EAAA;;AsB0BnB;EAgHM,qBAAkC;EC1IxC,yBD2I+C,EAAA;;AAjH/C;EAuHM,iDtBjJa,EAAA;;AsB0BnB;EA2HM,qBtBrJa,EAAA;;AsB0BnB;EAqII,qBtB/Je,EAAA;;AsB0BnB;EA0IM,qBtBpKa;EsBqKb,iDtBrKa,EAAA;;AsBsCrB;EACE,aAAa;EACb,WAAW;EACX,mBjB6c0C;ECpb1C,cAAW;EgBvBX,ctBxCmB,EAAA;;AsB2CrB;EACE,kBAAkB;EAClB,SAAS;EACT,UAAU;EACV,aAAa;EACb,eAAe;EACf,uBjBmyBqC;EiBlyBrC,iBAAiB;EhBoEf,mBAtCY;EgB5Bd,gBjB2O6B;EiB1O7B,WlBtCQ;EkBuCR,wCtBtDmB;EYJnB,sBP6OgC,EAAA;;AiBlNhC;;;;EAsCE,cAAc,EAAA;;AAtChB;EA4CE,qBtBnEiB;EsBsEf,oCHiCwD;EGhCxD,4UHpCmI;EGqCnI,4BAA4B;EAC5B,2DAA6D;EAC7D,gEH6BwD,EAAA;EGhF5D;IAuDI,qBtB9Ee;IsB+Ef,gDtB/Ee,EAAA;;AsBuBnB;EAiEI,oCHewD;EGdxD,kFHcwD,EAAA;;AGhF5D;EAyEE,qBtBhGiB;EsBmGf,uCHIwD;EGHxD,knBAA8J,EAAA;EA7ElK;IAiFI,qBtBxGe;IsByGf,gDtBzGe,EAAA;;AsBuBnB;EA0FI,ctBjHe,EAAA;;AsBuBnB;;;EA+FI,cAAc,EAAA;;AA/FlB;EAuGI,ctB9He,EAAA;EsBuBnB;IA0GM,qBtBjIa,EAAA;;AsBuBnB;EAgHM,qBAAkC;EC1IxC,yBD2I+C,EAAA;;AAjH/C;EAuHM,gDtB9Ia,EAAA;;AsBuBnB;EA2HM,qBtBlJa,EAAA;;AsBuBnB;EAqII,qBtB5Je,EAAA;;AsBuBnB;EA0IM,qBtBjKa;EsBkKb,gDtBlKa,EAAA;;AoBgQvB;EACE,oBAAa;EAAb,aAAa;EACb,8BAAmB;EAAnB,6BAAmB;UAAnB,mBAAmB;EACnB,yBAAmB;UAAnB,mBAAmB,EAAA;EAHrB;IASI,WAAW,EAAA;;AI5Qf;EACE,qBAAqB;EAErB,gBnB0R+B;EmBzR/B,cnBMgB;EmBLhB,kBAAkB;EAElB,sBAAsB;EACtB,eAAsD;EACtD,yBAAiB;KAAjB,sBAAiB;MAAjB,qBAAiB;UAAjB,iBAAiB;EACjB,6BAA6B;EAC7B,6BAA2C;ECuF3C,yBpB8RkC;ECvQ9B,eAtCY;EmBiBhB,gBpB8L+B;EOnS7B,sBP6OgC;EgB5O9B,6IhB6b6I;EgB7b7I,qIhB6b6I,EAAA;EEvbjJ;IiBUE,cnBNc;ImBOd,qBAAqB,EAAA;EAjBzB;IAsBI,UAAU;IACV,iDxBzBmB,EAAA;EwBEvB;IA6BI,anBoZ6B,EAAA;;AmBrYjC;;EAEE,oBAAoB,EAAA;;AASpB;ECvDA,WrBkBU;EmBlBR,yBvBFmB;EyBIrB,qBzBJqB,EAAA;EOQrB;IkBAE,WrBYQ;ImBlBR,yBEDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,WrBKQ;ImBlBR,yBEDoF;IAgBpF,qBAhByH;IAqBvH,gDAAiF,EAAA;EAKrF;IAEE,WrBTQ;IqBUR,yBzB9BmB;IyB+BnB,qBzB/BmB,EAAA;EyBsCrB;;IAGE,WrBrBQ;IqBsBR,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,gDAAiF,EAAA;;ADIvF;ECvDA,WrBkBU;EmBlBR,yBvBDmB;EyBGrB,qBzBHqB,EAAA;EOOrB;IkBAE,WrBYQ;ImBlBR,yBEDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,WrBKQ;ImBlBR,yBEDoF;IAgBpF,qBAhByH;IAqBvH,iDAAiF,EAAA;EAKrF;IAEE,WrBTQ;IqBUR,yBzB7BmB;IyB8BnB,qBzB9BmB,EAAA;EyBqCrB;;IAGE,WrBrBQ;IqBsBR,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,iDAAiF,EAAA;;ADIvF;ECvDA,WrBkBU;EmBlBR,yBvBAmB;EyBErB,qBzBFqB,EAAA;EOMrB;IkBAE,WrBYQ;ImBlBR,yBEDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,WrBKQ;ImBlBR,yBEDoF;IAgBpF,qBAhByH;IAqBvH,gDAAiF,EAAA;EAKrF;IAEE,WrBTQ;IqBUR,yBzB5BmB;IyB6BnB,qBzB7BmB,EAAA;EyBoCrB;;IAGE,WrBrBQ;IqBsBR,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,gDAAiF,EAAA;;ADIvF;ECvDA,WrBkBU;EmBlBR,yBlBuCa;EoBrCf,qBpBqCe,EAAA;EEjCf;IkBAE,WrBYQ;ImBlBR,yBEDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,WrBKQ;ImBlBR,yBEDoF;IAgBpF,qBAhByH;IAqBvH,gDAAiF,EAAA;EAKrF;IAEE,WrBTQ;IqBUR,yBpBWa;IoBVb,qBpBUa,EAAA;EoBHf;;IAGE,WrBrBQ;IqBsBR,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,gDAAiF,EAAA;;ADIvF;ECvDA,cpBUgB;EkBVd,yBvBEmB;EyBArB,qBzBAqB,EAAA;EOIrB;IkBAE,cpBIc;IkBVd,yBEDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,cpBHc;IkBVd,yBEDoF;IAgBpF,qBAhByH;IAqBvH,gDAAiF,EAAA;EAKrF;IAEE,cpBjBc;IoBkBd,yBzB1BmB;IyB2BnB,qBzB3BmB,EAAA;EyBkCrB;;IAGE,cpB7Bc;IoB8Bd,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,gDAAiF,EAAA;;ADIvF;ECvDA,WrBkBU;EmBlBR,yBvBGmB;EyBDrB,qBzBCqB,EAAA;EOGrB;IkBAE,WrBYQ;ImBlBR,yBEDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,WrBKQ;ImBlBR,yBEDoF;IAgBpF,qBAhByH;IAqBvH,iDAAiF,EAAA;EAKrF;IAEE,WrBTQ;IqBUR,yBzBzBmB;IyB0BnB,qBzB1BmB,EAAA;EyBiCrB;;IAGE,WrBrBQ;IqBsBR,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,iDAAiF,EAAA;;ADIvF;ECvDA,cpBUgB;EkBVd,yBvBImB;EyBFrB,qBzBEqB,EAAA;EOErB;IkBAE,cpBIc;IkBVd,yBEDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,cpBHc;IkBVd,yBEDoF;IAgBpF,qBAhByH;IAqBvH,iDAAiF,EAAA;EAKrF;IAEE,cpBjBc;IoBkBd,yBzBxBmB;IyByBnB,qBzBzBmB,EAAA;EyBgCrB;;IAGE,cpB7Bc;IoB8Bd,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,iDAAiF,EAAA;;ADIvF;ECvDA,WrBkBU;EmBlBR,yBvBKmB;EyBHrB,qBzBGqB,EAAA;EOCrB;IkBAE,WrBYQ;ImBlBR,yBEDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,WrBKQ;ImBlBR,yBEDoF;IAgBpF,qBAhByH;IAqBvH,iDAAiF,EAAA;EAKrF;IAEE,WrBTQ;IqBUR,yBzBvBmB;IyBwBnB,qBzBxBmB,EAAA;EyB+BrB;;IAGE,WrBrBQ;IqBsBR,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,iDAAiF,EAAA;;ADUvF;ECHA,czB5DqB;EyB6DrB,qBzB7DqB,EAAA;EOQrB;IkBwDE,WrB5CQ;IqB6CR,yBzBjEmB;IyBkEnB,qBzBlEmB,EAAA;EyBqErB;IAEE,gDzBvEmB,EAAA;EyB0ErB;IAEE,czB5EmB;IyB6EnB,6BAA6B,EAAA;EAG/B;;IAGE,WrB/DQ;IqBgER,yBzBpFmB;IyBqFnB,qBzBrFmB,EAAA;IyBuFnB;;MAKI,gDzB5Fe,EAAA;;AwB+DrB;ECHA,czB3DqB;EyB4DrB,qBzB5DqB,EAAA;EOOrB;IkBwDE,WrB5CQ;IqB6CR,yBzBhEmB;IyBiEnB,qBzBjEmB,EAAA;EyBoErB;IAEE,gDzBtEmB,EAAA;EyByErB;IAEE,czB3EmB;IyB4EnB,6BAA6B,EAAA;EAG/B;;IAGE,WrB/DQ;IqBgER,yBzBnFmB;IyBoFnB,qBzBpFmB,EAAA;IyBsFnB;;MAKI,gDzB3Fe,EAAA;;AwB8DrB;ECHA,czB1DqB;EyB2DrB,qBzB3DqB,EAAA;EOMrB;IkBwDE,WrB5CQ;IqB6CR,yBzB/DmB;IyBgEnB,qBzBhEmB,EAAA;EyBmErB;IAEE,gDzBrEmB,EAAA;EyBwErB;IAEE,czB1EmB;IyB2EnB,6BAA6B,EAAA;EAG/B;;IAGE,WrB/DQ;IqBgER,yBzBlFmB;IyBmFnB,qBzBnFmB,EAAA;IyBqFnB;;MAKI,gDzB1Fe,EAAA;;AwB6DrB;ECHA,cpBnBe;EoBoBf,qBpBpBe,EAAA;EEjCf;IkBwDE,WrB5CQ;IqB6CR,yBpBxBa;IoByBb,qBpBzBa,EAAA;EoB4Bf;IAEE,gDpB9Ba,EAAA;EoBiCf;IAEE,cpBnCa;IoBoCb,6BAA6B,EAAA;EAG/B;;IAGE,WrB/DQ;IqBgER,yBpB3Ca;IoB4Cb,qBpB5Ca,EAAA;IoB8Cb;;MAKI,gDpBnDS,EAAA;;AmBsBf;ECHA,czBxDqB;EyByDrB,qBzBzDqB,EAAA;EOIrB;IkBwDE,cpBpDc;IoBqDd,yBzB7DmB;IyB8DnB,qBzB9DmB,EAAA;EyBiErB;IAEE,gDzBnEmB,EAAA;EyBsErB;IAEE,czBxEmB;IyByEnB,6BAA6B,EAAA;EAG/B;;IAGE,cpBvEc;IoBwEd,yBzBhFmB;IyBiFnB,qBzBjFmB,EAAA;IyBmFnB;;MAKI,gDzBxFe,EAAA;;AwB2DrB;ECHA,czBvDqB;EyBwDrB,qBzBxDqB,EAAA;EOGrB;IkBwDE,WrB5CQ;IqB6CR,yBzB5DmB;IyB6DnB,qBzB7DmB,EAAA;EyBgErB;IAEE,+CzBlEmB,EAAA;EyBqErB;IAEE,czBvEmB;IyBwEnB,6BAA6B,EAAA;EAG/B;;IAGE,WrB/DQ;IqBgER,yBzB/EmB;IyBgFnB,qBzBhFmB,EAAA;IyBkFnB;;MAKI,+CzBvFe,EAAA;;AwB0DrB;ECHA,czBtDqB;EyBuDrB,qBzBvDqB,EAAA;EOErB;IkBwDE,cpBpDc;IoBqDd,yBzB3DmB;IyB4DnB,qBzB5DmB,EAAA;EyB+DrB;IAEE,iDzBjEmB,EAAA;EyBoErB;IAEE,czBtEmB;IyBuEnB,6BAA6B,EAAA;EAG/B;;IAGE,cpBvEc;IoBwEd,yBzB9EmB;IyB+EnB,qBzB/EmB,EAAA;IyBiFnB;;MAKI,iDzBtFe,EAAA;;AwByDrB;ECHA,czBrDqB;EyBsDrB,qBzBtDqB,EAAA;EOCrB;IkBwDE,WrB5CQ;IqB6CR,yBzB1DmB;IyB2DnB,qBzB3DmB,EAAA;EyB8DrB;IAEE,8CzBhEmB,EAAA;EyBmErB;IAEE,czBrEmB;IyBsEnB,6BAA6B,EAAA;EAG/B;;IAGE,WrB/DQ;IqBgER,yBzB7EmB;IyB8EnB,qBzB9EmB,EAAA;IyBgFnB;;MAKI,8CzBrFe,EAAA;;AwBmEvB;EACE,gBnBoN+B;EmBnN/B,cxB5EqB;EwB6ErB,qBnBkG4C,EAAA;EEvK5C;IiBwEE,cnBgG8D;ImB/F9D,0BnBgG+C,EAAA;EmBvGnD;IAYI,0BnB2F+C;ImB1F/C,gBAAgB,EAAA;EAbpB;IAkBI,cnBnFc;ImBoFd,oBAAoB,EAAA;;AAWxB;ECJE,oBpB6SgC;ECtR5B,kBAtCY;EmBiBhB,gBpBkI+B;EOvO7B,qBP8O+B,EAAA;;AmBnInC;ECRE,uBpBwSiC;ECjR7B,mBAtCY;EmBiBhB,gBpBmI+B;EOxO7B,qBP+O+B,EAAA;;AmB3HnC;EACE,cAAc;EACd,WAAW,EAAA;EAFb;IAMI,kBnB6T+B,EAAA;;AmBxTnC;;;EAII,WAAW,EAAA;;AExIf;ELMM,wChB8P2C;EgB9P3C,gChB8P2C,EAAA;EqBpQjD;IAII,UAAU,EAAA;;AAId;EAEI,aAAa,EAAA;;AAIjB;EACE,kBAAkB;EAClB,SAAS;EACT,gBAAgB;ELXZ,qChB+PwC;EgB/PxC,6BhB+PwC,EAAA;;AsBpQ9C;;;;EAIE,kBAAkB,EAAA;;AAGpB;EACE,mBAAmB,EAAA;ECoBjB;IACE,qBAAqB;IACrB,oBvBkO0C;IuBjO1C,uBvBgO0C;IuB/N1C,WAAW;IAhCf,uBAA8B;IAC9B,qCAA4C;IAC5C,gBAAgB;IAChB,oCAA2C,EAAA;EAqDzC;IACE,cAAc,EAAA;;AD1CpB;EACE,kBAAkB;EAClB,SAAS;EACT,OAAO;EACP,atB4pBsC;EsB3pBtC,aAAa;EACb,WAAW;EACX,gBtBkuBuC;EsBjuBvC,iBAA8B;EAC9B,oBAA4B;ErBsGxB,eAtCY;EqB9DhB,ctBXgB;EsBYhB,gBAAgB;EAChB,gBAAgB;EAChB,sBvBNU;EuBOV,4BAA4B;EAC5B,qCvBnBU;EQRR,sBP6OgC,EAAA;;AsBzMhC;EACE,WAAW;EACX,OAAO,EAAA;;AAGT;EACE,QAAQ;EACR,UAAU,EAAA;;AAOhB;EAEI,SAAS;EACT,YAAY;EACZ,aAAa;EACb,uBtB+rBuC,EAAA;;AuB9tBvC;EACE,qBAAqB;EACrB,oBvBkO0C;EuBjO1C,uBvBgO0C;EuB/N1C,WAAW;EAzBf,aAAa;EACb,qCAA4C;EAC5C,0BAAiC;EACjC,oCAA2C,EAAA;;AA8CzC;EACE,cAAc,EAAA;;ADUpB;EAEI,MAAM;EACN,WAAW;EACX,UAAU;EACV,aAAa;EACb,qBtBirBuC,EAAA;;AuB9tBvC;EACE,qBAAqB;EACrB,oBvBkO0C;EuBjO1C,uBvBgO0C;EuB/N1C,WAAW;EAlBf,mCAA0C;EAC1C,eAAe;EACf,sCAA6C;EAC7C,wBAA+B,EAAA;;AAuC7B;EACE,cAAc,EAAA;;AA7BhB;EDmDE,iBAAiB,EAAA;;AAKvB;EAEI,MAAM;EACN,WAAW;EACX,UAAU;EACV,aAAa;EACb,sBtBgqBuC,EAAA;;AuB9tBvC;EACE,qBAAqB;EACrB,oBvBkO0C;EuBjO1C,uBvBgO0C;EuB/N1C,WAAW,EAAA;;AAJb;EAgBI,aAAa,EAAA;;AAGf;EACE,qBAAqB;EACrB,qBvB+MwC;EuB9MxC,uBvB6MwC;EuB5MxC,WAAW;EA9BjB,mCAA0C;EAC1C,yBAAgC;EAChC,sCAA6C,EAAA;;AAiC3C;EACE,cAAc,EAAA;;AAVd;EDiDA,iBAAiB,EAAA;;AAOvB;EAKI,WAAW;EACX,YAAY,EAAA;;AAKhB;EE9GE,SAAS;EACT,gBAAmB;EACnB,gBAAgB;EAChB,6BxBCgB,EAAA;;AsBiHlB;EACE,cAAc;EACd,WAAW;EACX,uBtBopBwC;EsBnpBxC,WAAW;EACX,gBtBoK+B;EsBnK/B,ctBhHgB;EsBiHhB,mBAAmB;EACnB,mBAAmB;EACnB,6BAA6B;EAC7B,SAAS,EAAA;EpBpHT;IoBmIE,ctBqnBqD;IsBpnBrD,qBAAqB;IJ9IrB,yBlBEc,EAAA;EsBkHlB;IAgCI,WvBlIQ;IuBmIR,qBAAqB;IJrJrB,yBvBFmB,EAAA;E2BsHvB;IAuCI,ctBpJc;IsBqJd,oBAAoB;IACpB,6BAA6B,EAAA;;AAQjC;EACE,cAAc,EAAA;;AAIhB;EACE,cAAc;EACd,sBtB+lBwC;EsB9lBxC,gBAAgB;ErBpDZ,mBAtCY;EqB4FhB,ctBxKgB;EsByKhB,mBAAmB,EAAA;;AAIrB;EACE,cAAc;EACd,uBtBqlBwC;EsBplBxC,ctB7KgB,EAAA;;AyBblB;;EAEE,kBAAkB;EAClB,2BAAoB;EAApB,oBAAoB;EACpB,sBAAsB,EAAA;EAJxB;;IAOI,kBAAkB;IAClB,mBAAc;YAAd,cAAc,EAAA;IvBChB;;MuBII,UAAU,EAAA;IAbhB;;;;MAkBM,UAAU,EAAA;;AAMhB;EACE,oBAAa;EAAb,aAAa;EACb,eAAe;EACf,uBAA2B;UAA3B,2BAA2B,EAAA;EAH7B;IAMI,WAAW,EAAA;;AAIf;;EAII,iBzBsM6B,EAAA;;AyB1MjC;;ElBhBI,0BkB0B8B;ElBzB9B,6BkByB8B,EAAA;;AAVlC;;ElBFI,yBkBiB6B;ElBhB7B,4BkBgB6B,EAAA;;AAgBjC;EACE,wBAAmC;EACnC,uBAAkC,EAAA;EAFpC;;;IAOI,cAAc,EAAA;EAGhB;IACE,eAAe,EAAA;;AAInB;EACE,uBAAsC;EACtC,sBAAqC,EAAA;;AAGvC;EACE,sBAAsC;EACtC,qBAAqC,EAAA;;AAoBvC;EACE,4BAAsB;EAAtB,6BAAsB;UAAtB,sBAAsB;EACtB,wBAAuB;UAAvB,uBAAuB;EACvB,wBAAuB;UAAvB,uBAAuB,EAAA;EAHzB;;IAOI,WAAW,EAAA;EAPf;;IAYI,gBzBqH6B,EAAA;EyBjIjC;;IlBlFI,6BkBoG+B;IlBnG/B,4BkBmG+B,EAAA;EAlBnC;;IlBhGI,yBkBuH4B;IlBtH5B,0BkBsH4B,EAAA;;AAiBhC;;EAGI,gBAAgB,EAAA;EAHpB;;;;IAOM,kBAAkB;IAClB,sBAAsB;IACtB,oBAAoB,EAAA;;ACzJ1B;EACE,kBAAkB;EAClB,oBAAa;EAAb,aAAa;EACb,eAAe;EACf,0BAAoB;UAApB,oBAAoB;EACpB,WAAW,EAAA;EALb;;;;IAWI,kBAAkB;IAClB,mBAAY;YAAZ,YAAY;IACZ,YAAY;IACZ,gBAAgB,EAAA;IAdpB;;;;;;;;;;;;MAmBM,iB1BsN2B,EAAA;E0BzOjC;;;IA2BI,UAAU,EAAA;EA3Bd;IAgCI,UAAU,EAAA;EAhCd;;InBeI,0BmBsBmD;InBrBnD,6BmBqBmD,EAAA;EArCvD;;InB6BI,yBmBSmD;InBRnD,4BmBQmD,EAAA;EAtCvD;IA4CI,oBAAa;IAAb,aAAa;IACb,yBAAmB;YAAnB,mBAAmB,EAAA;IA7CvB;;MnBeI,0BmBiC6E;MnBhC7E,6BmBgC6E,EAAA;IAhDjF;MnB6BI,yBmBoBsE;MnBnBtE,4BmBmBsE,EAAA;;AAW1E;;EAEE,oBAAa;EAAb,aAAa,EAAA;EAFf;;IAQI,kBAAkB;IAClB,UAAU,EAAA;IATd;;MAYM,UAAU,EAAA;EAZhB;;;;;;;;IAoBI,iB1ByJ6B,EAAA;;A0BrJjC;EAAuB,kB1BqJU,EAAA;;A0BpJjC;EAAsB,iB1BoJW,EAAA;;A0B5IjC;EACE,oBAAa;EAAb,aAAa;EACb,yBAAmB;UAAnB,mBAAmB;EACnB,yB1BgSkC;E0B/RlC,gBAAgB;EzBwBZ,eAtCY;EyBgBhB,gB1B0L+B;E0BzL/B,gB1B8L+B;E0B7L/B,c1B7FgB;E0B8FhB,kBAAkB;EAClB,mBAAmB;EACnB,yB1BrGgB;E0BsGhB,yB1BpGgB;EONd,sBP6OgC,EAAA;E0B/IpC;;IAkBI,aAAa,EAAA;;AAUjB;;EAEE,gCZjB8D,EAAA;;AYoBhE;;;;;;EAME,oB1B2QgC;ECtR5B,kBAtCY;EyBmDhB,gB1BgG+B;EOvO7B,qBP8O+B,EAAA;;A0BnGnC;;EAEE,kCZlC8D,EAAA;;AYqChE;;;;;;EAME,uB1BqPiC;ECjR7B,mBAtCY;EyBoEhB,gB1BgF+B;EOxO7B,qBP+O+B,EAAA;;A0BnFnC;;EAEE,sBAA0E,EAAA;;AAW5E;;;;;;EnBzJI,0BmB+J4B;EnB9J5B,6BmB8J4B,EAAA;;AAGhC;;;;;;EnBpJI,yBmB0J2B;EnBzJ3B,4BmByJ2B,EAAA;;ACpL/B;EACE,kBAAkB;EAClB,cAAc;EACd,kBAA+C;EAC/C,oBAAqE,EAAA;;AAGvE;EACE,2BAAoB;EAApB,oBAAoB;EACpB,kB3B6f0C,EAAA;;A2B1f5C;EACE,kBAAkB;EAClB,OAAO;EACP,WAAW;EACX,W3Byf0C;E2Bxf1C,eAAkF;EAClF,UAAU,EAAA;EANZ;IASI,W5BNQ;I4BOR,qBhC3BmB;IuBEnB,yBvBFmB,EAAA;EgCiBvB;IAoBM,iDhCrCiB,EAAA;EgCiBvB;IAyBI,qB3B0bsE,EAAA;E2Bnd1E;IA6BI,W5B1BQ;I4B2BR,yB3Bsf8E;I2Brf9E,qB3Bqf8E,EAAA;E2BphBlF;IAuCM,c3B/CY,EAAA;I2BQlB;MA0CQ,yB3BtDU,EAAA;;A2BgElB;EACE,kBAAkB;EAClB,gBAAgB;EAEhB,mBAAmB,EAAA;EAJrB;IASI,kBAAkB;IAClB,YAA+E;IAC/E,aAA+D;IAC/D,cAAc;IACd,W3B4bwC;I2B3bxC,Y3B2bwC;I2B1bxC,oBAAoB;IACpB,WAAW;IACX,sB5BlEQ;I4BmER,yB3BoJ6B,EAAA;E2BtKjC;IAwBI,kBAAkB;IAClB,YAA+E;IAC/E,aAA+D;IAC/D,cAAc;IACd,W3B6awC;I2B5axC,Y3B4awC;I2B3axC,WAAW;IACX,mCAAgE,EAAA;;AASpE;EpB5GI,sBP6OgC,EAAA;;A2BjIpC;EAOM,kOb5EqI,EAAA;;AaqE3I;EAaM,qBhC1HiB;EuBEnB,yBvBFmB,EAAA;;AgC6GvB;EAkBM,+KbvFqI,EAAA;;AaqE3I;EAwBM,yChCrIiB,EAAA;;AgC6GvB;EA2BM,yChCxIiB,EAAA;;AgCiJvB;EAGI,kB3B8Z+C,EAAA;;A2BjanD;EAQM,8KbjHqI,EAAA;;AayG3I;EAcM,yChC/JiB,EAAA;;AgCyKvB;EACE,qBAA2D,EAAA;EAD7D;IAKM,cAAqD;IACrD,c3BsY+E;I2BrY/E,mBAAmB;IAEnB,qB3BoY4E,EAAA;E2B7YlF;IAaM,wBb1E0D;Ia2E1D,0Bb3E0D;Ia4E1D,uBbxD0D;IayD1D,wBbzD0D;Ia0D1D,yB3BlLY;I2BoLZ,qB3B0X4E;IgBpjB5E,yJhBsgB+H;IgBtgB/H,iJhBsgB+H;IgBtgB/H,yIhBsgB+H;IgBtgB/H,8KhBsgB+H,EAAA;E2B/VrI;IA0BM,sB5B/KM;I4BgLN,sCAA4E;YAA5E,8BAA4E,EAAA;EA3BlF;IAiCM,yChC1MiB,EAAA;;AgCsNvB;EACE,qBAAqB;EACrB,WAAW;EACX,mCb7G8D;Ea8G9D,0C3BwKkC;ECvQ9B,eAtCY;E0BwIhB,gB3BkE+B;E2BjE/B,gB3BsE+B;E2BrE/B,c3BrNgB;E2BsNhB,sBAAsB;EACtB,uO3BuW+I;E2BtW/I,yB3B3NgB;EONd,sBP6OgC;E2BTlC,wBAAgB;KAAhB,qBAAgB;UAAhB,gBAAgB,EAAA;EAflB;IAkBI,qB3B4PsE;I2B3PtE,UAAU;IAIR,iDhC7OiB,EAAA;IgCsNvB;MAgCM,c3B5OY;M2B6OZ,sB5BnOM,EAAA;E4BkMZ;IAuCI,YAAY;IACZ,sB3BoIgC;I2BnIhC,sBAAsB,EAAA;EAzC1B;IA6CI,c3B1Pc;I2B2Pd,yB3B/Pc,EAAA;E2BiNlB;IAmDI,aAAa,EAAA;EAnDjB;IAwDI,kBAAkB;IAClB,0B3BrQc,EAAA;;A2ByQlB;EACE,kCbxK8D;EayK9D,oB3BsHkC;E2BrHlC,uB3BqHkC;E2BpHlC,oB3BqHiC;ECjR7B,mBAtCY,EAAA;;A0BsMlB;EACE,gCbhL8D;EaiL9D,mB3BmHiC;E2BlHjC,sB3BkHiC;E2BjHjC,kB3BkHgC;ECtR5B,kBAtCY,EAAA;;A0BmNlB;EACE,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,mCbhM8D;EaiM9D,gBAAgB,EAAA;;AAGlB;EACE,kBAAkB;EAClB,UAAU;EACV,WAAW;EACX,mCbxM8D;EayM9D,SAAS;EACT,UAAU,EAAA;EANZ;IASI,qB3B2KsE;I2B1KtE,iDhC1TmB,EAAA;EgCgTvB;;IAgBI,yB3B3Tc,EAAA;E2B2SlB;IAqBM,iB3BkUQ,EAAA;E2BvVd;IA0BI,0BAA0B,EAAA;;AAI9B;EACE,kBAAkB;EAClB,MAAM;EACN,QAAQ;EACR,OAAO;EACP,UAAU;EACV,mCbxO8D;EayO9D,yB3B6CkC;E2B3ClC,gB3BxD+B;E2ByD/B,gB3BpD+B;E2BqD/B,c3B/UgB;E2BgVhB,sB5BtUU;E4BuUV,yB3BpVgB;EONd,sBP6OgC,EAAA;E2BgGpC;IAkBI,kBAAkB;IAClB,MAAM;IACN,QAAQ;IACR,SAAS;IACT,UAAU;IACV,cAAc;IACd,6Bb1P4D;Ia2P5D,yB3B2BgC;I2B1BhC,gB3BpE6B;I2BqE7B,c3B/Vc;I2BgWd,iBAAiB;ITxWjB,yBlBGc;I2BuWd,oBAAoB;IpB3WpB,kCoB4WgF,EAAA;;AAUpF;EACE,WAAW;EACX,cbhR2B;EaiR3B,UAAU;EACV,6BAA6B;EAC7B,wBAAgB;KAAhB,qBAAgB;UAAhB,gBAAgB,EAAA;EALlB;IAQI,aAAa,EAAA;IARjB;MAY8B,iEhCnYP,EAAA;IgCuXvB;MAa8B,iEhCpYP,EAAA;IgCuXvB;MAc8B,iEhCrYP,EAAA;EgCuXvB;IAkBI,SAAS,EAAA;EAlBb;IAsBI,W3B2N6C;I2B1N7C,Y3B0N6C;I2BzN7C,oBAAyE;IT7YzE,yBvBFmB;IgCiZnB,S3B0N0C;IO1mB1C,mBP2mB6C;IgB1mB3C,oHhBsgB+H;IgBtgB/H,4GhBsgB+H;I2BnHjI,wBAAgB;YAAhB,gBAAgB,EAAA;IA9BpB;MTrXI,yBlB8mB2E,EAAA;E2BzP/E;IAsCI,W3BoMoC;I2BnMpC,c3BoMqC;I2BnMrC,kBAAkB;IAClB,e3BmMuC;I2BlMvC,yB3B3Zc;I2B4Zd,yBAAyB;IpBjazB,mBPomBoC,EAAA;E2B9OxC;IAiDI,W3BgM6C;I2B/L7C,Y3B+L6C;IkBtmB7C,yBvBFmB;IgC2anB,S3BgM0C;IO1mB1C,mBP2mB6C;IgB1mB3C,iHhBsgB+H;IgBtgB/H,4GhBsgB+H;I2BzFjI,qBAAgB;SAAhB,gBAAgB,EAAA;IAxDpB;MTrXI,yBlB8mB2E,EAAA;E2BzP/E;IAgEI,W3B0KoC;I2BzKpC,c3B0KqC;I2BzKrC,kBAAkB;IAClB,e3ByKuC;I2BxKvC,yB3Brbc;I2Bsbd,yBAAyB;IpB3bzB,mBPomBoC,EAAA;E2B9OxC;IA2EI,W3BsK6C;I2BrK7C,Y3BqK6C;I2BpK7C,aAAa;IACb,oB3B9D+B;I2B+D/B,mB3B/D+B;IkBrY/B,yBvBFmB;IgCwcnB,S3BmK0C;IO1mB1C,mBP2mB6C;IgB1mB3C,gHhBsgB+H;IgBtgB/H,4GhBsgB+H;I2B5DjI,gBAAgB,EAAA;IArFpB;MTrXI,yBlB8mB2E,EAAA;E2BzP/E;IA6FI,W3B6IoC;I2B5IpC,c3B6IqC;I2B5IrC,kBAAkB;IAClB,e3B4IuC;I2B3IvC,6BAA6B;IAC7B,yBAAyB;IACzB,oBAA4C,EAAA;EAnGhD;IAwGI,yB3Bzdc;IOLd,mBPomBoC,EAAA;E2B9OxC;IA6GI,kBAAkB;IAClB,yB3B/dc;IOLd,mBPomBoC,EAAA;E2B9OxC;IAoHM,yB3BneY,EAAA;E2B+WlB;IAwHM,eAAe,EAAA;EAxHrB;IA4HM,yB3B3eY,EAAA;E2B+WlB;IAgIM,eAAe,EAAA;EAhIrB;IAoIM,yB3BnfY,EAAA;;A2BwflB;;;EX9fM,oHhBsgB+H;EgBtgB/H,4GhBsgB+H,EAAA;;A4BvgBrI;EACE,oBAAa;EAAb,aAAa;EACb,eAAe;EACf,eAAe;EACf,gBAAgB;EAChB,gBAAgB,EAAA;;AAGlB;EACE,cAAc;EACd,oB5B6qBsC,EAAA;EE5qBtC;I0BEE,qBAAqB,EAAA;EALzB;IAUI,c5BVc;I4BWd,oBAAoB;IACpB,eAAe,EAAA;;AAQnB;EACE,gC5BxBgB,EAAA;E4BuBlB;IAII,mB5B0M6B,EAAA;E4B9MjC;IAQI,6BAAgD;IrB3BhD,+BPoOgC;IOnOhC,gCPmOgC,EAAA;IElOlC;M0B6BI,qC5BnCY,EAAA;I4BuBlB;MAgBM,c5BpCY;M4BqCZ,6BAA6B;MAC7B,yBAAyB,EAAA;EAlB/B;;IAwBI,c5B3Cc;I4B4Cd,sB7BlCQ;I6BmCR,kC7BnCQ,EAAA;E6BSZ;IA+BI,gB5B+K6B;IOjO7B,yBqBoD4B;IrBnD5B,0BqBmD4B,EAAA;;AAShC;ErBtEI,sBP6OgC,EAAA;;A4BvKpC;;EAOI,W7B1DQ;E6B2DR,yBjC/EmB,EAAA;;AiCwFvB;EAEI,mBAAc;UAAd,cAAc;EACd,kBAAkB,EAAA;;AAItB;EAEI,aAAa;EACb,mBAAY;UAAZ,YAAY;EACZ,kBAAkB,EAAA;;AAStB;EAEI,aAAa,EAAA;;AAFjB;EAKI,cAAc,EAAA;;ACpGlB;EACE,kBAAkB;EAClB,oBAAa;EAAb,aAAa;EACb,eAAe;EACf,yBAAmB;UAAnB,mBAAmB;EACnB,yBAA8B;UAA9B,8BAA8B;EAC9B,oB7BiHW,EAAA;E6BvHb;;IAWI,oBAAa;IAAb,aAAa;IACb,eAAe;IACf,yBAAmB;YAAnB,mBAAmB;IACnB,yBAA8B;YAA9B,8BAA8B,EAAA;;AAoBlC;EACE,qBAAqB;EACrB,sB7BqqB+E;E6BpqB/E,yB7BoqB+E;E6BnqB/E,kB7BiFW;ECTP,kBAtCY;E4BhChB,oBAAoB;EACpB,mBAAmB,EAAA;E3B1CnB;I2B6CE,qBAAqB,EAAA;;AASzB;EACE,oBAAa;EAAb,aAAa;EACb,4BAAsB;EAAtB,6BAAsB;UAAtB,sBAAsB;EACtB,eAAe;EACf,gBAAgB;EAChB,gBAAgB,EAAA;EALlB;IAQI,gBAAgB;IAChB,eAAe,EAAA;EATnB;IAaI,gBAAgB;IAChB,WAAW,EAAA;;AASf;EACE,qBAAqB;EACrB,mB7B4lBuC;E6B3lBvC,sB7B2lBuC,EAAA;;A6B/kBzC;EACE,gBAAgB;EAChB,mBAAY;UAAZ,YAAY;EAGZ,yBAAmB;UAAnB,mBAAmB,EAAA;;AAIrB;EACE,wB7BumBwC;EC9lBpC,kBAtCY;E4B+BhB,cAAc;EACd,6BAA6B;EAC7B,6BAAuC;EtBrHrC,sBP6OgC,EAAA;EElOlC;I2B8GE,qBAAqB,EAAA;;AAMzB;EACE,qBAAqB;EACrB,YAAY;EACZ,aAAa;EACb,sBAAsB;EACtB,WAAW;EACX,mCAAmC;EACnC,0BAA0B,EAAA;;AAK5B;EAyBQ,8BAAqB;EAArB,6BAAqB;UAArB,qBAAqB;EACrB,uBAA2B;UAA3B,2BAA2B,EAAA;EA1BnC;;IAQU,gBAAgB;IAChB,eAAe,EAAA;EATzB;IA6BU,8BAAmB;IAAnB,6BAAmB;YAAnB,mBAAmB,EAAA;IA7B7B;MAgCY,kBAAkB,EAAA;IAhC9B;MAoCY,qB7BgiB6B;M6B/hB7B,oB7B+hB6B,EAAA;E6BpkBzC;;IA2CU,iBAAiB,EAAA;EA3C3B;IA0DU,+BAAwB;IAAxB,wBAAwB;IAGxB,gBAAgB,EAAA;EA7D1B;IAiEU,aAAa,EAAA;;AAavB;EAEI,yB9BnNQ,EAAA;EGGV;I2BmNI,yB9BtNM,EAAA;;A8BiNZ;EAWM,yB9B5NM,EAAA;EGGV;I2B4NM,yB9B/NI,EAAA;E8BiNZ;IAkBQ,yB9BnOI,EAAA;;A8BiNZ;;;;EA0BM,yB9B3OM,EAAA;;A8BiNZ;EA+BI,yB9BhPQ;E8BiPR,gC9BjPQ,EAAA;;A8BiNZ;EAoCI,+QftNuI,EAAA;;AekL3I;EAwCI,yB9BzPQ,EAAA;E8BiNZ;IA0CM,yB9B3PM,EAAA;IGGV;M2B2PM,yB9B9PI,EAAA;;A8BqQZ;EAEI,W9B5PQ,EAAA;EGRV;I2BuQI,W9B/PM,EAAA;;A8B0PZ;EAWM,+B9BrQM,EAAA;EGRV;I2BgRM,gC9BxQI,EAAA;E8B0PZ;IAkBQ,gC9B5QI,EAAA;;A8B0PZ;;;;EA0BM,W9BpRM,EAAA;;A8B0PZ;EA+BI,+B9BzRQ;E8B0RR,sC9B1RQ,EAAA;;A8B0PZ;EAoCI,qRf1QuI,EAAA;;AesO3I;EAwCI,+B9BlSQ,EAAA;E8B0PZ;IA0CM,W9BpSM,EAAA;IGRV;M2B+SM,W9BvSI,EAAA;;A+BpBZ;EACE,kBAAkB;EAClB,oBAAa;EAAb,aAAa;EACb,4BAAsB;EAAtB,6BAAsB;UAAtB,sBAAsB;EACtB,YAAY;EAEZ,qBAAqB;EACrB,sB/BaU;E+BZV,2BAA2B;EAC3B,sC/BAU;EQRR,sBP6OgC,EAAA;E8B9OpC;IAaI,eAAe;IACf,cAAc,EAAA;EAdlB;IvBUI,+BPoOgC;IOnOhC,gCPmOgC,EAAA;E8B9OpC;IvBwBI,mCPsNgC;IOrNhC,kCPqNgC,EAAA;;A8BhNpC;EAGE,mBAAc;UAAd,cAAc;EAGd,eAAe;EACf,gB9BsxByC,EAAA;;A8BlxB3C;EACE,sB9BgxBwC,EAAA;;A8B7wB1C;EACE,qBAA+B;EAC/B,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;A5B3ChB;E4BgDE,qBAAqB,EAAA;;AAFzB;EAMI,oB9B+vBuC,EAAA;;A8BvvB3C;EACE,wB9BsvByC;E8BrvBzC,gBAAgB;EAEhB,uBnC9DiB;EmC+DjB,6C/BhEU,EAAA;E+B2DZ;IvBnEI,0DuB2E8E,EAAA;EARlF;IAaM,aAAa,EAAA;;AAKnB;EACE,wB9BouByC;E8BnuBzC,uBnC9EiB;EmC+EjB,0C/BhFU,EAAA;E+B6EZ;IvBrFI,0DO+H4D,EAAA;;AgB3BhE;EACE,uBAAiC;EACjC,uB9BmtBwC;E8BltBxC,sBAAgC;EAChC,gBAAgB,EAAA;;AAGlB;EACE,uBAAiC;EACjC,sBAAgC,EAAA;;AAIlC;EACE,kBAAkB;EAClB,MAAM;EACN,QAAQ;EACR,SAAS;EACT,OAAO;EACP,gB9B8sByC,EAAA;;A8B3sB3C;;;EAGE,cAAc;EACd,WAAW,EAAA;;AAGb;;EvBxHI,2COsH4D;EPrH5D,4COqH4D,EAAA;;AgBOhE;;EvB/GI,+COwG4D;EPvG5D,8COuG4D,EAAA;;AgBehE;EAEI,mB9BurBsD,EAAA;;A8B/pB1D;EAII,mB9B2pBsD,EAAA;;A8BhmB1D;EAEI,sB9BglBsC,EAAA;;A8B3jB1C;EAEI,gBAAgB,EAAA;EAFpB;IAKM,gBAAgB;IvB5OlB,6BuB6OiC;IvB5OjC,4BuB4OiC,EAAA;EANrC;IvBrPI,yBuB+P8B;IvB9P9B,0BuB8P8B,EAAA;EAVlC;IvB9PI,gBuB4Q0B;IACxB,mB9BnC2B,EAAA;;A+B/OjC;EACE,oBAAa;EAAb,aAAa;EACb,eAAe;EACf,qB/B+hCsC;E+B9hCtC,mB/BiiCsC;E+B/hCtC,gBAAgB;EAChB,yBpCkBqB;EYpBnB,sBP6OgC,EAAA;;A+BvOpC;EAGI,oB/BqhCqC,EAAA;E+BxhCzC;IAMM,qBAAqB;IACrB,qB/BihCmC;I+BhhCnC,c/BNY;I+BOZ,Y/BshCuC,EAAA;;A+B/hC7C;EAoBI,0BAA0B,EAAA;;AApB9B;EAwBI,qBAAqB,EAAA;;AAxBzB;EA4BI,c/B1Bc,EAAA;;AgCblB;EACE,oBAAa;EAAb,aAAa;E5BGb,eAAe;EACf,gBAAgB;EGAd,sBP6OgC,EAAA;;AgC5OpC;EACE,kBAAkB;EAClB,cAAc;EACd,uBhCgxBwC;EgC/wBxC,iBhCqO+B;EgCpO/B,iBhCmxBsC;EgClxBtC,crCRqB;EqCSrB,sBjCWU;EiCVV,yBhCJgB,EAAA;EgCJlB;IAWI,UAAU;IACV,chCkK8D;IgCjK9D,qBAAqB;IACrB,yBhCXc;IgCYd,qBhCXc,EAAA;EgCJlB;IAmBI,UAAU;IACV,UhC4wBiC;IgC3wBjC,iDrCvBmB,EAAA;;AqC2BvB;EAGM,cAAc;EzBChB,+BP+MgC;EO9MhC,kCP8MgC,EAAA;;AgCnNpC;EzBVI,gCP6NgC;EO5NhC,mCP4NgC,EAAA;;AgCnNpC;EAcI,UAAU;EACV,WjCtBQ;EiCuBR,yBrC3CmB;EqC4CnB,qBrC5CmB,EAAA;;AqC2BvB;EAqBI,chCvCc;EgCwCd,oBAAoB;EAEpB,YAAY;EACZ,sBjChCQ;EiCiCR,qBhC/Cc,EAAA;;AiCPhB;EACE,uBjCyxBsC;EC9pBpC,kBAtCY;EgCnFd,gBjCsO6B,EAAA;;AiCjO3B;E1BwBF,8BPgN+B;EO/M/B,iCP+M+B,EAAA;;AiCnO7B;E1BKF,+BP8N+B;EO7N/B,kCP6N+B,EAAA;;AiChPjC;EACE,uBjCuxBqC;EC5pBnC,mBAtCY;EgCnFd,gBjCuO6B,EAAA;;AiClO3B;E1BwBF,8BPiN+B;EOhN/B,iCPgN+B,EAAA;;AiCpO7B;E1BKF,+BP+N+B;EO9N/B,kCP8N+B,EAAA;;AkC/OnC;EACE,qBAAqB;EACrB,qBlCw5BsC;ECv1BpC,cAAW;EiC/Db,gBlC2R+B;EkC1R/B,cAAc;EACd,kBAAkB;EAClB,mBAAmB;EACnB,wBAAwB;E3BRtB,sBP6OgC;EgB5O9B,6IhB6b6I;EgB7b7I,qIhB6b6I,EAAA;EEnbjJ;IgCGI,qBAAqB,EAAA;EAd3B;IAoBI,aAAa,EAAA;;AAKjB;EACE,kBAAkB;EAClB,SAAS,EAAA;;AAOX;EACE,oBlC63BsC;EkC53BtC,mBlC43BsC;EOh6BpC,oBPm6BqC,EAAA;;AkCt3BvC;ECjDA,WpCuBU;EoCtBV,yBxCEqB,EAAA;EOYrB;IiCVI,WpCkBM;IoCjBN,yBAAkC,EAAA;EAH9B;IAQJ,UAAU;IACV,gDxCTiB,EAAA;;AuC8CrB;ECjDA,WpCuBU;EoCtBV,yBxCGqB,EAAA;EOWrB;IiCVI,WpCkBM;IoCjBN,yBAAkC,EAAA;EAH9B;IAQJ,UAAU;IACV,gDxCRiB,EAAA;;AuC6CrB;ECjDA,WpCuBU;EoCtBV,yBxCIqB,EAAA;EOUrB;IiCVI,WpCkBM;IoCjBN,yBAAkC,EAAA;EAH9B;IAQJ,UAAU;IACV,gDxCPiB,EAAA;;AuC4CrB;ECjDA,WpCuBU;EoCtBV,yBnC2Ce,EAAA;EE7Bf;IiCVI,WpCkBM;IoCjBN,yBAAkC,EAAA;EAH9B;IAQJ,UAAU;IACV,gDnCgCW,EAAA;;AkCKf;ECjDA,cnCegB;EmCdhB,yBxCMqB,EAAA;EOQrB;IiCVI,cnCUY;ImCTZ,yBAAkC,EAAA;EAH9B;IAQJ,UAAU;IACV,gDxCLiB,EAAA;;AuC0CrB;ECjDA,WpCuBU;EoCtBV,yBxCOqB,EAAA;EOOrB;IiCVI,WpCkBM;IoCjBN,yBAAkC,EAAA;EAH9B;IAQJ,UAAU;IACV,+CxCJiB,EAAA;;AuCyCrB;ECjDA,cnCegB;EmCdhB,yBxCQqB,EAAA;EOMrB;IiCVI,cnCUY;ImCTZ,yBAAkC,EAAA;EAH9B;IAQJ,UAAU;IACV,iDxCHiB,EAAA;;AuCwCrB;ECjDA,WpCuBU;EoCtBV,yBxCSqB,EAAA;EOKrB;IiCVI,WpCkBM;IoCjBN,yBAAkC,EAAA;EAH9B;IAQJ,UAAU;IACV,8CxCFiB,EAAA;;AyCXvB;EACE,kBAAoD;EACpD,mBpCqzBsC;EoCnzBtC,yBpCKgB;EOJd,qBP8O+B,EAAA;;AoCvOnC;EACE,gBAAgB;EAChB,eAAe;E7BTb,gB6BUsB,EAAA;;ACX1B;EACE,kBAAkB;EAClB,wBrCq9ByC;EqCp9BzC,mBrCq9BsC;EqCp9BtC,6BAA6C;E9BH3C,sBP6OgC,EAAA;;AqCrOpC;EAEE,cAAc,EAAA;;AAIhB;EACE,gBrCgR+B,EAAA;;AqCxQjC;EACE,mBAAsD,EAAA;EADxD;IAKI,kBAAkB;IAClB,MAAM;IACN,QAAQ;IACR,wBrCu7BuC;IqCt7BvC,cAAc,EAAA;;AAUhB;EC9CA,cxB8FgE;EIzF9D,yBJyF8D;EwB5FhE,qBxB4FgE,EAAA;EwB1FhE;IACE,yBAAqC,EAAA;EAGvC;IACE,cAA0B,EAAA;;ADqC5B;EC9CA,cxB8FgE;EIzF9D,yBJyF8D;EwB5FhE,qBxB4FgE,EAAA;EwB1FhE;IACE,yBAAqC,EAAA;EAGvC;IACE,cAA0B,EAAA;;ADqC5B;EC9CA,cxB8FgE;EIzF9D,yBJyF8D;EwB5FhE,qBxB4FgE,EAAA;EwB1FhE;IACE,yBAAqC,EAAA;EAGvC;IACE,cAA0B,EAAA;;ADqC5B;EC9CA,cxB8FgE;EIzF9D,yBJyF8D;EwB5FhE,qBxB4FgE,EAAA;EwB1FhE;IACE,yBAAqC,EAAA;EAGvC;IACE,cAA0B,EAAA;;ADqC5B;EC9CA,cxB8FgE;EIzF9D,yBJyF8D;EwB5FhE,qBxB4FgE,EAAA;EwB1FhE;IACE,yBAAqC,EAAA;EAGvC;IACE,cAA0B,EAAA;;ADqC5B;EC9CA,cxB8FgE;EIzF9D,yBJyF8D;EwB5FhE,qBxB4FgE,EAAA;EwB1FhE;IACE,yBAAqC,EAAA;EAGvC;IACE,cAA0B,EAAA;;ADqC5B;EC9CA,cxB8FgE;EIzF9D,yBJyF8D;EwB5FhE,qBxB4FgE,EAAA;EwB1FhE;IACE,yBAAqC,EAAA;EAGvC;IACE,cAA0B,EAAA;;ADqC5B;EC9CA,cxB8FgE;EIzF9D,yBJyF8D;EwB5FhE,qBxB4FgE,EAAA;EwB1FhE;IACE,yBAAqC,EAAA;EAGvC;IACE,cAA0B,EAAA;;ACR5B;EACE;IAAO,2BAAuC,EAAA;EAC9C;IAAK,wBAAwB,EAAA,EAAA;;AAF/B;EACE;IAAO,2BAAuC,EAAA;EAC9C;IAAK,wBAAwB,EAAA,EAAA;;AAIjC;EACE,oBAAa;EAAb,aAAa;EACb,YvC89BsC;EuC79BtC,gBAAgB;EtCoHZ,kBAtCY;EsC5EhB,yBvCJgB;EOJd,sBP6OgC,EAAA;;AuChOpC;EACE,oBAAa;EAAb,aAAa;EACb,4BAAsB;EAAtB,6BAAsB;UAAtB,sBAAsB;EACtB,wBAAuB;UAAvB,uBAAuB;EACvB,gBAAgB;EAChB,WxCCU;EwCAV,kBAAkB;EAClB,mBAAmB;EACnB,yB5CtBqB;EqBEjB,mChB0+B4C;EgB1+B5C,2BhB0+B4C,EAAA;;AuCl9BlD;ErBaE,qMAA6I;EqBX7I,0BvCw8BsC,EAAA;;AuCp8BtC;EACE,0DvC08BkD;UuC18BlD,kDvC08BkD,EAAA;;AwC/+BtD;EACE,oBAAa;EAAb,aAAa;EACb,wBAAuB;UAAvB,uBAAuB,EAAA;;AAGzB;EACE,mBAAO;UAAP,OAAO,EAAA;;ACFT;EACE,oBAAa;EAAb,aAAa;EACb,4BAAsB;EAAtB,6BAAsB;UAAtB,sBAAsB;EAGtB,eAAe;EACf,gBAAgB,EAAA;;AASlB;EACE,WAAW;EACX,czCPgB;EyCQhB,mBAAmB,EAAA;EvCNnB;IuCUE,UAAU;IACV,czCbc;IyCcd,qBAAqB;IACrB,yBzCrBc,EAAA;EyCWlB;IAcI,czCjBc;IyCkBd,yBzCzBc,EAAA;;AyCkClB;EACE,kBAAkB;EAClB,cAAc;EACd,wBzC88ByC;EyC58BzC,sB1CxBU;E0CyBV,sC1CpCU,EAAA;E0C8BZ;IlC7BI,+BPoOgC;IOnOhC,gCPmOgC,EAAA;EyCvMpC;IlCfI,mCPsNgC;IOrNhC,kCPqNgC,EAAA;EyCvMpC;IAkBI,czChDc;IyCiDd,oBAAoB;IACpB,sB1CvCQ,EAAA;E0CmBZ;IAyBI,UAAU;IACV,W1C7CQ;I0C8CR,yB9ClEmB;I8CmEnB,qB9CnEmB,EAAA;E8CuCvB;IAgCI,mBAAmB,EAAA;IAhCvB;MAmCM,gBzCiK2B;MyChK3B,qBzCgK2B,EAAA;;AyClJ7B;EACE,8BAAmB;EAAnB,6BAAmB;UAAnB,mBAAmB,EAAA;EADrB;IlCjCA,kCPsLgC;IOlMhC,0BkCmDwC,EAAA;EANxC;IlC7CA,gCPkMgC;IOtLhC,4BkC4C0C,EAAA;EAX1C;IAeM,aAAa,EAAA;EAfnB;IAmBM,qBzC+HuB;IyC9HvB,oBAAoB,EAAA;IApB1B;MAuBQ,iBzC2HqB;MyC1HrB,sBzC0HqB,EAAA;;AyC5GjC;EAEI,qBAAqB;EACrB,oBAAoB;ElCjIpB,gBkCkIwB,EAAA;EAJ5B;IAOM,mBAAmB,EAAA;;AAPzB;EAaM,sBAAsB,EAAA;;AC7I1B;EACE,c5B2F8D;E4B1F9D,yB5B0F8D,EAAA;EZ/EhE;IwCPM,c5BsF0D;I4BrF1D,yBAAyC,EAAA;EAP/C;IAWM,W3CUI;I2CTJ,yB5BgF0D;I4B/E1D,qB5B+E0D,EAAA;;A4B5FhE;EACE,c5B2F8D;E4B1F9D,yB5B0F8D,EAAA;EZ/EhE;IwCPM,c5BsF0D;I4BrF1D,yBAAyC,EAAA;EAP/C;IAWM,W3CUI;I2CTJ,yB5BgF0D;I4B/E1D,qB5B+E0D,EAAA;;A4B5FhE;EACE,c5B2F8D;E4B1F9D,yB5B0F8D,EAAA;EZ/EhE;IwCPM,c5BsF0D;I4BrF1D,yBAAyC,EAAA;EAP/C;IAWM,W3CUI;I2CTJ,yB5BgF0D;I4B/E1D,qB5B+E0D,EAAA;;A4B5FhE;EACE,c5B2F8D;E4B1F9D,yB5B0F8D,EAAA;EZ/EhE;IwCPM,c5BsF0D;I4BrF1D,yBAAyC,EAAA;EAP/C;IAWM,W3CUI;I2CTJ,yB5BgF0D;I4B/E1D,qB5B+E0D,EAAA;;A4B5FhE;EACE,c5B2F8D;E4B1F9D,yB5B0F8D,EAAA;EZ/EhE;IwCPM,c5BsF0D;I4BrF1D,yBAAyC,EAAA;EAP/C;IAWM,W3CUI;I2CTJ,yB5BgF0D;I4B/E1D,qB5B+E0D,EAAA;;A4B5FhE;EACE,c5B2F8D;E4B1F9D,yB5B0F8D,EAAA;EZ/EhE;IwCPM,c5BsF0D;I4BrF1D,yBAAyC,EAAA;EAP/C;IAWM,W3CUI;I2CTJ,yB5BgF0D;I4B/E1D,qB5B+E0D,EAAA;;A4B5FhE;EACE,c5B2F8D;E4B1F9D,yB5B0F8D,EAAA;EZ/EhE;IwCPM,c5BsF0D;I4BrF1D,yBAAyC,EAAA;EAP/C;IAWM,W3CUI;I2CTJ,yB5BgF0D;I4B/E1D,qB5B+E0D,EAAA;;A4B5FhE;EACE,c5B2F8D;E4B1F9D,yB5B0F8D,EAAA;EZ/EhE;IwCPM,c5BsF0D;I4BrF1D,yBAAyC,EAAA;EAP/C;IAWM,W3CUI;I2CTJ,yB5BgF0D;I4B/E1D,qB5B+E0D,EAAA;;A6B/FlE;EACE,YAAY;E1C8HR,iBAtCY;E0CtFhB,gB3CiS+B;E2ChS/B,cAAc;EACd,W5CQU;E4CPV,yB5CkBU;E4CjBV,WAAW,EAAA;EzCKX;IyCDE,W5CEQ;I4CDR,qBAAqB,EAAA;EzCIvB;IyCCI,YAAY,EAAA;;AAWlB;EACE,UAAU;EACV,6BAA6B;EAC7B,SAAS;EACT,wBAAgB;KAAhB,qBAAgB;UAAhB,gBAAgB,EAAA;;AAMlB;EACE,oBAAoB,EAAA;;ACvCtB;EACE,gB5Cy4BuC;E4Cx4BvC,gBAAgB;E3C6HZ,mBAtCY;E2CpFhB,2C7CmBU;E6ClBV,4BAA4B;EAC5B,oC5C04BmD;E4Cz4BnD,gD7CKU;E6CJV,mCAA2B;UAA3B,2BAA2B;EAC3B,UAAU;ErCLR,sBP64BsC,EAAA;E4Cl5B1C;IAcI,sB5C63BsC,EAAA;E4C34B1C;IAkBI,UAAU,EAAA;EAlBd;IAsBI,cAAc;IACd,UAAU,EAAA;EAvBd;IA2BI,aAAa,EAAA;;AAIjB;EACE,oBAAa;EAAb,aAAa;EACb,yBAAmB;UAAnB,mBAAmB;EACnB,wB5Cy2BwC;E4Cx2BxC,c5CtBgB;E4CuBhB,2C7CZU;E6CaV,4BAA4B;EAC5B,4C5Ci3BoD,EAAA;;A4C92BtD;EACE,gB5Ci2BwC,EAAA;;A6Cr4B1C;EAEE,gBAAgB,EAAA;EAFlB;IAKI,kBAAkB;IAClB,gBAAgB,EAAA;;AAKpB;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,a7C+pBsC;E6C9pBtC,aAAa;EACb,WAAW;EACX,YAAY;EACZ,gBAAgB;EAGhB,UAAU,EAAA;;AAOZ;EACE,kBAAkB;EAClB,WAAW;EACX,c7C64BuC;E6C34BvC,oBAAoB,EAAA;EAGpB;I7BrCI,mDhB48BoD;IgB58BpD,2ChB48BoD;IgB58BpD,mChB48BoD;IgB58BpD,oEhB48BoD;I6Cr6BtD,sC7Cm6BmD;Y6Cn6BnD,8B7Cm6BmD,EAAA;E6Cj6BrD;IACE,uB7Ci6BoC;Y6Cj6BpC,e7Ci6BoC,EAAA;E6C75BtC;IACE,8B7C85B2C;Y6C95B3C,sB7C85B2C,EAAA;;A6C15B/C;EACE,oBAAa;EAAb,aAAa;EACb,6B/ByE8D,EAAA;E+B3EhE;IAKI,8B/BsE4D;I+BrE5D,gBAAgB,EAAA;EANpB;;IAWI,cAAc,EAAA;EAXlB;IAeI,gBAAgB,EAAA;;AAIpB;EACE,oBAAa;EAAb,aAAa;EACb,yBAAmB;UAAnB,mBAAmB;EACnB,6B/BqD8D,EAAA;E+BxDhE;IAOI,cAAc;IACd,0B/BgD4D;I+B/C5D,WAAW,EAAA;EATf;IAcI,4BAAsB;IAAtB,6BAAsB;YAAtB,sBAAsB;IACtB,wBAAuB;YAAvB,uBAAuB;IACvB,YAAY,EAAA;IAhBhB;MAmBM,gBAAgB,EAAA;IAnBtB;MAuBM,aAAa,EAAA;;AAMnB;EACE,kBAAkB;EAClB,oBAAa;EAAb,aAAa;EACb,4BAAsB;EAAtB,6BAAsB;UAAtB,sBAAsB;EACtB,WAAW;EAGX,oBAAoB;EACpB,sB9CzFU;E8C0FV,4BAA4B;EAC5B,oC9CtGU;EQRR,qBP8O+B;E6C5HjC,UAAU,EAAA;;AAIZ;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,a7CojBsC;E6CnjBtC,YAAY;EACZ,aAAa;EACb,sB9CrHU,EAAA;E8C8GZ;IAUW,UAAU,EAAA;EAVrB;IAWW,Y7C4zB2B,EAAA;;A6CvzBtC;EACE,oBAAa;EAAb,aAAa;EACb,wBAAuB;UAAvB,uBAAuB;EACvB,yBAA8B;UAA9B,8BAA8B;EAC9B,kB7CyzBsC;E6CxzBtC,gC7CtIgB;EOId,0COsH4D;EPrH5D,2COqH4D,EAAA;E+BOhE;IASI,kB7CozBoC;I6ClzBpC,8BAA6F,EAAA;;AAKjG;EACE,gBAAgB;EAChB,gB7C2I+B,EAAA;;A6CtIjC;EACE,kBAAkB;EAGlB,mBAAc;UAAd,cAAc;EACd,a7CuwBsC,EAAA;;A6CnwBxC;EACE,oBAAa;EAAb,aAAa;EACb,eAAe;EACf,yBAAmB;UAAnB,mBAAmB;EACnB,qBAAyB;UAAzB,yBAAyB;EACzB,gBAAgE;EAChE,6B7CvKgB;EOkBd,8COwG4D;EPvG5D,6COuG4D,EAAA;E+BuChE;IAcI,eAAwC,EAAA;;AAK5C;EACE,kBAAkB;EAClB,YAAY;EACZ,WAAW;EACX,YAAY;EACZ,gBAAgB,EAAA;;AClMlB;EACE,kBAAkB;EAClB,a9CmrBsC;E8ClrBtC,cAAc;EACd,S9Cy1BmC;E+C71BnC,kM/CuRiN;E+CrRjN,kBAAkB;EAClB,gB/C+R+B;E+C9R/B,gB/CmS+B;E+ClS/B,gBAAgB;EAChB,iBAAiB;EACjB,qBAAqB;EACrB,iBAAiB;EACjB,oBAAoB;EACpB,sBAAsB;EACtB,kBAAkB;EAClB,oBAAoB;EACpB,mBAAmB;EACnB,gBAAgB;E9CgHZ,mBAtCY;E6C9EhB,qBAAqB;EACrB,UAAU,EAAA;EAXZ;IAaW,Y9C60B2B,EAAA;E8C11BtC;IAgBI,kBAAkB;IAClB,cAAc;IACd,a9C60BqC;I8C50BrC,c9C60BqC,EAAA;I8Ch2BzC;MAsBM,kBAAkB;MAClB,WAAW;MACX,yBAAyB;MACzB,mBAAmB,EAAA;;AAKzB;EACE,iBAAgC,EAAA;EADlC;IAII,SAAS,EAAA;IAJb;MAOM,MAAM;MACN,6BAAgE;MAChE,sB/C3BM,EAAA;;A+CgCZ;EACE,iB9CmzBuC,EAAA;E8CpzBzC;IAII,OAAO;IACP,a9C+yBqC;I8C9yBrC,c9C6yBqC,EAAA;I8CnzBzC;MASM,QAAQ;MACR,oCAA2F;MAC3F,wB/C3CM,EAAA;;A+CgDZ;EACE,iBAAgC,EAAA;EADlC;IAII,MAAM,EAAA;IAJV;MAOM,SAAS;MACT,6B9C4xBmC;M8C3xBnC,yB/CzDM,EAAA;;A+C8DZ;EACE,iB9CqxBuC,EAAA;E8CtxBzC;IAII,QAAQ;IACR,a9CixBqC;I8ChxBrC,c9C+wBqC,EAAA;I8CrxBzC;MASM,OAAO;MACP,oC9C4wBmC;M8C3wBnC,uB/CzEM,EAAA;;A+C8FZ;EACE,gB9C2uBuC;E8C1uBvC,uB9CgvBuC;E8C/uBvC,W/CtFU;E+CuFV,kBAAkB;EAClB,sB/CnGU;EQRR,sBP6OgC,EAAA;;AgDlPpC;EACE,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,ahDirBsC;EgDhrBtC,cAAc;EACd,gBhD22BuC;E+Ch3BvC,kM/CuRiN;E+CrRjN,kBAAkB;EAClB,gB/C+R+B;E+C9R/B,gB/CmS+B;E+ClS/B,gBAAgB;EAChB,iBAAiB;EACjB,qBAAqB;EACrB,iBAAiB;EACjB,oBAAoB;EACpB,sBAAsB;EACtB,kBAAkB;EAClB,oBAAoB;EACpB,mBAAmB;EACnB,gBAAgB;E9CgHZ,mBAtCY;E+C7EhB,qBAAqB;EACrB,sBjDWU;EiDVV,4BAA4B;EAC5B,oCjDFU;EQRR,qBP8O+B,EAAA;EgDnPnC;IAoBI,kBAAkB;IAClB,cAAc;IACd,WhD22BoC;IgD12BpC,chD22BqC;IgD12BrC,gBhD2N+B,EAAA;IgDnPnC;MA4BM,kBAAkB;MAClB,cAAc;MACd,WAAW;MACX,yBAAyB;MACzB,mBAAmB,EAAA;;AAKzB;EACE,qBhD41BuC,EAAA;EgD71BzC;IAII,2BlC2F4D,EAAA;IkC/FhE;MAOM,SAAS;MACT,6BAAgE;MAChE,qChDu1BiE,EAAA;IgDh2BvE;MAaM,WhD6L2B;MgD5L3B,6BAAgE;MAChE,sBjD5BM,EAAA;;AiDiCZ;EACE,mBhDw0BuC,EAAA;EgDz0BzC;IAII,yBlCuE4D;IkCtE5D,ahDo0BqC;IgDn0BrC,YhDk0BoC;IgDj0BpC,gBAAgC,EAAA;IAPpC;MAUM,OAAO;MACP,oCAA2F;MAC3F,uChDg0BiE,EAAA;IgD50BvE;MAgBM,ShDsK2B;MgDrK3B,oCAA2F;MAC3F,wBjDnDM,EAAA;;AiDwDZ;EACE,kBhDizBuC,EAAA;EgDlzBzC;IAII,wBlCgD4D,EAAA;IkCpDhE;MAOM,MAAM;MACN,oCAA2F;MAC3F,wChD4yBiE,EAAA;IgDrzBvE;MAaM,QhDkJ2B;MgDjJ3B,oCAA2F;MAC3F,yBjDvEM,EAAA;EiDwDZ;IAqBI,kBAAkB;IAClB,MAAM;IACN,SAAS;IACT,cAAc;IACd,WhDwxBoC;IgDvxBpC,oBAAsC;IACtC,WAAW;IACX,gChD4wBuD,EAAA;;AgDxwB3D;EACE,oBhDixBuC,EAAA;EgDlxBzC;IAII,0BlCgB4D;IkCf5D,ahD6wBqC;IgD5wBrC,YhD2wBoC;IgD1wBpC,gBAAgC,EAAA;IAPpC;MAUM,QAAQ;MACR,oChDuwBmC;MgDtwBnC,sChDywBiE,EAAA;IgDrxBvE;MAgBM,UhD+G2B;MgD9G3B,oChDiwBmC;MgDhwBnC,uBjD1GM,EAAA;;AiDgIZ;EACE,uBhDkuBwC;EgDjuBxC,gBAAgB;E/C3BZ,eAtCY;E+CoEhB,yBhD2tByD;EgD1tBzD,gCAAyE;EzChJvE,0COsH4D;EPrH5D,2COqH4D,EAAA;EkCoBhE;IAUI,aAAa,EAAA;;AAIjB;EACE,uBhDotBwC;EgDntBxC,chDxJgB,EAAA;;AiDHlB;EACE,kBAAkB,EAAA;;AAGpB;EACE,mBAAmB,EAAA;;AAGrB;EACE,kBAAkB;EAClB,WAAW;EACX,gBAAgB,EAAA;ECvBhB;IACE,cAAc;IACd,WAAW;IACX,WAAW,EAAA;;ADwBf;EACE,kBAAkB;EAClB,aAAa;EACb,WAAW;EACX,WAAW;EACX,mBAAmB;EACnB,mCAA2B;UAA3B,2BAA2B;EjC5BvB,sDhBikCkF;EgBjkClF,8ChBikCkF;EgBjkClF,sChBikCkF;EgBjkClF,0EhBikCkF,EAAA;;AiDjiCxF;;;EAGE,cAAc,EAAA;;AAGhB;;EAEE,mCAA2B;UAA3B,2BAA2B,EAAA;;AAG7B;;EAEE,oCAA4B;UAA5B,4BAA4B,EAAA;;AAQ9B;EAEI,UAAU;EACV,oCAA4B;EAA5B,4BAA4B;EAC5B,uBAAe;UAAf,eAAe,EAAA;;AAJnB;;;EAUI,UAAU;EACV,UAAU,EAAA;;AAXd;;EAgBI,UAAU;EACV,UAAU;EjCtER,mChBgkCkC;EgBhkClC,2BhBgkCkC,EAAA;;AiDh/BxC;;EAEE,kBAAkB;EAClB,MAAM;EACN,SAAS;EACT,UAAU;EAEV,oBAAa;EAAb,aAAa;EACb,yBAAmB;UAAnB,mBAAmB;EACnB,wBAAuB;UAAvB,uBAAuB;EACvB,UjDk9BsC;EiDj9BtC,WlDzEU;EkD0EV,kBAAkB;EAClB,YjDg9BqC;EgB7iCjC,sChB+iCgD;EgB/iChD,8BhB+iCgD,EAAA;EEriCpD;;;I+CwFE,WlDhFQ;IkDiFR,qBAAqB;IACrB,UAAU;IACV,YjDy8BmC,EAAA;;AiDt8BvC;EACE,OAAO,EAAA;;AAKT;EACE,QAAQ,EAAA;;AAOV;;EAEE,qBAAqB;EACrB,WjDk8BuC;EiDj8BvC,YjDi8BuC;EiDh8BvC,qCAAqC,EAAA;;AAEvC;EACE,sNnCxFyI,EAAA;;AmC0F3I;EACE,uNnC3FyI,EAAA;;AmCoG3I;EACE,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,OAAO;EACP,WAAW;EACX,oBAAa;EAAb,aAAa;EACb,wBAAuB;UAAvB,uBAAuB;EACvB,eAAe;EAEf,iBjDw5BsC;EiDv5BtC,gBjDu5BsC;EiDt5BtC,gBAAgB,EAAA;EAZlB;IAeI,uBAAuB;IACvB,mBAAc;YAAd,cAAc;IACd,WjDs5BqC;IiDr5BrC,WjDs5BoC;IiDr5BpC,iBjDu5BoC;IiDt5BpC,gBjDs5BoC;IiDr5BpC,mBAAmB;IACnB,eAAe;IACf,sBlD/IQ;IkDgJR,4BAA4B;IAE5B,kCAAiE;IACjE,qCAAoE;IACpE,WAAW;IjCtKT,qChBsjC+C;IgBtjC/C,6BhBsjC+C,EAAA;EiD56BrD;IAiCI,UAAU,EAAA;;AASd;EACE,kBAAkB;EAClB,UAA2C;EAC3C,YAAY;EACZ,SAA0C;EAC1C,WAAW;EACX,iBAAiB;EACjB,oBAAoB;EACpB,WlD1KU;EkD2KV,kBAAkB,EAAA;;AE/LpB;EACE;IAAK,iCAAyB;YAAzB,yBAAyB,EAAA,EAAA;;AADhC;EACE;IAAK,iCAAyB;YAAzB,yBAAyB,EAAA,EAAA;;AAGhC;EACE,qBAAqB;EACrB,WnDkkC0B;EmDjkC1B,YnDikC0B;EmDhkC1B,2BAA2B;EAC3B,iCAAgD;EAChD,+BAA+B;EAE/B,kBAAkB;EAClB,sDAA8C;UAA9C,8CAA8C,EAAA;;AAGhD;EACE,WnD2jC4B;EmD1jC5B,YnD0jC4B;EmDzjC5B,mBnD2jC4B,EAAA;;AmDpjC9B;EACE;IACE,2BAAmB;YAAnB,mBAAmB,EAAA;EAErB;IACE,UAAU,EAAA,EAAA;;AALd;EACE;IACE,2BAAmB;YAAnB,mBAAmB,EAAA;EAErB;IACE,UAAU,EAAA,EAAA;;AAId;EACE,qBAAqB;EACrB,WnDmiC0B;EmDliC1B,YnDkiC0B;EmDjiC1B,2BAA2B;EAC3B,8BAA8B;EAE9B,kBAAkB;EAClB,UAAU;EACV,oDAA4C;UAA5C,4CAA4C,EAAA;;AAG9C;EACE,WnD4hC4B;EmD3hC5B,YnD2hC4B,EAAA;;AoD9kC9B;EAAqB,mCAAmC,EAAA;;AACxD;EAAqB,8BAA8B,EAAA;;AACnD;EAAqB,iCAAiC,EAAA;;AACtD;EAAqB,iCAAiC,EAAA;;AACtD;EAAqB,sCAAsC,EAAA;;AAC3D;EAAqB,mCAAmC,EAAA;;ACFtD;EACE,oCAAmC,EAAA;;AnDUrC;;;EmDLI,oCAAgD,EAAA;;AANpD;EACE,oCAAmC,EAAA;;AnDUrC;;;EmDLI,oCAAgD,EAAA;;AANpD;EACE,oCAAmC,EAAA;;AnDUrC;;;EmDLI,oCAAgD,EAAA;;AANpD;EACE,oCAAmC,EAAA;;AnDUrC;;;EmDLI,oCAAgD,EAAA;;AANpD;EACE,oCAAmC,EAAA;;AnDUrC;;;EmDLI,oCAAgD,EAAA;;AANpD;EACE,oCAAmC,EAAA;;AnDUrC;;;EmDLI,oCAAgD,EAAA;;AANpD;EACE,oCAAmC,EAAA;;AnDUrC;;;EmDLI,oCAAgD,EAAA;;AANpD;EACE,oCAAmC,EAAA;;AnDUrC;;;EmDLI,oCAAgD,EAAA;;ACCtD;EACE,iCAAmC,EAAA;;AAGrC;EACE,wCAAwC,EAAA;;ACX1C;EAAkB,oCAAoD,EAAA;;AACtE;EAAkB,wCAAwD,EAAA;;AAC1E;EAAkB,0CAA0D,EAAA;;AAC5E;EAAkB,2CAA2D,EAAA;;AAC7E;EAAkB,yCAAyD,EAAA;;AAE3E;EAAmB,oBAAoB,EAAA;;AACvC;EAAmB,wBAAwB,EAAA;;AAC3C;EAAmB,0BAA0B,EAAA;;AAC7C;EAAmB,2BAA2B,EAAA;;AAC9C;EAAmB,yBAAyB,EAAA;;AAG1C;EACE,gCAA+B,EAAA;;AADjC;EACE,gCAA+B,EAAA;;AADjC;EACE,gCAA+B,EAAA;;AADjC;EACE,gCAA+B,EAAA;;AADjC;EACE,gCAA+B,EAAA;;AADjC;EACE,gCAA+B,EAAA;;AADjC;EACE,gCAA+B,EAAA;;AADjC;EACE,gCAA+B,EAAA;;AAInC;EACE,6BAA+B,EAAA;;AAOjC;EACE,gCAA2C,EAAA;;AAG7C;EACE,iCAAwC,EAAA;;AAG1C;EACE,0CAAiD;EACjD,2CAAkD,EAAA;;AAGpD;EACE,2CAAkD;EAClD,8CAAqD,EAAA;;AAGvD;EACE,8CAAqD;EACrD,6CAAoD,EAAA;;AAGtD;EACE,0CAAiD;EACjD,6CAAoD,EAAA;;AAGtD;EACE,gCAA2C,EAAA;;AAG7C;EACE,6BAA6B,EAAA;;AAG/B;EACE,+BAAuC,EAAA;;AAGzC;EACE,2BAA2B,EAAA;;ALxE3B;EACE,cAAc;EACd,WAAW;EACX,WAAW,EAAA;;AMOT;EAAwB,wBAA0B,EAAA;;AAAlD;EAAwB,0BAA0B,EAAA;;AAAlD;EAAwB,gCAA0B,EAAA;;AAAlD;EAAwB,yBAA0B,EAAA;;AAAlD;EAAwB,yBAA0B,EAAA;;AAAlD;EAAwB,6BAA0B,EAAA;;AAAlD;EAAwB,8BAA0B,EAAA;;AAAlD;EAAwB,+BAA0B;EAA1B,wBAA0B,EAAA;;AAAlD;EAAwB,sCAA0B;EAA1B,+BAA0B,EAAA;;ACTxD;EACE,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,UAAU;EACV,gBAAgB,EAAA;EALlB;IAQI,cAAc;IACd,WAAW,EAAA;EATf;;;;;IAiBI,kBAAkB;IAClB,MAAM;IACN,SAAS;IACT,OAAO;IACP,WAAW;IACX,YAAY;IACZ,SAAS,EAAA;;AAQX;EAEI,sBAA4F,EAAA;;AAFhG;EAEI,mBAA4F,EAAA;;AAFhG;EAEI,gBAA4F,EAAA;;AAFhG;EAEI,iBAA4F,EAAA;;ACzB9F;EAAgC,yCAA8B;EAA9B,wCAA8B;UAA9B,8BAA8B,EAAA;;AAC9D;EAAgC,uCAAiC;EAAjC,wCAAiC;UAAjC,iCAAiC,EAAA;;AACjE;EAAgC,yCAAsC;EAAtC,yCAAsC;UAAtC,sCAAsC,EAAA;;AACtE;EAAgC,uCAAyC;EAAzC,yCAAyC;UAAzC,yCAAyC,EAAA;;AAEzE;EAA8B,0BAA0B,EAAA;;AACxD;EAA8B,4BAA4B,EAAA;;AAC1D;EAA8B,kCAAkC,EAAA;;AAChE;EAA8B,8BAAyB;UAAzB,yBAAyB,EAAA;;AACvD;EAA8B,8BAAuB;UAAvB,uBAAuB,EAAA;;AACrD;EAA8B,8BAAuB;UAAvB,uBAAuB,EAAA;;AACrD;EAA8B,yBAAyB,EAAA;;AACvD;EAA8B,yBAAyB,EAAA;;AAEvD;EAAoC,kCAAsC;UAAtC,sCAAsC,EAAA;;AAC1E;EAAoC,gCAAoC;UAApC,oCAAoC,EAAA;;AACxE;EAAoC,mCAAkC;UAAlC,kCAAkC,EAAA;;AACtE;EAAoC,oCAAyC;UAAzC,yCAAyC,EAAA;;AAC7E;EAAoC,wCAAwC,EAAA;;AAE5E;EAAiC,mCAAkC;UAAlC,kCAAkC,EAAA;;AACnE;EAAiC,iCAAgC;UAAhC,gCAAgC,EAAA;;AACjE;EAAiC,oCAA8B;UAA9B,8BAA8B,EAAA;;AAC/D;EAAiC,sCAAgC;UAAhC,gCAAgC,EAAA;;AACjE;EAAiC,qCAA+B;UAA/B,+BAA+B,EAAA;;AAEhE;EAAkC,oCAAoC,EAAA;;AACtE;EAAkC,kCAAkC,EAAA;;AACpE;EAAkC,gCAAgC,EAAA;;AAClE;EAAkC,uCAAuC,EAAA;;AACzE;EAAkC,sCAAsC,EAAA;;AACxE;EAAkC,iCAAiC,EAAA;;AAEnE;EAAgC,2BAA2B,EAAA;;AAC3D;EAAgC,iCAAiC,EAAA;;AACjE;EAAgC,+BAA+B,EAAA;;AAC/D;EAAgC,6BAA6B,EAAA;;AAC7D;EAAgC,+BAA+B,EAAA;;AAC/D;EAAgC,8BAA8B,EAAA;;AC1C9D;EAAwB,sBAAsB,EAAA;;AAC9C;EAAwB,uBAAuB,EAAA;;AAC/C;EAAwB,sBAAsB,EAAA;;ACLhD;EAAsB,yBAA2B,EAAA;;AAAjD;EAAsB,2BAA2B,EAAA;;ACCjD;EAAyB,2BAA8B,EAAA;;AAAvD;EAAyB,6BAA8B,EAAA;;AAAvD;EAAyB,6BAA8B,EAAA;;AAAvD;EAAyB,0BAA8B,EAAA;;AAAvD;EAAyB,mCAA8B;EAA9B,2BAA8B,EAAA;;AAKzD;EACE,eAAe;EACf,MAAM;EACN,QAAQ;EACR,OAAO;EACP,a7DoqBsC,EAAA;;A6DjqBxC;EACE,eAAe;EACf,QAAQ;EACR,SAAS;EACT,OAAO;EACP,a7D4pBsC,EAAA;;A6DxpBV;EAD9B;IAEI,wBAAgB;IAAhB,gBAAgB;IAChB,MAAM;IACN,a7DopBoC,EAAA,E6DlpBvC;;AC3BD;ECEE,kBAAkB;EAClB,UAAU;EACV,WAAW;EACX,UAAU;EACV,YAAY;EACZ,gBAAgB;EAChB,sBAAsB;EACtB,mBAAmB;EACnB,SAAS,EAAA;;AAUT;EAEE,gBAAgB;EAChB,WAAW;EACX,YAAY;EACZ,iBAAiB;EACjB,UAAU;EACV,mBAAmB,EAAA;;AC7BvB;EAAa,8DAAqC,EAAA;;AAClD;EAAU,wDAAkC,EAAA;;AAC5C;EAAa,uDAAqC,EAAA;;AAClD;EAAe,2BAA2B,EAAA;;ACCtC;EAAuB,qBAA4B,EAAA;;AAAnD;EAAuB,qBAA4B,EAAA;;AAAnD;EAAuB,qBAA4B,EAAA;;AAAnD;EAAuB,sBAA4B,EAAA;;AAAnD;EAAuB,sBAA4B,EAAA;;AAAnD;EAAuB,sBAA4B,EAAA;;AAAnD;EAAuB,sBAA4B,EAAA;;AAAnD;EAAuB,sBAA4B,EAAA;;AAAnD;EAAuB,uBAA4B,EAAA;;AAAnD;EAAuB,uBAA4B,EAAA;;AAIvD;EAAU,0BAA0B,EAAA;;AACpC;EAAU,2BAA2B,EAAA;;AAIrC;EAAc,2BAA2B,EAAA;;AACzC;EAAc,4BAA4B,EAAA;;AAE1C;EAAU,uBAAuB,EAAA;;AACjC;EAAU,wBAAwB,EAAA;;ACflC;EAEI,kBAAkB;EAClB,MAAM;EACN,QAAQ;EACR,SAAS;EACT,OAAO;EACP,UAAU;EAEV,oBAAoB;EACpB,WAAW;EAEX,kCAAkC,EAAA;;ACN9B;EAAgC,oBAA4B,EAAA;;AAC5D;;EAEE,wBAAoC,EAAA;;AAEtC;;EAEE,0BAAwC,EAAA;;AAE1C;;EAEE,2BAA0C,EAAA;;AAE5C;;EAEE,yBAAsC,EAAA;;AAfxC;EAAgC,0BAA4B,EAAA;;AAC5D;;EAEE,8BAAoC,EAAA;;AAEtC;;EAEE,gCAAwC,EAAA;;AAE1C;;EAEE,iCAA0C,EAAA;;AAE5C;;EAEE,+BAAsC,EAAA;;AAfxC;EAAgC,yBAA4B,EAAA;;AAC5D;;EAEE,6BAAoC,EAAA;;AAEtC;;EAEE,+BAAwC,EAAA;;AAE1C;;EAEE,gCAA0C,EAAA;;AAE5C;;EAEE,8BAAsC,EAAA;;AAfxC;EAAgC,uBAA4B,EAAA;;AAC5D;;EAEE,2BAAoC,EAAA;;AAEtC;;EAEE,6BAAwC,EAAA;;AAE1C;;EAEE,8BAA0C,EAAA;;AAE5C;;EAEE,4BAAsC,EAAA;;AAfxC;EAAgC,yBAA4B,EAAA;;AAC5D;;EAEE,6BAAoC,EAAA;;AAEtC;;EAEE,+BAAwC,EAAA;;AAE1C;;EAEE,gCAA0C,EAAA;;AAE5C;;EAEE,8BAAsC,EAAA;;AAfxC;EAAgC,uBAA4B,EAAA;;AAC5D;;EAEE,2BAAoC,EAAA;;AAEtC;;EAEE,6BAAwC,EAAA;;AAE1C;;EAEE,8BAA0C,EAAA;;AAE5C;;EAEE,4BAAsC,EAAA;;AAfxC;EAAgC,qBAA4B,EAAA;;AAC5D;;EAEE,yBAAoC,EAAA;;AAEtC;;EAEE,2BAAwC,EAAA;;AAE1C;;EAEE,4BAA0C,EAAA;;AAE5C;;EAEE,0BAAsC,EAAA;;AAfxC;EAAgC,2BAA4B,EAAA;;AAC5D;;EAEE,+BAAoC,EAAA;;AAEtC;;EAEE,iCAAwC,EAAA;;AAE1C;;EAEE,kCAA0C,EAAA;;AAE5C;;EAEE,gCAAsC,EAAA;;AAfxC;EAAgC,0BAA4B,EAAA;;AAC5D;;EAEE,8BAAoC,EAAA;;AAEtC;;EAEE,gCAAwC,EAAA;;AAE1C;;EAEE,iCAA0C,EAAA;;AAE5C;;EAEE,+BAAsC,EAAA;;AAfxC;EAAgC,wBAA4B,EAAA;;AAC5D;;EAEE,4BAAoC,EAAA;;AAEtC;;EAEE,8BAAwC,EAAA;;AAE1C;;EAEE,+BAA0C,EAAA;;AAE5C;;EAEE,6BAAsC,EAAA;;AAfxC;EAAgC,0BAA4B,EAAA;;AAC5D;;EAEE,8BAAoC,EAAA;;AAEtC;;EAEE,gCAAwC,EAAA;;AAE1C;;EAEE,iCAA0C,EAAA;;AAE5C;;EAEE,+BAAsC,EAAA;;AAfxC;EAAgC,wBAA4B,EAAA;;AAC5D;;EAEE,4BAAoC,EAAA;;AAEtC;;EAEE,8BAAwC,EAAA;;AAE1C;;EAEE,+BAA0C,EAAA;;AAE5C;;EAEE,6BAAsC,EAAA;;AAQxC;EAAwB,2BAA2B,EAAA;;AACnD;;EAEE,+BAA+B,EAAA;;AAEjC;;EAEE,iCAAiC,EAAA;;AAEnC;;EAEE,kCAAkC,EAAA;;AAEpC;;EAEE,gCAAgC,EAAA;;AAflC;EAAwB,0BAA2B,EAAA;;AACnD;;EAEE,8BAA+B,EAAA;;AAEjC;;EAEE,gCAAiC,EAAA;;AAEnC;;EAEE,iCAAkC,EAAA;;AAEpC;;EAEE,+BAAgC,EAAA;;AAflC;EAAwB,wBAA2B,EAAA;;AACnD;;EAEE,4BAA+B,EAAA;;AAEjC;;EAEE,8BAAiC,EAAA;;AAEnC;;EAEE,+BAAkC,EAAA;;AAEpC;;EAEE,6BAAgC,EAAA;;AAflC;EAAwB,0BAA2B,EAAA;;AACnD;;EAEE,8BAA+B,EAAA;;AAEjC;;EAEE,gCAAiC,EAAA;;AAEnC;;EAEE,iCAAkC,EAAA;;AAEpC;;EAEE,+BAAgC,EAAA;;AAflC;EAAwB,wBAA2B,EAAA;;AACnD;;EAEE,4BAA+B,EAAA;;AAEjC;;EAEE,8BAAiC,EAAA;;AAEnC;;EAEE,+BAAkC,EAAA;;AAEpC;;EAEE,6BAAgC,EAAA;;AAMtC;EAAmB,uBAAuB,EAAA;;AAC1C;;EAEE,2BAA2B,EAAA;;AAE7B;;EAEE,6BAA6B,EAAA;;AAE/B;;EAEE,8BAA8B,EAAA;;AAEhC;;EAEE,4BAA4B,EAAA;;AC/DlC;EAAkB,4GAA8C,EAAA;;AAIhE;EAAiB,8BAA8B,EAAA;;AAC/C;EAAiB,8BAA8B,EAAA;;AAC/C;EAAiB,8BAA8B,EAAA;;AAC/C;ECTE,gBAAgB;EAChB,uBAAuB;EACvB,mBAAmB,EAAA;;ADejB;EAAwB,2BAA2B,EAAA;;AACnD;EAAwB,4BAA4B,EAAA;;AACpD;EAAwB,6BAA6B,EAAA;;AAMzD;EAAmB,oCAAoC,EAAA;;AACvD;EAAmB,oCAAoC,EAAA;;AACvD;EAAmB,qCAAqC,EAAA;;AAIxD;EAAuB,2BAA0C,EAAA;;AACjE;EAAuB,+BAA4C,EAAA;;AACnE;EAAuB,2BAA2C,EAAA;;AAClE;EAAuB,2BAAyC,EAAA;;AAChE;EAAuB,8BAA2C,EAAA;;AAClE;EAAuB,6BAA6B,EAAA;;AAIpD;EAAc,sBAAwB,EAAA;;AEvCpC;EACE,yBAAwB,EAAA;;ApEU1B;EoELM,yBAA0E,EAAA;;AANhF;EACE,yBAAwB,EAAA;;ApEU1B;EoELM,yBAA0E,EAAA;;AANhF;EACE,yBAAwB,EAAA;;ApEU1B;EoELM,yBAA0E,EAAA;;AANhF;EACE,yBAAwB,EAAA;;ApEU1B;EoELM,yBAA0E,EAAA;;AANhF;EACE,yBAAwB,EAAA;;ApEU1B;EoELM,yBAA0E,EAAA;;AANhF;EACE,yBAAwB,EAAA;;ApEU1B;EoELM,yBAA0E,EAAA;;AANhF;EACE,yBAAwB,EAAA;;ApEU1B;EoELM,yBAA0E,EAAA;;AANhF;EACE,yBAAwB,EAAA;;ApEU1B;EoELM,yBAA0E,EAAA;;AFuClF;EAAa,yBAA6B,EAAA;;AAC1C;EAAc,yBAA6B,EAAA;;AAE3C;EAAiB,oCAAkC,EAAA;;AACnD;EAAiB,0CAAkC,EAAA;;AAInD;EGvDE,WAAW;EACX,kBAAkB;EAClB,iBAAiB;EACjB,6BAA6B;EAC7B,SAAS,EAAA;;AHuDX;EAAwB,gCAAgC,EAAA;;AAExD;EACE,iCAAiC;EACjC,oCAAoC,EAAA;;AAKtC;EAAc,yBAAyB,EAAA;;AIjEvC;EACE,8BAA8B,EAAA;;AAGhC;EACE,6BAA6B,EAAA;;A7EkB/B;;;;;;;;;CDo9NC;ACz8ND;;;;;;;;;;;;;;;;;;;;;;;;CDk+NC;ACt/ND;EA8CE,yBAAyB,EAAA;;AAG3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CD8+NC;ACx8ND;EyBrGE,WrBkBU;EmBlBR,yBvBsG6B;EyBpG/B,yBzBoG4C;EAC5C,eAAc,EAAA;EAFhB;IyB/FI,WrBYQ;ImBlBR,yBvBsGmD;IyB9FnD,yBzB8FgE,EAAA;EADpE;IyBxFI,WrBKQ;ImBlBR,yBvBsGmD;IyBvFnD,yBzBuFgE;IyBlF9D,iDAAiF,EAAA;EzBiFvF;IyB1EI,WrBTQ;IqBUR,yBzB0E6B;IyBzE7B,yBzByE0C,EAAA;EAD9C;;IyB9DI,WrBrBQ;IqBsBR,yBzB8DyE;IyB1DzE,yBzB0DsF,EAAA;IAD1F;;MyBlDQ,iDAAiF,EAAA;;AzBuDzF;EyB1GE,WrBkBU;EmBlBR,yBvB2G6B;EyBzG/B,yBzByG4C;EAC5C,eAAc,EAAA;EAFhB;IyBpGI,WrBYQ;ImBlBR,yBvB2GmD;IyBnGnD,yBzBmGgE,EAAA;EADpE;IyB7FI,WrBKQ;ImBlBR,yBvB2GmD;IyB5FnD,yBzB4FgE;IyBvF9D,iDAAiF,EAAA;EzBsFvF;IyB/EI,WrBTQ;IqBUR,yBzB+E6B;IyB9E7B,yBzB8E0C,EAAA;EAD9C;;IyBnEI,WrBrBQ;IqBsBR,yBzBmEyE;IyB/DzE,yBzB+DsF,EAAA;IAD1F;;MyBvDQ,iDAAiF,EAAA;;AzB4DzF;EyB/GE,cpBUgB;EkBVd,yBvBgH6B;EyB9G/B,qBzB8GwC;EACxC,cAAc;EACd,eAAc,EAAA;EAHhB;IyBzGI,cpBIc;IkBVd,yBvBgH+C;IyBxG/C,qBzBwGwD,EAAA;EAD5D;IyBlGI,cpBHc;IkBVd,yBvBgH+C;IyBjG/C,qBzBiGwD;IyB5FtD,8CAAiF,EAAA;EzB2FvF;IyBpFI,cpBjBc;IoBkBd,yBzBoF6B;IyBnF7B,qBzBmFsC,EAAA;EAD1C;;IyBxEI,cpB7Bc;IoB8Bd,yBzBwEiE;IyBpEjE,qBzBoE0E,EAAA;IAD9E;;MyB5DQ,8CAAiF,EAAA;;AzB4DzF;EAOE,cAAc,EAAA;;AAGhB;EyBzHE,cpBUgB;EkBVd,yBvB0H6B;EyBxH/B,yBzBwH4C;EAC5C,cAAc;EACd,eAAc,EAAA;EAHhB;IyBnHI,cpBIc;IkBVd,yBvB0HmD;IyBlHnD,yBzBkHgE,EAAA;EADpE;IyB5GI,cpBHc;IkBVd,yBvB0HmD;IyB3GnD,yBzB2GgE;IyBtG9D,8CAAiF,EAAA;EzBqGvF;IyB9FI,cpBjBc;IoBkBd,yBzB8F6B;IyB7F7B,yBzB6F0C,EAAA;EAD9C;;IyBlFI,cpB7Bc;IoB8Bd,yBzBkFyE;IyB9EzE,yBzB8EsF,EAAA;IAD1F;;MyBtEQ,8CAAiF,EAAA;;AzBsEzF;EAOE,cAAc,EAAA;;AAGhB;EyBnIE,WrBkBU;EmBlBR,6BvBoIiC;EyBlInC,yBzBkIgD;EAChD,iBAAiB;EACjB,eAAc,EAAA;EAHhB;IyB7HI,cpBIc;IkBVd,yBvBoIuD;IyB5HvD,yBzB4HoE,EAAA;EADxE;IyBtHI,cpBHc;IkBVd,yBvBoIuD;IyBrHvD,yBzBqHoE;IyBhHlE,iDAAiF,EAAA;EzB+GvF;IyBxGI,WrBTQ;IqBUR,6BzBwGiC;IyBvGjC,yBzBuG8C,EAAA;EADlD;;IyB5FI,cpB7Bc;IoB8Bd,yBzB4F6E;IyBxF7E,yBzBwF0F,EAAA;IAD9F;;MyBhFQ,iDAAiF,EAAA;;AzBgFzF;EAOE,cAAc,EAAA;;AAGhB;;;;;;;;;;;;;;;;;CDqkOC;ACnjOD;EyB/JE,cpBUgB;EkBVd,yBvBgK6B;EyB9J/B,qBzB8JwC;EACxC,cAAc;EACd,eAAc,EAAA;EAHhB;IyBzJI,cpBIc;IkBVd,yBvBgK+C;IyBxJ/C,qBzBwJwD,EAAA;EAD5D;IyBlJI,cpBHc;IkBVd,yBvBgK+C;IyBjJ/C,qBzBiJwD;IyB5ItD,8CAAiF,EAAA;EzB2IvF;IyBpII,cpBjBc;IoBkBd,yBzBoI6B;IyBnI7B,qBzBmIsC,EAAA;EAD1C;;IyBxHI,cpB7Bc;IoB8Bd,yBzBwHiE;IyBpHjE,qBzBoH0E,EAAA;IAD9E;;MyB5GQ,8CAAiF,EAAA;;AzB4GzF;EAOE,cAAc,EAAA;;AAGhB;EACE,oCAAoC;EACpC,sBAAsB;EACtB,2BAA2B,EAAA;;AAG7B;;;;;;;;;;;;;;;;;;;;CD4lOC;ACvkOD;EwCzME,cnCegB;EmCdhB,sBxCyM2B;EAC3B,qBAAqB;EACrB,yBAAyB;EACzB,cAAc;EACd,sBAAsB;EACtB,eAAe;EACf,mBAAmB,EAAA;EOjMnB;IiCVI,cnCUY;ImCTZ,yBAAkC,EAAA;EAH9B;IAQJ,UAAU;IACV,iDxC8LuB,EAAA;;AAQ7B;EwClNE,cnCegB;EmCdhB,sBxCkN2B;EAC3B,qBAAqB;EACrB,yBAAyB;EACzB,cAAc;EACd,sBAAsB;EACtB,eAAe;EACf,mBAAmB,EAAA;EO1MnB;IiCVI,cnCUY;ImCTZ,yBAAkC,EAAA;EAH9B;IAQJ,UAAU;IACV,iDxCuMuB,EAAA;;AAQ7B;EwC3NE,cnCegB;EmCdhB,sBxC2N2B;EAC3B,qBAAqB;EACrB,yBAAyB;EACzB,cAAc;EACd,sBAAsB;EACtB,eAAe;EACf,mBAAmB,EAAA;EOnNnB;IiCVI,cnCUY;ImCTZ,yBAAkC,EAAA;EAH9B;IAQJ,UAAU;IACV,iDxCgNuB,EAAA;;AAQ7B;EwCpOE,cnCegB;EmCdhB,sBxCoO2B;EAC3B,qBAAqB;EACrB,yBAAyB;EACzB,cAAc;EACd,sBAAsB;EACtB,eAAe;EACf,mBAAmB,EAAA;EO5NnB;IiCVI,cnCUY;ImCTZ,yBAAkC,EAAA;EAH9B;IAQJ,UAAU;IACV,iDxCyNuB,EAAA;;AAQ7B;EwC7OE,cnCegB;EmCdhB,sBxC6O2B;EAC3B,qBAAqB;EACrB,yBAAyB;EACzB,cAAc;EACd,sBAAsB;EACtB,eAAe;EACf,mBAAmB,EAAA;EOrOnB;IiCVI,cnCUY;ImCTZ,yBAAkC,EAAA;EAH9B;IAQJ,UAAU;IACV,iDxCkOuB,EAAA;;AAS7B;;;;;;;;;;;;CDsnOC;ACzmOD;EACE,6BAA6B,EAAA;;AAE/B;EACE,kCAA4C,EAAA;;AAG9C;;;;;;;;;;;CDonOC;ACxmOD;EACE,oCAAoC,EAAA;;AAEtC;EACE,oCAAoC,EAAA;;AAGtC;;;;;;;;;;CDknOC;ACvmOD;EAAgB,sBAAsB,EAAA;;AACtC;EAAqB,sBAAsB,EAAA;;A8E/OvC;IhEtDF;MCWI,gBVqMK,EAAA;ES/LL;IACE,gBT8LG,EAAA;EWxLL;IACE,aAAa;IACb,mBAAY;YAAZ,YAAY;IACZ,eAAe,EAAA;EAIf;ID4BJ,mBAAuB;YAAvB,cAAuB;IACvB,eAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,aAAuB;IACvB,cAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,mBAAuB;IACvB,oBAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,aAAuB;IACvB,cAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,aAAuB;IACvB,cAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,mBAAuB;IACvB,oBAAwB,EAAA;ECxBtB;IDMJ,mBAAc;YAAd,cAAc;IACd,WAAW;IACX,eAAe,EAAA;ECHT;IDPN,mBAAsC;YAAtC,kBAAsC;IAItC,mBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,aAAsC;IAItC,cAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,aAAsC;IAItC,cAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,aAAsC;IAItC,cAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,cAAsC;IAItC,eAAuC,EAAA;ECQnC;IAAwB,4BAAS;YAAT,SAAS,EAAA;EAEjC;IAAuB,6BX6KG;YW7KH,SX6KG,EAAA;EW1KxB;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,SADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,SADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,SADZ,EAAA;EAOV;IDRR,cAA4B,EAAA;ECQpB;IDRR,qBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,gBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,gBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,gBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;IKkNhD;MAeM,oBAAa;MAAb,aAAa;MACb,yBAAmB;cAAnB,mBAAmB;MACnB,wBAAuB;cAAvB,uBAAuB;MACvB,gBAAgB,EAAA;IAlBtB;MAuBM,oBAAa;MAAb,aAAa;MACb,mBAAc;cAAd,cAAc;MACd,8BAAmB;MAAnB,6BAAmB;cAAnB,mBAAmB;MACnB,yBAAmB;cAAnB,mBAAmB;MACnB,gBAAgB,EAAA;IA3BtB;MAgCM,qBAAqB;MACrB,WAAW;MACX,sBAAsB,EAAA;IAlC5B;MAuCM,qBAAqB,EAAA;IAvC3B;;MA4CM,WAAW,EAAA;IA5CjB;MAkDM,oBAAa;MAAb,aAAa;MACb,yBAAmB;cAAnB,mBAAmB;MACnB,wBAAuB;cAAvB,uBAAuB;MACvB,WAAW;MACX,eAAe,EAAA;IAtDrB;MAyDM,kBAAkB;MAClB,cAAc;MACd,aAAa;MACb,qBf2LwC;Me1LxC,cAAc,EAAA;IA7DpB;MAiEM,yBAAmB;cAAnB,mBAAmB;MACnB,wBAAuB;cAAvB,uBAAuB,EAAA;IAlE7B;MAqEM,gBAAgB,EAAA;EOrSlB;IACE,WAAW;IACX,OAAO,EAAA;EAGT;IACE,QAAQ;IACR,UAAU,EAAA;EOqGZ;IAoBI,8BAAqB;IAArB,6BAAqB;YAArB,qBAAqB;IACrB,uBAA2B;YAA3B,2BAA2B,EAAA;IArB9B;MAwBK,8BAAmB;MAAnB,6BAAmB;cAAnB,mBAAmB,EAAA;MAxBxB;QA2BO,kBAAkB,EAAA;MA3BzB;QA+BO,qB7BgiB6B;Q6B/hB7B,oB7B+hB6B,EAAA;I6B/jBpC;;MAsCK,iBAAiB,EAAA;IAtCtB;MAqDK,+BAAwB;MAAxB,wBAAwB;MAGxB,gBAAgB,EAAA;IAxDrB;MA4DK,aAAa,EAAA;EC9DvB;IAMI,oBAAa;IAAb,aAAa;IACb,8BAAmB;IAAnB,6BAAmB;YAAnB,mBAAmB;IACnB,mB9BirBsD;I8BhrBtD,kB9BgrBsD,EAAA;I8BzrB1D;MAaM,mBAAY;cAAZ,YAAY;MACZ,kB9B2qBoD;M8B1qBpD,gBAAgB;MAChB,iB9ByqBoD,EAAA;E8B/pB1D;IAQI,oBAAa;IAAb,aAAa;IACb,8BAAmB;IAAnB,6BAAmB;YAAnB,mBAAmB,EAAA;IATvB;MAcM,mBAAY;cAAZ,YAAY;MACZ,gBAAgB,EAAA;MAftB;QAkBQ,cAAc;QACd,cAAc,EAAA;MAnBtB;QvBxJI,0BuBiLoC;QvBhLpC,6BuBgLoC,EAAA;QAzBxC;;UA8BY,0BAA0B,EAAA;QA9BtC;;UAmCY,6BAA6B,EAAA;MAnCzC;QvB1II,yBuBkLmC;QvBjLnC,4BuBiLmC,EAAA;QAxCvC;;UA6CY,yBAAyB,EAAA;QA7CrC;;UAkDY,4BAA4B,EAAA;EAaxC;IAMI,uB9B6lBiC;O8B7lBjC,oB9B6lBiC;Y8B7lBjC,e9B6lBiC;I8B5lBjC,2B9B6lBuC;O8B7lBvC,wB9B6lBuC;Y8B7lBvC,mB9B6lBuC;I8B5lBvC,UAAU;IACV,SAAS,EAAA;IATb;MAYM,qBAAqB;MACrB,WAAW,EAAA;IMzPjB;MAQI,kBpC+yBoC,EAAA;EyC1tBpC;IACE,8BAAmB;IAAnB,6BAAmB;YAAnB,mBAAmB,EAAA;IADrB;MlCjCA,kCPsLgC;MOlMhC,0BkCmDwC,EAAA;IANxC;MlC7CA,gCPkMgC;MOtLhC,4BkC4C0C,EAAA;IAX1C;MAeM,aAAa,EAAA;IAfnB;MAmBM,qBzC+HuB;MyC9HvB,oBAAoB,EAAA;MApB1B;QAuBQ,iBzC2HqB;QyC1HrB,sBzC0HqB,EAAA;E6C5MjC;IAuKI,gB7CmwBqC;I6ClwBrC,oBAAyC,EAAA;EAlJ7C;IAsJI,+B/B3E4D,EAAA;I+B3EhE;MAyJM,gC/B9E0D,EAAA;E+BxDhE;IA2II,+B/BnF4D,EAAA;I+BxDhE;MA8IM,4B/BtF0D,EAAA;E+B8F9D;IAAY,gB7C4uB2B,EAAA;EwDn8BnC;IAAwB,wBAA0B,EAAA;EAAlD;IAAwB,0BAA0B,EAAA;EAAlD;IAAwB,gCAA0B,EAAA;EAAlD;IAAwB,yBAA0B,EAAA;EAAlD;IAAwB,yBAA0B,EAAA;EAAlD;IAAwB,6BAA0B,EAAA;EAAlD;IAAwB,8BAA0B,EAAA;EAAlD;IAAwB,+BAA0B;IAA1B,wBAA0B,EAAA;EAAlD;IAAwB,sCAA0B;IAA1B,+BAA0B,EAAA;EEDpD;IAAgC,yCAA8B;IAA9B,wCAA8B;YAA9B,8BAA8B,EAAA;EAC9D;IAAgC,uCAAiC;IAAjC,wCAAiC;YAAjC,iCAAiC,EAAA;EACjE;IAAgC,yCAAsC;IAAtC,yCAAsC;YAAtC,sCAAsC,EAAA;EACtE;IAAgC,uCAAyC;IAAzC,yCAAyC;YAAzC,yCAAyC,EAAA;EAEzE;IAA8B,0BAA0B,EAAA;EACxD;IAA8B,4BAA4B,EAAA;EAC1D;IAA8B,kCAAkC,EAAA;EAChE;IAA8B,8BAAyB;YAAzB,yBAAyB,EAAA;EACvD;IAA8B,8BAAuB;YAAvB,uBAAuB,EAAA;EACrD;IAA8B,8BAAuB;YAAvB,uBAAuB,EAAA;EACrD;IAA8B,yBAAyB,EAAA;EACvD;IAA8B,yBAAyB,EAAA;EAEvD;IAAoC,kCAAsC;YAAtC,sCAAsC,EAAA;EAC1E;IAAoC,gCAAoC;YAApC,oCAAoC,EAAA;EACxE;IAAoC,mCAAkC;YAAlC,kCAAkC,EAAA;EACtE;IAAoC,oCAAyC;YAAzC,yCAAyC,EAAA;EAC7E;IAAoC,wCAAwC,EAAA;EAE5E;IAAiC,mCAAkC;YAAlC,kCAAkC,EAAA;EACnE;IAAiC,iCAAgC;YAAhC,gCAAgC,EAAA;EACjE;IAAiC,oCAA8B;YAA9B,8BAA8B,EAAA;EAC/D;IAAiC,sCAAgC;YAAhC,gCAAgC,EAAA;EACjE;IAAiC,qCAA+B;YAA/B,+BAA+B,EAAA;EAEhE;IAAkC,oCAAoC,EAAA;EACtE;IAAkC,kCAAkC,EAAA;EACpE;IAAkC,gCAAgC,EAAA;EAClE;IAAkC,uCAAuC,EAAA;EACzE;IAAkC,sCAAsC,EAAA;EACxE;IAAkC,iCAAiC,EAAA;EAEnE;IAAgC,2BAA2B,EAAA;EAC3D;IAAgC,iCAAiC,EAAA;EACjE;IAAgC,+BAA+B,EAAA;EAC/D;IAAgC,6BAA6B,EAAA;EAC7D;IAAgC,+BAA+B,EAAA;EAC/D;IAAgC,8BAA8B,EAAA;EC1C9D;IAAwB,sBAAsB,EAAA;EAC9C;IAAwB,uBAAuB,EAAA;EAC/C;IAAwB,sBAAsB,EAAA;EQE1C;IAAgC,oBAA4B,EAAA;EAC5D;;IAEE,wBAAoC,EAAA;EAEtC;;IAEE,0BAAwC,EAAA;EAE1C;;IAEE,2BAA0C,EAAA;EAE5C;;IAEE,yBAAsC,EAAA;EAfxC;IAAgC,0BAA4B,EAAA;EAC5D;;IAEE,8BAAoC,EAAA;EAEtC;;IAEE,gCAAwC,EAAA;EAE1C;;IAEE,iCAA0C,EAAA;EAE5C;;IAEE,+BAAsC,EAAA;EAfxC;IAAgC,yBAA4B,EAAA;EAC5D;;IAEE,6BAAoC,EAAA;EAEtC;;IAEE,+BAAwC,EAAA;EAE1C;;IAEE,gCAA0C,EAAA;EAE5C;;IAEE,8BAAsC,EAAA;EAfxC;IAAgC,uBAA4B,EAAA;EAC5D;;IAEE,2BAAoC,EAAA;EAEtC;;IAEE,6BAAwC,EAAA;EAE1C;;IAEE,8BAA0C,EAAA;EAE5C;;IAEE,4BAAsC,EAAA;EAfxC;IAAgC,yBAA4B,EAAA;EAC5D;;IAEE,6BAAoC,EAAA;EAEtC;;IAEE,+BAAwC,EAAA;EAE1C;;IAEE,gCAA0C,EAAA;EAE5C;;IAEE,8BAAsC,EAAA;EAfxC;IAAgC,uBAA4B,EAAA;EAC5D;;IAEE,2BAAoC,EAAA;EAEtC;;IAEE,6BAAwC,EAAA;EAE1C;;IAEE,8BAA0C,EAAA;EAE5C;;IAEE,4BAAsC,EAAA;EAfxC;IAAgC,qBAA4B,EAAA;EAC5D;;IAEE,yBAAoC,EAAA;EAEtC;;IAEE,2BAAwC,EAAA;EAE1C;;IAEE,4BAA0C,EAAA;EAE5C;;IAEE,0BAAsC,EAAA;EAfxC;IAAgC,2BAA4B,EAAA;EAC5D;;IAEE,+BAAoC,EAAA;EAEtC;;IAEE,iCAAwC,EAAA;EAE1C;;IAEE,kCAA0C,EAAA;EAE5C;;IAEE,gCAAsC,EAAA;EAfxC;IAAgC,0BAA4B,EAAA;EAC5D;;IAEE,8BAAoC,EAAA;EAEtC;;IAEE,gCAAwC,EAAA;EAE1C;;IAEE,iCAA0C,EAAA;EAE5C;;IAEE,+BAAsC,EAAA;EAfxC;IAAgC,wBAA4B,EAAA;EAC5D;;IAEE,4BAAoC,EAAA;EAEtC;;IAEE,8BAAwC,EAAA;EAE1C;;IAEE,+BAA0C,EAAA;EAE5C;;IAEE,6BAAsC,EAAA;EAfxC;IAAgC,0BAA4B,EAAA;EAC5D;;IAEE,8BAAoC,EAAA;EAEtC;;IAEE,gCAAwC,EAAA;EAE1C;;IAEE,iCAA0C,EAAA;EAE5C;;IAEE,+BAAsC,EAAA;EAfxC;IAAgC,wBAA4B,EAAA;EAC5D;;IAEE,4BAAoC,EAAA;EAEtC;;IAEE,8BAAwC,EAAA;EAE1C;;IAEE,+BAA0C,EAAA;EAE5C;;IAEE,6BAAsC,EAAA;EAQxC;IAAwB,2BAA2B,EAAA;EACnD;;IAEE,+BAA+B,EAAA;EAEjC;;IAEE,iCAAiC,EAAA;EAEnC;;IAEE,kCAAkC,EAAA;EAEpC;;IAEE,gCAAgC,EAAA;EAflC;IAAwB,0BAA2B,EAAA;EACnD;;IAEE,8BAA+B,EAAA;EAEjC;;IAEE,gCAAiC,EAAA;EAEnC;;IAEE,iCAAkC,EAAA;EAEpC;;IAEE,+BAAgC,EAAA;EAflC;IAAwB,wBAA2B,EAAA;EACnD;;IAEE,4BAA+B,EAAA;EAEjC;;IAEE,8BAAiC,EAAA;EAEnC;;IAEE,+BAAkC,EAAA;EAEpC;;IAEE,6BAAgC,EAAA;EAflC;IAAwB,0BAA2B,EAAA;EACnD;;IAEE,8BAA+B,EAAA;EAEjC;;IAEE,gCAAiC,EAAA;EAEnC;;IAEE,iCAAkC,EAAA;EAEpC;;IAEE,+BAAgC,EAAA;EAflC;IAAwB,wBAA2B,EAAA;EACnD;;IAEE,4BAA+B,EAAA;EAEjC;;IAEE,8BAAiC,EAAA;EAEnC;;IAEE,+BAAkC,EAAA;EAEpC;;IAEE,6BAAgC,EAAA;EAMtC;IAAmB,uBAAuB,EAAA;EAC1C;;IAEE,2BAA2B,EAAA;EAE7B;;IAEE,6BAA6B,EAAA;EAE/B;;IAEE,8BAA8B,EAAA;EAEhC;;IAEE,4BAA4B,EAAA;EChD9B;IAAwB,2BAA2B,EAAA;EACnD;IAAwB,4BAA4B,EAAA;EACpD;IAAwB,6BAA6B,EAAA,E3DdtD;;AgEmDC;IhEtDF;MCWI,gBVsMK,EAAA;EShML;IACE,gBT+LG,EAAA;EWzLL;IACE,aAAa;IACb,mBAAY;YAAZ,YAAY;IACZ,eAAe,EAAA;EAIf;ID4BJ,mBAAuB;YAAvB,cAAuB;IACvB,eAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,aAAuB;IACvB,cAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,mBAAuB;IACvB,oBAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,aAAuB;IACvB,cAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,aAAuB;IACvB,cAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,mBAAuB;IACvB,oBAAwB,EAAA;ECxBtB;IDMJ,mBAAc;YAAd,cAAc;IACd,WAAW;IACX,eAAe,EAAA;ECHT;IDPN,mBAAsC;YAAtC,kBAAsC;IAItC,mBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,aAAsC;IAItC,cAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,aAAsC;IAItC,cAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,aAAsC;IAItC,cAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,cAAsC;IAItC,eAAuC,EAAA;ECQnC;IAAwB,4BAAS;YAAT,SAAS,EAAA;EAEjC;IAAuB,6BX6KG;YW7KH,SX6KG,EAAA;EW1KxB;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,SADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,SADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,SADZ,EAAA;EAOV;IDRR,cAA4B,EAAA;ECQpB;IDRR,qBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,gBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,gBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,gBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;EYd5C;IACE,WAAW;IACX,OAAO,EAAA;EAGT;IACE,QAAQ;IACR,UAAU,EAAA;EOqGZ;IAoBI,8BAAqB;IAArB,6BAAqB;YAArB,qBAAqB;IACrB,uBAA2B;YAA3B,2BAA2B,EAAA;IArB9B;MAwBK,8BAAmB;MAAnB,6BAAmB;cAAnB,mBAAmB,EAAA;MAxBxB;QA2BO,kBAAkB,EAAA;MA3BzB;QA+BO,qB7BgiB6B;Q6B/hB7B,oB7B+hB6B,EAAA;I6B/jBpC;;MAsCK,iBAAiB,EAAA;IAtCtB;MAqDK,+BAAwB;MAAxB,wBAAwB;MAGxB,gBAAgB,EAAA;IAxDrB;MA4DK,aAAa,EAAA;EYpHnB;IACE,8BAAmB;IAAnB,6BAAmB;YAAnB,mBAAmB,EAAA;IADrB;MlCjCA,kCPsLgC;MOlMhC,0BkCmDwC,EAAA;IANxC;MlC7CA,gCPkMgC;MOtLhC,4BkC4C0C,EAAA;IAX1C;MAeM,aAAa,EAAA;IAfnB;MAmBM,qBzC+HuB;MyC9HvB,oBAAoB,EAAA;MApB1B;QAuBQ,iBzC2HqB;QyC1HrB,sBzC0HqB,EAAA;EwDpO3B;IAAwB,wBAA0B,EAAA;EAAlD;IAAwB,0BAA0B,EAAA;EAAlD;IAAwB,gCAA0B,EAAA;EAAlD;IAAwB,yBAA0B,EAAA;EAAlD;IAAwB,yBAA0B,EAAA;EAAlD;IAAwB,6BAA0B,EAAA;EAAlD;IAAwB,8BAA0B,EAAA;EAAlD;IAAwB,+BAA0B;IAA1B,wBAA0B,EAAA;EAAlD;IAAwB,sCAA0B;IAA1B,+BAA0B,EAAA;EEDpD;IAAgC,yCAA8B;IAA9B,wCAA8B;YAA9B,8BAA8B,EAAA;EAC9D;IAAgC,uCAAiC;IAAjC,wCAAiC;YAAjC,iCAAiC,EAAA;EACjE;IAAgC,yCAAsC;IAAtC,yCAAsC;YAAtC,sCAAsC,EAAA;EACtE;IAAgC,uCAAyC;IAAzC,yCAAyC;YAAzC,yCAAyC,EAAA;EAEzE;IAA8B,0BAA0B,EAAA;EACxD;IAA8B,4BAA4B,EAAA;EAC1D;IAA8B,kCAAkC,EAAA;EAChE;IAA8B,8BAAyB;YAAzB,yBAAyB,EAAA;EACvD;IAA8B,8BAAuB;YAAvB,uBAAuB,EAAA;EACrD;IAA8B,8BAAuB;YAAvB,uBAAuB,EAAA;EACrD;IAA8B,yBAAyB,EAAA;EACvD;IAA8B,yBAAyB,EAAA;EAEvD;IAAoC,kCAAsC;YAAtC,sCAAsC,EAAA;EAC1E;IAAoC,gCAAoC;YAApC,oCAAoC,EAAA;EACxE;IAAoC,mCAAkC;YAAlC,kCAAkC,EAAA;EACtE;IAAoC,oCAAyC;YAAzC,yCAAyC,EAAA;EAC7E;IAAoC,wCAAwC,EAAA;EAE5E;IAAiC,mCAAkC;YAAlC,kCAAkC,EAAA;EACnE;IAAiC,iCAAgC;YAAhC,gCAAgC,EAAA;EACjE;IAAiC,oCAA8B;YAA9B,8BAA8B,EAAA;EAC/D;IAAiC,sCAAgC;YAAhC,gCAAgC,EAAA;EACjE;IAAiC,qCAA+B;YAA/B,+BAA+B,EAAA;EAEhE;IAAkC,oCAAoC,EAAA;EACtE;IAAkC,kCAAkC,EAAA;EACpE;IAAkC,gCAAgC,EAAA;EAClE;IAAkC,uCAAuC,EAAA;EACzE;IAAkC,sCAAsC,EAAA;EACxE;IAAkC,iCAAiC,EAAA;EAEnE;IAAgC,2BAA2B,EAAA;EAC3D;IAAgC,iCAAiC,EAAA;EACjE;IAAgC,+BAA+B,EAAA;EAC/D;IAAgC,6BAA6B,EAAA;EAC7D;IAAgC,+BAA+B,EAAA;EAC/D;IAAgC,8BAA8B,EAAA;EC1C9D;IAAwB,sBAAsB,EAAA;EAC9C;IAAwB,uBAAuB,EAAA;EAC/C;IAAwB,sBAAsB,EAAA;EQE1C;IAAgC,oBAA4B,EAAA;EAC5D;;IAEE,wBAAoC,EAAA;EAEtC;;IAEE,0BAAwC,EAAA;EAE1C;;IAEE,2BAA0C,EAAA;EAE5C;;IAEE,yBAAsC,EAAA;EAfxC;IAAgC,0BAA4B,EAAA;EAC5D;;IAEE,8BAAoC,EAAA;EAEtC;;IAEE,gCAAwC,EAAA;EAE1C;;IAEE,iCAA0C,EAAA;EAE5C;;IAEE,+BAAsC,EAAA;EAfxC;IAAgC,yBAA4B,EAAA;EAC5D;;IAEE,6BAAoC,EAAA;EAEtC;;IAEE,+BAAwC,EAAA;EAE1C;;IAEE,gCAA0C,EAAA;EAE5C;;IAEE,8BAAsC,EAAA;EAfxC;IAAgC,uBAA4B,EAAA;EAC5D;;IAEE,2BAAoC,EAAA;EAEtC;;IAEE,6BAAwC,EAAA;EAE1C;;IAEE,8BAA0C,EAAA;EAE5C;;IAEE,4BAAsC,EAAA;EAfxC;IAAgC,yBAA4B,EAAA;EAC5D;;IAEE,6BAAoC,EAAA;EAEtC;;IAEE,+BAAwC,EAAA;EAE1C;;IAEE,gCAA0C,EAAA;EAE5C;;IAEE,8BAAsC,EAAA;EAfxC;IAAgC,uBAA4B,EAAA;EAC5D;;IAEE,2BAAoC,EAAA;EAEtC;;IAEE,6BAAwC,EAAA;EAE1C;;IAEE,8BAA0C,EAAA;EAE5C;;IAEE,4BAAsC,EAAA;EAfxC;IAAgC,qBAA4B,EAAA;EAC5D;;IAEE,yBAAoC,EAAA;EAEtC;;IAEE,2BAAwC,EAAA;EAE1C;;IAEE,4BAA0C,EAAA;EAE5C;;IAEE,0BAAsC,EAAA;EAfxC;IAAgC,2BAA4B,EAAA;EAC5D;;IAEE,+BAAoC,EAAA;EAEtC;;IAEE,iCAAwC,EAAA;EAE1C;;IAEE,kCAA0C,EAAA;EAE5C;;IAEE,gCAAsC,EAAA;EAfxC;IAAgC,0BAA4B,EAAA;EAC5D;;IAEE,8BAAoC,EAAA;EAEtC;;IAEE,gCAAwC,EAAA;EAE1C;;IAEE,iCAA0C,EAAA;EAE5C;;IAEE,+BAAsC,EAAA;EAfxC;IAAgC,wBAA4B,EAAA;EAC5D;;IAEE,4BAAoC,EAAA;EAEtC;;IAEE,8BAAwC,EAAA;EAE1C;;IAEE,+BAA0C,EAAA;EAE5C;;IAEE,6BAAsC,EAAA;EAfxC;IAAgC,0BAA4B,EAAA;EAC5D;;IAEE,8BAAoC,EAAA;EAEtC;;IAEE,gCAAwC,EAAA;EAE1C;;IAEE,iCAA0C,EAAA;EAE5C;;IAEE,+BAAsC,EAAA;EAfxC;IAAgC,wBAA4B,EAAA;EAC5D;;IAEE,4BAAoC,EAAA;EAEtC;;IAEE,8BAAwC,EAAA;EAE1C;;IAEE,+BAA0C,EAAA;EAE5C;;IAEE,6BAAsC,EAAA;EAQxC;IAAwB,2BAA2B,EAAA;EACnD;;IAEE,+BAA+B,EAAA;EAEjC;;IAEE,iCAAiC,EAAA;EAEnC;;IAEE,kCAAkC,EAAA;EAEpC;;IAEE,gCAAgC,EAAA;EAflC;IAAwB,0BAA2B,EAAA;EACnD;;IAEE,8BAA+B,EAAA;EAEjC;;IAEE,gCAAiC,EAAA;EAEnC;;IAEE,iCAAkC,EAAA;EAEpC;;IAEE,+BAAgC,EAAA;EAflC;IAAwB,wBAA2B,EAAA;EACnD;;IAEE,4BAA+B,EAAA;EAEjC;;IAEE,8BAAiC,EAAA;EAEnC;;IAEE,+BAAkC,EAAA;EAEpC;;IAEE,6BAAgC,EAAA;EAflC;IAAwB,0BAA2B,EAAA;EACnD;;IAEE,8BAA+B,EAAA;EAEjC;;IAEE,gCAAiC,EAAA;EAEnC;;IAEE,iCAAkC,EAAA;EAEpC;;IAEE,+BAAgC,EAAA;EAflC;IAAwB,wBAA2B,EAAA;EACnD;;IAEE,4BAA+B,EAAA;EAEjC;;IAEE,8BAAiC,EAAA;EAEnC;;IAEE,+BAAkC,EAAA;EAEpC;;IAEE,6BAAgC,EAAA;EAMtC;IAAmB,uBAAuB,EAAA;EAC1C;;IAEE,2BAA2B,EAAA;EAE7B;;IAEE,6BAA6B,EAAA;EAE/B;;IAEE,8BAA8B,EAAA;EAEhC;;IAEE,4BAA4B,EAAA;EChD9B;IAAwB,2BAA2B,EAAA;EACnD;IAAwB,4BAA4B,EAAA;EACpD;IAAwB,6BAA6B,EAAA,E3DdtD;;AgEmDC;IhEtDF;MCWI,gBVuMK,EAAA;ESjML;IACE,gBTgMG,EAAA;EW1LL;IACE,aAAa;IACb,mBAAY;YAAZ,YAAY;IACZ,eAAe,EAAA;EAIf;ID4BJ,mBAAuB;YAAvB,cAAuB;IACvB,eAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,aAAuB;IACvB,cAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,mBAAuB;IACvB,oBAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,aAAuB;IACvB,cAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,aAAuB;IACvB,cAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,mBAAuB;IACvB,oBAAwB,EAAA;ECxBtB;IDMJ,mBAAc;YAAd,cAAc;IACd,WAAW;IACX,eAAe,EAAA;ECHT;IDPN,mBAAsC;YAAtC,kBAAsC;IAItC,mBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,aAAsC;IAItC,cAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,aAAsC;IAItC,cAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,aAAsC;IAItC,cAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,cAAsC;IAItC,eAAuC,EAAA;ECQnC;IAAwB,4BAAS;YAAT,SAAS,EAAA;EAEjC;IAAuB,6BX6KG;YW7KH,SX6KG,EAAA;EW1KxB;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,SADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,SADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,SADZ,EAAA;EAOV;IDRR,cAA4B,EAAA;ECQpB;IDRR,qBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,gBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,gBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,gBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;EYd5C;IACE,WAAW;IACX,OAAO,EAAA;EAGT;IACE,QAAQ;IACR,UAAU,EAAA;EOqGZ;IAoBI,8BAAqB;IAArB,6BAAqB;YAArB,qBAAqB;IACrB,uBAA2B;YAA3B,2BAA2B,EAAA;IArB9B;MAwBK,8BAAmB;MAAnB,6BAAmB;cAAnB,mBAAmB,EAAA;MAxBxB;QA2BO,kBAAkB,EAAA;MA3BzB;QA+BO,qB7BgiB6B;Q6B/hB7B,oB7B+hB6B,EAAA;I6B/jBpC;;MAsCK,iBAAiB,EAAA;IAtCtB;MAqDK,+BAAwB;MAAxB,wBAAwB;MAGxB,gBAAgB,EAAA;IAxDrB;MA4DK,aAAa,EAAA;EYpHnB;IACE,8BAAmB;IAAnB,6BAAmB;YAAnB,mBAAmB,EAAA;IADrB;MlCjCA,kCPsLgC;MOlMhC,0BkCmDwC,EAAA;IANxC;MlC7CA,gCPkMgC;MOtLhC,4BkC4C0C,EAAA;IAX1C;MAeM,aAAa,EAAA;IAfnB;MAmBM,qBzC+HuB;MyC9HvB,oBAAoB,EAAA;MApB1B;QAuBQ,iBzC2HqB;QyC1HrB,sBzC0HqB,EAAA;E6CT/B;;IAEE,gB7CouBqC,EAAA;EwDj8BnC;IAAwB,wBAA0B,EAAA;EAAlD;IAAwB,0BAA0B,EAAA;EAAlD;IAAwB,gCAA0B,EAAA;EAAlD;IAAwB,yBAA0B,EAAA;EAAlD;IAAwB,yBAA0B,EAAA;EAAlD;IAAwB,6BAA0B,EAAA;EAAlD;IAAwB,8BAA0B,EAAA;EAAlD;IAAwB,+BAA0B;IAA1B,wBAA0B,EAAA;EAAlD;IAAwB,sCAA0B;IAA1B,+BAA0B,EAAA;EEDpD;IAAgC,yCAA8B;IAA9B,wCAA8B;YAA9B,8BAA8B,EAAA;EAC9D;IAAgC,uCAAiC;IAAjC,wCAAiC;YAAjC,iCAAiC,EAAA;EACjE;IAAgC,yCAAsC;IAAtC,yCAAsC;YAAtC,sCAAsC,EAAA;EACtE;IAAgC,uCAAyC;IAAzC,yCAAyC;YAAzC,yCAAyC,EAAA;EAEzE;IAA8B,0BAA0B,EAAA;EACxD;IAA8B,4BAA4B,EAAA;EAC1D;IAA8B,kCAAkC,EAAA;EAChE;IAA8B,8BAAyB;YAAzB,yBAAyB,EAAA;EACvD;IAA8B,8BAAuB;YAAvB,uBAAuB,EAAA;EACrD;IAA8B,8BAAuB;YAAvB,uBAAuB,EAAA;EACrD;IAA8B,yBAAyB,EAAA;EACvD;IAA8B,yBAAyB,EAAA;EAEvD;IAAoC,kCAAsC;YAAtC,sCAAsC,EAAA;EAC1E;IAAoC,gCAAoC;YAApC,oCAAoC,EAAA;EACxE;IAAoC,mCAAkC;YAAlC,kCAAkC,EAAA;EACtE;IAAoC,oCAAyC;YAAzC,yCAAyC,EAAA;EAC7E;IAAoC,wCAAwC,EAAA;EAE5E;IAAiC,mCAAkC;YAAlC,kCAAkC,EAAA;EACnE;IAAiC,iCAAgC;YAAhC,gCAAgC,EAAA;EACjE;IAAiC,oCAA8B;YAA9B,8BAA8B,EAAA;EAC/D;IAAiC,sCAAgC;YAAhC,gCAAgC,EAAA;EACjE;IAAiC,qCAA+B;YAA/B,+BAA+B,EAAA;EAEhE;IAAkC,oCAAoC,EAAA;EACtE;IAAkC,kCAAkC,EAAA;EACpE;IAAkC,gCAAgC,EAAA;EAClE;IAAkC,uCAAuC,EAAA;EACzE;IAAkC,sCAAsC,EAAA;EACxE;IAAkC,iCAAiC,EAAA;EAEnE;IAAgC,2BAA2B,EAAA;EAC3D;IAAgC,iCAAiC,EAAA;EACjE;IAAgC,+BAA+B,EAAA;EAC/D;IAAgC,6BAA6B,EAAA;EAC7D;IAAgC,+BAA+B,EAAA;EAC/D;IAAgC,8BAA8B,EAAA;EC1C9D;IAAwB,sBAAsB,EAAA;EAC9C;IAAwB,uBAAuB,EAAA;EAC/C;IAAwB,sBAAsB,EAAA;EQE1C;IAAgC,oBAA4B,EAAA;EAC5D;;IAEE,wBAAoC,EAAA;EAEtC;;IAEE,0BAAwC,EAAA;EAE1C;;IAEE,2BAA0C,EAAA;EAE5C;;IAEE,yBAAsC,EAAA;EAfxC;IAAgC,0BAA4B,EAAA;EAC5D;;IAEE,8BAAoC,EAAA;EAEtC;;IAEE,gCAAwC,EAAA;EAE1C;;IAEE,iCAA0C,EAAA;EAE5C;;IAEE,+BAAsC,EAAA;EAfxC;IAAgC,yBAA4B,EAAA;EAC5D;;IAEE,6BAAoC,EAAA;EAEtC;;IAEE,+BAAwC,EAAA;EAE1C;;IAEE,gCAA0C,EAAA;EAE5C;;IAEE,8BAAsC,EAAA;EAfxC;IAAgC,uBAA4B,EAAA;EAC5D;;IAEE,2BAAoC,EAAA;EAEtC;;IAEE,6BAAwC,EAAA;EAE1C;;IAEE,8BAA0C,EAAA;EAE5C;;IAEE,4BAAsC,EAAA;EAfxC;IAAgC,yBAA4B,EAAA;EAC5D;;IAEE,6BAAoC,EAAA;EAEtC;;IAEE,+BAAwC,EAAA;EAE1C;;IAEE,gCAA0C,EAAA;EAE5C;;IAEE,8BAAsC,EAAA;EAfxC;IAAgC,uBAA4B,EAAA;EAC5D;;IAEE,2BAAoC,EAAA;EAEtC;;IAEE,6BAAwC,EAAA;EAE1C;;IAEE,8BAA0C,EAAA;EAE5C;;IAEE,4BAAsC,EAAA;EAfxC;IAAgC,qBAA4B,EAAA;EAC5D;;IAEE,yBAAoC,EAAA;EAEtC;;IAEE,2BAAwC,EAAA;EAE1C;;IAEE,4BAA0C,EAAA;EAE5C;;IAEE,0BAAsC,EAAA;EAfxC;IAAgC,2BAA4B,EAAA;EAC5D;;IAEE,+BAAoC,EAAA;EAEtC;;IAEE,iCAAwC,EAAA;EAE1C;;IAEE,kCAA0C,EAAA;EAE5C;;IAEE,gCAAsC,EAAA;EAfxC;IAAgC,0BAA4B,EAAA;EAC5D;;IAEE,8BAAoC,EAAA;EAEtC;;IAEE,gCAAwC,EAAA;EAE1C;;IAEE,iCAA0C,EAAA;EAE5C;;IAEE,+BAAsC,EAAA;EAfxC;IAAgC,wBAA4B,EAAA;EAC5D;;IAEE,4BAAoC,EAAA;EAEtC;;IAEE,8BAAwC,EAAA;EAE1C;;IAEE,+BAA0C,EAAA;EAE5C;;IAEE,6BAAsC,EAAA;EAfxC;IAAgC,0BAA4B,EAAA;EAC5D;;IAEE,8BAAoC,EAAA;EAEtC;;IAEE,gCAAwC,EAAA;EAE1C;;IAEE,iCAA0C,EAAA;EAE5C;;IAEE,+BAAsC,EAAA;EAfxC;IAAgC,wBAA4B,EAAA;EAC5D;;IAEE,4BAAoC,EAAA;EAEtC;;IAEE,8BAAwC,EAAA;EAE1C;;IAEE,+BAA0C,EAAA;EAE5C;;IAEE,6BAAsC,EAAA;EAQxC;IAAwB,2BAA2B,EAAA;EACnD;;IAEE,+BAA+B,EAAA;EAEjC;;IAEE,iCAAiC,EAAA;EAEnC;;IAEE,kCAAkC,EAAA;EAEpC;;IAEE,gCAAgC,EAAA;EAflC;IAAwB,0BAA2B,EAAA;EACnD;;IAEE,8BAA+B,EAAA;EAEjC;;IAEE,gCAAiC,EAAA;EAEnC;;IAEE,iCAAkC,EAAA;EAEpC;;IAEE,+BAAgC,EAAA;EAflC;IAAwB,wBAA2B,EAAA;EACnD;;IAEE,4BAA+B,EAAA;EAEjC;;IAEE,8BAAiC,EAAA;EAEnC;;IAEE,+BAAkC,EAAA;EAEpC;;IAEE,6BAAgC,EAAA;EAflC;IAAwB,0BAA2B,EAAA;EACnD;;IAEE,8BAA+B,EAAA;EAEjC;;IAEE,gCAAiC,EAAA;EAEnC;;IAEE,iCAAkC,EAAA;EAEpC;;IAEE,+BAAgC,EAAA;EAflC;IAAwB,wBAA2B,EAAA;EACnD;;IAEE,4BAA+B,EAAA;EAEjC;;IAEE,8BAAiC,EAAA;EAEnC;;IAEE,+BAAkC,EAAA;EAEpC;;IAEE,6BAAgC,EAAA;EAMtC;IAAmB,uBAAuB,EAAA;EAC1C;;IAEE,2BAA2B,EAAA;EAE7B;;IAEE,6BAA6B,EAAA;EAE/B;;IAEE,8BAA8B,EAAA;EAEhC;;IAEE,4BAA4B,EAAA;EChD9B;IAAwB,2BAA2B,EAAA;EACnD;IAAwB,4BAA4B,EAAA;EACpD;IAAwB,6BAA6B,EAAA,E3DdtD;;AgEmDC;IhEtDF;MCWI,iBVwMM,EAAA;ESlMN;IACE,iBTiMI,EAAA;EW3LN;IACE,aAAa;IACb,mBAAY;YAAZ,YAAY;IACZ,eAAe,EAAA;EAIf;ID4BJ,mBAAuB;YAAvB,cAAuB;IACvB,eAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,aAAuB;IACvB,cAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,mBAAuB;IACvB,oBAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,aAAuB;IACvB,cAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,aAAuB;IACvB,cAAwB,EAAA;EC7BpB;ID4BJ,mBAAuB;YAAvB,mBAAuB;IACvB,oBAAwB,EAAA;ECxBtB;IDMJ,mBAAc;YAAd,cAAc;IACd,WAAW;IACX,eAAe,EAAA;ECHT;IDPN,mBAAsC;YAAtC,kBAAsC;IAItC,mBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,aAAsC;IAItC,cAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,aAAsC;IAItC,cAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,aAAsC;IAItC,cAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,mBAAsC;IAItC,oBAAuC,EAAA;ECGjC;IDPN,mBAAsC;YAAtC,cAAsC;IAItC,eAAuC,EAAA;ECQnC;IAAwB,4BAAS;YAAT,SAAS,EAAA;EAEjC;IAAuB,6BX6KG;YW7KH,SX6KG,EAAA;EW1KxB;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,4BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,QADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,SADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,SADZ,EAAA;EACZ;IAAwB,6BADZ;YACY,SADZ,EAAA;EAOV;IDRR,cAA4B,EAAA;ECQpB;IDRR,qBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,gBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,gBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,gBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;ECQtC;IDRR,sBAA8C,EAAA;EYd5C;IACE,WAAW;IACX,OAAO,EAAA;EAGT;IACE,QAAQ;IACR,UAAU,EAAA;EOqGZ;IAoBI,8BAAqB;IAArB,6BAAqB;YAArB,qBAAqB;IACrB,uBAA2B;YAA3B,2BAA2B,EAAA;IArB9B;MAwBK,8BAAmB;MAAnB,6BAAmB;cAAnB,mBAAmB,EAAA;MAxBxB;QA2BO,kBAAkB,EAAA;MA3BzB;QA+BO,qB7BgiB6B;Q6B/hB7B,oB7B+hB6B,EAAA;I6B/jBpC;;MAsCK,iBAAiB,EAAA;IAtCtB;MAqDK,+BAAwB;MAAxB,wBAAwB;MAGxB,gBAAgB,EAAA;IAxDrB;MA4DK,aAAa,EAAA;EYpHnB;IACE,8BAAmB;IAAnB,6BAAmB;YAAnB,mBAAmB,EAAA;IADrB;MlCjCA,kCPsLgC;MOlMhC,0BkCmDwC,EAAA;IANxC;MlC7CA,gCPkMgC;MOtLhC,4BkC4C0C,EAAA;IAX1C;MAeM,aAAa,EAAA;IAfnB;MAmBM,qBzC+HuB;MyC9HvB,oBAAoB,EAAA;MApB1B;QAuBQ,iBzC2HqB;QyC1HrB,sBzC0HqB,EAAA;E6CF/B;IAAY,iB7C8tB4B,EAAA;EwDh8BpC;IAAwB,wBAA0B,EAAA;EAAlD;IAAwB,0BAA0B,EAAA;EAAlD;IAAwB,gCAA0B,EAAA;EAAlD;IAAwB,yBAA0B,EAAA;EAAlD;IAAwB,yBAA0B,EAAA;EAAlD;IAAwB,6BAA0B,EAAA;EAAlD;IAAwB,8BAA0B,EAAA;EAAlD;IAAwB,+BAA0B;IAA1B,wBAA0B,EAAA;EAAlD;IAAwB,sCAA0B;IAA1B,+BAA0B,EAAA;EEDpD;IAAgC,yCAA8B;IAA9B,wCAA8B;YAA9B,8BAA8B,EAAA;EAC9D;IAAgC,uCAAiC;IAAjC,wCAAiC;YAAjC,iCAAiC,EAAA;EACjE;IAAgC,yCAAsC;IAAtC,yCAAsC;YAAtC,sCAAsC,EAAA;EACtE;IAAgC,uCAAyC;IAAzC,yCAAyC;YAAzC,yCAAyC,EAAA;EAEzE;IAA8B,0BAA0B,EAAA;EACxD;IAA8B,4BAA4B,EAAA;EAC1D;IAA8B,kCAAkC,EAAA;EAChE;IAA8B,8BAAyB;YAAzB,yBAAyB,EAAA;EACvD;IAA8B,8BAAuB;YAAvB,uBAAuB,EAAA;EACrD;IAA8B,8BAAuB;YAAvB,uBAAuB,EAAA;EACrD;IAA8B,yBAAyB,EAAA;EACvD;IAA8B,yBAAyB,EAAA;EAEvD;IAAoC,kCAAsC;YAAtC,sCAAsC,EAAA;EAC1E;IAAoC,gCAAoC;YAApC,oCAAoC,EAAA;EACxE;IAAoC,mCAAkC;YAAlC,kCAAkC,EAAA;EACtE;IAAoC,oCAAyC;YAAzC,yCAAyC,EAAA;EAC7E;IAAoC,wCAAwC,EAAA;EAE5E;IAAiC,mCAAkC;YAAlC,kCAAkC,EAAA;EACnE;IAAiC,iCAAgC;YAAhC,gCAAgC,EAAA;EACjE;IAAiC,oCAA8B;YAA9B,8BAA8B,EAAA;EAC/D;IAAiC,sCAAgC;YAAhC,gCAAgC,EAAA;EACjE;IAAiC,qCAA+B;YAA/B,+BAA+B,EAAA;EAEhE;IAAkC,oCAAoC,EAAA;EACtE;IAAkC,kCAAkC,EAAA;EACpE;IAAkC,gCAAgC,EAAA;EAClE;IAAkC,uCAAuC,EAAA;EACzE;IAAkC,sCAAsC,EAAA;EACxE;IAAkC,iCAAiC,EAAA;EAEnE;IAAgC,2BAA2B,EAAA;EAC3D;IAAgC,iCAAiC,EAAA;EACjE;IAAgC,+BAA+B,EAAA;EAC/D;IAAgC,6BAA6B,EAAA;EAC7D;IAAgC,+BAA+B,EAAA;EAC/D;IAAgC,8BAA8B,EAAA;EC1C9D;IAAwB,sBAAsB,EAAA;EAC9C;IAAwB,uBAAuB,EAAA;EAC/C;IAAwB,sBAAsB,EAAA;EQE1C;IAAgC,oBAA4B,EAAA;EAC5D;;IAEE,wBAAoC,EAAA;EAEtC;;IAEE,0BAAwC,EAAA;EAE1C;;IAEE,2BAA0C,EAAA;EAE5C;;IAEE,yBAAsC,EAAA;EAfxC;IAAgC,0BAA4B,EAAA;EAC5D;;IAEE,8BAAoC,EAAA;EAEtC;;IAEE,gCAAwC,EAAA;EAE1C;;IAEE,iCAA0C,EAAA;EAE5C;;IAEE,+BAAsC,EAAA;EAfxC;IAAgC,yBAA4B,EAAA;EAC5D;;IAEE,6BAAoC,EAAA;EAEtC;;IAEE,+BAAwC,EAAA;EAE1C;;IAEE,gCAA0C,EAAA;EAE5C;;IAEE,8BAAsC,EAAA;EAfxC;IAAgC,uBAA4B,EAAA;EAC5D;;IAEE,2BAAoC,EAAA;EAEtC;;IAEE,6BAAwC,EAAA;EAE1C;;IAEE,8BAA0C,EAAA;EAE5C;;IAEE,4BAAsC,EAAA;EAfxC;IAAgC,yBAA4B,EAAA;EAC5D;;IAEE,6BAAoC,EAAA;EAEtC;;IAEE,+BAAwC,EAAA;EAE1C;;IAEE,gCAA0C,EAAA;EAE5C;;IAEE,8BAAsC,EAAA;EAfxC;IAAgC,uBAA4B,EAAA;EAC5D;;IAEE,2BAAoC,EAAA;EAEtC;;IAEE,6BAAwC,EAAA;EAE1C;;IAEE,8BAA0C,EAAA;EAE5C;;IAEE,4BAAsC,EAAA;EAfxC;IAAgC,qBAA4B,EAAA;EAC5D;;IAEE,yBAAoC,EAAA;EAEtC;;IAEE,2BAAwC,EAAA;EAE1C;;IAEE,4BAA0C,EAAA;EAE5C;;IAEE,0BAAsC,EAAA;EAfxC;IAAgC,2BAA4B,EAAA;EAC5D;;IAEE,+BAAoC,EAAA;EAEtC;;IAEE,iCAAwC,EAAA;EAE1C;;IAEE,kCAA0C,EAAA;EAE5C;;IAEE,gCAAsC,EAAA;EAfxC;IAAgC,0BAA4B,EAAA;EAC5D;;IAEE,8BAAoC,EAAA;EAEtC;;IAEE,gCAAwC,EAAA;EAE1C;;IAEE,iCAA0C,EAAA;EAE5C;;IAEE,+BAAsC,EAAA;EAfxC;IAAgC,wBAA4B,EAAA;EAC5D;;IAEE,4BAAoC,EAAA;EAEtC;;IAEE,8BAAwC,EAAA;EAE1C;;IAEE,+BAA0C,EAAA;EAE5C;;IAEE,6BAAsC,EAAA;EAfxC;IAAgC,0BAA4B,EAAA;EAC5D;;IAEE,8BAAoC,EAAA;EAEtC;;IAEE,gCAAwC,EAAA;EAE1C;;IAEE,iCAA0C,EAAA;EAE5C;;IAEE,+BAAsC,EAAA;EAfxC;IAAgC,wBAA4B,EAAA;EAC5D;;IAEE,4BAAoC,EAAA;EAEtC;;IAEE,8BAAwC,EAAA;EAE1C;;IAEE,+BAA0C,EAAA;EAE5C;;IAEE,6BAAsC,EAAA;EAQxC;IAAwB,2BAA2B,EAAA;EACnD;;IAEE,+BAA+B,EAAA;EAEjC;;IAEE,iCAAiC,EAAA;EAEnC;;IAEE,kCAAkC,EAAA;EAEpC;;IAEE,gCAAgC,EAAA;EAflC;IAAwB,0BAA2B,EAAA;EACnD;;IAEE,8BAA+B,EAAA;EAEjC;;IAEE,gCAAiC,EAAA;EAEnC;;IAEE,iCAAkC,EAAA;EAEpC;;IAEE,+BAAgC,EAAA;EAflC;IAAwB,wBAA2B,EAAA;EACnD;;IAEE,4BAA+B,EAAA;EAEjC;;IAEE,8BAAiC,EAAA;EAEnC;;IAEE,+BAAkC,EAAA;EAEpC;;IAEE,6BAAgC,EAAA;EAflC;IAAwB,0BAA2B,EAAA;EACnD;;IAEE,8BAA+B,EAAA;EAEjC;;IAEE,gCAAiC,EAAA;EAEnC;;IAEE,iCAAkC,EAAA;EAEpC;;IAEE,+BAAgC,EAAA;EAflC;IAAwB,wBAA2B,EAAA;EACnD;;IAEE,4BAA+B,EAAA;EAEjC;;IAEE,8BAAiC,EAAA;EAEnC;;IAEE,+BAAkC,EAAA;EAEpC;;IAEE,6BAAgC,EAAA;EAMtC;IAAmB,uBAAuB,EAAA;EAC1C;;IAEE,2BAA2B,EAAA;EAE7B;;IAEE,6BAA6B,EAAA;EAE/B;;IAEE,8BAA8B,EAAA;EAEhC;;IAEE,4BAA4B,EAAA;EChD9B;IAAwB,2BAA2B,EAAA;EACnD;IAAwB,4BAA4B,EAAA;EACpD;IAAwB,6BAA6B,EAAA,E3DdtD;;AgEgEC;E7DiGA;IAEI,cAAc;IACd,WAAW;IACX,gBAAgB;IAChB,iCAAiC,EAAA;IALpC;MASK,SAAS,EAAA;EiB9Bd;;IAGK,gBAAgB;IAChB,eAAe,EAAA,EjB2BhB;;A6D3GL;E7DiGA;IAEI,cAAc;IACd,WAAW;IACX,gBAAgB;IAChB,iCAAiC,EAAA;IALpC;MASK,SAAS,EAAA;EiB9Bd;;IAGK,gBAAgB;IAChB,eAAe,EAAA,EjB2BhB;;A6D3GL;E7DiGA;IAEI,cAAc;IACd,WAAW;IACX,gBAAgB;IAChB,iCAAiC,EAAA;IALpC;MASK,SAAS,EAAA;EiB9Bd;;IAGK,gBAAgB;IAChB,eAAe,EAAA,EjB2BhB;;A6D3GL;E7DiGA;IAEI,cAAc;IACd,WAAW;IACX,gBAAgB;IAChB,iCAAiC,EAAA;IALpC;MASK,SAAS,EAAA;EiB9Bd;;IAGK,gBAAgB;IAChB,eAAe,EAAA,EjB2BhB;;AIzKL;IDLJ;MCMM,wBAAgB;MAAhB,gBAAgB,EAAA;IGNtB;MHMM,wBAAgB;MAAhB,gBAAgB,EAAA;IKZtB;MLYM,wBAAgB;MAAhB,gBAAgB,EAAA;IKEtB;MLFM,wBAAgB;MAAhB,gBAAgB,EAAA;MWiKtB;QXjKM,wBAAgB;QAAhB,gBAAgB,EAAA;MW+WtB;QX/WM,wBAAgB;QAAhB,gBAAgB,EAAA;MW+WtB;QX/WM,qBAAgB;QAAhB,gBAAgB,EAAA;MW+WtB;QX/WM,oBAAgB;QAAhB,gBAAgB,EAAA;IWwftB;;;MXxfM,wBAAgB;MAAhB,gBAAgB,EAAA;IkBPtB;MlBOM,wBAAgB;MAAhB,gBAAgB,EAAA;IuBMtB;MvBNM,wBAAgB;MAAhB,gBAAgB,EAAA;IuBwBpB;MAKM,uBAAe;cAAf,eAAe,EAAA;MMErB;Q7B/BI,wBAAgB;QAAhB,gBAAgB,EAAA;IiCgBtB;MjChBM,wBAAgB;MAAhB,gBAAgB,EAAA;IiC+CtB;;MjC/CM,wBAAgB;MAAhB,gBAAgB,EAAA;IiC0EtB;;MjC1EM,wBAAgB;MAAhB,gBAAgB,EAAA;MiCoItB;QjCpIM,wBAAgB;QAAhB,gBAAgB,EAAA,ED+CrB;;AyCtCD;EAEI;IAAqB,wBAA0B,EAAA;EAA/C;IAAqB,0BAA0B,EAAA;EAA/C;IAAqB,gCAA0B,EAAA;EAA/C;IAAqB,yBAA0B,EAAA;EAA/C;IAAqB,yBAA0B,EAAA;EAA/C;IAAqB,6BAA0B,EAAA;EAA/C;IAAqB,8BAA0B,EAAA;EAA/C;IAAqB,+BAA0B;IAA1B,wBAA0B,EAAA;EAA/C;IAAqB,sCAA0B;IAA1B,+BAA0B,EAAA;E1DLnD;;;I4EDM,4BAA4B;IAE5B,2BAA2B,EAAA;EAG7B;IAEI,0BAA0B,EAAA;EAS9B;IACE,6BAA6B,EAAA;E5E8LnC;I4E/KM,gCAAgC,EAAA;EAElC;;IAEE,yB1EzCY;I0E0CZ,wBAAwB,EAAA;EAQ1B;IACE,2BAA2B,EAAA;EAG7B;;IAEE,wBAAwB,EAAA;EAG1B;;;IAGE,UAAU;IACV,SAAS,EAAA;EAGX;;IAEE,uBAAuB,EAAA;EAQzB;IACE,Q1E4hCgC,EAAA;EFxkCtC;I4E+CM,2BAA2C,EAAA;EjEtF/C;IiEyFI,2BAA2C,EAAA;E7C9EjD;I6CmFM,aAAa,EAAA;ExC/FnB;IwCkGM,sB3E1FM,EAAA;EaTZ;I8DuGM,oCAAoC,EAAA;IADtC;;MAKI,iCAAmC,EAAA;E9DnE3C;;I8D0EQ,oCAAsC,EAAA;E9DW9C;I8DNM,cAAc,EAAA;I7DtHlB;;;;M6D4HM,kB/EhHiB,EAAA;EiB8FzB;I8DuBM,cAAc;IACd,kB/EtHmB,EAAA,E6DM8B", "file": "admin\\assets\\css\\bootstrap.css", "sourcesContent": ["@charset \"UTF-8\";\n.table-ec-lightGray {\n  background-color: #f2f2f2; }\n\n/*!\n * Bootstrap v4.4.1 (https://getbootstrap.com/)\n * Copyright 2011-2019 The Bootstrap Authors\n * Copyright 2011-2019 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n:root {\n  --blue: #437ec4;\n  --indigo: #6610f2;\n  --purple: #6f42c1;\n  --pink: #e83e8c;\n  --red: #c04949;\n  --orange: #fd7e14;\n  --yellow: #eeb128;\n  --green: #25b877;\n  --teal: #20c997;\n  --cyan: #17a2b8;\n  --white: #fff;\n  --gray: #6c757d;\n  --gray-dark: #343a40;\n  --primary: #437ec4;\n  --secondary: #54687A;\n  --success: #25b877;\n  --info: #17a2b8;\n  --warning: #eeb128;\n  --danger: #c04949;\n  --light: #eff0f4;\n  --dark: #595959;\n  --breakpoint-xs: 0;\n  --breakpoint-sm: 576px;\n  --breakpoint-md: 768px;\n  --breakpoint-lg: 992px;\n  --breakpoint-xl: 1200px;\n  --font-family-sans-serif: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; }\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box; }\n\nhtml {\n  font-family: sans-serif;\n  line-height: 1.15;\n  -webkit-text-size-adjust: 100%;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0); }\n\narticle, aside, figcaption, figure, footer, header, hgroup, main, nav, section {\n  display: block; }\n\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-size: 1rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #212529;\n  text-align: left;\n  background-color: #fff; }\n\n[tabindex=\"-1\"]:focus:not(:focus-visible) {\n  outline: 0 !important; }\n\nhr {\n  box-sizing: content-box;\n  height: 0;\n  overflow: visible; }\n\nh1, h2, h3, h4, h5, h6 {\n  margin-top: 0;\n  margin-bottom: 0.5rem; }\n\np {\n  margin-top: 0;\n  margin-bottom: 1rem; }\n\nabbr[title],\nabbr[data-original-title] {\n  text-decoration: underline;\n  text-decoration: underline dotted;\n  cursor: help;\n  border-bottom: 0;\n  text-decoration-skip-ink: none; }\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit; }\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem; }\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0; }\n\ndt {\n  font-weight: 700; }\n\ndd {\n  margin-bottom: .5rem;\n  margin-left: 0; }\n\nblockquote {\n  margin: 0 0 1rem; }\n\nb,\nstrong {\n  font-weight: bolder; }\n\nsmall {\n  font-size: 80%; }\n\nsub,\nsup {\n  position: relative;\n  font-size: 75%;\n  line-height: 0;\n  vertical-align: baseline; }\n\nsub {\n  bottom: -.25em; }\n\nsup {\n  top: -.5em; }\n\na {\n  color: #437ec4;\n  text-decoration: none;\n  background-color: transparent; }\n  a:hover {\n    color: #2d598e;\n    text-decoration: underline; }\n\na:not([href]) {\n  color: inherit;\n  text-decoration: none; }\n  a:not([href]):hover {\n    color: inherit;\n    text-decoration: none; }\n\npre,\ncode,\nkbd,\nsamp {\n  font-family: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n  font-size: 1em; }\n\npre {\n  margin-top: 0;\n  margin-bottom: 1rem;\n  overflow: auto; }\n\nfigure {\n  margin: 0 0 1rem; }\n\nimg {\n  vertical-align: middle;\n  border-style: none; }\n\nsvg {\n  overflow: hidden;\n  vertical-align: middle; }\n\ntable {\n  border-collapse: collapse; }\n\ncaption {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n  color: #6c757d;\n  text-align: left;\n  caption-side: bottom; }\n\nth {\n  text-align: inherit; }\n\nlabel {\n  display: inline-block;\n  margin-bottom: 0.5rem; }\n\nbutton {\n  border-radius: 0; }\n\nbutton:focus {\n  outline: 1px dotted;\n  outline: 5px auto -webkit-focus-ring-color; }\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit; }\n\nbutton,\ninput {\n  overflow: visible; }\n\nbutton,\nselect {\n  text-transform: none; }\n\nselect {\n  word-wrap: normal; }\n\nbutton,\n[type=\"button\"],\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button; }\n\nbutton:not(:disabled),\n[type=\"button\"]:not(:disabled),\n[type=\"reset\"]:not(:disabled),\n[type=\"submit\"]:not(:disabled) {\n  cursor: pointer; }\n\nbutton::-moz-focus-inner,\n[type=\"button\"]::-moz-focus-inner,\n[type=\"reset\"]::-moz-focus-inner,\n[type=\"submit\"]::-moz-focus-inner {\n  padding: 0;\n  border-style: none; }\n\ninput[type=\"radio\"],\ninput[type=\"checkbox\"] {\n  box-sizing: border-box;\n  padding: 0; }\n\ninput[type=\"date\"],\ninput[type=\"time\"],\ninput[type=\"datetime-local\"],\ninput[type=\"month\"] {\n  -webkit-appearance: listbox; }\n\ntextarea {\n  overflow: auto;\n  resize: vertical; }\n\nfieldset {\n  min-width: 0;\n  padding: 0;\n  margin: 0;\n  border: 0; }\n\nlegend {\n  display: block;\n  width: 100%;\n  max-width: 100%;\n  padding: 0;\n  margin-bottom: .5rem;\n  font-size: 1.5rem;\n  line-height: inherit;\n  color: inherit;\n  white-space: normal; }\n\nprogress {\n  vertical-align: baseline; }\n\n[type=\"number\"]::-webkit-inner-spin-button,\n[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto; }\n\n[type=\"search\"] {\n  outline-offset: -2px;\n  -webkit-appearance: none; }\n\n[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none; }\n\n::-webkit-file-upload-button {\n  font: inherit;\n  -webkit-appearance: button; }\n\noutput {\n  display: inline-block; }\n\nsummary {\n  display: list-item;\n  cursor: pointer; }\n\ntemplate {\n  display: none; }\n\n[hidden] {\n  display: none !important; }\n\nh1, h2, h3, h4, h5, h6,\n.h1, .h2, .h3, .h4, .h5, .h6 {\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  line-height: 1.2; }\n\nh1, .h1 {\n  font-size: 2.5rem; }\n\nh2, .h2 {\n  font-size: 2rem; }\n\nh3, .h3 {\n  font-size: 1.75rem; }\n\nh4, .h4 {\n  font-size: 1.5rem; }\n\nh5, .h5 {\n  font-size: 1.25rem; }\n\nh6, .h6 {\n  font-size: 1rem; }\n\n.lead {\n  font-size: 1.25rem;\n  font-weight: 300; }\n\n.display-1 {\n  font-size: 6rem;\n  font-weight: 300;\n  line-height: 1.2; }\n\n.display-2 {\n  font-size: 5.5rem;\n  font-weight: 300;\n  line-height: 1.2; }\n\n.display-3 {\n  font-size: 4.5rem;\n  font-weight: 300;\n  line-height: 1.2; }\n\n.display-4 {\n  font-size: 3.5rem;\n  font-weight: 300;\n  line-height: 1.2; }\n\nhr {\n  margin-top: 1rem;\n  margin-bottom: 1rem;\n  border: 0;\n  border-top: 1px solid rgba(0, 0, 0, 0.1); }\n\nsmall,\n.small {\n  font-size: 80%;\n  font-weight: 400; }\n\nmark,\n.mark {\n  padding: 0.2em;\n  background-color: #fcf8e3; }\n\n.list-unstyled {\n  padding-left: 0;\n  list-style: none; }\n\n.list-inline {\n  padding-left: 0;\n  list-style: none; }\n\n.list-inline-item {\n  display: inline-block; }\n  .list-inline-item:not(:last-child) {\n    margin-right: 0.5rem; }\n\n.initialism {\n  font-size: 90%;\n  text-transform: uppercase; }\n\n.blockquote {\n  margin-bottom: 1rem;\n  font-size: 1.25rem; }\n\n.blockquote-footer {\n  display: block;\n  font-size: 80%;\n  color: #6c757d; }\n  .blockquote-footer::before {\n    content: \"\\2014\\00A0\"; }\n\n.img-fluid {\n  max-width: 100%;\n  height: auto; }\n\n.img-thumbnail {\n  padding: 0.25rem;\n  background-color: #fff;\n  border: 1px solid #dee2e6;\n  border-radius: 0.25rem;\n  max-width: 100%;\n  height: auto; }\n\n.figure {\n  display: inline-block; }\n\n.figure-img {\n  margin-bottom: 0.5rem;\n  line-height: 1; }\n\n.figure-caption {\n  font-size: 90%;\n  color: #6c757d; }\n\ncode {\n  font-size: 87.5%;\n  color: #e83e8c;\n  word-wrap: break-word; }\n  a > code {\n    color: inherit; }\n\nkbd {\n  padding: 0.2rem 0.4rem;\n  font-size: 87.5%;\n  color: #fff;\n  background-color: #212529;\n  border-radius: 0.2rem; }\n  kbd kbd {\n    padding: 0;\n    font-size: 100%;\n    font-weight: 700; }\n\npre {\n  display: block;\n  font-size: 87.5%;\n  color: #212529; }\n  pre code {\n    font-size: inherit;\n    color: inherit;\n    word-break: normal; }\n\n.pre-scrollable {\n  max-height: 340px;\n  overflow-y: scroll; }\n\n.container {\n  width: 100%;\n  padding-right: 15px;\n  padding-left: 15px;\n  margin-right: auto;\n  margin-left: auto; }\n  @media (min-width: 576px) {\n    .container {\n      max-width: 540px; } }\n  @media (min-width: 768px) {\n    .container {\n      max-width: 720px; } }\n  @media (min-width: 992px) {\n    .container {\n      max-width: 960px; } }\n  @media (min-width: 1200px) {\n    .container {\n      max-width: 1140px; } }\n\n.container-fluid, .container-sm, .container-md, .container-lg, .container-xl {\n  width: 100%;\n  padding-right: 15px;\n  padding-left: 15px;\n  margin-right: auto;\n  margin-left: auto; }\n\n@media (min-width: 576px) {\n  .container, .container-sm {\n    max-width: 540px; } }\n\n@media (min-width: 768px) {\n  .container, .container-sm, .container-md {\n    max-width: 720px; } }\n\n@media (min-width: 992px) {\n  .container, .container-sm, .container-md, .container-lg {\n    max-width: 960px; } }\n\n@media (min-width: 1200px) {\n  .container, .container-sm, .container-md, .container-lg, .container-xl {\n    max-width: 1140px; } }\n\n.row {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -15px;\n  margin-left: -15px; }\n\n.no-gutters {\n  margin-right: 0;\n  margin-left: 0; }\n  .no-gutters > .col,\n  .no-gutters > [class*=\"col-\"] {\n    padding-right: 0;\n    padding-left: 0; }\n\n.col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12, .col,\n.col-auto, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm,\n.col-sm-auto, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-md,\n.col-md-auto, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg,\n.col-lg-auto, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl,\n.col-xl-auto {\n  position: relative;\n  width: 100%;\n  padding-right: 15px;\n  padding-left: 15px; }\n\n.col {\n  flex-basis: 0;\n  flex-grow: 1;\n  max-width: 100%; }\n\n.row-cols-1 > * {\n  flex: 0 0 100%;\n  max-width: 100%; }\n\n.row-cols-2 > * {\n  flex: 0 0 50%;\n  max-width: 50%; }\n\n.row-cols-3 > * {\n  flex: 0 0 33.33333%;\n  max-width: 33.33333%; }\n\n.row-cols-4 > * {\n  flex: 0 0 25%;\n  max-width: 25%; }\n\n.row-cols-5 > * {\n  flex: 0 0 20%;\n  max-width: 20%; }\n\n.row-cols-6 > * {\n  flex: 0 0 16.66667%;\n  max-width: 16.66667%; }\n\n.col-auto {\n  flex: 0 0 auto;\n  width: auto;\n  max-width: 100%; }\n\n.col-1 {\n  flex: 0 0 8.33333%;\n  max-width: 8.33333%; }\n\n.col-2 {\n  flex: 0 0 16.66667%;\n  max-width: 16.66667%; }\n\n.col-3 {\n  flex: 0 0 25%;\n  max-width: 25%; }\n\n.col-4 {\n  flex: 0 0 33.33333%;\n  max-width: 33.33333%; }\n\n.col-5 {\n  flex: 0 0 41.66667%;\n  max-width: 41.66667%; }\n\n.col-6 {\n  flex: 0 0 50%;\n  max-width: 50%; }\n\n.col-7 {\n  flex: 0 0 58.33333%;\n  max-width: 58.33333%; }\n\n.col-8 {\n  flex: 0 0 66.66667%;\n  max-width: 66.66667%; }\n\n.col-9 {\n  flex: 0 0 75%;\n  max-width: 75%; }\n\n.col-10 {\n  flex: 0 0 83.33333%;\n  max-width: 83.33333%; }\n\n.col-11 {\n  flex: 0 0 91.66667%;\n  max-width: 91.66667%; }\n\n.col-12 {\n  flex: 0 0 100%;\n  max-width: 100%; }\n\n.order-first {\n  order: -1; }\n\n.order-last {\n  order: 13; }\n\n.order-0 {\n  order: 0; }\n\n.order-1 {\n  order: 1; }\n\n.order-2 {\n  order: 2; }\n\n.order-3 {\n  order: 3; }\n\n.order-4 {\n  order: 4; }\n\n.order-5 {\n  order: 5; }\n\n.order-6 {\n  order: 6; }\n\n.order-7 {\n  order: 7; }\n\n.order-8 {\n  order: 8; }\n\n.order-9 {\n  order: 9; }\n\n.order-10 {\n  order: 10; }\n\n.order-11 {\n  order: 11; }\n\n.order-12 {\n  order: 12; }\n\n.offset-1 {\n  margin-left: 8.33333%; }\n\n.offset-2 {\n  margin-left: 16.66667%; }\n\n.offset-3 {\n  margin-left: 25%; }\n\n.offset-4 {\n  margin-left: 33.33333%; }\n\n.offset-5 {\n  margin-left: 41.66667%; }\n\n.offset-6 {\n  margin-left: 50%; }\n\n.offset-7 {\n  margin-left: 58.33333%; }\n\n.offset-8 {\n  margin-left: 66.66667%; }\n\n.offset-9 {\n  margin-left: 75%; }\n\n.offset-10 {\n  margin-left: 83.33333%; }\n\n.offset-11 {\n  margin-left: 91.66667%; }\n\n@media (min-width: 576px) {\n  .col-sm {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%; }\n  .row-cols-sm-1 > * {\n    flex: 0 0 100%;\n    max-width: 100%; }\n  .row-cols-sm-2 > * {\n    flex: 0 0 50%;\n    max-width: 50%; }\n  .row-cols-sm-3 > * {\n    flex: 0 0 33.33333%;\n    max-width: 33.33333%; }\n  .row-cols-sm-4 > * {\n    flex: 0 0 25%;\n    max-width: 25%; }\n  .row-cols-sm-5 > * {\n    flex: 0 0 20%;\n    max-width: 20%; }\n  .row-cols-sm-6 > * {\n    flex: 0 0 16.66667%;\n    max-width: 16.66667%; }\n  .col-sm-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: 100%; }\n  .col-sm-1 {\n    flex: 0 0 8.33333%;\n    max-width: 8.33333%; }\n  .col-sm-2 {\n    flex: 0 0 16.66667%;\n    max-width: 16.66667%; }\n  .col-sm-3 {\n    flex: 0 0 25%;\n    max-width: 25%; }\n  .col-sm-4 {\n    flex: 0 0 33.33333%;\n    max-width: 33.33333%; }\n  .col-sm-5 {\n    flex: 0 0 41.66667%;\n    max-width: 41.66667%; }\n  .col-sm-6 {\n    flex: 0 0 50%;\n    max-width: 50%; }\n  .col-sm-7 {\n    flex: 0 0 58.33333%;\n    max-width: 58.33333%; }\n  .col-sm-8 {\n    flex: 0 0 66.66667%;\n    max-width: 66.66667%; }\n  .col-sm-9 {\n    flex: 0 0 75%;\n    max-width: 75%; }\n  .col-sm-10 {\n    flex: 0 0 83.33333%;\n    max-width: 83.33333%; }\n  .col-sm-11 {\n    flex: 0 0 91.66667%;\n    max-width: 91.66667%; }\n  .col-sm-12 {\n    flex: 0 0 100%;\n    max-width: 100%; }\n  .order-sm-first {\n    order: -1; }\n  .order-sm-last {\n    order: 13; }\n  .order-sm-0 {\n    order: 0; }\n  .order-sm-1 {\n    order: 1; }\n  .order-sm-2 {\n    order: 2; }\n  .order-sm-3 {\n    order: 3; }\n  .order-sm-4 {\n    order: 4; }\n  .order-sm-5 {\n    order: 5; }\n  .order-sm-6 {\n    order: 6; }\n  .order-sm-7 {\n    order: 7; }\n  .order-sm-8 {\n    order: 8; }\n  .order-sm-9 {\n    order: 9; }\n  .order-sm-10 {\n    order: 10; }\n  .order-sm-11 {\n    order: 11; }\n  .order-sm-12 {\n    order: 12; }\n  .offset-sm-0 {\n    margin-left: 0; }\n  .offset-sm-1 {\n    margin-left: 8.33333%; }\n  .offset-sm-2 {\n    margin-left: 16.66667%; }\n  .offset-sm-3 {\n    margin-left: 25%; }\n  .offset-sm-4 {\n    margin-left: 33.33333%; }\n  .offset-sm-5 {\n    margin-left: 41.66667%; }\n  .offset-sm-6 {\n    margin-left: 50%; }\n  .offset-sm-7 {\n    margin-left: 58.33333%; }\n  .offset-sm-8 {\n    margin-left: 66.66667%; }\n  .offset-sm-9 {\n    margin-left: 75%; }\n  .offset-sm-10 {\n    margin-left: 83.33333%; }\n  .offset-sm-11 {\n    margin-left: 91.66667%; } }\n\n@media (min-width: 768px) {\n  .col-md {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%; }\n  .row-cols-md-1 > * {\n    flex: 0 0 100%;\n    max-width: 100%; }\n  .row-cols-md-2 > * {\n    flex: 0 0 50%;\n    max-width: 50%; }\n  .row-cols-md-3 > * {\n    flex: 0 0 33.33333%;\n    max-width: 33.33333%; }\n  .row-cols-md-4 > * {\n    flex: 0 0 25%;\n    max-width: 25%; }\n  .row-cols-md-5 > * {\n    flex: 0 0 20%;\n    max-width: 20%; }\n  .row-cols-md-6 > * {\n    flex: 0 0 16.66667%;\n    max-width: 16.66667%; }\n  .col-md-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: 100%; }\n  .col-md-1 {\n    flex: 0 0 8.33333%;\n    max-width: 8.33333%; }\n  .col-md-2 {\n    flex: 0 0 16.66667%;\n    max-width: 16.66667%; }\n  .col-md-3 {\n    flex: 0 0 25%;\n    max-width: 25%; }\n  .col-md-4 {\n    flex: 0 0 33.33333%;\n    max-width: 33.33333%; }\n  .col-md-5 {\n    flex: 0 0 41.66667%;\n    max-width: 41.66667%; }\n  .col-md-6 {\n    flex: 0 0 50%;\n    max-width: 50%; }\n  .col-md-7 {\n    flex: 0 0 58.33333%;\n    max-width: 58.33333%; }\n  .col-md-8 {\n    flex: 0 0 66.66667%;\n    max-width: 66.66667%; }\n  .col-md-9 {\n    flex: 0 0 75%;\n    max-width: 75%; }\n  .col-md-10 {\n    flex: 0 0 83.33333%;\n    max-width: 83.33333%; }\n  .col-md-11 {\n    flex: 0 0 91.66667%;\n    max-width: 91.66667%; }\n  .col-md-12 {\n    flex: 0 0 100%;\n    max-width: 100%; }\n  .order-md-first {\n    order: -1; }\n  .order-md-last {\n    order: 13; }\n  .order-md-0 {\n    order: 0; }\n  .order-md-1 {\n    order: 1; }\n  .order-md-2 {\n    order: 2; }\n  .order-md-3 {\n    order: 3; }\n  .order-md-4 {\n    order: 4; }\n  .order-md-5 {\n    order: 5; }\n  .order-md-6 {\n    order: 6; }\n  .order-md-7 {\n    order: 7; }\n  .order-md-8 {\n    order: 8; }\n  .order-md-9 {\n    order: 9; }\n  .order-md-10 {\n    order: 10; }\n  .order-md-11 {\n    order: 11; }\n  .order-md-12 {\n    order: 12; }\n  .offset-md-0 {\n    margin-left: 0; }\n  .offset-md-1 {\n    margin-left: 8.33333%; }\n  .offset-md-2 {\n    margin-left: 16.66667%; }\n  .offset-md-3 {\n    margin-left: 25%; }\n  .offset-md-4 {\n    margin-left: 33.33333%; }\n  .offset-md-5 {\n    margin-left: 41.66667%; }\n  .offset-md-6 {\n    margin-left: 50%; }\n  .offset-md-7 {\n    margin-left: 58.33333%; }\n  .offset-md-8 {\n    margin-left: 66.66667%; }\n  .offset-md-9 {\n    margin-left: 75%; }\n  .offset-md-10 {\n    margin-left: 83.33333%; }\n  .offset-md-11 {\n    margin-left: 91.66667%; } }\n\n@media (min-width: 992px) {\n  .col-lg {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%; }\n  .row-cols-lg-1 > * {\n    flex: 0 0 100%;\n    max-width: 100%; }\n  .row-cols-lg-2 > * {\n    flex: 0 0 50%;\n    max-width: 50%; }\n  .row-cols-lg-3 > * {\n    flex: 0 0 33.33333%;\n    max-width: 33.33333%; }\n  .row-cols-lg-4 > * {\n    flex: 0 0 25%;\n    max-width: 25%; }\n  .row-cols-lg-5 > * {\n    flex: 0 0 20%;\n    max-width: 20%; }\n  .row-cols-lg-6 > * {\n    flex: 0 0 16.66667%;\n    max-width: 16.66667%; }\n  .col-lg-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: 100%; }\n  .col-lg-1 {\n    flex: 0 0 8.33333%;\n    max-width: 8.33333%; }\n  .col-lg-2 {\n    flex: 0 0 16.66667%;\n    max-width: 16.66667%; }\n  .col-lg-3 {\n    flex: 0 0 25%;\n    max-width: 25%; }\n  .col-lg-4 {\n    flex: 0 0 33.33333%;\n    max-width: 33.33333%; }\n  .col-lg-5 {\n    flex: 0 0 41.66667%;\n    max-width: 41.66667%; }\n  .col-lg-6 {\n    flex: 0 0 50%;\n    max-width: 50%; }\n  .col-lg-7 {\n    flex: 0 0 58.33333%;\n    max-width: 58.33333%; }\n  .col-lg-8 {\n    flex: 0 0 66.66667%;\n    max-width: 66.66667%; }\n  .col-lg-9 {\n    flex: 0 0 75%;\n    max-width: 75%; }\n  .col-lg-10 {\n    flex: 0 0 83.33333%;\n    max-width: 83.33333%; }\n  .col-lg-11 {\n    flex: 0 0 91.66667%;\n    max-width: 91.66667%; }\n  .col-lg-12 {\n    flex: 0 0 100%;\n    max-width: 100%; }\n  .order-lg-first {\n    order: -1; }\n  .order-lg-last {\n    order: 13; }\n  .order-lg-0 {\n    order: 0; }\n  .order-lg-1 {\n    order: 1; }\n  .order-lg-2 {\n    order: 2; }\n  .order-lg-3 {\n    order: 3; }\n  .order-lg-4 {\n    order: 4; }\n  .order-lg-5 {\n    order: 5; }\n  .order-lg-6 {\n    order: 6; }\n  .order-lg-7 {\n    order: 7; }\n  .order-lg-8 {\n    order: 8; }\n  .order-lg-9 {\n    order: 9; }\n  .order-lg-10 {\n    order: 10; }\n  .order-lg-11 {\n    order: 11; }\n  .order-lg-12 {\n    order: 12; }\n  .offset-lg-0 {\n    margin-left: 0; }\n  .offset-lg-1 {\n    margin-left: 8.33333%; }\n  .offset-lg-2 {\n    margin-left: 16.66667%; }\n  .offset-lg-3 {\n    margin-left: 25%; }\n  .offset-lg-4 {\n    margin-left: 33.33333%; }\n  .offset-lg-5 {\n    margin-left: 41.66667%; }\n  .offset-lg-6 {\n    margin-left: 50%; }\n  .offset-lg-7 {\n    margin-left: 58.33333%; }\n  .offset-lg-8 {\n    margin-left: 66.66667%; }\n  .offset-lg-9 {\n    margin-left: 75%; }\n  .offset-lg-10 {\n    margin-left: 83.33333%; }\n  .offset-lg-11 {\n    margin-left: 91.66667%; } }\n\n@media (min-width: 1200px) {\n  .col-xl {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%; }\n  .row-cols-xl-1 > * {\n    flex: 0 0 100%;\n    max-width: 100%; }\n  .row-cols-xl-2 > * {\n    flex: 0 0 50%;\n    max-width: 50%; }\n  .row-cols-xl-3 > * {\n    flex: 0 0 33.33333%;\n    max-width: 33.33333%; }\n  .row-cols-xl-4 > * {\n    flex: 0 0 25%;\n    max-width: 25%; }\n  .row-cols-xl-5 > * {\n    flex: 0 0 20%;\n    max-width: 20%; }\n  .row-cols-xl-6 > * {\n    flex: 0 0 16.66667%;\n    max-width: 16.66667%; }\n  .col-xl-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: 100%; }\n  .col-xl-1 {\n    flex: 0 0 8.33333%;\n    max-width: 8.33333%; }\n  .col-xl-2 {\n    flex: 0 0 16.66667%;\n    max-width: 16.66667%; }\n  .col-xl-3 {\n    flex: 0 0 25%;\n    max-width: 25%; }\n  .col-xl-4 {\n    flex: 0 0 33.33333%;\n    max-width: 33.33333%; }\n  .col-xl-5 {\n    flex: 0 0 41.66667%;\n    max-width: 41.66667%; }\n  .col-xl-6 {\n    flex: 0 0 50%;\n    max-width: 50%; }\n  .col-xl-7 {\n    flex: 0 0 58.33333%;\n    max-width: 58.33333%; }\n  .col-xl-8 {\n    flex: 0 0 66.66667%;\n    max-width: 66.66667%; }\n  .col-xl-9 {\n    flex: 0 0 75%;\n    max-width: 75%; }\n  .col-xl-10 {\n    flex: 0 0 83.33333%;\n    max-width: 83.33333%; }\n  .col-xl-11 {\n    flex: 0 0 91.66667%;\n    max-width: 91.66667%; }\n  .col-xl-12 {\n    flex: 0 0 100%;\n    max-width: 100%; }\n  .order-xl-first {\n    order: -1; }\n  .order-xl-last {\n    order: 13; }\n  .order-xl-0 {\n    order: 0; }\n  .order-xl-1 {\n    order: 1; }\n  .order-xl-2 {\n    order: 2; }\n  .order-xl-3 {\n    order: 3; }\n  .order-xl-4 {\n    order: 4; }\n  .order-xl-5 {\n    order: 5; }\n  .order-xl-6 {\n    order: 6; }\n  .order-xl-7 {\n    order: 7; }\n  .order-xl-8 {\n    order: 8; }\n  .order-xl-9 {\n    order: 9; }\n  .order-xl-10 {\n    order: 10; }\n  .order-xl-11 {\n    order: 11; }\n  .order-xl-12 {\n    order: 12; }\n  .offset-xl-0 {\n    margin-left: 0; }\n  .offset-xl-1 {\n    margin-left: 8.33333%; }\n  .offset-xl-2 {\n    margin-left: 16.66667%; }\n  .offset-xl-3 {\n    margin-left: 25%; }\n  .offset-xl-4 {\n    margin-left: 33.33333%; }\n  .offset-xl-5 {\n    margin-left: 41.66667%; }\n  .offset-xl-6 {\n    margin-left: 50%; }\n  .offset-xl-7 {\n    margin-left: 58.33333%; }\n  .offset-xl-8 {\n    margin-left: 66.66667%; }\n  .offset-xl-9 {\n    margin-left: 75%; }\n  .offset-xl-10 {\n    margin-left: 83.33333%; }\n  .offset-xl-11 {\n    margin-left: 91.66667%; } }\n\n.table {\n  width: 100%;\n  margin-bottom: 1rem;\n  color: #212529; }\n  .table th,\n  .table td {\n    padding: 0.75rem;\n    vertical-align: top;\n    border-top: 1px solid #ccc; }\n  .table thead th {\n    vertical-align: bottom;\n    border-bottom: 2px solid #ccc; }\n  .table tbody + tbody {\n    border-top: 2px solid #ccc; }\n\n.table-sm th,\n.table-sm td {\n  padding: 0.3rem; }\n\n.table-bordered {\n  border: 1px solid #ccc; }\n  .table-bordered th,\n  .table-bordered td {\n    border: 1px solid #ccc; }\n  .table-bordered thead th,\n  .table-bordered thead td {\n    border-bottom-width: 2px; }\n\n.table-borderless th,\n.table-borderless td,\n.table-borderless thead th,\n.table-borderless tbody + tbody {\n  border: 0; }\n\n.table-striped tbody tr:nth-of-type(odd) {\n  background-color: #f9f9f9; }\n\n.table-hover tbody tr:hover {\n  color: #212529;\n  background-color: rgba(0, 0, 0, 0.075); }\n\n.table-primary,\n.table-primary > th,\n.table-primary > td {\n  background-color: #cadbee; }\n\n.table-primary th,\n.table-primary td,\n.table-primary thead th,\n.table-primary tbody + tbody {\n  border-color: #9dbce0; }\n\n.table-hover .table-primary:hover {\n  background-color: #b7cee8; }\n  .table-hover .table-primary:hover > td,\n  .table-hover .table-primary:hover > th {\n    background-color: #b7cee8; }\n\n.table-secondary,\n.table-secondary > th,\n.table-secondary > td {\n  background-color: #cfd5da; }\n\n.table-secondary th,\n.table-secondary td,\n.table-secondary thead th,\n.table-secondary tbody + tbody {\n  border-color: #a6b0ba; }\n\n.table-hover .table-secondary:hover {\n  background-color: #c1c8cf; }\n  .table-hover .table-secondary:hover > td,\n  .table-hover .table-secondary:hover > th {\n    background-color: #c1c8cf; }\n\n.table-success,\n.table-success > th,\n.table-success > td {\n  background-color: #c2ebd9; }\n\n.table-success th,\n.table-success td,\n.table-success thead th,\n.table-success tbody + tbody {\n  border-color: #8edab8; }\n\n.table-hover .table-success:hover {\n  background-color: #afe5cd; }\n  .table-hover .table-success:hover > td,\n  .table-hover .table-success:hover > th {\n    background-color: #afe5cd; }\n\n.table-info,\n.table-info > th,\n.table-info > td {\n  background-color: #bee5eb; }\n\n.table-info th,\n.table-info td,\n.table-info thead th,\n.table-info tbody + tbody {\n  border-color: #86cfda; }\n\n.table-hover .table-info:hover {\n  background-color: #abdde5; }\n  .table-hover .table-info:hover > td,\n  .table-hover .table-info:hover > th {\n    background-color: #abdde5; }\n\n.table-warning,\n.table-warning > th,\n.table-warning > td {\n  background-color: #fae9c3; }\n\n.table-warning th,\n.table-warning td,\n.table-warning thead th,\n.table-warning tbody + tbody {\n  border-color: #f6d68f; }\n\n.table-hover .table-warning:hover {\n  background-color: #f8e0ab; }\n  .table-hover .table-warning:hover > td,\n  .table-hover .table-warning:hover > th {\n    background-color: #f8e0ab; }\n\n.table-danger,\n.table-danger > th,\n.table-danger > td {\n  background-color: #edcccc; }\n\n.table-danger th,\n.table-danger td,\n.table-danger thead th,\n.table-danger tbody + tbody {\n  border-color: #dea0a0; }\n\n.table-hover .table-danger:hover {\n  background-color: #e6b9b9; }\n  .table-hover .table-danger:hover > td,\n  .table-hover .table-danger:hover > th {\n    background-color: #e6b9b9; }\n\n.table-light,\n.table-light > th,\n.table-light > td {\n  background-color: #fbfbfc; }\n\n.table-light th,\n.table-light td,\n.table-light thead th,\n.table-light tbody + tbody {\n  border-color: #f7f7f9; }\n\n.table-hover .table-light:hover {\n  background-color: #ececf1; }\n  .table-hover .table-light:hover > td,\n  .table-hover .table-light:hover > th {\n    background-color: #ececf1; }\n\n.table-dark,\n.table-dark > th,\n.table-dark > td {\n  background-color: #d1d1d1; }\n\n.table-dark th,\n.table-dark td,\n.table-dark thead th,\n.table-dark tbody + tbody {\n  border-color: darkgray; }\n\n.table-hover .table-dark:hover {\n  background-color: #c4c4c4; }\n  .table-hover .table-dark:hover > td,\n  .table-hover .table-dark:hover > th {\n    background-color: #c4c4c4; }\n\n.table-active,\n.table-active > th,\n.table-active > td {\n  background-color: #f2f2f2; }\n\n.table-hover .table-active:hover {\n  background-color: #e5e5e5; }\n  .table-hover .table-active:hover > td,\n  .table-hover .table-active:hover > th {\n    background-color: #e5e5e5; }\n\n.table .thead-dark th {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #454d55; }\n\n.table .thead-light th {\n  color: #495057;\n  background-color: #e9ecef;\n  border-color: #ccc; }\n\n.table-dark {\n  color: #fff;\n  background-color: #343a40; }\n  .table-dark th,\n  .table-dark td,\n  .table-dark thead th {\n    border-color: #454d55; }\n  .table-dark.table-bordered {\n    border: 0; }\n  .table-dark.table-striped tbody tr:nth-of-type(odd) {\n    background-color: rgba(255, 255, 255, 0.05); }\n  .table-dark.table-hover tbody tr:hover {\n    color: #fff;\n    background-color: rgba(255, 255, 255, 0.075); }\n\n@media (max-width: 575.98px) {\n  .table-responsive-sm {\n    display: block;\n    width: 100%;\n    overflow-x: auto;\n    -webkit-overflow-scrolling: touch; }\n    .table-responsive-sm > .table-bordered {\n      border: 0; } }\n\n@media (max-width: 767.98px) {\n  .table-responsive-md {\n    display: block;\n    width: 100%;\n    overflow-x: auto;\n    -webkit-overflow-scrolling: touch; }\n    .table-responsive-md > .table-bordered {\n      border: 0; } }\n\n@media (max-width: 991.98px) {\n  .table-responsive-lg {\n    display: block;\n    width: 100%;\n    overflow-x: auto;\n    -webkit-overflow-scrolling: touch; }\n    .table-responsive-lg > .table-bordered {\n      border: 0; } }\n\n@media (max-width: 1199.98px) {\n  .table-responsive-xl {\n    display: block;\n    width: 100%;\n    overflow-x: auto;\n    -webkit-overflow-scrolling: touch; }\n    .table-responsive-xl > .table-bordered {\n      border: 0; } }\n\n.table-responsive {\n  display: block;\n  width: 100%;\n  overflow-x: auto;\n  -webkit-overflow-scrolling: touch; }\n  .table-responsive > .table-bordered {\n    border: 0; }\n\n.form-control {\n  display: block;\n  width: 100%;\n  height: calc(1.5em + 0.75rem + 2px);\n  padding: 0.375rem 0.75rem;\n  font-size: 1rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #495057;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid #ced4da;\n  border-radius: 0.25rem;\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; }\n  @media (prefers-reduced-motion: reduce) {\n    .form-control {\n      transition: none; } }\n  .form-control::-ms-expand {\n    background-color: transparent;\n    border: 0; }\n  .form-control:-moz-focusring {\n    color: transparent;\n    text-shadow: 0 0 0 #495057; }\n  .form-control:focus {\n    color: #495057;\n    background-color: #fff;\n    border-color: #a4c1e2;\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(67, 126, 196, 0.25); }\n  .form-control::placeholder {\n    color: #6c757d;\n    opacity: 1; }\n  .form-control:disabled, .form-control[readonly] {\n    background-color: #e9ecef;\n    opacity: 1; }\n\nselect.form-control:focus::-ms-value {\n  color: #495057;\n  background-color: #fff; }\n\n.form-control-file,\n.form-control-range {\n  display: block;\n  width: 100%; }\n\n.col-form-label {\n  padding-top: calc(0.375rem + 1px);\n  padding-bottom: calc(0.375rem + 1px);\n  margin-bottom: 0;\n  font-size: inherit;\n  line-height: 1.5; }\n\n.col-form-label-lg {\n  padding-top: calc(0.5rem + 1px);\n  padding-bottom: calc(0.5rem + 1px);\n  font-size: 1.25rem;\n  line-height: 1.5; }\n\n.col-form-label-sm {\n  padding-top: calc(0.25rem + 1px);\n  padding-bottom: calc(0.25rem + 1px);\n  font-size: 0.875rem;\n  line-height: 1.5; }\n\n.form-control-plaintext {\n  display: block;\n  width: 100%;\n  padding: 0.375rem 0;\n  margin-bottom: 0;\n  font-size: 1rem;\n  line-height: 1.5;\n  color: #212529;\n  background-color: transparent;\n  border: solid transparent;\n  border-width: 1px 0; }\n  .form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {\n    padding-right: 0;\n    padding-left: 0; }\n\n.form-control-sm {\n  height: calc(1.5em + 0.5rem + 2px);\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  border-radius: 0.2rem; }\n\n.form-control-lg {\n  height: calc(1.5em + 1rem + 2px);\n  padding: 0.5rem 1rem;\n  font-size: 1.25rem;\n  line-height: 1.5;\n  border-radius: 0.3rem; }\n\nselect.form-control[size], select.form-control[multiple] {\n  height: auto; }\n\ntextarea.form-control {\n  height: auto; }\n\n.form-group {\n  margin-bottom: 1rem; }\n\n.form-text {\n  display: block;\n  margin-top: 0.25rem; }\n\n.form-row {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -5px;\n  margin-left: -5px; }\n  .form-row > .col,\n  .form-row > [class*=\"col-\"] {\n    padding-right: 5px;\n    padding-left: 5px; }\n\n.form-check {\n  position: relative;\n  display: block;\n  padding-left: 1.25rem; }\n\n.form-check-input {\n  position: absolute;\n  margin-top: 0.3rem;\n  margin-left: -1.25rem; }\n  .form-check-input[disabled] ~ .form-check-label,\n  .form-check-input:disabled ~ .form-check-label {\n    color: #6c757d; }\n\n.form-check-label {\n  margin-bottom: 0; }\n\n.form-check-inline {\n  display: inline-flex;\n  align-items: center;\n  padding-left: 0;\n  margin-right: 0.75rem; }\n  .form-check-inline .form-check-input {\n    position: static;\n    margin-top: 0;\n    margin-right: 0.3125rem;\n    margin-left: 0; }\n\n.valid-feedback {\n  display: none;\n  width: 100%;\n  margin-top: 0.25rem;\n  font-size: 80%;\n  color: #25b877; }\n\n.valid-tooltip {\n  position: absolute;\n  top: 100%;\n  z-index: 5;\n  display: none;\n  max-width: 100%;\n  padding: 0.25rem 0.5rem;\n  margin-top: .1rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  color: #fff;\n  background-color: rgba(37, 184, 119, 0.9);\n  border-radius: 0.25rem; }\n\n.was-validated :valid ~ .valid-feedback,\n.was-validated :valid ~ .valid-tooltip,\n.is-valid ~ .valid-feedback,\n.is-valid ~ .valid-tooltip {\n  display: block; }\n\n.was-validated .form-control:valid, .form-control.is-valid {\n  border-color: #25b877;\n  padding-right: calc(1.5em + 0.75rem);\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2325b877' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e\");\n  background-repeat: no-repeat;\n  background-position: right calc(0.375em + 0.1875rem) center;\n  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem); }\n  .was-validated .form-control:valid:focus, .form-control.is-valid:focus {\n    border-color: #25b877;\n    box-shadow: 0 0 0 0.2rem rgba(37, 184, 119, 0.25); }\n\n.was-validated textarea.form-control:valid, textarea.form-control.is-valid {\n  padding-right: calc(1.5em + 0.75rem);\n  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem); }\n\n.was-validated .custom-select:valid, .custom-select.is-valid {\n  border-color: #25b877;\n  padding-right: calc(0.75em + 2.3125rem);\n  background: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e\") no-repeat right 0.75rem center/8px 10px, url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2325b877' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e\") #fff no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem); }\n  .was-validated .custom-select:valid:focus, .custom-select.is-valid:focus {\n    border-color: #25b877;\n    box-shadow: 0 0 0 0.2rem rgba(37, 184, 119, 0.25); }\n\n.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {\n  color: #25b877; }\n\n.was-validated .form-check-input:valid ~ .valid-feedback,\n.was-validated .form-check-input:valid ~ .valid-tooltip, .form-check-input.is-valid ~ .valid-feedback,\n.form-check-input.is-valid ~ .valid-tooltip {\n  display: block; }\n\n.was-validated .custom-control-input:valid ~ .custom-control-label, .custom-control-input.is-valid ~ .custom-control-label {\n  color: #25b877; }\n  .was-validated .custom-control-input:valid ~ .custom-control-label::before, .custom-control-input.is-valid ~ .custom-control-label::before {\n    border-color: #25b877; }\n\n.was-validated .custom-control-input:valid:checked ~ .custom-control-label::before, .custom-control-input.is-valid:checked ~ .custom-control-label::before {\n  border-color: #39d791;\n  background-color: #39d791; }\n\n.was-validated .custom-control-input:valid:focus ~ .custom-control-label::before, .custom-control-input.is-valid:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 0.2rem rgba(37, 184, 119, 0.25); }\n\n.was-validated .custom-control-input:valid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-valid:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #25b877; }\n\n.was-validated .custom-file-input:valid ~ .custom-file-label, .custom-file-input.is-valid ~ .custom-file-label {\n  border-color: #25b877; }\n\n.was-validated .custom-file-input:valid:focus ~ .custom-file-label, .custom-file-input.is-valid:focus ~ .custom-file-label {\n  border-color: #25b877;\n  box-shadow: 0 0 0 0.2rem rgba(37, 184, 119, 0.25); }\n\n.invalid-feedback {\n  display: none;\n  width: 100%;\n  margin-top: 0.25rem;\n  font-size: 80%;\n  color: #c04949; }\n\n.invalid-tooltip {\n  position: absolute;\n  top: 100%;\n  z-index: 5;\n  display: none;\n  max-width: 100%;\n  padding: 0.25rem 0.5rem;\n  margin-top: .1rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  color: #fff;\n  background-color: rgba(192, 73, 73, 0.9);\n  border-radius: 0.25rem; }\n\n.was-validated :invalid ~ .invalid-feedback,\n.was-validated :invalid ~ .invalid-tooltip,\n.is-invalid ~ .invalid-feedback,\n.is-invalid ~ .invalid-tooltip {\n  display: block; }\n\n.was-validated .form-control:invalid, .form-control.is-invalid {\n  border-color: #c04949;\n  padding-right: calc(1.5em + 0.75rem);\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23c04949' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23c04949' stroke='none'/%3e%3c/svg%3e\");\n  background-repeat: no-repeat;\n  background-position: right calc(0.375em + 0.1875rem) center;\n  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem); }\n  .was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {\n    border-color: #c04949;\n    box-shadow: 0 0 0 0.2rem rgba(192, 73, 73, 0.25); }\n\n.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {\n  padding-right: calc(1.5em + 0.75rem);\n  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem); }\n\n.was-validated .custom-select:invalid, .custom-select.is-invalid {\n  border-color: #c04949;\n  padding-right: calc(0.75em + 2.3125rem);\n  background: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e\") no-repeat right 0.75rem center/8px 10px, url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23c04949' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23c04949' stroke='none'/%3e%3c/svg%3e\") #fff no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem); }\n  .was-validated .custom-select:invalid:focus, .custom-select.is-invalid:focus {\n    border-color: #c04949;\n    box-shadow: 0 0 0 0.2rem rgba(192, 73, 73, 0.25); }\n\n.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {\n  color: #c04949; }\n\n.was-validated .form-check-input:invalid ~ .invalid-feedback,\n.was-validated .form-check-input:invalid ~ .invalid-tooltip, .form-check-input.is-invalid ~ .invalid-feedback,\n.form-check-input.is-invalid ~ .invalid-tooltip {\n  display: block; }\n\n.was-validated .custom-control-input:invalid ~ .custom-control-label, .custom-control-input.is-invalid ~ .custom-control-label {\n  color: #c04949; }\n  .was-validated .custom-control-input:invalid ~ .custom-control-label::before, .custom-control-input.is-invalid ~ .custom-control-label::before {\n    border-color: #c04949; }\n\n.was-validated .custom-control-input:invalid:checked ~ .custom-control-label::before, .custom-control-input.is-invalid:checked ~ .custom-control-label::before {\n  border-color: #cd6f6f;\n  background-color: #cd6f6f; }\n\n.was-validated .custom-control-input:invalid:focus ~ .custom-control-label::before, .custom-control-input.is-invalid:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 0.2rem rgba(192, 73, 73, 0.25); }\n\n.was-validated .custom-control-input:invalid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-invalid:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #c04949; }\n\n.was-validated .custom-file-input:invalid ~ .custom-file-label, .custom-file-input.is-invalid ~ .custom-file-label {\n  border-color: #c04949; }\n\n.was-validated .custom-file-input:invalid:focus ~ .custom-file-label, .custom-file-input.is-invalid:focus ~ .custom-file-label {\n  border-color: #c04949;\n  box-shadow: 0 0 0 0.2rem rgba(192, 73, 73, 0.25); }\n\n.form-inline {\n  display: flex;\n  flex-flow: row wrap;\n  align-items: center; }\n  .form-inline .form-check {\n    width: 100%; }\n  @media (min-width: 576px) {\n    .form-inline label {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 0; }\n    .form-inline .form-group {\n      display: flex;\n      flex: 0 0 auto;\n      flex-flow: row wrap;\n      align-items: center;\n      margin-bottom: 0; }\n    .form-inline .form-control {\n      display: inline-block;\n      width: auto;\n      vertical-align: middle; }\n    .form-inline .form-control-plaintext {\n      display: inline-block; }\n    .form-inline .input-group,\n    .form-inline .custom-select {\n      width: auto; }\n    .form-inline .form-check {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: auto;\n      padding-left: 0; }\n    .form-inline .form-check-input {\n      position: relative;\n      flex-shrink: 0;\n      margin-top: 0;\n      margin-right: 0.25rem;\n      margin-left: 0; }\n    .form-inline .custom-control {\n      align-items: center;\n      justify-content: center; }\n    .form-inline .custom-control-label {\n      margin-bottom: 0; } }\n\n.btn {\n  display: inline-block;\n  font-weight: 400;\n  color: #212529;\n  text-align: center;\n  vertical-align: middle;\n  cursor: pointer;\n  user-select: none;\n  background-color: transparent;\n  border: 1px solid transparent;\n  padding: 0.375rem 0.75rem;\n  font-size: 1rem;\n  line-height: 1.5;\n  border-radius: 0.25rem;\n  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; }\n  @media (prefers-reduced-motion: reduce) {\n    .btn {\n      transition: none; } }\n  .btn:hover {\n    color: #212529;\n    text-decoration: none; }\n  .btn:focus, .btn.focus {\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(67, 126, 196, 0.25); }\n  .btn.disabled, .btn:disabled {\n    opacity: 0.65; }\n\na.btn.disabled,\nfieldset:disabled a.btn {\n  pointer-events: none; }\n\n.btn-primary {\n  color: #fff;\n  background-color: #437ec4;\n  border-color: #437ec4; }\n  .btn-primary:hover {\n    color: #fff;\n    background-color: #366bab;\n    border-color: #3365a1; }\n  .btn-primary:focus, .btn-primary.focus {\n    color: #fff;\n    background-color: #366bab;\n    border-color: #3365a1;\n    box-shadow: 0 0 0 0.2rem rgba(95, 145, 205, 0.5); }\n  .btn-primary.disabled, .btn-primary:disabled {\n    color: #fff;\n    background-color: #437ec4;\n    border-color: #437ec4; }\n  .btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active,\n  .show > .btn-primary.dropdown-toggle {\n    color: #fff;\n    background-color: #3365a1;\n    border-color: #305f98; }\n    .btn-primary:not(:disabled):not(.disabled):active:focus, .btn-primary:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-primary.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(95, 145, 205, 0.5); }\n\n.btn-secondary {\n  color: #fff;\n  background-color: #54687A;\n  border-color: #54687A; }\n  .btn-secondary:hover {\n    color: #fff;\n    background-color: #445563;\n    border-color: #3f4e5c; }\n  .btn-secondary:focus, .btn-secondary.focus {\n    color: #fff;\n    background-color: #445563;\n    border-color: #3f4e5c;\n    box-shadow: 0 0 0 0.2rem rgba(110, 127, 142, 0.5); }\n  .btn-secondary.disabled, .btn-secondary:disabled {\n    color: #fff;\n    background-color: #54687A;\n    border-color: #54687A; }\n  .btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active,\n  .show > .btn-secondary.dropdown-toggle {\n    color: #fff;\n    background-color: #3f4e5c;\n    border-color: #3a4854; }\n    .btn-secondary:not(:disabled):not(.disabled):active:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-secondary.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(110, 127, 142, 0.5); }\n\n.btn-success {\n  color: #fff;\n  background-color: #25b877;\n  border-color: #25b877; }\n  .btn-success:hover {\n    color: #fff;\n    background-color: #1f9862;\n    border-color: #1c8e5c; }\n  .btn-success:focus, .btn-success.focus {\n    color: #fff;\n    background-color: #1f9862;\n    border-color: #1c8e5c;\n    box-shadow: 0 0 0 0.2rem rgba(70, 195, 139, 0.5); }\n  .btn-success.disabled, .btn-success:disabled {\n    color: #fff;\n    background-color: #25b877;\n    border-color: #25b877; }\n  .btn-success:not(:disabled):not(.disabled):active, .btn-success:not(:disabled):not(.disabled).active,\n  .show > .btn-success.dropdown-toggle {\n    color: #fff;\n    background-color: #1c8e5c;\n    border-color: #1a8355; }\n    .btn-success:not(:disabled):not(.disabled):active:focus, .btn-success:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-success.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(70, 195, 139, 0.5); }\n\n.btn-info {\n  color: #fff;\n  background-color: #17a2b8;\n  border-color: #17a2b8; }\n  .btn-info:hover {\n    color: #fff;\n    background-color: #138496;\n    border-color: #117a8b; }\n  .btn-info:focus, .btn-info.focus {\n    color: #fff;\n    background-color: #138496;\n    border-color: #117a8b;\n    box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5); }\n  .btn-info.disabled, .btn-info:disabled {\n    color: #fff;\n    background-color: #17a2b8;\n    border-color: #17a2b8; }\n  .btn-info:not(:disabled):not(.disabled):active, .btn-info:not(:disabled):not(.disabled).active,\n  .show > .btn-info.dropdown-toggle {\n    color: #fff;\n    background-color: #117a8b;\n    border-color: #10707f; }\n    .btn-info:not(:disabled):not(.disabled):active:focus, .btn-info:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-info.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5); }\n\n.btn-warning {\n  color: #212529;\n  background-color: #eeb128;\n  border-color: #eeb128; }\n  .btn-warning:hover {\n    color: #212529;\n    background-color: #de9f12;\n    border-color: #d29711; }\n  .btn-warning:focus, .btn-warning.focus {\n    color: #212529;\n    background-color: #de9f12;\n    border-color: #d29711;\n    box-shadow: 0 0 0 0.2rem rgba(207, 156, 40, 0.5); }\n  .btn-warning.disabled, .btn-warning:disabled {\n    color: #212529;\n    background-color: #eeb128;\n    border-color: #eeb128; }\n  .btn-warning:not(:disabled):not(.disabled):active, .btn-warning:not(:disabled):not(.disabled).active,\n  .show > .btn-warning.dropdown-toggle {\n    color: #212529;\n    background-color: #d29711;\n    border-color: #c78e10; }\n    .btn-warning:not(:disabled):not(.disabled):active:focus, .btn-warning:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-warning.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(207, 156, 40, 0.5); }\n\n.btn-danger {\n  color: #fff;\n  background-color: #c04949;\n  border-color: #c04949; }\n  .btn-danger:hover {\n    color: #fff;\n    background-color: #a83a3a;\n    border-color: #9f3737; }\n  .btn-danger:focus, .btn-danger.focus {\n    color: #fff;\n    background-color: #a83a3a;\n    border-color: #9f3737;\n    box-shadow: 0 0 0 0.2rem rgba(201, 100, 100, 0.5); }\n  .btn-danger.disabled, .btn-danger:disabled {\n    color: #fff;\n    background-color: #c04949;\n    border-color: #c04949; }\n  .btn-danger:not(:disabled):not(.disabled):active, .btn-danger:not(:disabled):not(.disabled).active,\n  .show > .btn-danger.dropdown-toggle {\n    color: #fff;\n    background-color: #9f3737;\n    border-color: #963434; }\n    .btn-danger:not(:disabled):not(.disabled):active:focus, .btn-danger:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-danger.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(201, 100, 100, 0.5); }\n\n.btn-light {\n  color: #212529;\n  background-color: #eff0f4;\n  border-color: #eff0f4; }\n  .btn-light:hover {\n    color: #212529;\n    background-color: #d8dbe4;\n    border-color: #d1d4df; }\n  .btn-light:focus, .btn-light.focus {\n    color: #212529;\n    background-color: #d8dbe4;\n    border-color: #d1d4df;\n    box-shadow: 0 0 0 0.2rem rgba(208, 210, 214, 0.5); }\n  .btn-light.disabled, .btn-light:disabled {\n    color: #212529;\n    background-color: #eff0f4;\n    border-color: #eff0f4; }\n  .btn-light:not(:disabled):not(.disabled):active, .btn-light:not(:disabled):not(.disabled).active,\n  .show > .btn-light.dropdown-toggle {\n    color: #212529;\n    background-color: #d1d4df;\n    border-color: #c9cdda; }\n    .btn-light:not(:disabled):not(.disabled):active:focus, .btn-light:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-light.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(208, 210, 214, 0.5); }\n\n.btn-dark {\n  color: #fff;\n  background-color: #595959;\n  border-color: #595959; }\n  .btn-dark:hover {\n    color: #fff;\n    background-color: #464646;\n    border-color: #404040; }\n  .btn-dark:focus, .btn-dark.focus {\n    color: #fff;\n    background-color: #464646;\n    border-color: #404040;\n    box-shadow: 0 0 0 0.2rem rgba(114, 114, 114, 0.5); }\n  .btn-dark.disabled, .btn-dark:disabled {\n    color: #fff;\n    background-color: #595959;\n    border-color: #595959; }\n  .btn-dark:not(:disabled):not(.disabled):active, .btn-dark:not(:disabled):not(.disabled).active,\n  .show > .btn-dark.dropdown-toggle {\n    color: #fff;\n    background-color: #404040;\n    border-color: #393939; }\n    .btn-dark:not(:disabled):not(.disabled):active:focus, .btn-dark:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-dark.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(114, 114, 114, 0.5); }\n\n.btn-outline-primary {\n  color: #437ec4;\n  border-color: #437ec4; }\n  .btn-outline-primary:hover {\n    color: #fff;\n    background-color: #437ec4;\n    border-color: #437ec4; }\n  .btn-outline-primary:focus, .btn-outline-primary.focus {\n    box-shadow: 0 0 0 0.2rem rgba(67, 126, 196, 0.5); }\n  .btn-outline-primary.disabled, .btn-outline-primary:disabled {\n    color: #437ec4;\n    background-color: transparent; }\n  .btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active,\n  .show > .btn-outline-primary.dropdown-toggle {\n    color: #fff;\n    background-color: #437ec4;\n    border-color: #437ec4; }\n    .btn-outline-primary:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-outline-primary.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(67, 126, 196, 0.5); }\n\n.btn-outline-secondary {\n  color: #54687A;\n  border-color: #54687A; }\n  .btn-outline-secondary:hover {\n    color: #fff;\n    background-color: #54687A;\n    border-color: #54687A; }\n  .btn-outline-secondary:focus, .btn-outline-secondary.focus {\n    box-shadow: 0 0 0 0.2rem rgba(84, 104, 122, 0.5); }\n  .btn-outline-secondary.disabled, .btn-outline-secondary:disabled {\n    color: #54687A;\n    background-color: transparent; }\n  .btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active,\n  .show > .btn-outline-secondary.dropdown-toggle {\n    color: #fff;\n    background-color: #54687A;\n    border-color: #54687A; }\n    .btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-outline-secondary.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(84, 104, 122, 0.5); }\n\n.btn-outline-success {\n  color: #25b877;\n  border-color: #25b877; }\n  .btn-outline-success:hover {\n    color: #fff;\n    background-color: #25b877;\n    border-color: #25b877; }\n  .btn-outline-success:focus, .btn-outline-success.focus {\n    box-shadow: 0 0 0 0.2rem rgba(37, 184, 119, 0.5); }\n  .btn-outline-success.disabled, .btn-outline-success:disabled {\n    color: #25b877;\n    background-color: transparent; }\n  .btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active,\n  .show > .btn-outline-success.dropdown-toggle {\n    color: #fff;\n    background-color: #25b877;\n    border-color: #25b877; }\n    .btn-outline-success:not(:disabled):not(.disabled):active:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-outline-success.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(37, 184, 119, 0.5); }\n\n.btn-outline-info {\n  color: #17a2b8;\n  border-color: #17a2b8; }\n  .btn-outline-info:hover {\n    color: #fff;\n    background-color: #17a2b8;\n    border-color: #17a2b8; }\n  .btn-outline-info:focus, .btn-outline-info.focus {\n    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5); }\n  .btn-outline-info.disabled, .btn-outline-info:disabled {\n    color: #17a2b8;\n    background-color: transparent; }\n  .btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active,\n  .show > .btn-outline-info.dropdown-toggle {\n    color: #fff;\n    background-color: #17a2b8;\n    border-color: #17a2b8; }\n    .btn-outline-info:not(:disabled):not(.disabled):active:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-outline-info.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5); }\n\n.btn-outline-warning {\n  color: #eeb128;\n  border-color: #eeb128; }\n  .btn-outline-warning:hover {\n    color: #212529;\n    background-color: #eeb128;\n    border-color: #eeb128; }\n  .btn-outline-warning:focus, .btn-outline-warning.focus {\n    box-shadow: 0 0 0 0.2rem rgba(238, 177, 40, 0.5); }\n  .btn-outline-warning.disabled, .btn-outline-warning:disabled {\n    color: #eeb128;\n    background-color: transparent; }\n  .btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active,\n  .show > .btn-outline-warning.dropdown-toggle {\n    color: #212529;\n    background-color: #eeb128;\n    border-color: #eeb128; }\n    .btn-outline-warning:not(:disabled):not(.disabled):active:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-outline-warning.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(238, 177, 40, 0.5); }\n\n.btn-outline-danger {\n  color: #c04949;\n  border-color: #c04949; }\n  .btn-outline-danger:hover {\n    color: #fff;\n    background-color: #c04949;\n    border-color: #c04949; }\n  .btn-outline-danger:focus, .btn-outline-danger.focus {\n    box-shadow: 0 0 0 0.2rem rgba(192, 73, 73, 0.5); }\n  .btn-outline-danger.disabled, .btn-outline-danger:disabled {\n    color: #c04949;\n    background-color: transparent; }\n  .btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active,\n  .show > .btn-outline-danger.dropdown-toggle {\n    color: #fff;\n    background-color: #c04949;\n    border-color: #c04949; }\n    .btn-outline-danger:not(:disabled):not(.disabled):active:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-outline-danger.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(192, 73, 73, 0.5); }\n\n.btn-outline-light {\n  color: #eff0f4;\n  border-color: #eff0f4; }\n  .btn-outline-light:hover {\n    color: #212529;\n    background-color: #eff0f4;\n    border-color: #eff0f4; }\n  .btn-outline-light:focus, .btn-outline-light.focus {\n    box-shadow: 0 0 0 0.2rem rgba(239, 240, 244, 0.5); }\n  .btn-outline-light.disabled, .btn-outline-light:disabled {\n    color: #eff0f4;\n    background-color: transparent; }\n  .btn-outline-light:not(:disabled):not(.disabled):active, .btn-outline-light:not(:disabled):not(.disabled).active,\n  .show > .btn-outline-light.dropdown-toggle {\n    color: #212529;\n    background-color: #eff0f4;\n    border-color: #eff0f4; }\n    .btn-outline-light:not(:disabled):not(.disabled):active:focus, .btn-outline-light:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-outline-light.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(239, 240, 244, 0.5); }\n\n.btn-outline-dark {\n  color: #595959;\n  border-color: #595959; }\n  .btn-outline-dark:hover {\n    color: #fff;\n    background-color: #595959;\n    border-color: #595959; }\n  .btn-outline-dark:focus, .btn-outline-dark.focus {\n    box-shadow: 0 0 0 0.2rem rgba(89, 89, 89, 0.5); }\n  .btn-outline-dark.disabled, .btn-outline-dark:disabled {\n    color: #595959;\n    background-color: transparent; }\n  .btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active,\n  .show > .btn-outline-dark.dropdown-toggle {\n    color: #fff;\n    background-color: #595959;\n    border-color: #595959; }\n    .btn-outline-dark:not(:disabled):not(.disabled):active:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-outline-dark.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(89, 89, 89, 0.5); }\n\n.btn-link {\n  font-weight: 400;\n  color: #437ec4;\n  text-decoration: none; }\n  .btn-link:hover {\n    color: #2d598e;\n    text-decoration: underline; }\n  .btn-link:focus, .btn-link.focus {\n    text-decoration: underline;\n    box-shadow: none; }\n  .btn-link:disabled, .btn-link.disabled {\n    color: #6c757d;\n    pointer-events: none; }\n\n.btn-lg, .btn-group-lg > .btn {\n  padding: 0.5rem 1rem;\n  font-size: 1.25rem;\n  line-height: 1.5;\n  border-radius: 0.3rem; }\n\n.btn-sm, .btn-group-sm > .btn {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  border-radius: 0.2rem; }\n\n.btn-block {\n  display: block;\n  width: 100%; }\n  .btn-block + .btn-block {\n    margin-top: 0.5rem; }\n\ninput[type=\"submit\"].btn-block,\ninput[type=\"reset\"].btn-block,\ninput[type=\"button\"].btn-block {\n  width: 100%; }\n\n.fade {\n  transition: opacity 0.15s linear; }\n  @media (prefers-reduced-motion: reduce) {\n    .fade {\n      transition: none; } }\n  .fade:not(.show) {\n    opacity: 0; }\n\n.collapse:not(.show) {\n  display: none; }\n\n.collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  transition: height 0.35s ease; }\n  @media (prefers-reduced-motion: reduce) {\n    .collapsing {\n      transition: none; } }\n\n.dropup,\n.dropright,\n.dropdown,\n.dropleft {\n  position: relative; }\n\n.dropdown-toggle {\n  white-space: nowrap; }\n  .dropdown-toggle::after {\n    display: inline-block;\n    margin-left: 0.255em;\n    vertical-align: 0.255em;\n    content: \"\";\n    border-top: 0.3em solid;\n    border-right: 0.3em solid transparent;\n    border-bottom: 0;\n    border-left: 0.3em solid transparent; }\n  .dropdown-toggle:empty::after {\n    margin-left: 0; }\n\n.dropdown-menu {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  z-index: 1000;\n  display: none;\n  float: left;\n  min-width: 10rem;\n  padding: 0.5rem 0;\n  margin: 0.125rem 0 0;\n  font-size: 1rem;\n  color: #212529;\n  text-align: left;\n  list-style: none;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 0.25rem; }\n\n.dropdown-menu-left {\n  right: auto;\n  left: 0; }\n\n.dropdown-menu-right {\n  right: 0;\n  left: auto; }\n\n@media (min-width: 576px) {\n  .dropdown-menu-sm-left {\n    right: auto;\n    left: 0; }\n  .dropdown-menu-sm-right {\n    right: 0;\n    left: auto; } }\n\n@media (min-width: 768px) {\n  .dropdown-menu-md-left {\n    right: auto;\n    left: 0; }\n  .dropdown-menu-md-right {\n    right: 0;\n    left: auto; } }\n\n@media (min-width: 992px) {\n  .dropdown-menu-lg-left {\n    right: auto;\n    left: 0; }\n  .dropdown-menu-lg-right {\n    right: 0;\n    left: auto; } }\n\n@media (min-width: 1200px) {\n  .dropdown-menu-xl-left {\n    right: auto;\n    left: 0; }\n  .dropdown-menu-xl-right {\n    right: 0;\n    left: auto; } }\n\n.dropup .dropdown-menu {\n  top: auto;\n  bottom: 100%;\n  margin-top: 0;\n  margin-bottom: 0.125rem; }\n\n.dropup .dropdown-toggle::after {\n  display: inline-block;\n  margin-left: 0.255em;\n  vertical-align: 0.255em;\n  content: \"\";\n  border-top: 0;\n  border-right: 0.3em solid transparent;\n  border-bottom: 0.3em solid;\n  border-left: 0.3em solid transparent; }\n\n.dropup .dropdown-toggle:empty::after {\n  margin-left: 0; }\n\n.dropright .dropdown-menu {\n  top: 0;\n  right: auto;\n  left: 100%;\n  margin-top: 0;\n  margin-left: 0.125rem; }\n\n.dropright .dropdown-toggle::after {\n  display: inline-block;\n  margin-left: 0.255em;\n  vertical-align: 0.255em;\n  content: \"\";\n  border-top: 0.3em solid transparent;\n  border-right: 0;\n  border-bottom: 0.3em solid transparent;\n  border-left: 0.3em solid; }\n\n.dropright .dropdown-toggle:empty::after {\n  margin-left: 0; }\n\n.dropright .dropdown-toggle::after {\n  vertical-align: 0; }\n\n.dropleft .dropdown-menu {\n  top: 0;\n  right: 100%;\n  left: auto;\n  margin-top: 0;\n  margin-right: 0.125rem; }\n\n.dropleft .dropdown-toggle::after {\n  display: inline-block;\n  margin-left: 0.255em;\n  vertical-align: 0.255em;\n  content: \"\"; }\n\n.dropleft .dropdown-toggle::after {\n  display: none; }\n\n.dropleft .dropdown-toggle::before {\n  display: inline-block;\n  margin-right: 0.255em;\n  vertical-align: 0.255em;\n  content: \"\";\n  border-top: 0.3em solid transparent;\n  border-right: 0.3em solid;\n  border-bottom: 0.3em solid transparent; }\n\n.dropleft .dropdown-toggle:empty::after {\n  margin-left: 0; }\n\n.dropleft .dropdown-toggle::before {\n  vertical-align: 0; }\n\n.dropdown-menu[x-placement^=\"top\"], .dropdown-menu[x-placement^=\"right\"], .dropdown-menu[x-placement^=\"bottom\"], .dropdown-menu[x-placement^=\"left\"] {\n  right: auto;\n  bottom: auto; }\n\n.dropdown-divider {\n  height: 0;\n  margin: 0.5rem 0;\n  overflow: hidden;\n  border-top: 1px solid #e9ecef; }\n\n.dropdown-item {\n  display: block;\n  width: 100%;\n  padding: 0.25rem 1.5rem;\n  clear: both;\n  font-weight: 400;\n  color: #212529;\n  text-align: inherit;\n  white-space: nowrap;\n  background-color: transparent;\n  border: 0; }\n  .dropdown-item:hover, .dropdown-item:focus {\n    color: #16181b;\n    text-decoration: none;\n    background-color: #f8f9fa; }\n  .dropdown-item.active, .dropdown-item:active {\n    color: #fff;\n    text-decoration: none;\n    background-color: #437ec4; }\n  .dropdown-item.disabled, .dropdown-item:disabled {\n    color: #6c757d;\n    pointer-events: none;\n    background-color: transparent; }\n\n.dropdown-menu.show {\n  display: block; }\n\n.dropdown-header {\n  display: block;\n  padding: 0.5rem 1.5rem;\n  margin-bottom: 0;\n  font-size: 0.875rem;\n  color: #6c757d;\n  white-space: nowrap; }\n\n.dropdown-item-text {\n  display: block;\n  padding: 0.25rem 1.5rem;\n  color: #212529; }\n\n.btn-group,\n.btn-group-vertical {\n  position: relative;\n  display: inline-flex;\n  vertical-align: middle; }\n  .btn-group > .btn,\n  .btn-group-vertical > .btn {\n    position: relative;\n    flex: 1 1 auto; }\n    .btn-group > .btn:hover,\n    .btn-group-vertical > .btn:hover {\n      z-index: 1; }\n    .btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active,\n    .btn-group-vertical > .btn:focus,\n    .btn-group-vertical > .btn:active,\n    .btn-group-vertical > .btn.active {\n      z-index: 1; }\n\n.btn-toolbar {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start; }\n  .btn-toolbar .input-group {\n    width: auto; }\n\n.btn-group > .btn:not(:first-child),\n.btn-group > .btn-group:not(:first-child) {\n  margin-left: -1px; }\n\n.btn-group > .btn:not(:last-child):not(.dropdown-toggle),\n.btn-group > .btn-group:not(:last-child) > .btn {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0; }\n\n.btn-group > .btn:not(:first-child),\n.btn-group > .btn-group:not(:first-child) > .btn {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0; }\n\n.dropdown-toggle-split {\n  padding-right: 0.5625rem;\n  padding-left: 0.5625rem; }\n  .dropdown-toggle-split::after,\n  .dropup .dropdown-toggle-split::after,\n  .dropright .dropdown-toggle-split::after {\n    margin-left: 0; }\n  .dropleft .dropdown-toggle-split::before {\n    margin-right: 0; }\n\n.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split {\n  padding-right: 0.375rem;\n  padding-left: 0.375rem; }\n\n.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split {\n  padding-right: 0.75rem;\n  padding-left: 0.75rem; }\n\n.btn-group-vertical {\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center; }\n  .btn-group-vertical > .btn,\n  .btn-group-vertical > .btn-group {\n    width: 100%; }\n  .btn-group-vertical > .btn:not(:first-child),\n  .btn-group-vertical > .btn-group:not(:first-child) {\n    margin-top: -1px; }\n  .btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle),\n  .btn-group-vertical > .btn-group:not(:last-child) > .btn {\n    border-bottom-right-radius: 0;\n    border-bottom-left-radius: 0; }\n  .btn-group-vertical > .btn:not(:first-child),\n  .btn-group-vertical > .btn-group:not(:first-child) > .btn {\n    border-top-left-radius: 0;\n    border-top-right-radius: 0; }\n\n.btn-group-toggle > .btn,\n.btn-group-toggle > .btn-group > .btn {\n  margin-bottom: 0; }\n  .btn-group-toggle > .btn input[type=\"radio\"],\n  .btn-group-toggle > .btn input[type=\"checkbox\"],\n  .btn-group-toggle > .btn-group > .btn input[type=\"radio\"],\n  .btn-group-toggle > .btn-group > .btn input[type=\"checkbox\"] {\n    position: absolute;\n    clip: rect(0, 0, 0, 0);\n    pointer-events: none; }\n\n.input-group {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: stretch;\n  width: 100%; }\n  .input-group > .form-control,\n  .input-group > .form-control-plaintext,\n  .input-group > .custom-select,\n  .input-group > .custom-file {\n    position: relative;\n    flex: 1 1 0%;\n    min-width: 0;\n    margin-bottom: 0; }\n    .input-group > .form-control + .form-control,\n    .input-group > .form-control + .custom-select,\n    .input-group > .form-control + .custom-file,\n    .input-group > .form-control-plaintext + .form-control,\n    .input-group > .form-control-plaintext + .custom-select,\n    .input-group > .form-control-plaintext + .custom-file,\n    .input-group > .custom-select + .form-control,\n    .input-group > .custom-select + .custom-select,\n    .input-group > .custom-select + .custom-file,\n    .input-group > .custom-file + .form-control,\n    .input-group > .custom-file + .custom-select,\n    .input-group > .custom-file + .custom-file {\n      margin-left: -1px; }\n  .input-group > .form-control:focus,\n  .input-group > .custom-select:focus,\n  .input-group > .custom-file .custom-file-input:focus ~ .custom-file-label {\n    z-index: 3; }\n  .input-group > .custom-file .custom-file-input:focus {\n    z-index: 4; }\n  .input-group > .form-control:not(:last-child),\n  .input-group > .custom-select:not(:last-child) {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0; }\n  .input-group > .form-control:not(:first-child),\n  .input-group > .custom-select:not(:first-child) {\n    border-top-left-radius: 0;\n    border-bottom-left-radius: 0; }\n  .input-group > .custom-file {\n    display: flex;\n    align-items: center; }\n    .input-group > .custom-file:not(:last-child) .custom-file-label,\n    .input-group > .custom-file:not(:last-child) .custom-file-label::after {\n      border-top-right-radius: 0;\n      border-bottom-right-radius: 0; }\n    .input-group > .custom-file:not(:first-child) .custom-file-label {\n      border-top-left-radius: 0;\n      border-bottom-left-radius: 0; }\n\n.input-group-prepend,\n.input-group-append {\n  display: flex; }\n  .input-group-prepend .btn,\n  .input-group-append .btn {\n    position: relative;\n    z-index: 2; }\n    .input-group-prepend .btn:focus,\n    .input-group-append .btn:focus {\n      z-index: 3; }\n  .input-group-prepend .btn + .btn,\n  .input-group-prepend .btn + .input-group-text,\n  .input-group-prepend .input-group-text + .input-group-text,\n  .input-group-prepend .input-group-text + .btn,\n  .input-group-append .btn + .btn,\n  .input-group-append .btn + .input-group-text,\n  .input-group-append .input-group-text + .input-group-text,\n  .input-group-append .input-group-text + .btn {\n    margin-left: -1px; }\n\n.input-group-prepend {\n  margin-right: -1px; }\n\n.input-group-append {\n  margin-left: -1px; }\n\n.input-group-text {\n  display: flex;\n  align-items: center;\n  padding: 0.375rem 0.75rem;\n  margin-bottom: 0;\n  font-size: 1rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #495057;\n  text-align: center;\n  white-space: nowrap;\n  background-color: #e9ecef;\n  border: 1px solid #ced4da;\n  border-radius: 0.25rem; }\n  .input-group-text input[type=\"radio\"],\n  .input-group-text input[type=\"checkbox\"] {\n    margin-top: 0; }\n\n.input-group-lg > .form-control:not(textarea),\n.input-group-lg > .custom-select {\n  height: calc(1.5em + 1rem + 2px); }\n\n.input-group-lg > .form-control,\n.input-group-lg > .custom-select,\n.input-group-lg > .input-group-prepend > .input-group-text,\n.input-group-lg > .input-group-append > .input-group-text,\n.input-group-lg > .input-group-prepend > .btn,\n.input-group-lg > .input-group-append > .btn {\n  padding: 0.5rem 1rem;\n  font-size: 1.25rem;\n  line-height: 1.5;\n  border-radius: 0.3rem; }\n\n.input-group-sm > .form-control:not(textarea),\n.input-group-sm > .custom-select {\n  height: calc(1.5em + 0.5rem + 2px); }\n\n.input-group-sm > .form-control,\n.input-group-sm > .custom-select,\n.input-group-sm > .input-group-prepend > .input-group-text,\n.input-group-sm > .input-group-append > .input-group-text,\n.input-group-sm > .input-group-prepend > .btn,\n.input-group-sm > .input-group-append > .btn {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  border-radius: 0.2rem; }\n\n.input-group-lg > .custom-select,\n.input-group-sm > .custom-select {\n  padding-right: 1.75rem; }\n\n.input-group > .input-group-prepend > .btn,\n.input-group > .input-group-prepend > .input-group-text,\n.input-group > .input-group-append:not(:last-child) > .btn,\n.input-group > .input-group-append:not(:last-child) > .input-group-text,\n.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),\n.input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0; }\n\n.input-group > .input-group-append > .btn,\n.input-group > .input-group-append > .input-group-text,\n.input-group > .input-group-prepend:not(:first-child) > .btn,\n.input-group > .input-group-prepend:not(:first-child) > .input-group-text,\n.input-group > .input-group-prepend:first-child > .btn:not(:first-child),\n.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0; }\n\n.custom-control {\n  position: relative;\n  display: block;\n  min-height: 1.5rem;\n  padding-left: 1.5rem; }\n\n.custom-control-inline {\n  display: inline-flex;\n  margin-right: 1rem; }\n\n.custom-control-input {\n  position: absolute;\n  left: 0;\n  z-index: -1;\n  width: 1rem;\n  height: 1.25rem;\n  opacity: 0; }\n  .custom-control-input:checked ~ .custom-control-label::before {\n    color: #fff;\n    border-color: #437ec4;\n    background-color: #437ec4; }\n  .custom-control-input:focus ~ .custom-control-label::before {\n    box-shadow: 0 0 0 0.2rem rgba(67, 126, 196, 0.25); }\n  .custom-control-input:focus:not(:checked) ~ .custom-control-label::before {\n    border-color: #a4c1e2; }\n  .custom-control-input:not(:disabled):active ~ .custom-control-label::before {\n    color: #fff;\n    background-color: #cbdbef;\n    border-color: #cbdbef; }\n  .custom-control-input[disabled] ~ .custom-control-label, .custom-control-input:disabled ~ .custom-control-label {\n    color: #6c757d; }\n    .custom-control-input[disabled] ~ .custom-control-label::before, .custom-control-input:disabled ~ .custom-control-label::before {\n      background-color: #e9ecef; }\n\n.custom-control-label {\n  position: relative;\n  margin-bottom: 0;\n  vertical-align: top; }\n  .custom-control-label::before {\n    position: absolute;\n    top: 0.25rem;\n    left: -1.5rem;\n    display: block;\n    width: 1rem;\n    height: 1rem;\n    pointer-events: none;\n    content: \"\";\n    background-color: #fff;\n    border: #adb5bd solid 1px; }\n  .custom-control-label::after {\n    position: absolute;\n    top: 0.25rem;\n    left: -1.5rem;\n    display: block;\n    width: 1rem;\n    height: 1rem;\n    content: \"\";\n    background: no-repeat 50% / 50% 50%; }\n\n.custom-checkbox .custom-control-label::before {\n  border-radius: 0.25rem; }\n\n.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e\"); }\n\n.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {\n  border-color: #437ec4;\n  background-color: #437ec4; }\n\n.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e\"); }\n\n.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {\n  background-color: rgba(67, 126, 196, 0.5); }\n\n.custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {\n  background-color: rgba(67, 126, 196, 0.5); }\n\n.custom-radio .custom-control-label::before {\n  border-radius: 50%; }\n\n.custom-radio .custom-control-input:checked ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e\"); }\n\n.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before {\n  background-color: rgba(67, 126, 196, 0.5); }\n\n.custom-switch {\n  padding-left: 2.25rem; }\n  .custom-switch .custom-control-label::before {\n    left: -2.25rem;\n    width: 1.75rem;\n    pointer-events: all;\n    border-radius: 0.5rem; }\n  .custom-switch .custom-control-label::after {\n    top: calc(0.25rem + 2px);\n    left: calc(-2.25rem + 2px);\n    width: calc(1rem - 4px);\n    height: calc(1rem - 4px);\n    background-color: #adb5bd;\n    border-radius: 0.5rem;\n    transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; }\n    @media (prefers-reduced-motion: reduce) {\n      .custom-switch .custom-control-label::after {\n        transition: none; } }\n  .custom-switch .custom-control-input:checked ~ .custom-control-label::after {\n    background-color: #fff;\n    transform: translateX(0.75rem); }\n  .custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {\n    background-color: rgba(67, 126, 196, 0.5); }\n\n.custom-select {\n  display: inline-block;\n  width: 100%;\n  height: calc(1.5em + 0.75rem + 2px);\n  padding: 0.375rem 1.75rem 0.375rem 0.75rem;\n  font-size: 1rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #495057;\n  vertical-align: middle;\n  background: #fff url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e\") no-repeat right 0.75rem center/8px 10px;\n  border: 1px solid #ced4da;\n  border-radius: 0.25rem;\n  appearance: none; }\n  .custom-select:focus {\n    border-color: #a4c1e2;\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(67, 126, 196, 0.25); }\n    .custom-select:focus::-ms-value {\n      color: #495057;\n      background-color: #fff; }\n  .custom-select[multiple], .custom-select[size]:not([size=\"1\"]) {\n    height: auto;\n    padding-right: 0.75rem;\n    background-image: none; }\n  .custom-select:disabled {\n    color: #6c757d;\n    background-color: #e9ecef; }\n  .custom-select::-ms-expand {\n    display: none; }\n  .custom-select:-moz-focusring {\n    color: transparent;\n    text-shadow: 0 0 0 #495057; }\n\n.custom-select-sm {\n  height: calc(1.5em + 0.5rem + 2px);\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n  padding-left: 0.5rem;\n  font-size: 0.875rem; }\n\n.custom-select-lg {\n  height: calc(1.5em + 1rem + 2px);\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n  padding-left: 1rem;\n  font-size: 1.25rem; }\n\n.custom-file {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  height: calc(1.5em + 0.75rem + 2px);\n  margin-bottom: 0; }\n\n.custom-file-input {\n  position: relative;\n  z-index: 2;\n  width: 100%;\n  height: calc(1.5em + 0.75rem + 2px);\n  margin: 0;\n  opacity: 0; }\n  .custom-file-input:focus ~ .custom-file-label {\n    border-color: #a4c1e2;\n    box-shadow: 0 0 0 0.2rem rgba(67, 126, 196, 0.25); }\n  .custom-file-input[disabled] ~ .custom-file-label,\n  .custom-file-input:disabled ~ .custom-file-label {\n    background-color: #e9ecef; }\n  .custom-file-input:lang(en) ~ .custom-file-label::after {\n    content: \"Browse\"; }\n  .custom-file-input ~ .custom-file-label[data-browse]::after {\n    content: attr(data-browse); }\n\n.custom-file-label {\n  position: absolute;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1;\n  height: calc(1.5em + 0.75rem + 2px);\n  padding: 0.375rem 0.75rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #495057;\n  background-color: #fff;\n  border: 1px solid #ced4da;\n  border-radius: 0.25rem; }\n  .custom-file-label::after {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 3;\n    display: block;\n    height: calc(1.5em + 0.75rem);\n    padding: 0.375rem 0.75rem;\n    line-height: 1.5;\n    color: #495057;\n    content: \"Browse\";\n    background-color: #e9ecef;\n    border-left: inherit;\n    border-radius: 0 0.25rem 0.25rem 0; }\n\n.custom-range {\n  width: 100%;\n  height: 1.4rem;\n  padding: 0;\n  background-color: transparent;\n  appearance: none; }\n  .custom-range:focus {\n    outline: none; }\n    .custom-range:focus::-webkit-slider-thumb {\n      box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(67, 126, 196, 0.25); }\n    .custom-range:focus::-moz-range-thumb {\n      box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(67, 126, 196, 0.25); }\n    .custom-range:focus::-ms-thumb {\n      box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(67, 126, 196, 0.25); }\n  .custom-range::-moz-focus-outer {\n    border: 0; }\n  .custom-range::-webkit-slider-thumb {\n    width: 1rem;\n    height: 1rem;\n    margin-top: -0.25rem;\n    background-color: #437ec4;\n    border: 0;\n    border-radius: 1rem;\n    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n    appearance: none; }\n    @media (prefers-reduced-motion: reduce) {\n      .custom-range::-webkit-slider-thumb {\n        transition: none; } }\n    .custom-range::-webkit-slider-thumb:active {\n      background-color: #cbdbef; }\n  .custom-range::-webkit-slider-runnable-track {\n    width: 100%;\n    height: 0.5rem;\n    color: transparent;\n    cursor: pointer;\n    background-color: #dee2e6;\n    border-color: transparent;\n    border-radius: 1rem; }\n  .custom-range::-moz-range-thumb {\n    width: 1rem;\n    height: 1rem;\n    background-color: #437ec4;\n    border: 0;\n    border-radius: 1rem;\n    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n    appearance: none; }\n    @media (prefers-reduced-motion: reduce) {\n      .custom-range::-moz-range-thumb {\n        transition: none; } }\n    .custom-range::-moz-range-thumb:active {\n      background-color: #cbdbef; }\n  .custom-range::-moz-range-track {\n    width: 100%;\n    height: 0.5rem;\n    color: transparent;\n    cursor: pointer;\n    background-color: #dee2e6;\n    border-color: transparent;\n    border-radius: 1rem; }\n  .custom-range::-ms-thumb {\n    width: 1rem;\n    height: 1rem;\n    margin-top: 0;\n    margin-right: 0.2rem;\n    margin-left: 0.2rem;\n    background-color: #437ec4;\n    border: 0;\n    border-radius: 1rem;\n    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n    appearance: none; }\n    @media (prefers-reduced-motion: reduce) {\n      .custom-range::-ms-thumb {\n        transition: none; } }\n    .custom-range::-ms-thumb:active {\n      background-color: #cbdbef; }\n  .custom-range::-ms-track {\n    width: 100%;\n    height: 0.5rem;\n    color: transparent;\n    cursor: pointer;\n    background-color: transparent;\n    border-color: transparent;\n    border-width: 0.5rem; }\n  .custom-range::-ms-fill-lower {\n    background-color: #dee2e6;\n    border-radius: 1rem; }\n  .custom-range::-ms-fill-upper {\n    margin-right: 15px;\n    background-color: #dee2e6;\n    border-radius: 1rem; }\n  .custom-range:disabled::-webkit-slider-thumb {\n    background-color: #adb5bd; }\n  .custom-range:disabled::-webkit-slider-runnable-track {\n    cursor: default; }\n  .custom-range:disabled::-moz-range-thumb {\n    background-color: #adb5bd; }\n  .custom-range:disabled::-moz-range-track {\n    cursor: default; }\n  .custom-range:disabled::-ms-thumb {\n    background-color: #adb5bd; }\n\n.custom-control-label::before,\n.custom-file-label,\n.custom-select {\n  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; }\n  @media (prefers-reduced-motion: reduce) {\n    .custom-control-label::before,\n    .custom-file-label,\n    .custom-select {\n      transition: none; } }\n\n.nav {\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none; }\n\n.nav-link {\n  display: block;\n  padding: 0.5rem 1rem; }\n  .nav-link:hover, .nav-link:focus {\n    text-decoration: none; }\n  .nav-link.disabled {\n    color: #6c757d;\n    pointer-events: none;\n    cursor: default; }\n\n.nav-tabs {\n  border-bottom: 1px solid #dee2e6; }\n  .nav-tabs .nav-item {\n    margin-bottom: -1px; }\n  .nav-tabs .nav-link {\n    border: 1px solid transparent;\n    border-top-left-radius: 0.25rem;\n    border-top-right-radius: 0.25rem; }\n    .nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {\n      border-color: #e9ecef #e9ecef #dee2e6; }\n    .nav-tabs .nav-link.disabled {\n      color: #6c757d;\n      background-color: transparent;\n      border-color: transparent; }\n  .nav-tabs .nav-link.active,\n  .nav-tabs .nav-item.show .nav-link {\n    color: #495057;\n    background-color: #fff;\n    border-color: #dee2e6 #dee2e6 #fff; }\n  .nav-tabs .dropdown-menu {\n    margin-top: -1px;\n    border-top-left-radius: 0;\n    border-top-right-radius: 0; }\n\n.nav-pills .nav-link {\n  border-radius: 0.25rem; }\n\n.nav-pills .nav-link.active,\n.nav-pills .show > .nav-link {\n  color: #fff;\n  background-color: #437ec4; }\n\n.nav-fill .nav-item {\n  flex: 1 1 auto;\n  text-align: center; }\n\n.nav-justified .nav-item {\n  flex-basis: 0;\n  flex-grow: 1;\n  text-align: center; }\n\n.tab-content > .tab-pane {\n  display: none; }\n\n.tab-content > .active {\n  display: block; }\n\n.navbar {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0.5rem 1rem; }\n  .navbar .container,\n  .navbar .container-fluid, .navbar .container-sm, .navbar .container-md, .navbar .container-lg, .navbar .container-xl {\n    display: flex;\n    flex-wrap: wrap;\n    align-items: center;\n    justify-content: space-between; }\n\n.navbar-brand {\n  display: inline-block;\n  padding-top: 0.3125rem;\n  padding-bottom: 0.3125rem;\n  margin-right: 1rem;\n  font-size: 1.25rem;\n  line-height: inherit;\n  white-space: nowrap; }\n  .navbar-brand:hover, .navbar-brand:focus {\n    text-decoration: none; }\n\n.navbar-nav {\n  display: flex;\n  flex-direction: column;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none; }\n  .navbar-nav .nav-link {\n    padding-right: 0;\n    padding-left: 0; }\n  .navbar-nav .dropdown-menu {\n    position: static;\n    float: none; }\n\n.navbar-text {\n  display: inline-block;\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem; }\n\n.navbar-collapse {\n  flex-basis: 100%;\n  flex-grow: 1;\n  align-items: center; }\n\n.navbar-toggler {\n  padding: 0.25rem 0.75rem;\n  font-size: 1.25rem;\n  line-height: 1;\n  background-color: transparent;\n  border: 1px solid transparent;\n  border-radius: 0.25rem; }\n  .navbar-toggler:hover, .navbar-toggler:focus {\n    text-decoration: none; }\n\n.navbar-toggler-icon {\n  display: inline-block;\n  width: 1.5em;\n  height: 1.5em;\n  vertical-align: middle;\n  content: \"\";\n  background: no-repeat center center;\n  background-size: 100% 100%; }\n\n@media (max-width: 575.98px) {\n  .navbar-expand-sm > .container,\n  .navbar-expand-sm > .container-fluid, .navbar-expand-sm > .container-sm, .navbar-expand-sm > .container-md, .navbar-expand-sm > .container-lg, .navbar-expand-sm > .container-xl {\n    padding-right: 0;\n    padding-left: 0; } }\n\n@media (min-width: 576px) {\n  .navbar-expand-sm {\n    flex-flow: row nowrap;\n    justify-content: flex-start; }\n    .navbar-expand-sm .navbar-nav {\n      flex-direction: row; }\n      .navbar-expand-sm .navbar-nav .dropdown-menu {\n        position: absolute; }\n      .navbar-expand-sm .navbar-nav .nav-link {\n        padding-right: 0.5rem;\n        padding-left: 0.5rem; }\n    .navbar-expand-sm > .container,\n    .navbar-expand-sm > .container-fluid, .navbar-expand-sm > .container-sm, .navbar-expand-sm > .container-md, .navbar-expand-sm > .container-lg, .navbar-expand-sm > .container-xl {\n      flex-wrap: nowrap; }\n    .navbar-expand-sm .navbar-collapse {\n      display: flex !important;\n      flex-basis: auto; }\n    .navbar-expand-sm .navbar-toggler {\n      display: none; } }\n\n@media (max-width: 767.98px) {\n  .navbar-expand-md > .container,\n  .navbar-expand-md > .container-fluid, .navbar-expand-md > .container-sm, .navbar-expand-md > .container-md, .navbar-expand-md > .container-lg, .navbar-expand-md > .container-xl {\n    padding-right: 0;\n    padding-left: 0; } }\n\n@media (min-width: 768px) {\n  .navbar-expand-md {\n    flex-flow: row nowrap;\n    justify-content: flex-start; }\n    .navbar-expand-md .navbar-nav {\n      flex-direction: row; }\n      .navbar-expand-md .navbar-nav .dropdown-menu {\n        position: absolute; }\n      .navbar-expand-md .navbar-nav .nav-link {\n        padding-right: 0.5rem;\n        padding-left: 0.5rem; }\n    .navbar-expand-md > .container,\n    .navbar-expand-md > .container-fluid, .navbar-expand-md > .container-sm, .navbar-expand-md > .container-md, .navbar-expand-md > .container-lg, .navbar-expand-md > .container-xl {\n      flex-wrap: nowrap; }\n    .navbar-expand-md .navbar-collapse {\n      display: flex !important;\n      flex-basis: auto; }\n    .navbar-expand-md .navbar-toggler {\n      display: none; } }\n\n@media (max-width: 991.98px) {\n  .navbar-expand-lg > .container,\n  .navbar-expand-lg > .container-fluid, .navbar-expand-lg > .container-sm, .navbar-expand-lg > .container-md, .navbar-expand-lg > .container-lg, .navbar-expand-lg > .container-xl {\n    padding-right: 0;\n    padding-left: 0; } }\n\n@media (min-width: 992px) {\n  .navbar-expand-lg {\n    flex-flow: row nowrap;\n    justify-content: flex-start; }\n    .navbar-expand-lg .navbar-nav {\n      flex-direction: row; }\n      .navbar-expand-lg .navbar-nav .dropdown-menu {\n        position: absolute; }\n      .navbar-expand-lg .navbar-nav .nav-link {\n        padding-right: 0.5rem;\n        padding-left: 0.5rem; }\n    .navbar-expand-lg > .container,\n    .navbar-expand-lg > .container-fluid, .navbar-expand-lg > .container-sm, .navbar-expand-lg > .container-md, .navbar-expand-lg > .container-lg, .navbar-expand-lg > .container-xl {\n      flex-wrap: nowrap; }\n    .navbar-expand-lg .navbar-collapse {\n      display: flex !important;\n      flex-basis: auto; }\n    .navbar-expand-lg .navbar-toggler {\n      display: none; } }\n\n@media (max-width: 1199.98px) {\n  .navbar-expand-xl > .container,\n  .navbar-expand-xl > .container-fluid, .navbar-expand-xl > .container-sm, .navbar-expand-xl > .container-md, .navbar-expand-xl > .container-lg, .navbar-expand-xl > .container-xl {\n    padding-right: 0;\n    padding-left: 0; } }\n\n@media (min-width: 1200px) {\n  .navbar-expand-xl {\n    flex-flow: row nowrap;\n    justify-content: flex-start; }\n    .navbar-expand-xl .navbar-nav {\n      flex-direction: row; }\n      .navbar-expand-xl .navbar-nav .dropdown-menu {\n        position: absolute; }\n      .navbar-expand-xl .navbar-nav .nav-link {\n        padding-right: 0.5rem;\n        padding-left: 0.5rem; }\n    .navbar-expand-xl > .container,\n    .navbar-expand-xl > .container-fluid, .navbar-expand-xl > .container-sm, .navbar-expand-xl > .container-md, .navbar-expand-xl > .container-lg, .navbar-expand-xl > .container-xl {\n      flex-wrap: nowrap; }\n    .navbar-expand-xl .navbar-collapse {\n      display: flex !important;\n      flex-basis: auto; }\n    .navbar-expand-xl .navbar-toggler {\n      display: none; } }\n\n.navbar-expand {\n  flex-flow: row nowrap;\n  justify-content: flex-start; }\n  .navbar-expand > .container,\n  .navbar-expand > .container-fluid, .navbar-expand > .container-sm, .navbar-expand > .container-md, .navbar-expand > .container-lg, .navbar-expand > .container-xl {\n    padding-right: 0;\n    padding-left: 0; }\n  .navbar-expand .navbar-nav {\n    flex-direction: row; }\n    .navbar-expand .navbar-nav .dropdown-menu {\n      position: absolute; }\n    .navbar-expand .navbar-nav .nav-link {\n      padding-right: 0.5rem;\n      padding-left: 0.5rem; }\n  .navbar-expand > .container,\n  .navbar-expand > .container-fluid, .navbar-expand > .container-sm, .navbar-expand > .container-md, .navbar-expand > .container-lg, .navbar-expand > .container-xl {\n    flex-wrap: nowrap; }\n  .navbar-expand .navbar-collapse {\n    display: flex !important;\n    flex-basis: auto; }\n  .navbar-expand .navbar-toggler {\n    display: none; }\n\n.navbar-light .navbar-brand {\n  color: rgba(0, 0, 0, 0.9); }\n  .navbar-light .navbar-brand:hover, .navbar-light .navbar-brand:focus {\n    color: rgba(0, 0, 0, 0.9); }\n\n.navbar-light .navbar-nav .nav-link {\n  color: rgba(0, 0, 0, 0.5); }\n  .navbar-light .navbar-nav .nav-link:hover, .navbar-light .navbar-nav .nav-link:focus {\n    color: rgba(0, 0, 0, 0.7); }\n  .navbar-light .navbar-nav .nav-link.disabled {\n    color: rgba(0, 0, 0, 0.3); }\n\n.navbar-light .navbar-nav .show > .nav-link,\n.navbar-light .navbar-nav .active > .nav-link,\n.navbar-light .navbar-nav .nav-link.show,\n.navbar-light .navbar-nav .nav-link.active {\n  color: rgba(0, 0, 0, 0.9); }\n\n.navbar-light .navbar-toggler {\n  color: rgba(0, 0, 0, 0.5);\n  border-color: rgba(0, 0, 0, 0.1); }\n\n.navbar-light .navbar-toggler-icon {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(0, 0, 0, 0.5)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\"); }\n\n.navbar-light .navbar-text {\n  color: rgba(0, 0, 0, 0.5); }\n  .navbar-light .navbar-text a {\n    color: rgba(0, 0, 0, 0.9); }\n    .navbar-light .navbar-text a:hover, .navbar-light .navbar-text a:focus {\n      color: rgba(0, 0, 0, 0.9); }\n\n.navbar-dark .navbar-brand {\n  color: #fff; }\n  .navbar-dark .navbar-brand:hover, .navbar-dark .navbar-brand:focus {\n    color: #fff; }\n\n.navbar-dark .navbar-nav .nav-link {\n  color: rgba(255, 255, 255, 0.5); }\n  .navbar-dark .navbar-nav .nav-link:hover, .navbar-dark .navbar-nav .nav-link:focus {\n    color: rgba(255, 255, 255, 0.75); }\n  .navbar-dark .navbar-nav .nav-link.disabled {\n    color: rgba(255, 255, 255, 0.25); }\n\n.navbar-dark .navbar-nav .show > .nav-link,\n.navbar-dark .navbar-nav .active > .nav-link,\n.navbar-dark .navbar-nav .nav-link.show,\n.navbar-dark .navbar-nav .nav-link.active {\n  color: #fff; }\n\n.navbar-dark .navbar-toggler {\n  color: rgba(255, 255, 255, 0.5);\n  border-color: rgba(255, 255, 255, 0.1); }\n\n.navbar-dark .navbar-toggler-icon {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(255, 255, 255, 0.5)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\"); }\n\n.navbar-dark .navbar-text {\n  color: rgba(255, 255, 255, 0.5); }\n  .navbar-dark .navbar-text a {\n    color: #fff; }\n    .navbar-dark .navbar-text a:hover, .navbar-dark .navbar-text a:focus {\n      color: #fff; }\n\n.card {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  min-width: 0;\n  word-wrap: break-word;\n  background-color: #fff;\n  background-clip: border-box;\n  border: 1px solid rgba(0, 0, 0, 0.125);\n  border-radius: 0.25rem; }\n  .card > hr {\n    margin-right: 0;\n    margin-left: 0; }\n  .card > .list-group:first-child .list-group-item:first-child {\n    border-top-left-radius: 0.25rem;\n    border-top-right-radius: 0.25rem; }\n  .card > .list-group:last-child .list-group-item:last-child {\n    border-bottom-right-radius: 0.25rem;\n    border-bottom-left-radius: 0.25rem; }\n\n.card-body {\n  flex: 1 1 auto;\n  min-height: 1px;\n  padding: 1.25rem; }\n\n.card-title {\n  margin-bottom: 0.75rem; }\n\n.card-subtitle {\n  margin-top: -0.375rem;\n  margin-bottom: 0; }\n\n.card-text:last-child {\n  margin-bottom: 0; }\n\n.card-link:hover {\n  text-decoration: none; }\n\n.card-link + .card-link {\n  margin-left: 1.25rem; }\n\n.card-header {\n  padding: 0.75rem 1.25rem;\n  margin-bottom: 0;\n  background-color: white;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125); }\n  .card-header:first-child {\n    border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0; }\n  .card-header + .list-group .list-group-item:first-child {\n    border-top: 0; }\n\n.card-footer {\n  padding: 0.75rem 1.25rem;\n  background-color: white;\n  border-top: 1px solid rgba(0, 0, 0, 0.125); }\n  .card-footer:last-child {\n    border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px); }\n\n.card-header-tabs {\n  margin-right: -0.625rem;\n  margin-bottom: -0.75rem;\n  margin-left: -0.625rem;\n  border-bottom: 0; }\n\n.card-header-pills {\n  margin-right: -0.625rem;\n  margin-left: -0.625rem; }\n\n.card-img-overlay {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  padding: 1.25rem; }\n\n.card-img,\n.card-img-top,\n.card-img-bottom {\n  flex-shrink: 0;\n  width: 100%; }\n\n.card-img,\n.card-img-top {\n  border-top-left-radius: calc(0.25rem - 1px);\n  border-top-right-radius: calc(0.25rem - 1px); }\n\n.card-img,\n.card-img-bottom {\n  border-bottom-right-radius: calc(0.25rem - 1px);\n  border-bottom-left-radius: calc(0.25rem - 1px); }\n\n.card-deck .card {\n  margin-bottom: 15px; }\n\n@media (min-width: 576px) {\n  .card-deck {\n    display: flex;\n    flex-flow: row wrap;\n    margin-right: -15px;\n    margin-left: -15px; }\n    .card-deck .card {\n      flex: 1 0 0%;\n      margin-right: 15px;\n      margin-bottom: 0;\n      margin-left: 15px; } }\n\n.card-group > .card {\n  margin-bottom: 15px; }\n\n@media (min-width: 576px) {\n  .card-group {\n    display: flex;\n    flex-flow: row wrap; }\n    .card-group > .card {\n      flex: 1 0 0%;\n      margin-bottom: 0; }\n      .card-group > .card + .card {\n        margin-left: 0;\n        border-left: 0; }\n      .card-group > .card:not(:last-child) {\n        border-top-right-radius: 0;\n        border-bottom-right-radius: 0; }\n        .card-group > .card:not(:last-child) .card-img-top,\n        .card-group > .card:not(:last-child) .card-header {\n          border-top-right-radius: 0; }\n        .card-group > .card:not(:last-child) .card-img-bottom,\n        .card-group > .card:not(:last-child) .card-footer {\n          border-bottom-right-radius: 0; }\n      .card-group > .card:not(:first-child) {\n        border-top-left-radius: 0;\n        border-bottom-left-radius: 0; }\n        .card-group > .card:not(:first-child) .card-img-top,\n        .card-group > .card:not(:first-child) .card-header {\n          border-top-left-radius: 0; }\n        .card-group > .card:not(:first-child) .card-img-bottom,\n        .card-group > .card:not(:first-child) .card-footer {\n          border-bottom-left-radius: 0; } }\n\n.card-columns .card {\n  margin-bottom: 0.75rem; }\n\n@media (min-width: 576px) {\n  .card-columns {\n    column-count: 3;\n    column-gap: 1.25rem;\n    orphans: 1;\n    widows: 1; }\n    .card-columns .card {\n      display: inline-block;\n      width: 100%; } }\n\n.accordion > .card {\n  overflow: hidden; }\n  .accordion > .card:not(:last-of-type) {\n    border-bottom: 0;\n    border-bottom-right-radius: 0;\n    border-bottom-left-radius: 0; }\n  .accordion > .card:not(:first-of-type) {\n    border-top-left-radius: 0;\n    border-top-right-radius: 0; }\n  .accordion > .card > .card-header {\n    border-radius: 0;\n    margin-bottom: -1px; }\n\n.breadcrumb {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 0.75rem 1rem;\n  margin-bottom: 1rem;\n  list-style: none;\n  background-color: #eff0f4;\n  border-radius: 0.25rem; }\n\n.breadcrumb-item + .breadcrumb-item {\n  padding-left: 0.5rem; }\n  .breadcrumb-item + .breadcrumb-item::before {\n    display: inline-block;\n    padding-right: 0.5rem;\n    color: #6c757d;\n    content: \"/\"; }\n\n.breadcrumb-item + .breadcrumb-item:hover::before {\n  text-decoration: underline; }\n\n.breadcrumb-item + .breadcrumb-item:hover::before {\n  text-decoration: none; }\n\n.breadcrumb-item.active {\n  color: #6c757d; }\n\n.pagination {\n  display: flex;\n  padding-left: 0;\n  list-style: none;\n  border-radius: 0.25rem; }\n\n.page-link {\n  position: relative;\n  display: block;\n  padding: 0.5rem 0.75rem;\n  margin-left: -1px;\n  line-height: 1.25;\n  color: #437ec4;\n  background-color: #fff;\n  border: 1px solid #dee2e6; }\n  .page-link:hover {\n    z-index: 2;\n    color: #2d598e;\n    text-decoration: none;\n    background-color: #e9ecef;\n    border-color: #dee2e6; }\n  .page-link:focus {\n    z-index: 3;\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(67, 126, 196, 0.25); }\n\n.page-item:first-child .page-link {\n  margin-left: 0;\n  border-top-left-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem; }\n\n.page-item:last-child .page-link {\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem; }\n\n.page-item.active .page-link {\n  z-index: 3;\n  color: #fff;\n  background-color: #437ec4;\n  border-color: #437ec4; }\n\n.page-item.disabled .page-link {\n  color: #6c757d;\n  pointer-events: none;\n  cursor: auto;\n  background-color: #fff;\n  border-color: #dee2e6; }\n\n.pagination-lg .page-link {\n  padding: 0.75rem 1.5rem;\n  font-size: 1.25rem;\n  line-height: 1.5; }\n\n.pagination-lg .page-item:first-child .page-link {\n  border-top-left-radius: 0.3rem;\n  border-bottom-left-radius: 0.3rem; }\n\n.pagination-lg .page-item:last-child .page-link {\n  border-top-right-radius: 0.3rem;\n  border-bottom-right-radius: 0.3rem; }\n\n.pagination-sm .page-link {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.5; }\n\n.pagination-sm .page-item:first-child .page-link {\n  border-top-left-radius: 0.2rem;\n  border-bottom-left-radius: 0.2rem; }\n\n.pagination-sm .page-item:last-child .page-link {\n  border-top-right-radius: 0.2rem;\n  border-bottom-right-radius: 0.2rem; }\n\n.badge {\n  display: inline-block;\n  padding: 0.25em 0.4em;\n  font-size: 75%;\n  font-weight: 700;\n  line-height: 1;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: baseline;\n  border-radius: 0.25rem;\n  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; }\n  @media (prefers-reduced-motion: reduce) {\n    .badge {\n      transition: none; } }\n  a.badge:hover, a.badge:focus {\n    text-decoration: none; }\n  .badge:empty {\n    display: none; }\n\n.btn .badge {\n  position: relative;\n  top: -1px; }\n\n.badge-pill {\n  padding-right: 0.6em;\n  padding-left: 0.6em;\n  border-radius: 10rem; }\n\n.badge-primary {\n  color: #fff;\n  background-color: #437ec4; }\n  a.badge-primary:hover, a.badge-primary:focus {\n    color: #fff;\n    background-color: #3365a1; }\n  a.badge-primary:focus, a.badge-primary.focus {\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(67, 126, 196, 0.5); }\n\n.badge-secondary {\n  color: #fff;\n  background-color: #54687A; }\n  a.badge-secondary:hover, a.badge-secondary:focus {\n    color: #fff;\n    background-color: #3f4e5c; }\n  a.badge-secondary:focus, a.badge-secondary.focus {\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(84, 104, 122, 0.5); }\n\n.badge-success {\n  color: #fff;\n  background-color: #25b877; }\n  a.badge-success:hover, a.badge-success:focus {\n    color: #fff;\n    background-color: #1c8e5c; }\n  a.badge-success:focus, a.badge-success.focus {\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(37, 184, 119, 0.5); }\n\n.badge-info {\n  color: #fff;\n  background-color: #17a2b8; }\n  a.badge-info:hover, a.badge-info:focus {\n    color: #fff;\n    background-color: #117a8b; }\n  a.badge-info:focus, a.badge-info.focus {\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5); }\n\n.badge-warning {\n  color: #212529;\n  background-color: #eeb128; }\n  a.badge-warning:hover, a.badge-warning:focus {\n    color: #212529;\n    background-color: #d29711; }\n  a.badge-warning:focus, a.badge-warning.focus {\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(238, 177, 40, 0.5); }\n\n.badge-danger {\n  color: #fff;\n  background-color: #c04949; }\n  a.badge-danger:hover, a.badge-danger:focus {\n    color: #fff;\n    background-color: #9f3737; }\n  a.badge-danger:focus, a.badge-danger.focus {\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(192, 73, 73, 0.5); }\n\n.badge-light {\n  color: #212529;\n  background-color: #eff0f4; }\n  a.badge-light:hover, a.badge-light:focus {\n    color: #212529;\n    background-color: #d1d4df; }\n  a.badge-light:focus, a.badge-light.focus {\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(239, 240, 244, 0.5); }\n\n.badge-dark {\n  color: #fff;\n  background-color: #595959; }\n  a.badge-dark:hover, a.badge-dark:focus {\n    color: #fff;\n    background-color: #404040; }\n  a.badge-dark:focus, a.badge-dark.focus {\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(89, 89, 89, 0.5); }\n\n.jumbotron {\n  padding: 2rem 1rem;\n  margin-bottom: 2rem;\n  background-color: #e9ecef;\n  border-radius: 0.3rem; }\n  @media (min-width: 576px) {\n    .jumbotron {\n      padding: 4rem 2rem; } }\n\n.jumbotron-fluid {\n  padding-right: 0;\n  padding-left: 0;\n  border-radius: 0; }\n\n.alert {\n  position: relative;\n  padding: 0.75rem 1.25rem;\n  margin-bottom: 1rem;\n  border: 1px solid transparent;\n  border-radius: 0.25rem; }\n\n.alert-heading {\n  color: inherit; }\n\n.alert-link {\n  font-weight: 700; }\n\n.alert-dismissible {\n  padding-right: 4rem; }\n  .alert-dismissible .close {\n    position: absolute;\n    top: 0;\n    right: 0;\n    padding: 0.75rem 1.25rem;\n    color: inherit; }\n\n.alert-primary {\n  color: #234266;\n  background-color: #d9e5f3;\n  border-color: #cadbee; }\n  .alert-primary hr {\n    border-top-color: #b7cee8; }\n  .alert-primary .alert-link {\n    color: #162940; }\n\n.alert-secondary {\n  color: #2c363f;\n  background-color: #dde1e4;\n  border-color: #cfd5da; }\n  .alert-secondary hr {\n    border-top-color: #c1c8cf; }\n  .alert-secondary .alert-link {\n    color: #171c21; }\n\n.alert-success {\n  color: #13603e;\n  background-color: #d3f1e4;\n  border-color: #c2ebd9; }\n  .alert-success hr {\n    border-top-color: #afe5cd; }\n  .alert-success .alert-link {\n    color: #0b3523; }\n\n.alert-info {\n  color: #0c5460;\n  background-color: #d1ecf1;\n  border-color: #bee5eb; }\n  .alert-info hr {\n    border-top-color: #abdde5; }\n  .alert-info .alert-link {\n    color: #062c33; }\n\n.alert-warning {\n  color: #7c5c15;\n  background-color: #fcefd4;\n  border-color: #fae9c3; }\n  .alert-warning hr {\n    border-top-color: #f8e0ab; }\n  .alert-warning .alert-link {\n    color: #503c0e; }\n\n.alert-danger {\n  color: #642626;\n  background-color: #f2dbdb;\n  border-color: #edcccc; }\n  .alert-danger hr {\n    border-top-color: #e6b9b9; }\n  .alert-danger .alert-link {\n    color: #3f1818; }\n\n.alert-light {\n  color: #7c7d7f;\n  background-color: #fcfcfd;\n  border-color: #fbfbfc; }\n  .alert-light hr {\n    border-top-color: #ececf1; }\n  .alert-light .alert-link {\n    color: #636465; }\n\n.alert-dark {\n  color: #2e2e2e;\n  background-color: #dedede;\n  border-color: #d1d1d1; }\n  .alert-dark hr {\n    border-top-color: #c4c4c4; }\n  .alert-dark .alert-link {\n    color: #151515; }\n\n@keyframes progress-bar-stripes {\n  from {\n    background-position: 1rem 0; }\n  to {\n    background-position: 0 0; } }\n\n.progress {\n  display: flex;\n  height: 1rem;\n  overflow: hidden;\n  font-size: 0.75rem;\n  background-color: #e9ecef;\n  border-radius: 0.25rem; }\n\n.progress-bar {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  overflow: hidden;\n  color: #fff;\n  text-align: center;\n  white-space: nowrap;\n  background-color: #437ec4;\n  transition: width 0.6s ease; }\n  @media (prefers-reduced-motion: reduce) {\n    .progress-bar {\n      transition: none; } }\n\n.progress-bar-striped {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-size: 1rem 1rem; }\n\n.progress-bar-animated {\n  animation: progress-bar-stripes 1s linear infinite; }\n  @media (prefers-reduced-motion: reduce) {\n    .progress-bar-animated {\n      animation: none; } }\n\n.media {\n  display: flex;\n  align-items: flex-start; }\n\n.media-body {\n  flex: 1; }\n\n.list-group {\n  display: flex;\n  flex-direction: column;\n  padding-left: 0;\n  margin-bottom: 0; }\n\n.list-group-item-action {\n  width: 100%;\n  color: #495057;\n  text-align: inherit; }\n  .list-group-item-action:hover, .list-group-item-action:focus {\n    z-index: 1;\n    color: #495057;\n    text-decoration: none;\n    background-color: #f8f9fa; }\n  .list-group-item-action:active {\n    color: #212529;\n    background-color: #e9ecef; }\n\n.list-group-item {\n  position: relative;\n  display: block;\n  padding: 0.75rem 1.25rem;\n  background-color: #fff;\n  border: 1px solid rgba(0, 0, 0, 0.125); }\n  .list-group-item:first-child {\n    border-top-left-radius: 0.25rem;\n    border-top-right-radius: 0.25rem; }\n  .list-group-item:last-child {\n    border-bottom-right-radius: 0.25rem;\n    border-bottom-left-radius: 0.25rem; }\n  .list-group-item.disabled, .list-group-item:disabled {\n    color: #6c757d;\n    pointer-events: none;\n    background-color: #fff; }\n  .list-group-item.active {\n    z-index: 2;\n    color: #fff;\n    background-color: #437ec4;\n    border-color: #437ec4; }\n  .list-group-item + .list-group-item {\n    border-top-width: 0; }\n    .list-group-item + .list-group-item.active {\n      margin-top: -1px;\n      border-top-width: 1px; }\n\n.list-group-horizontal {\n  flex-direction: row; }\n  .list-group-horizontal .list-group-item:first-child {\n    border-bottom-left-radius: 0.25rem;\n    border-top-right-radius: 0; }\n  .list-group-horizontal .list-group-item:last-child {\n    border-top-right-radius: 0.25rem;\n    border-bottom-left-radius: 0; }\n  .list-group-horizontal .list-group-item.active {\n    margin-top: 0; }\n  .list-group-horizontal .list-group-item + .list-group-item {\n    border-top-width: 1px;\n    border-left-width: 0; }\n    .list-group-horizontal .list-group-item + .list-group-item.active {\n      margin-left: -1px;\n      border-left-width: 1px; }\n\n@media (min-width: 576px) {\n  .list-group-horizontal-sm {\n    flex-direction: row; }\n    .list-group-horizontal-sm .list-group-item:first-child {\n      border-bottom-left-radius: 0.25rem;\n      border-top-right-radius: 0; }\n    .list-group-horizontal-sm .list-group-item:last-child {\n      border-top-right-radius: 0.25rem;\n      border-bottom-left-radius: 0; }\n    .list-group-horizontal-sm .list-group-item.active {\n      margin-top: 0; }\n    .list-group-horizontal-sm .list-group-item + .list-group-item {\n      border-top-width: 1px;\n      border-left-width: 0; }\n      .list-group-horizontal-sm .list-group-item + .list-group-item.active {\n        margin-left: -1px;\n        border-left-width: 1px; } }\n\n@media (min-width: 768px) {\n  .list-group-horizontal-md {\n    flex-direction: row; }\n    .list-group-horizontal-md .list-group-item:first-child {\n      border-bottom-left-radius: 0.25rem;\n      border-top-right-radius: 0; }\n    .list-group-horizontal-md .list-group-item:last-child {\n      border-top-right-radius: 0.25rem;\n      border-bottom-left-radius: 0; }\n    .list-group-horizontal-md .list-group-item.active {\n      margin-top: 0; }\n    .list-group-horizontal-md .list-group-item + .list-group-item {\n      border-top-width: 1px;\n      border-left-width: 0; }\n      .list-group-horizontal-md .list-group-item + .list-group-item.active {\n        margin-left: -1px;\n        border-left-width: 1px; } }\n\n@media (min-width: 992px) {\n  .list-group-horizontal-lg {\n    flex-direction: row; }\n    .list-group-horizontal-lg .list-group-item:first-child {\n      border-bottom-left-radius: 0.25rem;\n      border-top-right-radius: 0; }\n    .list-group-horizontal-lg .list-group-item:last-child {\n      border-top-right-radius: 0.25rem;\n      border-bottom-left-radius: 0; }\n    .list-group-horizontal-lg .list-group-item.active {\n      margin-top: 0; }\n    .list-group-horizontal-lg .list-group-item + .list-group-item {\n      border-top-width: 1px;\n      border-left-width: 0; }\n      .list-group-horizontal-lg .list-group-item + .list-group-item.active {\n        margin-left: -1px;\n        border-left-width: 1px; } }\n\n@media (min-width: 1200px) {\n  .list-group-horizontal-xl {\n    flex-direction: row; }\n    .list-group-horizontal-xl .list-group-item:first-child {\n      border-bottom-left-radius: 0.25rem;\n      border-top-right-radius: 0; }\n    .list-group-horizontal-xl .list-group-item:last-child {\n      border-top-right-radius: 0.25rem;\n      border-bottom-left-radius: 0; }\n    .list-group-horizontal-xl .list-group-item.active {\n      margin-top: 0; }\n    .list-group-horizontal-xl .list-group-item + .list-group-item {\n      border-top-width: 1px;\n      border-left-width: 0; }\n      .list-group-horizontal-xl .list-group-item + .list-group-item.active {\n        margin-left: -1px;\n        border-left-width: 1px; } }\n\n.list-group-flush .list-group-item {\n  border-right-width: 0;\n  border-left-width: 0;\n  border-radius: 0; }\n  .list-group-flush .list-group-item:first-child {\n    border-top-width: 0; }\n\n.list-group-flush:last-child .list-group-item:last-child {\n  border-bottom-width: 0; }\n\n.list-group-item-primary {\n  color: #234266;\n  background-color: #cadbee; }\n  .list-group-item-primary.list-group-item-action:hover, .list-group-item-primary.list-group-item-action:focus {\n    color: #234266;\n    background-color: #b7cee8; }\n  .list-group-item-primary.list-group-item-action.active {\n    color: #fff;\n    background-color: #234266;\n    border-color: #234266; }\n\n.list-group-item-secondary {\n  color: #2c363f;\n  background-color: #cfd5da; }\n  .list-group-item-secondary.list-group-item-action:hover, .list-group-item-secondary.list-group-item-action:focus {\n    color: #2c363f;\n    background-color: #c1c8cf; }\n  .list-group-item-secondary.list-group-item-action.active {\n    color: #fff;\n    background-color: #2c363f;\n    border-color: #2c363f; }\n\n.list-group-item-success {\n  color: #13603e;\n  background-color: #c2ebd9; }\n  .list-group-item-success.list-group-item-action:hover, .list-group-item-success.list-group-item-action:focus {\n    color: #13603e;\n    background-color: #afe5cd; }\n  .list-group-item-success.list-group-item-action.active {\n    color: #fff;\n    background-color: #13603e;\n    border-color: #13603e; }\n\n.list-group-item-info {\n  color: #0c5460;\n  background-color: #bee5eb; }\n  .list-group-item-info.list-group-item-action:hover, .list-group-item-info.list-group-item-action:focus {\n    color: #0c5460;\n    background-color: #abdde5; }\n  .list-group-item-info.list-group-item-action.active {\n    color: #fff;\n    background-color: #0c5460;\n    border-color: #0c5460; }\n\n.list-group-item-warning {\n  color: #7c5c15;\n  background-color: #fae9c3; }\n  .list-group-item-warning.list-group-item-action:hover, .list-group-item-warning.list-group-item-action:focus {\n    color: #7c5c15;\n    background-color: #f8e0ab; }\n  .list-group-item-warning.list-group-item-action.active {\n    color: #fff;\n    background-color: #7c5c15;\n    border-color: #7c5c15; }\n\n.list-group-item-danger {\n  color: #642626;\n  background-color: #edcccc; }\n  .list-group-item-danger.list-group-item-action:hover, .list-group-item-danger.list-group-item-action:focus {\n    color: #642626;\n    background-color: #e6b9b9; }\n  .list-group-item-danger.list-group-item-action.active {\n    color: #fff;\n    background-color: #642626;\n    border-color: #642626; }\n\n.list-group-item-light {\n  color: #7c7d7f;\n  background-color: #fbfbfc; }\n  .list-group-item-light.list-group-item-action:hover, .list-group-item-light.list-group-item-action:focus {\n    color: #7c7d7f;\n    background-color: #ececf1; }\n  .list-group-item-light.list-group-item-action.active {\n    color: #fff;\n    background-color: #7c7d7f;\n    border-color: #7c7d7f; }\n\n.list-group-item-dark {\n  color: #2e2e2e;\n  background-color: #d1d1d1; }\n  .list-group-item-dark.list-group-item-action:hover, .list-group-item-dark.list-group-item-action:focus {\n    color: #2e2e2e;\n    background-color: #c4c4c4; }\n  .list-group-item-dark.list-group-item-action.active {\n    color: #fff;\n    background-color: #2e2e2e;\n    border-color: #2e2e2e; }\n\n.close {\n  float: right;\n  font-size: 1.5rem;\n  font-weight: 700;\n  line-height: 1;\n  color: #000;\n  text-shadow: 0 1px 0 #fff;\n  opacity: .5; }\n  .close:hover {\n    color: #000;\n    text-decoration: none; }\n  .close:not(:disabled):not(.disabled):hover, .close:not(:disabled):not(.disabled):focus {\n    opacity: .75; }\n\nbutton.close {\n  padding: 0;\n  background-color: transparent;\n  border: 0;\n  appearance: none; }\n\na.close.disabled {\n  pointer-events: none; }\n\n.toast {\n  max-width: 350px;\n  overflow: hidden;\n  font-size: 0.875rem;\n  background-color: rgba(255, 255, 255, 0.85);\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.1);\n  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10px);\n  opacity: 0;\n  border-radius: 0.25rem; }\n  .toast:not(:last-child) {\n    margin-bottom: 0.75rem; }\n  .toast.showing {\n    opacity: 1; }\n  .toast.show {\n    display: block;\n    opacity: 1; }\n  .toast.hide {\n    display: none; }\n\n.toast-header {\n  display: flex;\n  align-items: center;\n  padding: 0.25rem 0.75rem;\n  color: #6c757d;\n  background-color: rgba(255, 255, 255, 0.85);\n  background-clip: padding-box;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05); }\n\n.toast-body {\n  padding: 0.75rem; }\n\n.modal-open {\n  overflow: hidden; }\n  .modal-open .modal {\n    overflow-x: hidden;\n    overflow-y: auto; }\n\n.modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 1050;\n  display: none;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n  outline: 0; }\n\n.modal-dialog {\n  position: relative;\n  width: auto;\n  margin: 0.5rem;\n  pointer-events: none; }\n  .modal.fade .modal-dialog {\n    transition: transform 0.3s ease-out;\n    transform: translate(0, -50px); }\n    @media (prefers-reduced-motion: reduce) {\n      .modal.fade .modal-dialog {\n        transition: none; } }\n  .modal.show .modal-dialog {\n    transform: none; }\n  .modal.modal-static .modal-dialog {\n    transform: scale(1.02); }\n\n.modal-dialog-scrollable {\n  display: flex;\n  max-height: calc(100% - 1rem); }\n  .modal-dialog-scrollable .modal-content {\n    max-height: calc(100vh - 1rem);\n    overflow: hidden; }\n  .modal-dialog-scrollable .modal-header,\n  .modal-dialog-scrollable .modal-footer {\n    flex-shrink: 0; }\n  .modal-dialog-scrollable .modal-body {\n    overflow-y: auto; }\n\n.modal-dialog-centered {\n  display: flex;\n  align-items: center;\n  min-height: calc(100% - 1rem); }\n  .modal-dialog-centered::before {\n    display: block;\n    height: calc(100vh - 1rem);\n    content: \"\"; }\n  .modal-dialog-centered.modal-dialog-scrollable {\n    flex-direction: column;\n    justify-content: center;\n    height: 100%; }\n    .modal-dialog-centered.modal-dialog-scrollable .modal-content {\n      max-height: none; }\n    .modal-dialog-centered.modal-dialog-scrollable::before {\n      content: none; }\n\n.modal-content {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  pointer-events: auto;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.2);\n  border-radius: 0.3rem;\n  outline: 0; }\n\n.modal-backdrop {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 1040;\n  width: 100vw;\n  height: 100vh;\n  background-color: #000; }\n  .modal-backdrop.fade {\n    opacity: 0; }\n  .modal-backdrop.show {\n    opacity: 0.5; }\n\n.modal-header {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  padding: 1rem 1rem;\n  border-bottom: 1px solid #dee2e6;\n  border-top-left-radius: calc(0.3rem - 1px);\n  border-top-right-radius: calc(0.3rem - 1px); }\n  .modal-header .close {\n    padding: 1rem 1rem;\n    margin: -1rem -1rem -1rem auto; }\n\n.modal-title {\n  margin-bottom: 0;\n  line-height: 1.5; }\n\n.modal-body {\n  position: relative;\n  flex: 1 1 auto;\n  padding: 1rem; }\n\n.modal-footer {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: flex-end;\n  padding: 0.75rem;\n  border-top: 1px solid #dee2e6;\n  border-bottom-right-radius: calc(0.3rem - 1px);\n  border-bottom-left-radius: calc(0.3rem - 1px); }\n  .modal-footer > * {\n    margin: 0.25rem; }\n\n.modal-scrollbar-measure {\n  position: absolute;\n  top: -9999px;\n  width: 50px;\n  height: 50px;\n  overflow: scroll; }\n\n@media (min-width: 576px) {\n  .modal-dialog {\n    max-width: 500px;\n    margin: 1.75rem auto; }\n  .modal-dialog-scrollable {\n    max-height: calc(100% - 3.5rem); }\n    .modal-dialog-scrollable .modal-content {\n      max-height: calc(100vh - 3.5rem); }\n  .modal-dialog-centered {\n    min-height: calc(100% - 3.5rem); }\n    .modal-dialog-centered::before {\n      height: calc(100vh - 3.5rem); }\n  .modal-sm {\n    max-width: 300px; } }\n\n@media (min-width: 992px) {\n  .modal-lg,\n  .modal-xl {\n    max-width: 800px; } }\n\n@media (min-width: 1200px) {\n  .modal-xl {\n    max-width: 1140px; } }\n\n.tooltip {\n  position: absolute;\n  z-index: 1070;\n  display: block;\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-style: normal;\n  font-weight: 400;\n  line-height: 1.5;\n  text-align: left;\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n  font-size: 0.875rem;\n  word-wrap: break-word;\n  opacity: 0; }\n  .tooltip.show {\n    opacity: 0.9; }\n  .tooltip .arrow {\n    position: absolute;\n    display: block;\n    width: 0.8rem;\n    height: 0.4rem; }\n    .tooltip .arrow::before {\n      position: absolute;\n      content: \"\";\n      border-color: transparent;\n      border-style: solid; }\n\n.bs-tooltip-top, .bs-tooltip-auto[x-placement^=\"top\"] {\n  padding: 0.4rem 0; }\n  .bs-tooltip-top .arrow, .bs-tooltip-auto[x-placement^=\"top\"] .arrow {\n    bottom: 0; }\n    .bs-tooltip-top .arrow::before, .bs-tooltip-auto[x-placement^=\"top\"] .arrow::before {\n      top: 0;\n      border-width: 0.4rem 0.4rem 0;\n      border-top-color: #000; }\n\n.bs-tooltip-right, .bs-tooltip-auto[x-placement^=\"right\"] {\n  padding: 0 0.4rem; }\n  .bs-tooltip-right .arrow, .bs-tooltip-auto[x-placement^=\"right\"] .arrow {\n    left: 0;\n    width: 0.4rem;\n    height: 0.8rem; }\n    .bs-tooltip-right .arrow::before, .bs-tooltip-auto[x-placement^=\"right\"] .arrow::before {\n      right: 0;\n      border-width: 0.4rem 0.4rem 0.4rem 0;\n      border-right-color: #000; }\n\n.bs-tooltip-bottom, .bs-tooltip-auto[x-placement^=\"bottom\"] {\n  padding: 0.4rem 0; }\n  .bs-tooltip-bottom .arrow, .bs-tooltip-auto[x-placement^=\"bottom\"] .arrow {\n    top: 0; }\n    .bs-tooltip-bottom .arrow::before, .bs-tooltip-auto[x-placement^=\"bottom\"] .arrow::before {\n      bottom: 0;\n      border-width: 0 0.4rem 0.4rem;\n      border-bottom-color: #000; }\n\n.bs-tooltip-left, .bs-tooltip-auto[x-placement^=\"left\"] {\n  padding: 0 0.4rem; }\n  .bs-tooltip-left .arrow, .bs-tooltip-auto[x-placement^=\"left\"] .arrow {\n    right: 0;\n    width: 0.4rem;\n    height: 0.8rem; }\n    .bs-tooltip-left .arrow::before, .bs-tooltip-auto[x-placement^=\"left\"] .arrow::before {\n      left: 0;\n      border-width: 0.4rem 0 0.4rem 0.4rem;\n      border-left-color: #000; }\n\n.tooltip-inner {\n  max-width: 200px;\n  padding: 0.25rem 0.5rem;\n  color: #fff;\n  text-align: center;\n  background-color: #000;\n  border-radius: 0.25rem; }\n\n.popover {\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: 1060;\n  display: block;\n  max-width: 276px;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-style: normal;\n  font-weight: 400;\n  line-height: 1.5;\n  text-align: left;\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n  font-size: 0.875rem;\n  word-wrap: break-word;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.2);\n  border-radius: 0.3rem; }\n  .popover .arrow {\n    position: absolute;\n    display: block;\n    width: 1rem;\n    height: 0.5rem;\n    margin: 0 0.3rem; }\n    .popover .arrow::before, .popover .arrow::after {\n      position: absolute;\n      display: block;\n      content: \"\";\n      border-color: transparent;\n      border-style: solid; }\n\n.bs-popover-top, .bs-popover-auto[x-placement^=\"top\"] {\n  margin-bottom: 0.5rem; }\n  .bs-popover-top > .arrow, .bs-popover-auto[x-placement^=\"top\"] > .arrow {\n    bottom: calc(-0.5rem - 1px); }\n    .bs-popover-top > .arrow::before, .bs-popover-auto[x-placement^=\"top\"] > .arrow::before {\n      bottom: 0;\n      border-width: 0.5rem 0.5rem 0;\n      border-top-color: rgba(0, 0, 0, 0.25); }\n    .bs-popover-top > .arrow::after, .bs-popover-auto[x-placement^=\"top\"] > .arrow::after {\n      bottom: 1px;\n      border-width: 0.5rem 0.5rem 0;\n      border-top-color: #fff; }\n\n.bs-popover-right, .bs-popover-auto[x-placement^=\"right\"] {\n  margin-left: 0.5rem; }\n  .bs-popover-right > .arrow, .bs-popover-auto[x-placement^=\"right\"] > .arrow {\n    left: calc(-0.5rem - 1px);\n    width: 0.5rem;\n    height: 1rem;\n    margin: 0.3rem 0; }\n    .bs-popover-right > .arrow::before, .bs-popover-auto[x-placement^=\"right\"] > .arrow::before {\n      left: 0;\n      border-width: 0.5rem 0.5rem 0.5rem 0;\n      border-right-color: rgba(0, 0, 0, 0.25); }\n    .bs-popover-right > .arrow::after, .bs-popover-auto[x-placement^=\"right\"] > .arrow::after {\n      left: 1px;\n      border-width: 0.5rem 0.5rem 0.5rem 0;\n      border-right-color: #fff; }\n\n.bs-popover-bottom, .bs-popover-auto[x-placement^=\"bottom\"] {\n  margin-top: 0.5rem; }\n  .bs-popover-bottom > .arrow, .bs-popover-auto[x-placement^=\"bottom\"] > .arrow {\n    top: calc(-0.5rem - 1px); }\n    .bs-popover-bottom > .arrow::before, .bs-popover-auto[x-placement^=\"bottom\"] > .arrow::before {\n      top: 0;\n      border-width: 0 0.5rem 0.5rem 0.5rem;\n      border-bottom-color: rgba(0, 0, 0, 0.25); }\n    .bs-popover-bottom > .arrow::after, .bs-popover-auto[x-placement^=\"bottom\"] > .arrow::after {\n      top: 1px;\n      border-width: 0 0.5rem 0.5rem 0.5rem;\n      border-bottom-color: #fff; }\n  .bs-popover-bottom .popover-header::before, .bs-popover-auto[x-placement^=\"bottom\"] .popover-header::before {\n    position: absolute;\n    top: 0;\n    left: 50%;\n    display: block;\n    width: 1rem;\n    margin-left: -0.5rem;\n    content: \"\";\n    border-bottom: 1px solid #f7f7f7; }\n\n.bs-popover-left, .bs-popover-auto[x-placement^=\"left\"] {\n  margin-right: 0.5rem; }\n  .bs-popover-left > .arrow, .bs-popover-auto[x-placement^=\"left\"] > .arrow {\n    right: calc(-0.5rem - 1px);\n    width: 0.5rem;\n    height: 1rem;\n    margin: 0.3rem 0; }\n    .bs-popover-left > .arrow::before, .bs-popover-auto[x-placement^=\"left\"] > .arrow::before {\n      right: 0;\n      border-width: 0.5rem 0 0.5rem 0.5rem;\n      border-left-color: rgba(0, 0, 0, 0.25); }\n    .bs-popover-left > .arrow::after, .bs-popover-auto[x-placement^=\"left\"] > .arrow::after {\n      right: 1px;\n      border-width: 0.5rem 0 0.5rem 0.5rem;\n      border-left-color: #fff; }\n\n.popover-header {\n  padding: 0.5rem 0.75rem;\n  margin-bottom: 0;\n  font-size: 1rem;\n  background-color: #f7f7f7;\n  border-bottom: 1px solid #ebebeb;\n  border-top-left-radius: calc(0.3rem - 1px);\n  border-top-right-radius: calc(0.3rem - 1px); }\n  .popover-header:empty {\n    display: none; }\n\n.popover-body {\n  padding: 0.5rem 0.75rem;\n  color: #212529; }\n\n.carousel {\n  position: relative; }\n\n.carousel.pointer-event {\n  touch-action: pan-y; }\n\n.carousel-inner {\n  position: relative;\n  width: 100%;\n  overflow: hidden; }\n  .carousel-inner::after {\n    display: block;\n    clear: both;\n    content: \"\"; }\n\n.carousel-item {\n  position: relative;\n  display: none;\n  float: left;\n  width: 100%;\n  margin-right: -100%;\n  backface-visibility: hidden;\n  transition: transform 0.6s ease-in-out; }\n  @media (prefers-reduced-motion: reduce) {\n    .carousel-item {\n      transition: none; } }\n\n.carousel-item.active,\n.carousel-item-next,\n.carousel-item-prev {\n  display: block; }\n\n.carousel-item-next:not(.carousel-item-left),\n.active.carousel-item-right {\n  transform: translateX(100%); }\n\n.carousel-item-prev:not(.carousel-item-right),\n.active.carousel-item-left {\n  transform: translateX(-100%); }\n\n.carousel-fade .carousel-item {\n  opacity: 0;\n  transition-property: opacity;\n  transform: none; }\n\n.carousel-fade .carousel-item.active,\n.carousel-fade .carousel-item-next.carousel-item-left,\n.carousel-fade .carousel-item-prev.carousel-item-right {\n  z-index: 1;\n  opacity: 1; }\n\n.carousel-fade .active.carousel-item-left,\n.carousel-fade .active.carousel-item-right {\n  z-index: 0;\n  opacity: 0;\n  transition: opacity 0s 0.6s; }\n  @media (prefers-reduced-motion: reduce) {\n    .carousel-fade .active.carousel-item-left,\n    .carousel-fade .active.carousel-item-right {\n      transition: none; } }\n\n.carousel-control-prev,\n.carousel-control-next {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  z-index: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 15%;\n  color: #fff;\n  text-align: center;\n  opacity: 0.5;\n  transition: opacity 0.15s ease; }\n  @media (prefers-reduced-motion: reduce) {\n    .carousel-control-prev,\n    .carousel-control-next {\n      transition: none; } }\n  .carousel-control-prev:hover, .carousel-control-prev:focus,\n  .carousel-control-next:hover,\n  .carousel-control-next:focus {\n    color: #fff;\n    text-decoration: none;\n    outline: 0;\n    opacity: 0.9; }\n\n.carousel-control-prev {\n  left: 0; }\n\n.carousel-control-next {\n  right: 0; }\n\n.carousel-control-prev-icon,\n.carousel-control-next-icon {\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  background: no-repeat 50% / 100% 100%; }\n\n.carousel-control-prev-icon {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/%3e%3c/svg%3e\"); }\n\n.carousel-control-next-icon {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/%3e%3c/svg%3e\"); }\n\n.carousel-indicators {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 15;\n  display: flex;\n  justify-content: center;\n  padding-left: 0;\n  margin-right: 15%;\n  margin-left: 15%;\n  list-style: none; }\n  .carousel-indicators li {\n    box-sizing: content-box;\n    flex: 0 1 auto;\n    width: 30px;\n    height: 3px;\n    margin-right: 3px;\n    margin-left: 3px;\n    text-indent: -999px;\n    cursor: pointer;\n    background-color: #fff;\n    background-clip: padding-box;\n    border-top: 10px solid transparent;\n    border-bottom: 10px solid transparent;\n    opacity: .5;\n    transition: opacity 0.6s ease; }\n    @media (prefers-reduced-motion: reduce) {\n      .carousel-indicators li {\n        transition: none; } }\n  .carousel-indicators .active {\n    opacity: 1; }\n\n.carousel-caption {\n  position: absolute;\n  right: 15%;\n  bottom: 20px;\n  left: 15%;\n  z-index: 10;\n  padding-top: 20px;\n  padding-bottom: 20px;\n  color: #fff;\n  text-align: center; }\n\n@keyframes spinner-border {\n  to {\n    transform: rotate(360deg); } }\n\n.spinner-border {\n  display: inline-block;\n  width: 2rem;\n  height: 2rem;\n  vertical-align: text-bottom;\n  border: 0.25em solid currentColor;\n  border-right-color: transparent;\n  border-radius: 50%;\n  animation: spinner-border .75s linear infinite; }\n\n.spinner-border-sm {\n  width: 1rem;\n  height: 1rem;\n  border-width: 0.2em; }\n\n@keyframes spinner-grow {\n  0% {\n    transform: scale(0); }\n  50% {\n    opacity: 1; } }\n\n.spinner-grow {\n  display: inline-block;\n  width: 2rem;\n  height: 2rem;\n  vertical-align: text-bottom;\n  background-color: currentColor;\n  border-radius: 50%;\n  opacity: 0;\n  animation: spinner-grow .75s linear infinite; }\n\n.spinner-grow-sm {\n  width: 1rem;\n  height: 1rem; }\n\n.align-baseline {\n  vertical-align: baseline !important; }\n\n.align-top {\n  vertical-align: top !important; }\n\n.align-middle {\n  vertical-align: middle !important; }\n\n.align-bottom {\n  vertical-align: bottom !important; }\n\n.align-text-bottom {\n  vertical-align: text-bottom !important; }\n\n.align-text-top {\n  vertical-align: text-top !important; }\n\n.bg-primary {\n  background-color: #437ec4 !important; }\n\na.bg-primary:hover, a.bg-primary:focus,\nbutton.bg-primary:hover,\nbutton.bg-primary:focus {\n  background-color: #3365a1 !important; }\n\n.bg-secondary {\n  background-color: #54687A !important; }\n\na.bg-secondary:hover, a.bg-secondary:focus,\nbutton.bg-secondary:hover,\nbutton.bg-secondary:focus {\n  background-color: #3f4e5c !important; }\n\n.bg-success {\n  background-color: #25b877 !important; }\n\na.bg-success:hover, a.bg-success:focus,\nbutton.bg-success:hover,\nbutton.bg-success:focus {\n  background-color: #1c8e5c !important; }\n\n.bg-info {\n  background-color: #17a2b8 !important; }\n\na.bg-info:hover, a.bg-info:focus,\nbutton.bg-info:hover,\nbutton.bg-info:focus {\n  background-color: #117a8b !important; }\n\n.bg-warning {\n  background-color: #eeb128 !important; }\n\na.bg-warning:hover, a.bg-warning:focus,\nbutton.bg-warning:hover,\nbutton.bg-warning:focus {\n  background-color: #d29711 !important; }\n\n.bg-danger {\n  background-color: #c04949 !important; }\n\na.bg-danger:hover, a.bg-danger:focus,\nbutton.bg-danger:hover,\nbutton.bg-danger:focus {\n  background-color: #9f3737 !important; }\n\n.bg-light {\n  background-color: #eff0f4 !important; }\n\na.bg-light:hover, a.bg-light:focus,\nbutton.bg-light:hover,\nbutton.bg-light:focus {\n  background-color: #d1d4df !important; }\n\n.bg-dark {\n  background-color: #595959 !important; }\n\na.bg-dark:hover, a.bg-dark:focus,\nbutton.bg-dark:hover,\nbutton.bg-dark:focus {\n  background-color: #404040 !important; }\n\n.bg-white {\n  background-color: #fff !important; }\n\n.bg-transparent {\n  background-color: transparent !important; }\n\n.border {\n  border: 1px solid #dee2e6 !important; }\n\n.border-top {\n  border-top: 1px solid #dee2e6 !important; }\n\n.border-right {\n  border-right: 1px solid #dee2e6 !important; }\n\n.border-bottom {\n  border-bottom: 1px solid #dee2e6 !important; }\n\n.border-left {\n  border-left: 1px solid #dee2e6 !important; }\n\n.border-0 {\n  border: 0 !important; }\n\n.border-top-0 {\n  border-top: 0 !important; }\n\n.border-right-0 {\n  border-right: 0 !important; }\n\n.border-bottom-0 {\n  border-bottom: 0 !important; }\n\n.border-left-0 {\n  border-left: 0 !important; }\n\n.border-primary {\n  border-color: #437ec4 !important; }\n\n.border-secondary {\n  border-color: #54687A !important; }\n\n.border-success {\n  border-color: #25b877 !important; }\n\n.border-info {\n  border-color: #17a2b8 !important; }\n\n.border-warning {\n  border-color: #eeb128 !important; }\n\n.border-danger {\n  border-color: #c04949 !important; }\n\n.border-light {\n  border-color: #eff0f4 !important; }\n\n.border-dark {\n  border-color: #595959 !important; }\n\n.border-white {\n  border-color: #fff !important; }\n\n.rounded-sm {\n  border-radius: 0.2rem !important; }\n\n.rounded {\n  border-radius: 0.25rem !important; }\n\n.rounded-top {\n  border-top-left-radius: 0.25rem !important;\n  border-top-right-radius: 0.25rem !important; }\n\n.rounded-right {\n  border-top-right-radius: 0.25rem !important;\n  border-bottom-right-radius: 0.25rem !important; }\n\n.rounded-bottom {\n  border-bottom-right-radius: 0.25rem !important;\n  border-bottom-left-radius: 0.25rem !important; }\n\n.rounded-left {\n  border-top-left-radius: 0.25rem !important;\n  border-bottom-left-radius: 0.25rem !important; }\n\n.rounded-lg {\n  border-radius: 0.3rem !important; }\n\n.rounded-circle {\n  border-radius: 50% !important; }\n\n.rounded-pill {\n  border-radius: 50rem !important; }\n\n.rounded-0 {\n  border-radius: 0 !important; }\n\n.clearfix::after {\n  display: block;\n  clear: both;\n  content: \"\"; }\n\n.d-none {\n  display: none !important; }\n\n.d-inline {\n  display: inline !important; }\n\n.d-inline-block {\n  display: inline-block !important; }\n\n.d-block {\n  display: block !important; }\n\n.d-table {\n  display: table !important; }\n\n.d-table-row {\n  display: table-row !important; }\n\n.d-table-cell {\n  display: table-cell !important; }\n\n.d-flex {\n  display: flex !important; }\n\n.d-inline-flex {\n  display: inline-flex !important; }\n\n@media (min-width: 576px) {\n  .d-sm-none {\n    display: none !important; }\n  .d-sm-inline {\n    display: inline !important; }\n  .d-sm-inline-block {\n    display: inline-block !important; }\n  .d-sm-block {\n    display: block !important; }\n  .d-sm-table {\n    display: table !important; }\n  .d-sm-table-row {\n    display: table-row !important; }\n  .d-sm-table-cell {\n    display: table-cell !important; }\n  .d-sm-flex {\n    display: flex !important; }\n  .d-sm-inline-flex {\n    display: inline-flex !important; } }\n\n@media (min-width: 768px) {\n  .d-md-none {\n    display: none !important; }\n  .d-md-inline {\n    display: inline !important; }\n  .d-md-inline-block {\n    display: inline-block !important; }\n  .d-md-block {\n    display: block !important; }\n  .d-md-table {\n    display: table !important; }\n  .d-md-table-row {\n    display: table-row !important; }\n  .d-md-table-cell {\n    display: table-cell !important; }\n  .d-md-flex {\n    display: flex !important; }\n  .d-md-inline-flex {\n    display: inline-flex !important; } }\n\n@media (min-width: 992px) {\n  .d-lg-none {\n    display: none !important; }\n  .d-lg-inline {\n    display: inline !important; }\n  .d-lg-inline-block {\n    display: inline-block !important; }\n  .d-lg-block {\n    display: block !important; }\n  .d-lg-table {\n    display: table !important; }\n  .d-lg-table-row {\n    display: table-row !important; }\n  .d-lg-table-cell {\n    display: table-cell !important; }\n  .d-lg-flex {\n    display: flex !important; }\n  .d-lg-inline-flex {\n    display: inline-flex !important; } }\n\n@media (min-width: 1200px) {\n  .d-xl-none {\n    display: none !important; }\n  .d-xl-inline {\n    display: inline !important; }\n  .d-xl-inline-block {\n    display: inline-block !important; }\n  .d-xl-block {\n    display: block !important; }\n  .d-xl-table {\n    display: table !important; }\n  .d-xl-table-row {\n    display: table-row !important; }\n  .d-xl-table-cell {\n    display: table-cell !important; }\n  .d-xl-flex {\n    display: flex !important; }\n  .d-xl-inline-flex {\n    display: inline-flex !important; } }\n\n@media print {\n  .d-print-none {\n    display: none !important; }\n  .d-print-inline {\n    display: inline !important; }\n  .d-print-inline-block {\n    display: inline-block !important; }\n  .d-print-block {\n    display: block !important; }\n  .d-print-table {\n    display: table !important; }\n  .d-print-table-row {\n    display: table-row !important; }\n  .d-print-table-cell {\n    display: table-cell !important; }\n  .d-print-flex {\n    display: flex !important; }\n  .d-print-inline-flex {\n    display: inline-flex !important; } }\n\n.embed-responsive {\n  position: relative;\n  display: block;\n  width: 100%;\n  padding: 0;\n  overflow: hidden; }\n  .embed-responsive::before {\n    display: block;\n    content: \"\"; }\n  .embed-responsive .embed-responsive-item,\n  .embed-responsive iframe,\n  .embed-responsive embed,\n  .embed-responsive object,\n  .embed-responsive video {\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    border: 0; }\n\n.embed-responsive-21by9::before {\n  padding-top: 42.85714%; }\n\n.embed-responsive-16by9::before {\n  padding-top: 56.25%; }\n\n.embed-responsive-4by3::before {\n  padding-top: 75%; }\n\n.embed-responsive-1by1::before {\n  padding-top: 100%; }\n\n.flex-row {\n  flex-direction: row !important; }\n\n.flex-column {\n  flex-direction: column !important; }\n\n.flex-row-reverse {\n  flex-direction: row-reverse !important; }\n\n.flex-column-reverse {\n  flex-direction: column-reverse !important; }\n\n.flex-wrap {\n  flex-wrap: wrap !important; }\n\n.flex-nowrap {\n  flex-wrap: nowrap !important; }\n\n.flex-wrap-reverse {\n  flex-wrap: wrap-reverse !important; }\n\n.flex-fill {\n  flex: 1 1 auto !important; }\n\n.flex-grow-0 {\n  flex-grow: 0 !important; }\n\n.flex-grow-1 {\n  flex-grow: 1 !important; }\n\n.flex-shrink-0 {\n  flex-shrink: 0 !important; }\n\n.flex-shrink-1 {\n  flex-shrink: 1 !important; }\n\n.justify-content-start {\n  justify-content: flex-start !important; }\n\n.justify-content-end {\n  justify-content: flex-end !important; }\n\n.justify-content-center {\n  justify-content: center !important; }\n\n.justify-content-between {\n  justify-content: space-between !important; }\n\n.justify-content-around {\n  justify-content: space-around !important; }\n\n.align-items-start {\n  align-items: flex-start !important; }\n\n.align-items-end {\n  align-items: flex-end !important; }\n\n.align-items-center {\n  align-items: center !important; }\n\n.align-items-baseline {\n  align-items: baseline !important; }\n\n.align-items-stretch {\n  align-items: stretch !important; }\n\n.align-content-start {\n  align-content: flex-start !important; }\n\n.align-content-end {\n  align-content: flex-end !important; }\n\n.align-content-center {\n  align-content: center !important; }\n\n.align-content-between {\n  align-content: space-between !important; }\n\n.align-content-around {\n  align-content: space-around !important; }\n\n.align-content-stretch {\n  align-content: stretch !important; }\n\n.align-self-auto {\n  align-self: auto !important; }\n\n.align-self-start {\n  align-self: flex-start !important; }\n\n.align-self-end {\n  align-self: flex-end !important; }\n\n.align-self-center {\n  align-self: center !important; }\n\n.align-self-baseline {\n  align-self: baseline !important; }\n\n.align-self-stretch {\n  align-self: stretch !important; }\n\n@media (min-width: 576px) {\n  .flex-sm-row {\n    flex-direction: row !important; }\n  .flex-sm-column {\n    flex-direction: column !important; }\n  .flex-sm-row-reverse {\n    flex-direction: row-reverse !important; }\n  .flex-sm-column-reverse {\n    flex-direction: column-reverse !important; }\n  .flex-sm-wrap {\n    flex-wrap: wrap !important; }\n  .flex-sm-nowrap {\n    flex-wrap: nowrap !important; }\n  .flex-sm-wrap-reverse {\n    flex-wrap: wrap-reverse !important; }\n  .flex-sm-fill {\n    flex: 1 1 auto !important; }\n  .flex-sm-grow-0 {\n    flex-grow: 0 !important; }\n  .flex-sm-grow-1 {\n    flex-grow: 1 !important; }\n  .flex-sm-shrink-0 {\n    flex-shrink: 0 !important; }\n  .flex-sm-shrink-1 {\n    flex-shrink: 1 !important; }\n  .justify-content-sm-start {\n    justify-content: flex-start !important; }\n  .justify-content-sm-end {\n    justify-content: flex-end !important; }\n  .justify-content-sm-center {\n    justify-content: center !important; }\n  .justify-content-sm-between {\n    justify-content: space-between !important; }\n  .justify-content-sm-around {\n    justify-content: space-around !important; }\n  .align-items-sm-start {\n    align-items: flex-start !important; }\n  .align-items-sm-end {\n    align-items: flex-end !important; }\n  .align-items-sm-center {\n    align-items: center !important; }\n  .align-items-sm-baseline {\n    align-items: baseline !important; }\n  .align-items-sm-stretch {\n    align-items: stretch !important; }\n  .align-content-sm-start {\n    align-content: flex-start !important; }\n  .align-content-sm-end {\n    align-content: flex-end !important; }\n  .align-content-sm-center {\n    align-content: center !important; }\n  .align-content-sm-between {\n    align-content: space-between !important; }\n  .align-content-sm-around {\n    align-content: space-around !important; }\n  .align-content-sm-stretch {\n    align-content: stretch !important; }\n  .align-self-sm-auto {\n    align-self: auto !important; }\n  .align-self-sm-start {\n    align-self: flex-start !important; }\n  .align-self-sm-end {\n    align-self: flex-end !important; }\n  .align-self-sm-center {\n    align-self: center !important; }\n  .align-self-sm-baseline {\n    align-self: baseline !important; }\n  .align-self-sm-stretch {\n    align-self: stretch !important; } }\n\n@media (min-width: 768px) {\n  .flex-md-row {\n    flex-direction: row !important; }\n  .flex-md-column {\n    flex-direction: column !important; }\n  .flex-md-row-reverse {\n    flex-direction: row-reverse !important; }\n  .flex-md-column-reverse {\n    flex-direction: column-reverse !important; }\n  .flex-md-wrap {\n    flex-wrap: wrap !important; }\n  .flex-md-nowrap {\n    flex-wrap: nowrap !important; }\n  .flex-md-wrap-reverse {\n    flex-wrap: wrap-reverse !important; }\n  .flex-md-fill {\n    flex: 1 1 auto !important; }\n  .flex-md-grow-0 {\n    flex-grow: 0 !important; }\n  .flex-md-grow-1 {\n    flex-grow: 1 !important; }\n  .flex-md-shrink-0 {\n    flex-shrink: 0 !important; }\n  .flex-md-shrink-1 {\n    flex-shrink: 1 !important; }\n  .justify-content-md-start {\n    justify-content: flex-start !important; }\n  .justify-content-md-end {\n    justify-content: flex-end !important; }\n  .justify-content-md-center {\n    justify-content: center !important; }\n  .justify-content-md-between {\n    justify-content: space-between !important; }\n  .justify-content-md-around {\n    justify-content: space-around !important; }\n  .align-items-md-start {\n    align-items: flex-start !important; }\n  .align-items-md-end {\n    align-items: flex-end !important; }\n  .align-items-md-center {\n    align-items: center !important; }\n  .align-items-md-baseline {\n    align-items: baseline !important; }\n  .align-items-md-stretch {\n    align-items: stretch !important; }\n  .align-content-md-start {\n    align-content: flex-start !important; }\n  .align-content-md-end {\n    align-content: flex-end !important; }\n  .align-content-md-center {\n    align-content: center !important; }\n  .align-content-md-between {\n    align-content: space-between !important; }\n  .align-content-md-around {\n    align-content: space-around !important; }\n  .align-content-md-stretch {\n    align-content: stretch !important; }\n  .align-self-md-auto {\n    align-self: auto !important; }\n  .align-self-md-start {\n    align-self: flex-start !important; }\n  .align-self-md-end {\n    align-self: flex-end !important; }\n  .align-self-md-center {\n    align-self: center !important; }\n  .align-self-md-baseline {\n    align-self: baseline !important; }\n  .align-self-md-stretch {\n    align-self: stretch !important; } }\n\n@media (min-width: 992px) {\n  .flex-lg-row {\n    flex-direction: row !important; }\n  .flex-lg-column {\n    flex-direction: column !important; }\n  .flex-lg-row-reverse {\n    flex-direction: row-reverse !important; }\n  .flex-lg-column-reverse {\n    flex-direction: column-reverse !important; }\n  .flex-lg-wrap {\n    flex-wrap: wrap !important; }\n  .flex-lg-nowrap {\n    flex-wrap: nowrap !important; }\n  .flex-lg-wrap-reverse {\n    flex-wrap: wrap-reverse !important; }\n  .flex-lg-fill {\n    flex: 1 1 auto !important; }\n  .flex-lg-grow-0 {\n    flex-grow: 0 !important; }\n  .flex-lg-grow-1 {\n    flex-grow: 1 !important; }\n  .flex-lg-shrink-0 {\n    flex-shrink: 0 !important; }\n  .flex-lg-shrink-1 {\n    flex-shrink: 1 !important; }\n  .justify-content-lg-start {\n    justify-content: flex-start !important; }\n  .justify-content-lg-end {\n    justify-content: flex-end !important; }\n  .justify-content-lg-center {\n    justify-content: center !important; }\n  .justify-content-lg-between {\n    justify-content: space-between !important; }\n  .justify-content-lg-around {\n    justify-content: space-around !important; }\n  .align-items-lg-start {\n    align-items: flex-start !important; }\n  .align-items-lg-end {\n    align-items: flex-end !important; }\n  .align-items-lg-center {\n    align-items: center !important; }\n  .align-items-lg-baseline {\n    align-items: baseline !important; }\n  .align-items-lg-stretch {\n    align-items: stretch !important; }\n  .align-content-lg-start {\n    align-content: flex-start !important; }\n  .align-content-lg-end {\n    align-content: flex-end !important; }\n  .align-content-lg-center {\n    align-content: center !important; }\n  .align-content-lg-between {\n    align-content: space-between !important; }\n  .align-content-lg-around {\n    align-content: space-around !important; }\n  .align-content-lg-stretch {\n    align-content: stretch !important; }\n  .align-self-lg-auto {\n    align-self: auto !important; }\n  .align-self-lg-start {\n    align-self: flex-start !important; }\n  .align-self-lg-end {\n    align-self: flex-end !important; }\n  .align-self-lg-center {\n    align-self: center !important; }\n  .align-self-lg-baseline {\n    align-self: baseline !important; }\n  .align-self-lg-stretch {\n    align-self: stretch !important; } }\n\n@media (min-width: 1200px) {\n  .flex-xl-row {\n    flex-direction: row !important; }\n  .flex-xl-column {\n    flex-direction: column !important; }\n  .flex-xl-row-reverse {\n    flex-direction: row-reverse !important; }\n  .flex-xl-column-reverse {\n    flex-direction: column-reverse !important; }\n  .flex-xl-wrap {\n    flex-wrap: wrap !important; }\n  .flex-xl-nowrap {\n    flex-wrap: nowrap !important; }\n  .flex-xl-wrap-reverse {\n    flex-wrap: wrap-reverse !important; }\n  .flex-xl-fill {\n    flex: 1 1 auto !important; }\n  .flex-xl-grow-0 {\n    flex-grow: 0 !important; }\n  .flex-xl-grow-1 {\n    flex-grow: 1 !important; }\n  .flex-xl-shrink-0 {\n    flex-shrink: 0 !important; }\n  .flex-xl-shrink-1 {\n    flex-shrink: 1 !important; }\n  .justify-content-xl-start {\n    justify-content: flex-start !important; }\n  .justify-content-xl-end {\n    justify-content: flex-end !important; }\n  .justify-content-xl-center {\n    justify-content: center !important; }\n  .justify-content-xl-between {\n    justify-content: space-between !important; }\n  .justify-content-xl-around {\n    justify-content: space-around !important; }\n  .align-items-xl-start {\n    align-items: flex-start !important; }\n  .align-items-xl-end {\n    align-items: flex-end !important; }\n  .align-items-xl-center {\n    align-items: center !important; }\n  .align-items-xl-baseline {\n    align-items: baseline !important; }\n  .align-items-xl-stretch {\n    align-items: stretch !important; }\n  .align-content-xl-start {\n    align-content: flex-start !important; }\n  .align-content-xl-end {\n    align-content: flex-end !important; }\n  .align-content-xl-center {\n    align-content: center !important; }\n  .align-content-xl-between {\n    align-content: space-between !important; }\n  .align-content-xl-around {\n    align-content: space-around !important; }\n  .align-content-xl-stretch {\n    align-content: stretch !important; }\n  .align-self-xl-auto {\n    align-self: auto !important; }\n  .align-self-xl-start {\n    align-self: flex-start !important; }\n  .align-self-xl-end {\n    align-self: flex-end !important; }\n  .align-self-xl-center {\n    align-self: center !important; }\n  .align-self-xl-baseline {\n    align-self: baseline !important; }\n  .align-self-xl-stretch {\n    align-self: stretch !important; } }\n\n.float-left {\n  float: left !important; }\n\n.float-right {\n  float: right !important; }\n\n.float-none {\n  float: none !important; }\n\n@media (min-width: 576px) {\n  .float-sm-left {\n    float: left !important; }\n  .float-sm-right {\n    float: right !important; }\n  .float-sm-none {\n    float: none !important; } }\n\n@media (min-width: 768px) {\n  .float-md-left {\n    float: left !important; }\n  .float-md-right {\n    float: right !important; }\n  .float-md-none {\n    float: none !important; } }\n\n@media (min-width: 992px) {\n  .float-lg-left {\n    float: left !important; }\n  .float-lg-right {\n    float: right !important; }\n  .float-lg-none {\n    float: none !important; } }\n\n@media (min-width: 1200px) {\n  .float-xl-left {\n    float: left !important; }\n  .float-xl-right {\n    float: right !important; }\n  .float-xl-none {\n    float: none !important; } }\n\n.overflow-auto {\n  overflow: auto !important; }\n\n.overflow-hidden {\n  overflow: hidden !important; }\n\n.position-static {\n  position: static !important; }\n\n.position-relative {\n  position: relative !important; }\n\n.position-absolute {\n  position: absolute !important; }\n\n.position-fixed {\n  position: fixed !important; }\n\n.position-sticky {\n  position: sticky !important; }\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1030; }\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1030; }\n\n@supports (position: sticky) {\n  .sticky-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020; } }\n\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0; }\n\n.sr-only-focusable:active, .sr-only-focusable:focus {\n  position: static;\n  width: auto;\n  height: auto;\n  overflow: visible;\n  clip: auto;\n  white-space: normal; }\n\n.shadow-sm {\n  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important; }\n\n.shadow {\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; }\n\n.shadow-lg {\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important; }\n\n.shadow-none {\n  box-shadow: none !important; }\n\n.w-25 {\n  width: 25% !important; }\n\n.w-50 {\n  width: 50% !important; }\n\n.w-75 {\n  width: 75% !important; }\n\n.w-100 {\n  width: 100% !important; }\n\n.w-auto {\n  width: auto !important; }\n\n.h-25 {\n  height: 25% !important; }\n\n.h-50 {\n  height: 50% !important; }\n\n.h-75 {\n  height: 75% !important; }\n\n.h-100 {\n  height: 100% !important; }\n\n.h-auto {\n  height: auto !important; }\n\n.mw-100 {\n  max-width: 100% !important; }\n\n.mh-100 {\n  max-height: 100% !important; }\n\n.min-vw-100 {\n  min-width: 100vw !important; }\n\n.min-vh-100 {\n  min-height: 100vh !important; }\n\n.vw-100 {\n  width: 100vw !important; }\n\n.vh-100 {\n  height: 100vh !important; }\n\n.stretched-link::after {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1;\n  pointer-events: auto;\n  content: \"\";\n  background-color: rgba(0, 0, 0, 0); }\n\n.m-0 {\n  margin: 0 !important; }\n\n.mt-0,\n.my-0 {\n  margin-top: 0 !important; }\n\n.mr-0,\n.mx-0 {\n  margin-right: 0 !important; }\n\n.mb-0,\n.my-0 {\n  margin-bottom: 0 !important; }\n\n.ml-0,\n.mx-0 {\n  margin-left: 0 !important; }\n\n.m-1 {\n  margin: 0.25rem !important; }\n\n.mt-1,\n.my-1 {\n  margin-top: 0.25rem !important; }\n\n.mr-1,\n.mx-1 {\n  margin-right: 0.25rem !important; }\n\n.mb-1,\n.my-1 {\n  margin-bottom: 0.25rem !important; }\n\n.ml-1,\n.mx-1 {\n  margin-left: 0.25rem !important; }\n\n.m-2 {\n  margin: 0.5rem !important; }\n\n.mt-2,\n.my-2 {\n  margin-top: 0.5rem !important; }\n\n.mr-2,\n.mx-2 {\n  margin-right: 0.5rem !important; }\n\n.mb-2,\n.my-2 {\n  margin-bottom: 0.5rem !important; }\n\n.ml-2,\n.mx-2 {\n  margin-left: 0.5rem !important; }\n\n.m-3 {\n  margin: 1rem !important; }\n\n.mt-3,\n.my-3 {\n  margin-top: 1rem !important; }\n\n.mr-3,\n.mx-3 {\n  margin-right: 1rem !important; }\n\n.mb-3,\n.my-3 {\n  margin-bottom: 1rem !important; }\n\n.ml-3,\n.mx-3 {\n  margin-left: 1rem !important; }\n\n.m-4 {\n  margin: 1.5rem !important; }\n\n.mt-4,\n.my-4 {\n  margin-top: 1.5rem !important; }\n\n.mr-4,\n.mx-4 {\n  margin-right: 1.5rem !important; }\n\n.mb-4,\n.my-4 {\n  margin-bottom: 1.5rem !important; }\n\n.ml-4,\n.mx-4 {\n  margin-left: 1.5rem !important; }\n\n.m-5 {\n  margin: 3rem !important; }\n\n.mt-5,\n.my-5 {\n  margin-top: 3rem !important; }\n\n.mr-5,\n.mx-5 {\n  margin-right: 3rem !important; }\n\n.mb-5,\n.my-5 {\n  margin-bottom: 3rem !important; }\n\n.ml-5,\n.mx-5 {\n  margin-left: 3rem !important; }\n\n.p-0 {\n  padding: 0 !important; }\n\n.pt-0,\n.py-0 {\n  padding-top: 0 !important; }\n\n.pr-0,\n.px-0 {\n  padding-right: 0 !important; }\n\n.pb-0,\n.py-0 {\n  padding-bottom: 0 !important; }\n\n.pl-0,\n.px-0 {\n  padding-left: 0 !important; }\n\n.p-1 {\n  padding: 0.25rem !important; }\n\n.pt-1,\n.py-1 {\n  padding-top: 0.25rem !important; }\n\n.pr-1,\n.px-1 {\n  padding-right: 0.25rem !important; }\n\n.pb-1,\n.py-1 {\n  padding-bottom: 0.25rem !important; }\n\n.pl-1,\n.px-1 {\n  padding-left: 0.25rem !important; }\n\n.p-2 {\n  padding: 0.5rem !important; }\n\n.pt-2,\n.py-2 {\n  padding-top: 0.5rem !important; }\n\n.pr-2,\n.px-2 {\n  padding-right: 0.5rem !important; }\n\n.pb-2,\n.py-2 {\n  padding-bottom: 0.5rem !important; }\n\n.pl-2,\n.px-2 {\n  padding-left: 0.5rem !important; }\n\n.p-3 {\n  padding: 1rem !important; }\n\n.pt-3,\n.py-3 {\n  padding-top: 1rem !important; }\n\n.pr-3,\n.px-3 {\n  padding-right: 1rem !important; }\n\n.pb-3,\n.py-3 {\n  padding-bottom: 1rem !important; }\n\n.pl-3,\n.px-3 {\n  padding-left: 1rem !important; }\n\n.p-4 {\n  padding: 1.5rem !important; }\n\n.pt-4,\n.py-4 {\n  padding-top: 1.5rem !important; }\n\n.pr-4,\n.px-4 {\n  padding-right: 1.5rem !important; }\n\n.pb-4,\n.py-4 {\n  padding-bottom: 1.5rem !important; }\n\n.pl-4,\n.px-4 {\n  padding-left: 1.5rem !important; }\n\n.p-5 {\n  padding: 3rem !important; }\n\n.pt-5,\n.py-5 {\n  padding-top: 3rem !important; }\n\n.pr-5,\n.px-5 {\n  padding-right: 3rem !important; }\n\n.pb-5,\n.py-5 {\n  padding-bottom: 3rem !important; }\n\n.pl-5,\n.px-5 {\n  padding-left: 3rem !important; }\n\n.m-n1 {\n  margin: -0.25rem !important; }\n\n.mt-n1,\n.my-n1 {\n  margin-top: -0.25rem !important; }\n\n.mr-n1,\n.mx-n1 {\n  margin-right: -0.25rem !important; }\n\n.mb-n1,\n.my-n1 {\n  margin-bottom: -0.25rem !important; }\n\n.ml-n1,\n.mx-n1 {\n  margin-left: -0.25rem !important; }\n\n.m-n2 {\n  margin: -0.5rem !important; }\n\n.mt-n2,\n.my-n2 {\n  margin-top: -0.5rem !important; }\n\n.mr-n2,\n.mx-n2 {\n  margin-right: -0.5rem !important; }\n\n.mb-n2,\n.my-n2 {\n  margin-bottom: -0.5rem !important; }\n\n.ml-n2,\n.mx-n2 {\n  margin-left: -0.5rem !important; }\n\n.m-n3 {\n  margin: -1rem !important; }\n\n.mt-n3,\n.my-n3 {\n  margin-top: -1rem !important; }\n\n.mr-n3,\n.mx-n3 {\n  margin-right: -1rem !important; }\n\n.mb-n3,\n.my-n3 {\n  margin-bottom: -1rem !important; }\n\n.ml-n3,\n.mx-n3 {\n  margin-left: -1rem !important; }\n\n.m-n4 {\n  margin: -1.5rem !important; }\n\n.mt-n4,\n.my-n4 {\n  margin-top: -1.5rem !important; }\n\n.mr-n4,\n.mx-n4 {\n  margin-right: -1.5rem !important; }\n\n.mb-n4,\n.my-n4 {\n  margin-bottom: -1.5rem !important; }\n\n.ml-n4,\n.mx-n4 {\n  margin-left: -1.5rem !important; }\n\n.m-n5 {\n  margin: -3rem !important; }\n\n.mt-n5,\n.my-n5 {\n  margin-top: -3rem !important; }\n\n.mr-n5,\n.mx-n5 {\n  margin-right: -3rem !important; }\n\n.mb-n5,\n.my-n5 {\n  margin-bottom: -3rem !important; }\n\n.ml-n5,\n.mx-n5 {\n  margin-left: -3rem !important; }\n\n.m-auto {\n  margin: auto !important; }\n\n.mt-auto,\n.my-auto {\n  margin-top: auto !important; }\n\n.mr-auto,\n.mx-auto {\n  margin-right: auto !important; }\n\n.mb-auto,\n.my-auto {\n  margin-bottom: auto !important; }\n\n.ml-auto,\n.mx-auto {\n  margin-left: auto !important; }\n\n@media (min-width: 576px) {\n  .m-sm-0 {\n    margin: 0 !important; }\n  .mt-sm-0,\n  .my-sm-0 {\n    margin-top: 0 !important; }\n  .mr-sm-0,\n  .mx-sm-0 {\n    margin-right: 0 !important; }\n  .mb-sm-0,\n  .my-sm-0 {\n    margin-bottom: 0 !important; }\n  .ml-sm-0,\n  .mx-sm-0 {\n    margin-left: 0 !important; }\n  .m-sm-1 {\n    margin: 0.25rem !important; }\n  .mt-sm-1,\n  .my-sm-1 {\n    margin-top: 0.25rem !important; }\n  .mr-sm-1,\n  .mx-sm-1 {\n    margin-right: 0.25rem !important; }\n  .mb-sm-1,\n  .my-sm-1 {\n    margin-bottom: 0.25rem !important; }\n  .ml-sm-1,\n  .mx-sm-1 {\n    margin-left: 0.25rem !important; }\n  .m-sm-2 {\n    margin: 0.5rem !important; }\n  .mt-sm-2,\n  .my-sm-2 {\n    margin-top: 0.5rem !important; }\n  .mr-sm-2,\n  .mx-sm-2 {\n    margin-right: 0.5rem !important; }\n  .mb-sm-2,\n  .my-sm-2 {\n    margin-bottom: 0.5rem !important; }\n  .ml-sm-2,\n  .mx-sm-2 {\n    margin-left: 0.5rem !important; }\n  .m-sm-3 {\n    margin: 1rem !important; }\n  .mt-sm-3,\n  .my-sm-3 {\n    margin-top: 1rem !important; }\n  .mr-sm-3,\n  .mx-sm-3 {\n    margin-right: 1rem !important; }\n  .mb-sm-3,\n  .my-sm-3 {\n    margin-bottom: 1rem !important; }\n  .ml-sm-3,\n  .mx-sm-3 {\n    margin-left: 1rem !important; }\n  .m-sm-4 {\n    margin: 1.5rem !important; }\n  .mt-sm-4,\n  .my-sm-4 {\n    margin-top: 1.5rem !important; }\n  .mr-sm-4,\n  .mx-sm-4 {\n    margin-right: 1.5rem !important; }\n  .mb-sm-4,\n  .my-sm-4 {\n    margin-bottom: 1.5rem !important; }\n  .ml-sm-4,\n  .mx-sm-4 {\n    margin-left: 1.5rem !important; }\n  .m-sm-5 {\n    margin: 3rem !important; }\n  .mt-sm-5,\n  .my-sm-5 {\n    margin-top: 3rem !important; }\n  .mr-sm-5,\n  .mx-sm-5 {\n    margin-right: 3rem !important; }\n  .mb-sm-5,\n  .my-sm-5 {\n    margin-bottom: 3rem !important; }\n  .ml-sm-5,\n  .mx-sm-5 {\n    margin-left: 3rem !important; }\n  .p-sm-0 {\n    padding: 0 !important; }\n  .pt-sm-0,\n  .py-sm-0 {\n    padding-top: 0 !important; }\n  .pr-sm-0,\n  .px-sm-0 {\n    padding-right: 0 !important; }\n  .pb-sm-0,\n  .py-sm-0 {\n    padding-bottom: 0 !important; }\n  .pl-sm-0,\n  .px-sm-0 {\n    padding-left: 0 !important; }\n  .p-sm-1 {\n    padding: 0.25rem !important; }\n  .pt-sm-1,\n  .py-sm-1 {\n    padding-top: 0.25rem !important; }\n  .pr-sm-1,\n  .px-sm-1 {\n    padding-right: 0.25rem !important; }\n  .pb-sm-1,\n  .py-sm-1 {\n    padding-bottom: 0.25rem !important; }\n  .pl-sm-1,\n  .px-sm-1 {\n    padding-left: 0.25rem !important; }\n  .p-sm-2 {\n    padding: 0.5rem !important; }\n  .pt-sm-2,\n  .py-sm-2 {\n    padding-top: 0.5rem !important; }\n  .pr-sm-2,\n  .px-sm-2 {\n    padding-right: 0.5rem !important; }\n  .pb-sm-2,\n  .py-sm-2 {\n    padding-bottom: 0.5rem !important; }\n  .pl-sm-2,\n  .px-sm-2 {\n    padding-left: 0.5rem !important; }\n  .p-sm-3 {\n    padding: 1rem !important; }\n  .pt-sm-3,\n  .py-sm-3 {\n    padding-top: 1rem !important; }\n  .pr-sm-3,\n  .px-sm-3 {\n    padding-right: 1rem !important; }\n  .pb-sm-3,\n  .py-sm-3 {\n    padding-bottom: 1rem !important; }\n  .pl-sm-3,\n  .px-sm-3 {\n    padding-left: 1rem !important; }\n  .p-sm-4 {\n    padding: 1.5rem !important; }\n  .pt-sm-4,\n  .py-sm-4 {\n    padding-top: 1.5rem !important; }\n  .pr-sm-4,\n  .px-sm-4 {\n    padding-right: 1.5rem !important; }\n  .pb-sm-4,\n  .py-sm-4 {\n    padding-bottom: 1.5rem !important; }\n  .pl-sm-4,\n  .px-sm-4 {\n    padding-left: 1.5rem !important; }\n  .p-sm-5 {\n    padding: 3rem !important; }\n  .pt-sm-5,\n  .py-sm-5 {\n    padding-top: 3rem !important; }\n  .pr-sm-5,\n  .px-sm-5 {\n    padding-right: 3rem !important; }\n  .pb-sm-5,\n  .py-sm-5 {\n    padding-bottom: 3rem !important; }\n  .pl-sm-5,\n  .px-sm-5 {\n    padding-left: 3rem !important; }\n  .m-sm-n1 {\n    margin: -0.25rem !important; }\n  .mt-sm-n1,\n  .my-sm-n1 {\n    margin-top: -0.25rem !important; }\n  .mr-sm-n1,\n  .mx-sm-n1 {\n    margin-right: -0.25rem !important; }\n  .mb-sm-n1,\n  .my-sm-n1 {\n    margin-bottom: -0.25rem !important; }\n  .ml-sm-n1,\n  .mx-sm-n1 {\n    margin-left: -0.25rem !important; }\n  .m-sm-n2 {\n    margin: -0.5rem !important; }\n  .mt-sm-n2,\n  .my-sm-n2 {\n    margin-top: -0.5rem !important; }\n  .mr-sm-n2,\n  .mx-sm-n2 {\n    margin-right: -0.5rem !important; }\n  .mb-sm-n2,\n  .my-sm-n2 {\n    margin-bottom: -0.5rem !important; }\n  .ml-sm-n2,\n  .mx-sm-n2 {\n    margin-left: -0.5rem !important; }\n  .m-sm-n3 {\n    margin: -1rem !important; }\n  .mt-sm-n3,\n  .my-sm-n3 {\n    margin-top: -1rem !important; }\n  .mr-sm-n3,\n  .mx-sm-n3 {\n    margin-right: -1rem !important; }\n  .mb-sm-n3,\n  .my-sm-n3 {\n    margin-bottom: -1rem !important; }\n  .ml-sm-n3,\n  .mx-sm-n3 {\n    margin-left: -1rem !important; }\n  .m-sm-n4 {\n    margin: -1.5rem !important; }\n  .mt-sm-n4,\n  .my-sm-n4 {\n    margin-top: -1.5rem !important; }\n  .mr-sm-n4,\n  .mx-sm-n4 {\n    margin-right: -1.5rem !important; }\n  .mb-sm-n4,\n  .my-sm-n4 {\n    margin-bottom: -1.5rem !important; }\n  .ml-sm-n4,\n  .mx-sm-n4 {\n    margin-left: -1.5rem !important; }\n  .m-sm-n5 {\n    margin: -3rem !important; }\n  .mt-sm-n5,\n  .my-sm-n5 {\n    margin-top: -3rem !important; }\n  .mr-sm-n5,\n  .mx-sm-n5 {\n    margin-right: -3rem !important; }\n  .mb-sm-n5,\n  .my-sm-n5 {\n    margin-bottom: -3rem !important; }\n  .ml-sm-n5,\n  .mx-sm-n5 {\n    margin-left: -3rem !important; }\n  .m-sm-auto {\n    margin: auto !important; }\n  .mt-sm-auto,\n  .my-sm-auto {\n    margin-top: auto !important; }\n  .mr-sm-auto,\n  .mx-sm-auto {\n    margin-right: auto !important; }\n  .mb-sm-auto,\n  .my-sm-auto {\n    margin-bottom: auto !important; }\n  .ml-sm-auto,\n  .mx-sm-auto {\n    margin-left: auto !important; } }\n\n@media (min-width: 768px) {\n  .m-md-0 {\n    margin: 0 !important; }\n  .mt-md-0,\n  .my-md-0 {\n    margin-top: 0 !important; }\n  .mr-md-0,\n  .mx-md-0 {\n    margin-right: 0 !important; }\n  .mb-md-0,\n  .my-md-0 {\n    margin-bottom: 0 !important; }\n  .ml-md-0,\n  .mx-md-0 {\n    margin-left: 0 !important; }\n  .m-md-1 {\n    margin: 0.25rem !important; }\n  .mt-md-1,\n  .my-md-1 {\n    margin-top: 0.25rem !important; }\n  .mr-md-1,\n  .mx-md-1 {\n    margin-right: 0.25rem !important; }\n  .mb-md-1,\n  .my-md-1 {\n    margin-bottom: 0.25rem !important; }\n  .ml-md-1,\n  .mx-md-1 {\n    margin-left: 0.25rem !important; }\n  .m-md-2 {\n    margin: 0.5rem !important; }\n  .mt-md-2,\n  .my-md-2 {\n    margin-top: 0.5rem !important; }\n  .mr-md-2,\n  .mx-md-2 {\n    margin-right: 0.5rem !important; }\n  .mb-md-2,\n  .my-md-2 {\n    margin-bottom: 0.5rem !important; }\n  .ml-md-2,\n  .mx-md-2 {\n    margin-left: 0.5rem !important; }\n  .m-md-3 {\n    margin: 1rem !important; }\n  .mt-md-3,\n  .my-md-3 {\n    margin-top: 1rem !important; }\n  .mr-md-3,\n  .mx-md-3 {\n    margin-right: 1rem !important; }\n  .mb-md-3,\n  .my-md-3 {\n    margin-bottom: 1rem !important; }\n  .ml-md-3,\n  .mx-md-3 {\n    margin-left: 1rem !important; }\n  .m-md-4 {\n    margin: 1.5rem !important; }\n  .mt-md-4,\n  .my-md-4 {\n    margin-top: 1.5rem !important; }\n  .mr-md-4,\n  .mx-md-4 {\n    margin-right: 1.5rem !important; }\n  .mb-md-4,\n  .my-md-4 {\n    margin-bottom: 1.5rem !important; }\n  .ml-md-4,\n  .mx-md-4 {\n    margin-left: 1.5rem !important; }\n  .m-md-5 {\n    margin: 3rem !important; }\n  .mt-md-5,\n  .my-md-5 {\n    margin-top: 3rem !important; }\n  .mr-md-5,\n  .mx-md-5 {\n    margin-right: 3rem !important; }\n  .mb-md-5,\n  .my-md-5 {\n    margin-bottom: 3rem !important; }\n  .ml-md-5,\n  .mx-md-5 {\n    margin-left: 3rem !important; }\n  .p-md-0 {\n    padding: 0 !important; }\n  .pt-md-0,\n  .py-md-0 {\n    padding-top: 0 !important; }\n  .pr-md-0,\n  .px-md-0 {\n    padding-right: 0 !important; }\n  .pb-md-0,\n  .py-md-0 {\n    padding-bottom: 0 !important; }\n  .pl-md-0,\n  .px-md-0 {\n    padding-left: 0 !important; }\n  .p-md-1 {\n    padding: 0.25rem !important; }\n  .pt-md-1,\n  .py-md-1 {\n    padding-top: 0.25rem !important; }\n  .pr-md-1,\n  .px-md-1 {\n    padding-right: 0.25rem !important; }\n  .pb-md-1,\n  .py-md-1 {\n    padding-bottom: 0.25rem !important; }\n  .pl-md-1,\n  .px-md-1 {\n    padding-left: 0.25rem !important; }\n  .p-md-2 {\n    padding: 0.5rem !important; }\n  .pt-md-2,\n  .py-md-2 {\n    padding-top: 0.5rem !important; }\n  .pr-md-2,\n  .px-md-2 {\n    padding-right: 0.5rem !important; }\n  .pb-md-2,\n  .py-md-2 {\n    padding-bottom: 0.5rem !important; }\n  .pl-md-2,\n  .px-md-2 {\n    padding-left: 0.5rem !important; }\n  .p-md-3 {\n    padding: 1rem !important; }\n  .pt-md-3,\n  .py-md-3 {\n    padding-top: 1rem !important; }\n  .pr-md-3,\n  .px-md-3 {\n    padding-right: 1rem !important; }\n  .pb-md-3,\n  .py-md-3 {\n    padding-bottom: 1rem !important; }\n  .pl-md-3,\n  .px-md-3 {\n    padding-left: 1rem !important; }\n  .p-md-4 {\n    padding: 1.5rem !important; }\n  .pt-md-4,\n  .py-md-4 {\n    padding-top: 1.5rem !important; }\n  .pr-md-4,\n  .px-md-4 {\n    padding-right: 1.5rem !important; }\n  .pb-md-4,\n  .py-md-4 {\n    padding-bottom: 1.5rem !important; }\n  .pl-md-4,\n  .px-md-4 {\n    padding-left: 1.5rem !important; }\n  .p-md-5 {\n    padding: 3rem !important; }\n  .pt-md-5,\n  .py-md-5 {\n    padding-top: 3rem !important; }\n  .pr-md-5,\n  .px-md-5 {\n    padding-right: 3rem !important; }\n  .pb-md-5,\n  .py-md-5 {\n    padding-bottom: 3rem !important; }\n  .pl-md-5,\n  .px-md-5 {\n    padding-left: 3rem !important; }\n  .m-md-n1 {\n    margin: -0.25rem !important; }\n  .mt-md-n1,\n  .my-md-n1 {\n    margin-top: -0.25rem !important; }\n  .mr-md-n1,\n  .mx-md-n1 {\n    margin-right: -0.25rem !important; }\n  .mb-md-n1,\n  .my-md-n1 {\n    margin-bottom: -0.25rem !important; }\n  .ml-md-n1,\n  .mx-md-n1 {\n    margin-left: -0.25rem !important; }\n  .m-md-n2 {\n    margin: -0.5rem !important; }\n  .mt-md-n2,\n  .my-md-n2 {\n    margin-top: -0.5rem !important; }\n  .mr-md-n2,\n  .mx-md-n2 {\n    margin-right: -0.5rem !important; }\n  .mb-md-n2,\n  .my-md-n2 {\n    margin-bottom: -0.5rem !important; }\n  .ml-md-n2,\n  .mx-md-n2 {\n    margin-left: -0.5rem !important; }\n  .m-md-n3 {\n    margin: -1rem !important; }\n  .mt-md-n3,\n  .my-md-n3 {\n    margin-top: -1rem !important; }\n  .mr-md-n3,\n  .mx-md-n3 {\n    margin-right: -1rem !important; }\n  .mb-md-n3,\n  .my-md-n3 {\n    margin-bottom: -1rem !important; }\n  .ml-md-n3,\n  .mx-md-n3 {\n    margin-left: -1rem !important; }\n  .m-md-n4 {\n    margin: -1.5rem !important; }\n  .mt-md-n4,\n  .my-md-n4 {\n    margin-top: -1.5rem !important; }\n  .mr-md-n4,\n  .mx-md-n4 {\n    margin-right: -1.5rem !important; }\n  .mb-md-n4,\n  .my-md-n4 {\n    margin-bottom: -1.5rem !important; }\n  .ml-md-n4,\n  .mx-md-n4 {\n    margin-left: -1.5rem !important; }\n  .m-md-n5 {\n    margin: -3rem !important; }\n  .mt-md-n5,\n  .my-md-n5 {\n    margin-top: -3rem !important; }\n  .mr-md-n5,\n  .mx-md-n5 {\n    margin-right: -3rem !important; }\n  .mb-md-n5,\n  .my-md-n5 {\n    margin-bottom: -3rem !important; }\n  .ml-md-n5,\n  .mx-md-n5 {\n    margin-left: -3rem !important; }\n  .m-md-auto {\n    margin: auto !important; }\n  .mt-md-auto,\n  .my-md-auto {\n    margin-top: auto !important; }\n  .mr-md-auto,\n  .mx-md-auto {\n    margin-right: auto !important; }\n  .mb-md-auto,\n  .my-md-auto {\n    margin-bottom: auto !important; }\n  .ml-md-auto,\n  .mx-md-auto {\n    margin-left: auto !important; } }\n\n@media (min-width: 992px) {\n  .m-lg-0 {\n    margin: 0 !important; }\n  .mt-lg-0,\n  .my-lg-0 {\n    margin-top: 0 !important; }\n  .mr-lg-0,\n  .mx-lg-0 {\n    margin-right: 0 !important; }\n  .mb-lg-0,\n  .my-lg-0 {\n    margin-bottom: 0 !important; }\n  .ml-lg-0,\n  .mx-lg-0 {\n    margin-left: 0 !important; }\n  .m-lg-1 {\n    margin: 0.25rem !important; }\n  .mt-lg-1,\n  .my-lg-1 {\n    margin-top: 0.25rem !important; }\n  .mr-lg-1,\n  .mx-lg-1 {\n    margin-right: 0.25rem !important; }\n  .mb-lg-1,\n  .my-lg-1 {\n    margin-bottom: 0.25rem !important; }\n  .ml-lg-1,\n  .mx-lg-1 {\n    margin-left: 0.25rem !important; }\n  .m-lg-2 {\n    margin: 0.5rem !important; }\n  .mt-lg-2,\n  .my-lg-2 {\n    margin-top: 0.5rem !important; }\n  .mr-lg-2,\n  .mx-lg-2 {\n    margin-right: 0.5rem !important; }\n  .mb-lg-2,\n  .my-lg-2 {\n    margin-bottom: 0.5rem !important; }\n  .ml-lg-2,\n  .mx-lg-2 {\n    margin-left: 0.5rem !important; }\n  .m-lg-3 {\n    margin: 1rem !important; }\n  .mt-lg-3,\n  .my-lg-3 {\n    margin-top: 1rem !important; }\n  .mr-lg-3,\n  .mx-lg-3 {\n    margin-right: 1rem !important; }\n  .mb-lg-3,\n  .my-lg-3 {\n    margin-bottom: 1rem !important; }\n  .ml-lg-3,\n  .mx-lg-3 {\n    margin-left: 1rem !important; }\n  .m-lg-4 {\n    margin: 1.5rem !important; }\n  .mt-lg-4,\n  .my-lg-4 {\n    margin-top: 1.5rem !important; }\n  .mr-lg-4,\n  .mx-lg-4 {\n    margin-right: 1.5rem !important; }\n  .mb-lg-4,\n  .my-lg-4 {\n    margin-bottom: 1.5rem !important; }\n  .ml-lg-4,\n  .mx-lg-4 {\n    margin-left: 1.5rem !important; }\n  .m-lg-5 {\n    margin: 3rem !important; }\n  .mt-lg-5,\n  .my-lg-5 {\n    margin-top: 3rem !important; }\n  .mr-lg-5,\n  .mx-lg-5 {\n    margin-right: 3rem !important; }\n  .mb-lg-5,\n  .my-lg-5 {\n    margin-bottom: 3rem !important; }\n  .ml-lg-5,\n  .mx-lg-5 {\n    margin-left: 3rem !important; }\n  .p-lg-0 {\n    padding: 0 !important; }\n  .pt-lg-0,\n  .py-lg-0 {\n    padding-top: 0 !important; }\n  .pr-lg-0,\n  .px-lg-0 {\n    padding-right: 0 !important; }\n  .pb-lg-0,\n  .py-lg-0 {\n    padding-bottom: 0 !important; }\n  .pl-lg-0,\n  .px-lg-0 {\n    padding-left: 0 !important; }\n  .p-lg-1 {\n    padding: 0.25rem !important; }\n  .pt-lg-1,\n  .py-lg-1 {\n    padding-top: 0.25rem !important; }\n  .pr-lg-1,\n  .px-lg-1 {\n    padding-right: 0.25rem !important; }\n  .pb-lg-1,\n  .py-lg-1 {\n    padding-bottom: 0.25rem !important; }\n  .pl-lg-1,\n  .px-lg-1 {\n    padding-left: 0.25rem !important; }\n  .p-lg-2 {\n    padding: 0.5rem !important; }\n  .pt-lg-2,\n  .py-lg-2 {\n    padding-top: 0.5rem !important; }\n  .pr-lg-2,\n  .px-lg-2 {\n    padding-right: 0.5rem !important; }\n  .pb-lg-2,\n  .py-lg-2 {\n    padding-bottom: 0.5rem !important; }\n  .pl-lg-2,\n  .px-lg-2 {\n    padding-left: 0.5rem !important; }\n  .p-lg-3 {\n    padding: 1rem !important; }\n  .pt-lg-3,\n  .py-lg-3 {\n    padding-top: 1rem !important; }\n  .pr-lg-3,\n  .px-lg-3 {\n    padding-right: 1rem !important; }\n  .pb-lg-3,\n  .py-lg-3 {\n    padding-bottom: 1rem !important; }\n  .pl-lg-3,\n  .px-lg-3 {\n    padding-left: 1rem !important; }\n  .p-lg-4 {\n    padding: 1.5rem !important; }\n  .pt-lg-4,\n  .py-lg-4 {\n    padding-top: 1.5rem !important; }\n  .pr-lg-4,\n  .px-lg-4 {\n    padding-right: 1.5rem !important; }\n  .pb-lg-4,\n  .py-lg-4 {\n    padding-bottom: 1.5rem !important; }\n  .pl-lg-4,\n  .px-lg-4 {\n    padding-left: 1.5rem !important; }\n  .p-lg-5 {\n    padding: 3rem !important; }\n  .pt-lg-5,\n  .py-lg-5 {\n    padding-top: 3rem !important; }\n  .pr-lg-5,\n  .px-lg-5 {\n    padding-right: 3rem !important; }\n  .pb-lg-5,\n  .py-lg-5 {\n    padding-bottom: 3rem !important; }\n  .pl-lg-5,\n  .px-lg-5 {\n    padding-left: 3rem !important; }\n  .m-lg-n1 {\n    margin: -0.25rem !important; }\n  .mt-lg-n1,\n  .my-lg-n1 {\n    margin-top: -0.25rem !important; }\n  .mr-lg-n1,\n  .mx-lg-n1 {\n    margin-right: -0.25rem !important; }\n  .mb-lg-n1,\n  .my-lg-n1 {\n    margin-bottom: -0.25rem !important; }\n  .ml-lg-n1,\n  .mx-lg-n1 {\n    margin-left: -0.25rem !important; }\n  .m-lg-n2 {\n    margin: -0.5rem !important; }\n  .mt-lg-n2,\n  .my-lg-n2 {\n    margin-top: -0.5rem !important; }\n  .mr-lg-n2,\n  .mx-lg-n2 {\n    margin-right: -0.5rem !important; }\n  .mb-lg-n2,\n  .my-lg-n2 {\n    margin-bottom: -0.5rem !important; }\n  .ml-lg-n2,\n  .mx-lg-n2 {\n    margin-left: -0.5rem !important; }\n  .m-lg-n3 {\n    margin: -1rem !important; }\n  .mt-lg-n3,\n  .my-lg-n3 {\n    margin-top: -1rem !important; }\n  .mr-lg-n3,\n  .mx-lg-n3 {\n    margin-right: -1rem !important; }\n  .mb-lg-n3,\n  .my-lg-n3 {\n    margin-bottom: -1rem !important; }\n  .ml-lg-n3,\n  .mx-lg-n3 {\n    margin-left: -1rem !important; }\n  .m-lg-n4 {\n    margin: -1.5rem !important; }\n  .mt-lg-n4,\n  .my-lg-n4 {\n    margin-top: -1.5rem !important; }\n  .mr-lg-n4,\n  .mx-lg-n4 {\n    margin-right: -1.5rem !important; }\n  .mb-lg-n4,\n  .my-lg-n4 {\n    margin-bottom: -1.5rem !important; }\n  .ml-lg-n4,\n  .mx-lg-n4 {\n    margin-left: -1.5rem !important; }\n  .m-lg-n5 {\n    margin: -3rem !important; }\n  .mt-lg-n5,\n  .my-lg-n5 {\n    margin-top: -3rem !important; }\n  .mr-lg-n5,\n  .mx-lg-n5 {\n    margin-right: -3rem !important; }\n  .mb-lg-n5,\n  .my-lg-n5 {\n    margin-bottom: -3rem !important; }\n  .ml-lg-n5,\n  .mx-lg-n5 {\n    margin-left: -3rem !important; }\n  .m-lg-auto {\n    margin: auto !important; }\n  .mt-lg-auto,\n  .my-lg-auto {\n    margin-top: auto !important; }\n  .mr-lg-auto,\n  .mx-lg-auto {\n    margin-right: auto !important; }\n  .mb-lg-auto,\n  .my-lg-auto {\n    margin-bottom: auto !important; }\n  .ml-lg-auto,\n  .mx-lg-auto {\n    margin-left: auto !important; } }\n\n@media (min-width: 1200px) {\n  .m-xl-0 {\n    margin: 0 !important; }\n  .mt-xl-0,\n  .my-xl-0 {\n    margin-top: 0 !important; }\n  .mr-xl-0,\n  .mx-xl-0 {\n    margin-right: 0 !important; }\n  .mb-xl-0,\n  .my-xl-0 {\n    margin-bottom: 0 !important; }\n  .ml-xl-0,\n  .mx-xl-0 {\n    margin-left: 0 !important; }\n  .m-xl-1 {\n    margin: 0.25rem !important; }\n  .mt-xl-1,\n  .my-xl-1 {\n    margin-top: 0.25rem !important; }\n  .mr-xl-1,\n  .mx-xl-1 {\n    margin-right: 0.25rem !important; }\n  .mb-xl-1,\n  .my-xl-1 {\n    margin-bottom: 0.25rem !important; }\n  .ml-xl-1,\n  .mx-xl-1 {\n    margin-left: 0.25rem !important; }\n  .m-xl-2 {\n    margin: 0.5rem !important; }\n  .mt-xl-2,\n  .my-xl-2 {\n    margin-top: 0.5rem !important; }\n  .mr-xl-2,\n  .mx-xl-2 {\n    margin-right: 0.5rem !important; }\n  .mb-xl-2,\n  .my-xl-2 {\n    margin-bottom: 0.5rem !important; }\n  .ml-xl-2,\n  .mx-xl-2 {\n    margin-left: 0.5rem !important; }\n  .m-xl-3 {\n    margin: 1rem !important; }\n  .mt-xl-3,\n  .my-xl-3 {\n    margin-top: 1rem !important; }\n  .mr-xl-3,\n  .mx-xl-3 {\n    margin-right: 1rem !important; }\n  .mb-xl-3,\n  .my-xl-3 {\n    margin-bottom: 1rem !important; }\n  .ml-xl-3,\n  .mx-xl-3 {\n    margin-left: 1rem !important; }\n  .m-xl-4 {\n    margin: 1.5rem !important; }\n  .mt-xl-4,\n  .my-xl-4 {\n    margin-top: 1.5rem !important; }\n  .mr-xl-4,\n  .mx-xl-4 {\n    margin-right: 1.5rem !important; }\n  .mb-xl-4,\n  .my-xl-4 {\n    margin-bottom: 1.5rem !important; }\n  .ml-xl-4,\n  .mx-xl-4 {\n    margin-left: 1.5rem !important; }\n  .m-xl-5 {\n    margin: 3rem !important; }\n  .mt-xl-5,\n  .my-xl-5 {\n    margin-top: 3rem !important; }\n  .mr-xl-5,\n  .mx-xl-5 {\n    margin-right: 3rem !important; }\n  .mb-xl-5,\n  .my-xl-5 {\n    margin-bottom: 3rem !important; }\n  .ml-xl-5,\n  .mx-xl-5 {\n    margin-left: 3rem !important; }\n  .p-xl-0 {\n    padding: 0 !important; }\n  .pt-xl-0,\n  .py-xl-0 {\n    padding-top: 0 !important; }\n  .pr-xl-0,\n  .px-xl-0 {\n    padding-right: 0 !important; }\n  .pb-xl-0,\n  .py-xl-0 {\n    padding-bottom: 0 !important; }\n  .pl-xl-0,\n  .px-xl-0 {\n    padding-left: 0 !important; }\n  .p-xl-1 {\n    padding: 0.25rem !important; }\n  .pt-xl-1,\n  .py-xl-1 {\n    padding-top: 0.25rem !important; }\n  .pr-xl-1,\n  .px-xl-1 {\n    padding-right: 0.25rem !important; }\n  .pb-xl-1,\n  .py-xl-1 {\n    padding-bottom: 0.25rem !important; }\n  .pl-xl-1,\n  .px-xl-1 {\n    padding-left: 0.25rem !important; }\n  .p-xl-2 {\n    padding: 0.5rem !important; }\n  .pt-xl-2,\n  .py-xl-2 {\n    padding-top: 0.5rem !important; }\n  .pr-xl-2,\n  .px-xl-2 {\n    padding-right: 0.5rem !important; }\n  .pb-xl-2,\n  .py-xl-2 {\n    padding-bottom: 0.5rem !important; }\n  .pl-xl-2,\n  .px-xl-2 {\n    padding-left: 0.5rem !important; }\n  .p-xl-3 {\n    padding: 1rem !important; }\n  .pt-xl-3,\n  .py-xl-3 {\n    padding-top: 1rem !important; }\n  .pr-xl-3,\n  .px-xl-3 {\n    padding-right: 1rem !important; }\n  .pb-xl-3,\n  .py-xl-3 {\n    padding-bottom: 1rem !important; }\n  .pl-xl-3,\n  .px-xl-3 {\n    padding-left: 1rem !important; }\n  .p-xl-4 {\n    padding: 1.5rem !important; }\n  .pt-xl-4,\n  .py-xl-4 {\n    padding-top: 1.5rem !important; }\n  .pr-xl-4,\n  .px-xl-4 {\n    padding-right: 1.5rem !important; }\n  .pb-xl-4,\n  .py-xl-4 {\n    padding-bottom: 1.5rem !important; }\n  .pl-xl-4,\n  .px-xl-4 {\n    padding-left: 1.5rem !important; }\n  .p-xl-5 {\n    padding: 3rem !important; }\n  .pt-xl-5,\n  .py-xl-5 {\n    padding-top: 3rem !important; }\n  .pr-xl-5,\n  .px-xl-5 {\n    padding-right: 3rem !important; }\n  .pb-xl-5,\n  .py-xl-5 {\n    padding-bottom: 3rem !important; }\n  .pl-xl-5,\n  .px-xl-5 {\n    padding-left: 3rem !important; }\n  .m-xl-n1 {\n    margin: -0.25rem !important; }\n  .mt-xl-n1,\n  .my-xl-n1 {\n    margin-top: -0.25rem !important; }\n  .mr-xl-n1,\n  .mx-xl-n1 {\n    margin-right: -0.25rem !important; }\n  .mb-xl-n1,\n  .my-xl-n1 {\n    margin-bottom: -0.25rem !important; }\n  .ml-xl-n1,\n  .mx-xl-n1 {\n    margin-left: -0.25rem !important; }\n  .m-xl-n2 {\n    margin: -0.5rem !important; }\n  .mt-xl-n2,\n  .my-xl-n2 {\n    margin-top: -0.5rem !important; }\n  .mr-xl-n2,\n  .mx-xl-n2 {\n    margin-right: -0.5rem !important; }\n  .mb-xl-n2,\n  .my-xl-n2 {\n    margin-bottom: -0.5rem !important; }\n  .ml-xl-n2,\n  .mx-xl-n2 {\n    margin-left: -0.5rem !important; }\n  .m-xl-n3 {\n    margin: -1rem !important; }\n  .mt-xl-n3,\n  .my-xl-n3 {\n    margin-top: -1rem !important; }\n  .mr-xl-n3,\n  .mx-xl-n3 {\n    margin-right: -1rem !important; }\n  .mb-xl-n3,\n  .my-xl-n3 {\n    margin-bottom: -1rem !important; }\n  .ml-xl-n3,\n  .mx-xl-n3 {\n    margin-left: -1rem !important; }\n  .m-xl-n4 {\n    margin: -1.5rem !important; }\n  .mt-xl-n4,\n  .my-xl-n4 {\n    margin-top: -1.5rem !important; }\n  .mr-xl-n4,\n  .mx-xl-n4 {\n    margin-right: -1.5rem !important; }\n  .mb-xl-n4,\n  .my-xl-n4 {\n    margin-bottom: -1.5rem !important; }\n  .ml-xl-n4,\n  .mx-xl-n4 {\n    margin-left: -1.5rem !important; }\n  .m-xl-n5 {\n    margin: -3rem !important; }\n  .mt-xl-n5,\n  .my-xl-n5 {\n    margin-top: -3rem !important; }\n  .mr-xl-n5,\n  .mx-xl-n5 {\n    margin-right: -3rem !important; }\n  .mb-xl-n5,\n  .my-xl-n5 {\n    margin-bottom: -3rem !important; }\n  .ml-xl-n5,\n  .mx-xl-n5 {\n    margin-left: -3rem !important; }\n  .m-xl-auto {\n    margin: auto !important; }\n  .mt-xl-auto,\n  .my-xl-auto {\n    margin-top: auto !important; }\n  .mr-xl-auto,\n  .mx-xl-auto {\n    margin-right: auto !important; }\n  .mb-xl-auto,\n  .my-xl-auto {\n    margin-bottom: auto !important; }\n  .ml-xl-auto,\n  .mx-xl-auto {\n    margin-left: auto !important; } }\n\n.text-monospace {\n  font-family: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !important; }\n\n.text-justify {\n  text-align: justify !important; }\n\n.text-wrap {\n  white-space: normal !important; }\n\n.text-nowrap {\n  white-space: nowrap !important; }\n\n.text-truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap; }\n\n.text-left {\n  text-align: left !important; }\n\n.text-right {\n  text-align: right !important; }\n\n.text-center {\n  text-align: center !important; }\n\n@media (min-width: 576px) {\n  .text-sm-left {\n    text-align: left !important; }\n  .text-sm-right {\n    text-align: right !important; }\n  .text-sm-center {\n    text-align: center !important; } }\n\n@media (min-width: 768px) {\n  .text-md-left {\n    text-align: left !important; }\n  .text-md-right {\n    text-align: right !important; }\n  .text-md-center {\n    text-align: center !important; } }\n\n@media (min-width: 992px) {\n  .text-lg-left {\n    text-align: left !important; }\n  .text-lg-right {\n    text-align: right !important; }\n  .text-lg-center {\n    text-align: center !important; } }\n\n@media (min-width: 1200px) {\n  .text-xl-left {\n    text-align: left !important; }\n  .text-xl-right {\n    text-align: right !important; }\n  .text-xl-center {\n    text-align: center !important; } }\n\n.text-lowercase {\n  text-transform: lowercase !important; }\n\n.text-uppercase {\n  text-transform: uppercase !important; }\n\n.text-capitalize {\n  text-transform: capitalize !important; }\n\n.font-weight-light {\n  font-weight: 300 !important; }\n\n.font-weight-lighter {\n  font-weight: lighter !important; }\n\n.font-weight-normal {\n  font-weight: 400 !important; }\n\n.font-weight-bold {\n  font-weight: 700 !important; }\n\n.font-weight-bolder {\n  font-weight: bolder !important; }\n\n.font-italic {\n  font-style: italic !important; }\n\n.text-white {\n  color: #fff !important; }\n\n.text-primary {\n  color: #437ec4 !important; }\n\na.text-primary:hover, a.text-primary:focus {\n  color: #2d598e !important; }\n\n.text-secondary {\n  color: #54687A !important; }\n\na.text-secondary:hover, a.text-secondary:focus {\n  color: #35414d !important; }\n\n.text-success {\n  color: #25b877 !important; }\n\na.text-success:hover, a.text-success:focus {\n  color: #18784e !important; }\n\n.text-info {\n  color: #17a2b8 !important; }\n\na.text-info:hover, a.text-info:focus {\n  color: #0f6674 !important; }\n\n.text-warning {\n  color: #eeb128 !important; }\n\na.text-warning:hover, a.text-warning:focus {\n  color: #bb860f !important; }\n\n.text-danger {\n  color: #c04949 !important; }\n\na.text-danger:hover, a.text-danger:focus {\n  color: #8c3030 !important; }\n\n.text-light {\n  color: #eff0f4 !important; }\n\na.text-light:hover, a.text-light:focus {\n  color: #c2c6d5 !important; }\n\n.text-dark {\n  color: #595959 !important; }\n\na.text-dark:hover, a.text-dark:focus {\n  color: #333333 !important; }\n\n.text-body {\n  color: #212529 !important; }\n\n.text-muted {\n  color: #6c757d !important; }\n\n.text-black-50 {\n  color: rgba(0, 0, 0, 0.5) !important; }\n\n.text-white-50 {\n  color: rgba(255, 255, 255, 0.5) !important; }\n\n.text-hide {\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0; }\n\n.text-decoration-none {\n  text-decoration: none !important; }\n\n.text-break {\n  word-break: break-word !important;\n  overflow-wrap: break-word !important; }\n\n.text-reset {\n  color: inherit !important; }\n\n.visible {\n  visibility: visible !important; }\n\n.invisible {\n  visibility: hidden !important; }\n\n@media print {\n  *,\n  *::before,\n  *::after {\n    text-shadow: none !important;\n    box-shadow: none !important; }\n  a:not(.btn) {\n    text-decoration: underline; }\n  abbr[title]::after {\n    content: \" (\" attr(title) \")\"; }\n  pre {\n    white-space: pre-wrap !important; }\n  pre,\n  blockquote {\n    border: 1px solid #adb5bd;\n    page-break-inside: avoid; }\n  thead {\n    display: table-header-group; }\n  tr,\n  img {\n    page-break-inside: avoid; }\n  p,\n  h2,\n  h3 {\n    orphans: 3;\n    widows: 3; }\n  h2,\n  h3 {\n    page-break-after: avoid; }\n  @page {\n    size: a3; }\n  body {\n    min-width: 992px !important; }\n  .container {\n    min-width: 992px !important; }\n  .navbar {\n    display: none; }\n  .badge {\n    border: 1px solid #000; }\n  .table {\n    border-collapse: collapse !important; }\n    .table td,\n    .table th {\n      background-color: #fff !important; }\n  .table-bordered th,\n  .table-bordered td {\n    border: 1px solid #dee2e6 !important; }\n  .table-dark {\n    color: inherit; }\n    .table-dark th,\n    .table-dark td,\n    .table-dark thead th,\n    .table-dark tbody + tbody {\n      border-color: #ccc; }\n  .table .thead-dark th {\n    color: inherit;\n    border-color: #ccc; } }\n\n/*\nBootstrap\n\nスタイルガイドで使用される一部クラスは [Bootstrap](https://getbootstrap.com/) を使用しています。\nBootstrap で利用可能なクラス群については、 [Bootstrap 公式サイト](https://getbootstrap.com/) をご利用ください。\n\nBootstrapを用いて変更を加えたEC-CUBE管理画面専用のオリジナルクラスを以下にまとめています。\n\nStyleguide 12.0\n*/\n/*\nテーブル\n\n `table-ec-lightGray`：背景色を#f2f2f2に指定します。thに使用しています。\n\nMarkup:\n<table class=\"table table-striped table-bordered\">\n  <tbody>\n    <tr>\n      <th class=\"w-25 align-middle table-ec-lightGray\">カテゴリID</th>\n      <td class=\"align-middle\">新規登録時は未設定<br/>既存商品の更新は商品IDを設定</td>\n    </tr>\n    <tr>\n      <th class=\"w-25 align-middle table-ec-lightGray\">カテゴリ名<span class=\"badge badge-primary ml-1\">必須</span></th>\n      <td class=\"align-middle\"></td>\n    </tr>\n    <tr>\n      <th class=\"w-25 align-middle table-ec-lightGray\">親カテゴリID</th>\n      <td class=\"align-middle\"></td>\n    </tr>\n  </tbody>\n</table>\n\nStyleguide 12.1\n*/\n.table-ec-lightGray {\n  background-color: #f2f2f2; }\n\n/*\nボタン\n\nEC-CUBE管理画面専用のオリジナルbuttonクラスを定義しています。`.btn`とセットでの使用を前提としています。<br>\n<br>\n<br>\n【コンバージョン】 `.btn-ec-conversion`<br>\nテキスト…#FFFFFF<br>\n背景…#437EC4 → #2963AB (hover)<br>\n<br>\n【デリート】 `.btn-ec-delete`<br>\nテキスト…#FFFFFF<br>\n背景…#C04949 → #A62E2E (hover)<br>\n<br>\n【レギュラー】 `.btn-ec-regular`<br>\nテキスト…#595959 → #262626 (hover)<br>\n背景…#FFFFFF → #F2F2F2 (hover)<br>\nボーダー…#595959 → #262626 (hover)<br>\n<br>\n【サブ】 `.btn-ec-sub`<br>\nテキスト…#FFFFFF<br>\n背景…#F5F6F8 → #D6D9E0(hover)<br>\n<br>\n【アイコン】 `.btn-ec-actionIcon`<br>\nテキスト…#54687A<br>\n背景…#FFFFFF → #54687A (hover)<br>\n\n\nMarkup:\n<!-- `style=\"margin-right: 5px;\"` はスタイルガイドにおけるサンプル表示の都合上付与しています。-->\n<a href=\"\" style=\"margin-right: 5px;\" class=\"btn btn-ec-conversion\">コンバージョン</a>\n<a href=\"\" style=\"margin-right: 5px;\" class=\"btn btn-ec-delete\">デリート</a>\n<a href=\"\" style=\"margin-right: 5px;\" class=\"btn btn-ec-regular\">レギュラー</a>\n<a href=\"\" style=\"margin-right: 5px;\" class=\"btn btn-ec-sub\">サブ</a>\n<a class=\"btn btn-ec-actionIcon\"><i class=\"fa fa-close fa-lg text-secondary\" aria-hidden=\"true\"></i></a>\n\nStyleguide 12.2\n*/\n.btn-ec-conversion {\n  color: #fff;\n  background-color: #437EC4;\n  border-color: transparent;\n  cursor: pointer; }\n  .btn-ec-conversion:hover {\n    color: #fff;\n    background-color: #2963AB;\n    border-color: transparent; }\n  .btn-ec-conversion:focus, .btn-ec-conversion.focus {\n    color: #fff;\n    background-color: #2963AB;\n    border-color: transparent;\n    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }\n  .btn-ec-conversion.disabled, .btn-ec-conversion:disabled {\n    color: #fff;\n    background-color: #437EC4;\n    border-color: transparent; }\n  .btn-ec-conversion:not(:disabled):not(.disabled):active, .btn-ec-conversion:not(:disabled):not(.disabled).active,\n  .show > .btn-ec-conversion.dropdown-toggle {\n    color: #fff;\n    background-color: #2963AB;\n    border-color: transparent; }\n    .btn-ec-conversion:not(:disabled):not(.disabled):active:focus, .btn-ec-conversion:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-ec-conversion.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }\n\n.btn-ec-delete {\n  color: #fff;\n  background-color: #C04949;\n  border-color: transparent;\n  cursor: pointer; }\n  .btn-ec-delete:hover {\n    color: #fff;\n    background-color: #A62E2E;\n    border-color: transparent; }\n  .btn-ec-delete:focus, .btn-ec-delete.focus {\n    color: #fff;\n    background-color: #A62E2E;\n    border-color: transparent;\n    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }\n  .btn-ec-delete.disabled, .btn-ec-delete:disabled {\n    color: #fff;\n    background-color: #C04949;\n    border-color: transparent; }\n  .btn-ec-delete:not(:disabled):not(.disabled):active, .btn-ec-delete:not(:disabled):not(.disabled).active,\n  .show > .btn-ec-delete.dropdown-toggle {\n    color: #fff;\n    background-color: #A62E2E;\n    border-color: transparent; }\n    .btn-ec-delete:not(:disabled):not(.disabled):active:focus, .btn-ec-delete:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-ec-delete.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }\n\n.btn-ec-regular {\n  color: #212529;\n  background-color: #FFFFFF;\n  border-color: #595959;\n  color: #595959;\n  cursor: pointer; }\n  .btn-ec-regular:hover {\n    color: #212529;\n    background-color: #F2F2F2;\n    border-color: #262626; }\n  .btn-ec-regular:focus, .btn-ec-regular.focus {\n    color: #212529;\n    background-color: #F2F2F2;\n    border-color: #262626;\n    box-shadow: 0 0 0 0.2rem rgba(81, 81, 82, 0.5); }\n  .btn-ec-regular.disabled, .btn-ec-regular:disabled {\n    color: #212529;\n    background-color: #FFFFFF;\n    border-color: #595959; }\n  .btn-ec-regular:not(:disabled):not(.disabled):active, .btn-ec-regular:not(:disabled):not(.disabled).active,\n  .show > .btn-ec-regular.dropdown-toggle {\n    color: #212529;\n    background-color: #F2F2F2;\n    border-color: #262626; }\n    .btn-ec-regular:not(:disabled):not(.disabled):active:focus, .btn-ec-regular:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-ec-regular.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(81, 81, 82, 0.5); }\n\n.btn-ec-regular:hover {\n  color: #262626; }\n\n.btn-ec-sub {\n  color: #212529;\n  background-color: #F5F6F8;\n  border-color: transparent;\n  color: #262626;\n  cursor: pointer; }\n  .btn-ec-sub:hover {\n    color: #212529;\n    background-color: #D6D9E0;\n    border-color: transparent; }\n  .btn-ec-sub:focus, .btn-ec-sub.focus {\n    color: #212529;\n    background-color: #D6D9E0;\n    border-color: transparent;\n    box-shadow: 0 0 0 0.2rem rgba(33, 37, 41, 0.5); }\n  .btn-ec-sub.disabled, .btn-ec-sub:disabled {\n    color: #212529;\n    background-color: #F5F6F8;\n    border-color: transparent; }\n  .btn-ec-sub:not(:disabled):not(.disabled):active, .btn-ec-sub:not(:disabled):not(.disabled).active,\n  .show > .btn-ec-sub.dropdown-toggle {\n    color: #212529;\n    background-color: #D6D9E0;\n    border-color: transparent; }\n    .btn-ec-sub:not(:disabled):not(.disabled):active:focus, .btn-ec-sub:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-ec-sub.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(33, 37, 41, 0.5); }\n\n.btn-ec-sub:hover {\n  color: #262626; }\n\n.btn-ec-actionIcon {\n  color: #fff;\n  background-color: transparent;\n  border-color: transparent;\n  padding: 6px 12px;\n  cursor: pointer; }\n  .btn-ec-actionIcon:hover {\n    color: #212529;\n    background-color: #EFF0F4;\n    border-color: transparent; }\n  .btn-ec-actionIcon:focus, .btn-ec-actionIcon.focus {\n    color: #212529;\n    background-color: #EFF0F4;\n    border-color: transparent;\n    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }\n  .btn-ec-actionIcon.disabled, .btn-ec-actionIcon:disabled {\n    color: #fff;\n    background-color: transparent;\n    border-color: transparent; }\n  .btn-ec-actionIcon:not(:disabled):not(.disabled):active, .btn-ec-actionIcon:not(:disabled):not(.disabled).active,\n  .show > .btn-ec-actionIcon.dropdown-toggle {\n    color: #212529;\n    background-color: #EFF0F4;\n    border-color: transparent; }\n    .btn-ec-actionIcon:not(:disabled):not(.disabled):active:focus, .btn-ec-actionIcon:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-ec-actionIcon.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }\n\n.btn-ec-actionIcon:hover {\n  color: #54687A; }\n\n/*\nボタングループ（タブ）\n\nEC-CUBE管理画面専用のオリジナルbutton-groupクラスを定義しています。ページの切り替えに用います。<br>\n<br>\n【タブ】 `.btn-ec-tab`<br>\nテキスト…#595959 → #262626 (hover) → #FFFFFF(active)<br>\n背景…#FFFFFF → #F2F2F2 (hover) → #595959(active)<br>\nボーダー…#595959 → #262626 (hover)<br>\n<br>\n\nMarkup:\n<div class=\"btn-group nav d-inline-flex\" id=\"pills-tab\" role=\"tablist\">\n  <a class=\"nav-link active btn btn-ec-tab py-2 pl-4 pr-4\" id=\"pills-pc-tab\" data-toggle=\"pill\" href=\"#pills-pc\" role=\"tab\" aria-controls=\"pills-pc\" aria-selected=\"true\">PC</a>\n  <a class=\"nav-link btn btn-ec-tab py-2 pl-4 pr-4\" id=\"pills-mobile-tab\" data-toggle=\"pill\" href=\"#pills-mobile\" role=\"tab\" aria-controls=\"pills-mobile\" aria-selected=\"false\">モバイル</a></div>\n\nStyleguide 12.3\n*/\n.btn-ec-tab {\n  color: #212529;\n  background-color: #FFFFFF;\n  border-color: #595959;\n  color: #595959;\n  cursor: pointer; }\n  .btn-ec-tab:hover {\n    color: #212529;\n    background-color: #F2F2F2;\n    border-color: #262626; }\n  .btn-ec-tab:focus, .btn-ec-tab.focus {\n    color: #212529;\n    background-color: #F2F2F2;\n    border-color: #262626;\n    box-shadow: 0 0 0 0.2rem rgba(81, 81, 82, 0.5); }\n  .btn-ec-tab.disabled, .btn-ec-tab:disabled {\n    color: #212529;\n    background-color: #FFFFFF;\n    border-color: #595959; }\n  .btn-ec-tab:not(:disabled):not(.disabled):active, .btn-ec-tab:not(:disabled):not(.disabled).active,\n  .show > .btn-ec-tab.dropdown-toggle {\n    color: #212529;\n    background-color: #F2F2F2;\n    border-color: #262626; }\n    .btn-ec-tab:not(:disabled):not(.disabled):active:focus, .btn-ec-tab:not(:disabled):not(.disabled).active:focus,\n    .show > .btn-ec-tab.dropdown-toggle:focus {\n      box-shadow: 0 0 0 0.2rem rgba(81, 81, 82, 0.5); }\n\n.btn-ec-tab:hover {\n  color: #262626; }\n\n.btn-ec-tab.active {\n  background-color: #595959 !important;\n  color: #fff !important;\n  box-shadow: none !important; }\n\n/*\nバッジ\n\nEC-CUBE管理画面専用のオリジナルbadgeクラスを定義しています。`.badge`とセットでの使用を前提としています。\n<br>\n `badge-ec-blue`   :#437EC4;<br>\n `badge-ec-green`  :#25B877;<br>\n `badge-ec-red`    :#C04949;<br>\n `badge-ec-yellow` :#EEB128;<br>\n<br>\n\nMarkup:\n<!-- `style=\"margin-right: 5px;\"` はスタイルガイドにおけるサンプル表示の都合上付与しています。-->\n<span style=\"margin-right: 5px;\" class=\"badge badge-ec-blue\">新規受付</span>\n<span style=\"margin-right: 5px;\" class=\"badge badge-ec-green\">入金済み</span>\n<span style=\"margin-right: 5px;\" class=\"badge badge-ec-red\">入金待ち</span>\n<span style=\"margin-right: 5px;\" class=\"badge badge-ec-yellow\">処理中</span>\n<span class=\"badge badge-ec-glay\">キャンセル</span>\n\nStyleguide 12.4\n*/\n.badge-ec-blue {\n  color: #212529;\n  background-color: #fff;\n  display: inline-block;\n  border: 1px solid #437EC4;\n  color: #437EC4;\n  padding: .5rem 0.75rem;\n  font-size: 14px;\n  font-weight: normal; }\n  a.badge-ec-blue:hover, a.badge-ec-blue:focus {\n    color: #212529;\n    background-color: #e6e6e6; }\n  a.badge-ec-blue:focus, a.badge-ec-blue.focus {\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }\n\n.badge-ec-green {\n  color: #212529;\n  background-color: #fff;\n  display: inline-block;\n  border: 1px solid #25B877;\n  color: #25B877;\n  padding: .5rem 0.75rem;\n  font-size: 14px;\n  font-weight: normal; }\n  a.badge-ec-green:hover, a.badge-ec-green:focus {\n    color: #212529;\n    background-color: #e6e6e6; }\n  a.badge-ec-green:focus, a.badge-ec-green.focus {\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }\n\n.badge-ec-red {\n  color: #212529;\n  background-color: #fff;\n  display: inline-block;\n  border: 1px solid #C04949;\n  color: #C04949;\n  padding: .5rem 0.75rem;\n  font-size: 14px;\n  font-weight: normal; }\n  a.badge-ec-red:hover, a.badge-ec-red:focus {\n    color: #212529;\n    background-color: #e6e6e6; }\n  a.badge-ec-red:focus, a.badge-ec-red.focus {\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }\n\n.badge-ec-yellow {\n  color: #212529;\n  background-color: #fff;\n  display: inline-block;\n  border: 1px solid #EEB128;\n  color: #EEB128;\n  padding: .5rem 0.75rem;\n  font-size: 14px;\n  font-weight: normal; }\n  a.badge-ec-yellow:hover, a.badge-ec-yellow:focus {\n    color: #212529;\n    background-color: #e6e6e6; }\n  a.badge-ec-yellow:focus, a.badge-ec-yellow.focus {\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }\n\n.badge-ec-glay {\n  color: #212529;\n  background-color: #fff;\n  display: inline-block;\n  border: 1px solid #A3A3A3;\n  color: #A3A3A3;\n  padding: .5rem 0.75rem;\n  font-size: 14px;\n  font-weight: normal; }\n  a.badge-ec-glay:hover, a.badge-ec-glay:focus {\n    color: #212529;\n    background-color: #e6e6e6; }\n  a.badge-ec-glay:focus, a.badge-ec-glay.focus {\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }\n\n/*\nボーダー\n\nEC-CUBE管理画面専用のオリジナルborderクラスを定義しています。`.border`とセットでの使用を前提としています。<br>\nボーダー色は#cccを設定しています。\n\nMarkup:\n<!-- `style=\"padding: 20px; margin-bottom: 10px;\"` はスタイルガイドにおけるサンプル表示の都合上付与しています。-->\n.d-block.border.border-ec-gray(style=\"padding: 20px; margin-bottom: 10px;\") ボーダーsolid　 .border-ec-gray 　\n.d-block.border-ec-dashed(style=\"padding: 20px; margin-bottom: 10px;\") ボーダーdashed　 .border-ec-dashed\n\nStyleguide 12.5\n*/\n.border-ec-gray {\n  border-color: #ccc !important; }\n\n.border-ec-dashed {\n  border: 1px dashed #ccc !important; }\n\n/*\n背景色\n\nEC-CUBE管理画面専用のオリジナルbackground-colorクラスを定義しています。\n\nMarkup:\n<!-- `style=\"padding: 20px; margin-bottom: 10px;\"` はスタイルガイドにおけるサンプル表示の都合上付与しています。-->\n.d-block.bg-ec-lightGray(style=\"padding: 20px; margin-bottom: 10px;\") 背景色 .bg-ec-lightGray #f9f9f9\n.d-block.bg-ec-formGray(style=\"padding: 20px; margin-bottom: 10px;\") 背景色 .bg-ec-formGray #F8F9FA\n\nStyleguide 12.6\n*/\n.bg-ec-lightGray {\n  background-color: #f9f9f9 !important; }\n\n.bg-ec-formGray {\n  background-color: #F8F9FA !important; }\n\n/*\nカラー\n\nEC-CUBE管理画面専用のオリジナルcolorクラスを定義しています。\n\nMarkup:\n.text-ec-gray テキスト .text-ec-gray　#999\n.text-ec-lightGray テキスト .text-ec-lightGray　#ccc\n\nStyleguide 12.7\n*/\n.text-ec-gray {\n  color: #999 !important; }\n\n.text-ec-lightGray {\n  color: #ccc !important; }\n", "@import \"../library/_variable\";\n@import \"../mixin/_media\";\n\n//Theme color\n$primary:       #437ec4 !default;\n$secondary:     #54687A !default;\n$success:       #25b877 !default;\n//$info:          $cyan !default;\n$warning:       #eeb128 !default;\n$danger:        #c04949 !default;\n$light:         #eff0f4 !default;\n$dark:          #595959 !default;\n\n//card\n$card-cap-bg: white;\n\n//table\n$table-border-color: #ccc;\n$table-accent-bg: #f9f9f9;\n$table-active-bg: #f2f2f2;\n.table-ec-lightGray{\n  background-color: #f2f2f2;\n}\n\n//breadcrumb\n$breadcrumb-bg: #eff0f4;\n\n@import \"../../../../../node_modules/bootstrap/scss/bootstrap\";\n\n/*\nBootstrap\n\nスタイルガイドで使用される一部クラスは [Bootstrap](https://getbootstrap.com/) を使用しています。\nBootstrap で利用可能なクラス群については、 [Bootstrap 公式サイト](https://getbootstrap.com/) をご利用ください。\n\nBootstrapを用いて変更を加えたEC-CUBE管理画面専用のオリジナルクラスを以下にまとめています。\n\nStyleguide 12.0\n*/\n\n/*\nテーブル\n\n `table-ec-lightGray`：背景色を#f2f2f2に指定します。thに使用しています。\n\nMarkup:\n<table class=\"table table-striped table-bordered\">\n  <tbody>\n    <tr>\n      <th class=\"w-25 align-middle table-ec-lightGray\">カテゴリID</th>\n      <td class=\"align-middle\">新規登録時は未設定<br/>既存商品の更新は商品IDを設定</td>\n    </tr>\n    <tr>\n      <th class=\"w-25 align-middle table-ec-lightGray\">カテゴリ名<span class=\"badge badge-primary ml-1\">必須</span></th>\n      <td class=\"align-middle\"></td>\n    </tr>\n    <tr>\n      <th class=\"w-25 align-middle table-ec-lightGray\">親カテゴリID</th>\n      <td class=\"align-middle\"></td>\n    </tr>\n  </tbody>\n</table>\n\nStyleguide 12.1\n*/\n.table-ec-lightGray{\n  background-color: #f2f2f2;\n}\n\n/*\nボタン\n\nEC-CUBE管理画面専用のオリジナルbuttonクラスを定義しています。`.btn`とセットでの使用を前提としています。<br>\n<br>\n<br>\n【コンバージョン】 `.btn-ec-conversion`<br>\nテキスト…#FFFFFF<br>\n背景…#437EC4 → #2963AB (hover)<br>\n<br>\n【デリート】 `.btn-ec-delete`<br>\nテキスト…#FFFFFF<br>\n背景…#C04949 → #A62E2E (hover)<br>\n<br>\n【レギュラー】 `.btn-ec-regular`<br>\nテキスト…#595959 → #262626 (hover)<br>\n背景…#FFFFFF → #F2F2F2 (hover)<br>\nボーダー…#595959 → #262626 (hover)<br>\n<br>\n【サブ】 `.btn-ec-sub`<br>\nテキスト…#FFFFFF<br>\n背景…#F5F6F8 → #D6D9E0(hover)<br>\n<br>\n【アイコン】 `.btn-ec-actionIcon`<br>\nテキスト…#54687A<br>\n背景…#FFFFFF → #54687A (hover)<br>\n\n\nMarkup:\n<!-- `style=\"margin-right: 5px;\"` はスタイルガイドにおけるサンプル表示の都合上付与しています。-->\n<a href=\"\" style=\"margin-right: 5px;\" class=\"btn btn-ec-conversion\">コンバージョン</a>\n<a href=\"\" style=\"margin-right: 5px;\" class=\"btn btn-ec-delete\">デリート</a>\n<a href=\"\" style=\"margin-right: 5px;\" class=\"btn btn-ec-regular\">レギュラー</a>\n<a href=\"\" style=\"margin-right: 5px;\" class=\"btn btn-ec-sub\">サブ</a>\n<a class=\"btn btn-ec-actionIcon\"><i class=\"fa fa-close fa-lg text-secondary\" aria-hidden=\"true\"></i></a>\n\nStyleguide 12.2\n*/\n.btn-ec-conversion{\n  @include button-variant(#437EC4, transparent, #2963AB, transparent, #2963AB, transparent );\n  cursor:pointer;\n}\n\n.btn-ec-delete{\n  @include button-variant(#C04949, transparent, #A62E2E, transparent, #A62E2E, transparent );\n  cursor:pointer;\n}\n\n.btn-ec-regular{\n  @include button-variant(#FFFFFF, #595959, #F2F2F2, #262626, #F2F2F2, #262626 );\n  color: #595959;\n  cursor:pointer;\n}\n\n.btn-ec-regular:hover {\n  color: #262626;\n}\n\n.btn-ec-sub{\n  @include button-variant(#F5F6F8, transparent, #D6D9E0, transparent, #D6D9E0, transparent );\n  color: #262626;\n  cursor:pointer;\n}\n\n.btn-ec-sub:hover {\n  color: #262626;\n}\n\n.btn-ec-actionIcon{\n  @include button-variant(transparent, transparent, #EFF0F4, transparent, #EFF0F4, transparent );\n  padding: 6px 12px;\n  cursor:pointer;\n}\n\n.btn-ec-actionIcon:hover {\n  color: #54687A;\n}\n\n/*\nボタングループ（タブ）\n\nEC-CUBE管理画面専用のオリジナルbutton-groupクラスを定義しています。ページの切り替えに用います。<br>\n<br>\n【タブ】 `.btn-ec-tab`<br>\nテキスト…#595959 → #262626 (hover) → #FFFFFF(active)<br>\n背景…#FFFFFF → #F2F2F2 (hover) → #595959(active)<br>\nボーダー…#595959 → #262626 (hover)<br>\n<br>\n\nMarkup:\n<div class=\"btn-group nav d-inline-flex\" id=\"pills-tab\" role=\"tablist\">\n  <a class=\"nav-link active btn btn-ec-tab py-2 pl-4 pr-4\" id=\"pills-pc-tab\" data-toggle=\"pill\" href=\"#pills-pc\" role=\"tab\" aria-controls=\"pills-pc\" aria-selected=\"true\">PC</a>\n  <a class=\"nav-link btn btn-ec-tab py-2 pl-4 pr-4\" id=\"pills-mobile-tab\" data-toggle=\"pill\" href=\"#pills-mobile\" role=\"tab\" aria-controls=\"pills-mobile\" aria-selected=\"false\">モバイル</a></div>\n\nStyleguide 12.3\n*/\n.btn-ec-tab {\n  @include button-variant(#FFFFFF, #595959, #F2F2F2, #262626, #F2F2F2, #262626 );\n  color: #595959;\n  cursor:pointer;\n}\n\n.btn-ec-tab:hover {\n  color: #262626;\n}\n\n.btn-ec-tab.active{\n  background-color: #595959 !important;\n  color: #fff !important;\n  box-shadow: none !important;\n}\n\n/*\nバッジ\n\nEC-CUBE管理画面専用のオリジナルbadgeクラスを定義しています。`.badge`とセットでの使用を前提としています。\n<br>\n `badge-ec-blue`   :#437EC4;<br>\n `badge-ec-green`  :#25B877;<br>\n `badge-ec-red`    :#C04949;<br>\n `badge-ec-yellow` :#EEB128;<br>\n<br>\n\nMarkup:\n<!-- `style=\"margin-right: 5px;\"` はスタイルガイドにおけるサンプル表示の都合上付与しています。-->\n<span style=\"margin-right: 5px;\" class=\"badge badge-ec-blue\">新規受付</span>\n<span style=\"margin-right: 5px;\" class=\"badge badge-ec-green\">入金済み</span>\n<span style=\"margin-right: 5px;\" class=\"badge badge-ec-red\">入金待ち</span>\n<span style=\"margin-right: 5px;\" class=\"badge badge-ec-yellow\">処理中</span>\n<span class=\"badge badge-ec-glay\">キャンセル</span>\n\nStyleguide 12.4\n*/\n.badge-ec-blue{\n  @include badge-variant(#fff);\n  display: inline-block;\n  border: 1px solid #437EC4;\n  color: #437EC4;\n  padding: .5rem 0.75rem;\n  font-size: 14px;\n  font-weight: normal;\n}\n.badge-ec-green{\n  @include badge-variant(#fff);\n  display: inline-block;\n  border: 1px solid #25B877;\n  color: #25B877;\n  padding: .5rem 0.75rem;\n  font-size: 14px;\n  font-weight: normal;\n}\n.badge-ec-red{\n  @include badge-variant(#fff);\n  display: inline-block;\n  border: 1px solid #C04949;\n  color: #C04949;\n  padding: .5rem 0.75rem;\n  font-size: 14px;\n  font-weight: normal;\n}\n.badge-ec-yellow{\n  @include badge-variant(#fff);\n  display: inline-block;\n  border: 1px solid #EEB128;\n  color: #EEB128;\n  padding: .5rem 0.75rem;\n  font-size: 14px;\n  font-weight: normal;\n}\n.badge-ec-glay{\n  @include badge-variant(#fff);\n  display: inline-block;\n  border: 1px solid #A3A3A3;\n  color: #A3A3A3;\n  padding: .5rem 0.75rem;\n  font-size: 14px;\n  font-weight: normal;\n}\n\n/*\nボーダー\n\nEC-CUBE管理画面専用のオリジナルborderクラスを定義しています。`.border`とセットでの使用を前提としています。<br>\nボーダー色は#cccを設定しています。\n\nMarkup:\n<!-- `style=\"padding: 20px; margin-bottom: 10px;\"` はスタイルガイドにおけるサンプル表示の都合上付与しています。-->\n.d-block.border.border-ec-gray(style=\"padding: 20px; margin-bottom: 10px;\") ボーダーsolid　 .border-ec-gray 　\n.d-block.border-ec-dashed(style=\"padding: 20px; margin-bottom: 10px;\") ボーダーdashed　 .border-ec-dashed\n\nStyleguide 12.5\n*/\n.border-ec-gray {\n  border-color: #ccc !important;\n}\n.border-ec-dashed{\n  border: $border-width dashed #ccc !important;\n}\n\n/*\n背景色\n\nEC-CUBE管理画面専用のオリジナルbackground-colorクラスを定義しています。\n\nMarkup:\n<!-- `style=\"padding: 20px; margin-bottom: 10px;\"` はスタイルガイドにおけるサンプル表示の都合上付与しています。-->\n.d-block.bg-ec-lightGray(style=\"padding: 20px; margin-bottom: 10px;\") 背景色 .bg-ec-lightGray #f9f9f9\n.d-block.bg-ec-formGray(style=\"padding: 20px; margin-bottom: 10px;\") 背景色 .bg-ec-formGray #F8F9FA\n\nStyleguide 12.6\n*/\n.bg-ec-lightGray{\n  background-color: #f9f9f9 !important;\n}\n.bg-ec-formGray{\n  background-color: #F8F9FA !important;\n}\n\n/*\nカラー\n\nEC-CUBE管理画面専用のオリジナルcolorクラスを定義しています。\n\nMarkup:\n.text-ec-gray テキスト .text-ec-gray　#999\n.text-ec-lightGray テキスト .text-ec-lightGray　#ccc\n\nStyleguide 12.7\n*/\n.text-ec-gray { color: #999 !important; }\n.text-ec-lightGray { color: #ccc !important; }\n", "/*!\n * Bootstrap v4.4.1 (https://getbootstrap.com/)\n * Copyright 2011-2019 The Bootstrap Authors\n * Copyright 2011-2019 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n\n@import \"functions\";\n@import \"variables\";\n@import \"mixins\";\n@import \"root\";\n@import \"reboot\";\n@import \"type\";\n@import \"images\";\n@import \"code\";\n@import \"grid\";\n@import \"tables\";\n@import \"forms\";\n@import \"buttons\";\n@import \"transitions\";\n@import \"dropdown\";\n@import \"button-group\";\n@import \"input-group\";\n@import \"custom-forms\";\n@import \"nav\";\n@import \"navbar\";\n@import \"card\";\n@import \"breadcrumb\";\n@import \"pagination\";\n@import \"badge\";\n@import \"jumbotron\";\n@import \"alert\";\n@import \"progress\";\n@import \"media\";\n@import \"list-group\";\n@import \"close\";\n@import \"toasts\";\n@import \"modal\";\n@import \"tooltip\";\n@import \"popover\";\n@import \"carousel\";\n@import \"spinners\";\n@import \"utilities\";\n@import \"print\";\n", "// Do not forget to update getting-started/theming.md!\n:root {\n  // Custom variable values only support SassScript inside `#{}`.\n  @each $color, $value in $colors {\n    --#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors {\n    --#{$color}: #{$value};\n  }\n\n  @each $bp, $value in $grid-breakpoints {\n    --breakpoint-#{$bp}: #{$value};\n  }\n\n  // Use `inspect` for lists so that quoted items keep the quotes.\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\n  --font-family-sans-serif: #{inspect($font-family-sans-serif)};\n  --font-family-monospace: #{inspect($font-family-monospace)};\n}\n", "// stylelint-disable at-rule-no-vendor-prefix, declaration-no-important, selector-no-qualifying-type, property-no-vendor-prefix\n\n// Reboot\n//\n// Normalization of HTML elements, manually forked from Normalize.css to remove\n// styles targeting irrelevant browsers while applying new styles.\n//\n// Normalize is licensed MIT. https://github.com/necolas/normalize.css\n\n\n// Document\n//\n// 1. Change from `box-sizing: content-box` so that `width` is not affected by `padding` or `border`.\n// 2. Change the default font family in all browsers.\n// 3. Correct the line height in all browsers.\n// 4. Prevent adjustments of font size after orientation changes in IE on Windows Phone and in iOS.\n// 5. Change the default tap highlight to be completely transparent in iOS.\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box; // 1\n}\n\nhtml {\n  font-family: sans-serif; // 2\n  line-height: 1.15; // 3\n  -webkit-text-size-adjust: 100%; // 4\n  -webkit-tap-highlight-color: rgba($black, 0); // 5\n}\n\n// Shim for \"new\" HTML5 structural elements to display correctly (IE10, older browsers)\n// TODO: remove in v5\n// stylelint-disable-next-line selector-list-comma-newline-after\narticle, aside, figcaption, figure, footer, header, hgroup, main, nav, section {\n  display: block;\n}\n\n// Body\n//\n// 1. Remove the margin in all browsers.\n// 2. As a best practice, apply a default `background-color`.\n// 3. Set an explicit initial text-align value so that we can later use\n//    the `inherit` value on things like `<th>` elements.\n\nbody {\n  margin: 0; // 1\n  font-family: $font-family-base;\n  @include font-size($font-size-base);\n  font-weight: $font-weight-base;\n  line-height: $line-height-base;\n  color: $body-color;\n  text-align: left; // 3\n  background-color: $body-bg; // 2\n}\n\n// Future-proof rule: in browsers that support :focus-visible, suppress the focus outline\n// on elements that programmatically receive focus but wouldn't normally show a visible\n// focus outline. In general, this would mean that the outline is only applied if the\n// interaction that led to the element receiving programmatic focus was a keyboard interaction,\n// or the browser has somehow determined that the user is primarily a keyboard user and/or\n// wants focus outlines to always be presented.\n//\n// See https://developer.mozilla.org/en-US/docs/Web/CSS/:focus-visible\n// and https://developer.paciellogroup.com/blog/2018/03/focus-visible-and-backwards-compatibility/\n[tabindex=\"-1\"]:focus:not(:focus-visible) {\n  outline: 0 !important;\n}\n\n\n// Content grouping\n//\n// 1. Add the correct box sizing in Firefox.\n// 2. Show the overflow in Edge and IE.\n\nhr {\n  box-sizing: content-box; // 1\n  height: 0; // 1\n  overflow: visible; // 2\n}\n\n\n//\n// Typography\n//\n\n// Remove top margins from headings\n//\n// By default, `<h1>`-`<h6>` all receive top and bottom margins. We nuke the top\n// margin for easier control within type scales as it avoids margin collapsing.\n// stylelint-disable-next-line selector-list-comma-newline-after\nh1, h2, h3, h4, h5, h6 {\n  margin-top: 0;\n  margin-bottom: $headings-margin-bottom;\n}\n\n// Reset margins on paragraphs\n//\n// Similarly, the top margin on `<p>`s get reset. However, we also reset the\n// bottom margin to use `rem` units instead of `em`.\np {\n  margin-top: 0;\n  margin-bottom: $paragraph-margin-bottom;\n}\n\n// Abbreviations\n//\n// 1. Duplicate behavior to the data-* attribute for our tooltip plugin\n// 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.\n// 3. Add explicit cursor to indicate changed behavior.\n// 4. Remove the bottom border in Firefox 39-.\n// 5. Prevent the text-decoration to be skipped.\n\nabbr[title],\nabbr[data-original-title] { // 1\n  text-decoration: underline; // 2\n  text-decoration: underline dotted; // 2\n  cursor: help; // 3\n  border-bottom: 0; // 4\n  text-decoration-skip-ink: none; // 5\n}\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\ndt {\n  font-weight: $dt-font-weight;\n}\n\ndd {\n  margin-bottom: .5rem;\n  margin-left: 0; // Undo browser default\n}\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\nb,\nstrong {\n  font-weight: $font-weight-bolder; // Add the correct font weight in Chrome, Edge, and Safari\n}\n\nsmall {\n  @include font-size(80%); // Add the correct font size in all browsers\n}\n\n//\n// Prevent `sub` and `sup` elements from affecting the line height in\n// all browsers.\n//\n\nsub,\nsup {\n  position: relative;\n  @include font-size(75%);\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub { bottom: -.25em; }\nsup { top: -.5em; }\n\n\n//\n// Links\n//\n\na {\n  color: $link-color;\n  text-decoration: $link-decoration;\n  background-color: transparent; // Remove the gray background on active links in IE 10.\n\n  @include hover() {\n    color: $link-hover-color;\n    text-decoration: $link-hover-decoration;\n  }\n}\n\n// And undo these styles for placeholder links/named anchors (without href).\n// It would be more straightforward to just use a[href] in previous block, but that\n// causes specificity issues in many other styles that are too complex to fix.\n// See https://github.com/twbs/bootstrap/issues/19402\n\na:not([href]) {\n  color: inherit;\n  text-decoration: none;\n\n  @include hover() {\n    color: inherit;\n    text-decoration: none;\n  }\n}\n\n\n//\n// Code\n//\n\npre,\ncode,\nkbd,\nsamp {\n  font-family: $font-family-monospace;\n  @include font-size(1em); // Correct the odd `em` font sizing in all browsers.\n}\n\npre {\n  // Remove browser default top margin\n  margin-top: 0;\n  // Reset browser default of `1em` to use `rem`s\n  margin-bottom: 1rem;\n  // Don't allow content to break outside\n  overflow: auto;\n}\n\n\n//\n// Figures\n//\n\nfigure {\n  // Apply a consistent margin strategy (matches our type styles).\n  margin: 0 0 1rem;\n}\n\n\n//\n// Images and content\n//\n\nimg {\n  vertical-align: middle;\n  border-style: none; // Remove the border on images inside links in IE 10-.\n}\n\nsvg {\n  // Workaround for the SVG overflow bug in IE10/11 is still required.\n  // See https://github.com/twbs/bootstrap/issues/26878\n  overflow: hidden;\n  vertical-align: middle;\n}\n\n\n//\n// Tables\n//\n\ntable {\n  border-collapse: collapse; // Prevent double borders\n}\n\ncaption {\n  padding-top: $table-cell-padding;\n  padding-bottom: $table-cell-padding;\n  color: $table-caption-color;\n  text-align: left;\n  caption-side: bottom;\n}\n\nth {\n  // Matches default `<td>` alignment by inheriting from the `<body>`, or the\n  // closest parent with a set `text-align`.\n  text-align: inherit;\n}\n\n\n//\n// Forms\n//\n\nlabel {\n  // Allow labels to use `margin` for spacing.\n  display: inline-block;\n  margin-bottom: $label-margin-bottom;\n}\n\n// Remove the default `border-radius` that macOS Chrome adds.\n//\n// Details at https://github.com/twbs/bootstrap/issues/24093\nbutton {\n  // stylelint-disable-next-line property-blacklist\n  border-radius: 0;\n}\n\n// Work around a Firefox/IE bug where the transparent `button` background\n// results in a loss of the default `button` focus styles.\n//\n// Credit: https://github.com/suitcss/base/\nbutton:focus {\n  outline: 1px dotted;\n  outline: 5px auto -webkit-focus-ring-color;\n}\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0; // Remove the margin in Firefox and Safari\n  font-family: inherit;\n  @include font-size(inherit);\n  line-height: inherit;\n}\n\nbutton,\ninput {\n  overflow: visible; // Show the overflow in Edge\n}\n\nbutton,\nselect {\n  text-transform: none; // Remove the inheritance of text transform in Firefox\n}\n\n// Remove the inheritance of word-wrap in Safari.\n//\n// Details at https://github.com/twbs/bootstrap/issues/24990\nselect {\n  word-wrap: normal;\n}\n\n\n// 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`\n//    controls in Android 4.\n// 2. Correct the inability to style clickable types in iOS and Safari.\nbutton,\n[type=\"button\"], // 1\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button; // 2\n}\n\n// Opinionated: add \"hand\" cursor to non-disabled button elements.\n@if $enable-pointer-cursor-for-buttons {\n  button,\n  [type=\"button\"],\n  [type=\"reset\"],\n  [type=\"submit\"] {\n    &:not(:disabled) {\n      cursor: pointer;\n    }\n  }\n}\n\n// Remove inner border and padding from Firefox, but don't restore the outline like Normalize.\nbutton::-moz-focus-inner,\n[type=\"button\"]::-moz-focus-inner,\n[type=\"reset\"]::-moz-focus-inner,\n[type=\"submit\"]::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\ninput[type=\"radio\"],\ninput[type=\"checkbox\"] {\n  box-sizing: border-box; // 1. Add the correct box sizing in IE 10-\n  padding: 0; // 2. Remove the padding in IE 10-\n}\n\n\ninput[type=\"date\"],\ninput[type=\"time\"],\ninput[type=\"datetime-local\"],\ninput[type=\"month\"] {\n  // Remove the default appearance of temporal inputs to avoid a Mobile Safari\n  // bug where setting a custom line-height prevents text from being vertically\n  // centered within the input.\n  // See https://bugs.webkit.org/show_bug.cgi?id=139848\n  // and https://github.com/twbs/bootstrap/issues/11266\n  -webkit-appearance: listbox;\n}\n\ntextarea {\n  overflow: auto; // Remove the default vertical scrollbar in IE.\n  // Textareas should really only resize vertically so they don't break their (horizontal) containers.\n  resize: vertical;\n}\n\nfieldset {\n  // Browsers set a default `min-width: min-content;` on fieldsets,\n  // unlike e.g. `<div>`s, which have `min-width: 0;` by default.\n  // So we reset that to ensure fieldsets behave more like a standard block element.\n  // See https://github.com/twbs/bootstrap/issues/12359\n  // and https://html.spec.whatwg.org/multipage/#the-fieldset-and-legend-elements\n  min-width: 0;\n  // Reset the default outline behavior of fieldsets so they don't affect page layout.\n  padding: 0;\n  margin: 0;\n  border: 0;\n}\n\n// 1. Correct the text wrapping in Edge and IE.\n// 2. Correct the color inheritance from `fieldset` elements in IE.\nlegend {\n  display: block;\n  width: 100%;\n  max-width: 100%; // 1\n  padding: 0;\n  margin-bottom: .5rem;\n  @include font-size(1.5rem);\n  line-height: inherit;\n  color: inherit; // 2\n  white-space: normal; // 1\n}\n\nprogress {\n  vertical-align: baseline; // Add the correct vertical alignment in Chrome, Firefox, and Opera.\n}\n\n// Correct the cursor style of increment and decrement buttons in Chrome.\n[type=\"number\"]::-webkit-inner-spin-button,\n[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n[type=\"search\"] {\n  // This overrides the extra rounded corners on search inputs in iOS so that our\n  // `.form-control` class can properly style them. Note that this cannot simply\n  // be added to `.form-control` as it's not specific enough. For details, see\n  // https://github.com/twbs/bootstrap/issues/11586.\n  outline-offset: -2px; // 2. Correct the outline style in Safari.\n  -webkit-appearance: none;\n}\n\n//\n// Remove the inner padding in Chrome and Safari on macOS.\n//\n\n[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n//\n// 1. Correct the inability to style clickable types in iOS and Safari.\n// 2. Change font properties to `inherit` in Safari.\n//\n\n::-webkit-file-upload-button {\n  font: inherit; // 2\n  -webkit-appearance: button; // 1\n}\n\n//\n// Correct element displays\n//\n\noutput {\n  display: inline-block;\n}\n\nsummary {\n  display: list-item; // Add the correct display in all browsers\n  cursor: pointer;\n}\n\ntemplate {\n  display: none; // Add the correct display in IE\n}\n\n// Always hide an element with the `hidden` HTML attribute (from PureCSS).\n// Needed for proper display in IE 10-.\n[hidden] {\n  display: none !important;\n}\n", "//カラー\n\n// Brand\n$ecCube_navy: #2f3f4e;\n$ecCube_yellow: #f7d622;\n\n// Theme\n$navy80: #2f3f4e; //アイコン等\n$navy70: #54687A; //アイコン等\n$navy60: #7c90a2; //アイコン等\n$dllNavy20: #d6d9e0; //メインナビボーダー\n\n// Gray Scale\n$black: #000;\n$black85: #262626; //通常のテキスト\n$black65: #595959; //キャプション等\n$black40: #999; //プレースホルダ\n$black20: #ccc; //ボーダーカラー\n\n// Background\n$paleBlue: #eff0f4; //メインの背景色\n$paleBlue60 : #f5f6f8; //メインナビ等背景色\n$paleBlue100: #F8F9FA;\n$paleRed: #faf1f1; //エラー\n$white: #fff; //ブロックの背景色\n\n// Overlay\n$black85: rgba(15,15,15,.85);\n$black65: rgba(15,15,15,.65);\n$black05: rgba(15,15,15,.05); //hover時の色変化に使用\n$white15: rgba(255,255,255,.15); //hover時の色変化に使用\n\n// Accent\n$blue: #437ec4; //進む・リンク\n$green: #25b877; //成功\n$lightGreen: #5AB67C;\n$yellow: #eeb128; //警告\n$red: #c04949; //失敗・危険\n\n// Button\n$lochmara: #527dbf;\n$iron: #d1d1d1;\n$mineShaft: #333;\n$boulder: #797979;\n$brickRedLight: #b2514d;\n$blueBayou: #586878;\n$seaShell: #f0f0f0;\n$wildSand: #f3f4f6;\n$gothic: #8090a0;", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n$grays: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$grays: map-merge(\n  (\n    \"100\": $gray-100,\n    \"200\": $gray-200,\n    \"300\": $gray-300,\n    \"400\": $gray-400,\n    \"500\": $gray-500,\n    \"600\": $gray-600,\n    \"700\": $gray-700,\n    \"800\": $gray-800,\n    \"900\": $gray-900\n  ),\n  $grays\n);\n\n$blue:    #007bff !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$colors: map-merge(\n  (\n    \"blue\":       $blue,\n    \"indigo\":     $indigo,\n    \"purple\":     $purple,\n    \"pink\":       $pink,\n    \"red\":        $red,\n    \"orange\":     $orange,\n    \"yellow\":     $yellow,\n    \"green\":      $green,\n    \"teal\":       $teal,\n    \"cyan\":       $cyan,\n    \"white\":      $white,\n    \"gray\":       $gray-600,\n    \"gray-dark\":  $gray-800\n  ),\n  $colors\n);\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-800 !default;\n\n$theme-colors: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$theme-colors: map-merge(\n  (\n    \"primary\":    $primary,\n    \"secondary\":  $secondary,\n    \"success\":    $success,\n    \"info\":       $info,\n    \"warning\":    $warning,\n    \"danger\":     $danger,\n    \"light\":      $light,\n    \"dark\":       $dark\n  ),\n  $theme-colors\n);\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval:      8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold:  150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark:             $gray-900 !default;\n$yiq-text-light:            $white !default;\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\",\"%3c\"),\n  (\">\",\"%3e\"),\n  (\"#\",\"%23\"),\n) !default;\n\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                true !default;\n$enable-rounded:                              true !default;\n$enable-shadows:                              false !default;\n$enable-gradients:                            false !default;\n$enable-transitions:                          true !default;\n$enable-prefers-reduced-motion-media-query:   true !default;\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes:                         true !default;\n$enable-pointer-cursor-for-buttons:           true !default;\n$enable-print-styles:                         true !default;\n$enable-responsive-font-sizes:                false !default;\n$enable-validation-icons:                     true !default;\n$enable-deprecation-messages:                 true !default;\n\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n$spacer: 1rem !default;\n$spacers: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$spacers: map-merge(\n  (\n    0: 0,\n    1: ($spacer * .25),\n    2: ($spacer * .5),\n    3: $spacer,\n    4: ($spacer * 1.5),\n    5: ($spacer * 3)\n  ),\n  $spacers\n);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$sizes: map-merge(\n  (\n    25: 25%,\n    50: 50%,\n    75: 75%,\n    100: 100%,\n    auto: auto\n  ),\n  $sizes\n);\n\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              theme-color(\"primary\") !default;\n$link-decoration:                         none !default;\n$link-hover-color:                        darken($link-color, 15%) !default;\n$link-hover-decoration:                   underline !default;\n// Darken percentage for links with `.text-*` class (e.g. `.text-success`)\n$emphasized-link-hover-darken-percentage: 15% !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n) !default;\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px\n) !default;\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           30px !default;\n$grid-row-columns:            6 !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.5 !default;\n$line-height-sm:              1.5 !default;\n\n$border-width:                1px !default;\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-sm:            .2rem !default;\n\n$rounded-pill:                50rem !default;\n\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         theme-color(\"primary\") !default;\n\n$caret-width:                 .3em !default;\n$caret-vertical-align:        $caret-width * .85 !default;\n$caret-spacing:               $caret-width * .85 !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n$embed-responsive-aspect-ratios: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$embed-responsive-aspect-ratios: join(\n  (\n    (21 9),\n    (16 9),\n    (4 3),\n    (1 1),\n  ),\n  $embed-responsive-aspect-ratios\n);\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:            $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:                $font-size-base * 1.25 !default;\n$font-size-sm:                $font-size-base * .875 !default;\n\n$font-weight-lighter:         lighter !default;\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          bolder !default;\n\n$font-weight-base:            $font-weight-normal !default;\n$line-height-base:            1.5 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n$headings-margin-bottom:      $spacer / 2 !default;\n$headings-font-family:        null !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              null !default;\n\n$display1-size:               6rem !default;\n$display2-size:               5.5rem !default;\n$display3-size:               4.5rem !default;\n$display4-size:               3.5rem !default;\n\n$display1-weight:             300 !default;\n$display2-weight:             300 !default;\n$display3-weight:             300 !default;\n$display4-weight:             300 !default;\n$display-line-height:         $headings-line-height !default;\n\n$lead-font-size:              $font-size-base * 1.25 !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             80% !default;\n\n$text-muted:                  $gray-600 !default;\n\n$blockquote-small-color:      $gray-600 !default;\n$blockquote-small-font-size:  $small-font-size !default;\n$blockquote-font-size:        $font-size-base * 1.25 !default;\n\n$hr-border-color:             rgba($black, .1) !default;\n$hr-border-width:             $border-width !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n$hr-margin-y:                 $spacer !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          .75rem !default;\n$table-cell-padding-sm:       .3rem !default;\n\n$table-color:                 $body-color !default;\n$table-bg:                    null !default;\n$table-accent-bg:             rgba($black, .05) !default;\n$table-hover-color:           $table-color !default;\n$table-hover-bg:              rgba($black, .075) !default;\n$table-active-bg:             $table-hover-bg !default;\n\n$table-border-width:          $border-width !default;\n$table-border-color:          $border-color !default;\n\n$table-head-bg:               $gray-200 !default;\n$table-head-color:            $gray-700 !default;\n\n$table-dark-color:            $white !default;\n$table-dark-bg:               $gray-800 !default;\n$table-dark-accent-bg:        rgba($white, .05) !default;\n$table-dark-hover-color:      $table-dark-color !default;\n$table-dark-hover-bg:         rgba($white, .075) !default;\n$table-dark-border-color:     lighten($table-dark-bg, 7.5%) !default;\n\n$table-striped-order:         odd !default;\n\n$table-caption-color:         $text-muted !default;\n\n$table-bg-level:              -9 !default;\n$table-border-level:          -6 !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-font-family:       null !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:       .2rem !default;\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n$input-btn-line-height-sm:    $line-height-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n$input-btn-line-height-lg:    $line-height-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-disabled-color:     $gray-600 !default;\n\n$btn-block-spacing-y:         .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$label-margin-bottom:                   .5rem !default;\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-base !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n\n$input-color:                           $gray-700 !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      inset 0 1px 1px rgba($black, .075) !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color:               $gray-600 !default;\n$input-plaintext-color:                 $body-color !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y / 2) !default;\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm:                       add($input-line-height-sm * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg:                       add($input-line-height-lg * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-text-margin-top:                  .25rem !default;\n\n$form-check-input-gutter:               1.25rem !default;\n$form-check-input-margin-y:             .3rem !default;\n$form-check-input-margin-x:             .25rem !default;\n\n$form-check-inline-margin-x:            .75rem !default;\n$form-check-inline-input-margin-x:      .3125rem !default;\n\n$form-grid-gutter-width:                10px !default;\n$form-group-margin-bottom:              1rem !default;\n\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$custom-forms-transition:               background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$custom-control-gutter:                 .5rem !default;\n$custom-control-spacer-x:               1rem !default;\n$custom-control-cursor:                 null !default;\n\n$custom-control-indicator-size:         1rem !default;\n$custom-control-indicator-bg:           $input-bg !default;\n\n$custom-control-indicator-bg-size:      50% 50% !default;\n$custom-control-indicator-box-shadow:   $input-box-shadow !default;\n$custom-control-indicator-border-color: $gray-500 !default;\n$custom-control-indicator-border-width: $input-border-width !default;\n\n$custom-control-label-color:            null !default;\n\n$custom-control-indicator-disabled-bg:          $input-disabled-bg !default;\n$custom-control-label-disabled-color:           $gray-600 !default;\n\n$custom-control-indicator-checked-color:        $component-active-color !default;\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\n$custom-control-indicator-checked-box-shadow:   none !default;\n$custom-control-indicator-checked-border-color: $custom-control-indicator-checked-bg !default;\n\n$custom-control-indicator-focus-box-shadow:     $input-focus-box-shadow !default;\n$custom-control-indicator-focus-border-color:   $input-focus-border-color !default;\n\n$custom-control-indicator-active-color:         $component-active-color !default;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow:    none !default;\n$custom-control-indicator-active-border-color:  $custom-control-indicator-active-bg !default;\n\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\n$custom-checkbox-indicator-icon-checked:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'><path fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/></svg>\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg:           $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color:        $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'><path stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/></svg>\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow:   none !default;\n$custom-checkbox-indicator-indeterminate-border-color: $custom-checkbox-indicator-indeterminate-bg !default;\n\n$custom-radio-indicator-border-radius:          50% !default;\n$custom-radio-indicator-icon-checked:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='-4 -4 8 8'><circle r='3' fill='#{$custom-control-indicator-checked-color}'/></svg>\") !default;\n\n$custom-switch-width:                           $custom-control-indicator-size * 1.75 !default;\n$custom-switch-indicator-border-radius:         $custom-control-indicator-size / 2 !default;\n$custom-switch-indicator-size:                  subtract($custom-control-indicator-size, $custom-control-indicator-border-width * 4) !default;\n\n$custom-select-padding-y:           $input-padding-y !default;\n$custom-select-padding-x:           $input-padding-x !default;\n$custom-select-font-family:         $input-font-family !default;\n$custom-select-font-size:           $input-font-size !default;\n$custom-select-height:              $input-height !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-font-weight:         $input-font-weight !default;\n$custom-select-line-height:         $input-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:                  $input-bg !default;\n$custom-select-disabled-bg:         $gray-200 !default;\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800 !default;\n$custom-select-indicator:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'><path fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>\") !default;\n$custom-select-background:          escape-svg($custom-select-indicator) no-repeat right $custom-select-padding-x center / $custom-select-bg-size !default; // Used so we can have multiple background elements (e.g., arrow and feedback icon)\n\n$custom-select-feedback-icon-padding-right: add(1em * .75, (2 * $custom-select-padding-y * .75) + $custom-select-padding-x + $custom-select-indicator-padding) !default;\n$custom-select-feedback-icon-position:      center right ($custom-select-padding-x + $custom-select-indicator-padding) !default;\n$custom-select-feedback-icon-size:          $input-height-inner-half $input-height-inner-half !default;\n\n$custom-select-border-width:        $input-border-width !default;\n$custom-select-border-color:        $input-border-color !default;\n$custom-select-border-radius:       $border-radius !default;\n$custom-select-box-shadow:          inset 0 1px 2px rgba($black, .075) !default;\n\n$custom-select-focus-border-color:  $input-focus-border-color !default;\n$custom-select-focus-width:         $input-focus-width !default;\n$custom-select-focus-box-shadow:    0 0 0 $custom-select-focus-width $input-btn-focus-color !default;\n\n$custom-select-padding-y-sm:        $input-padding-y-sm !default;\n$custom-select-padding-x-sm:        $input-padding-x-sm !default;\n$custom-select-font-size-sm:        $input-font-size-sm !default;\n$custom-select-height-sm:           $input-height-sm !default;\n\n$custom-select-padding-y-lg:        $input-padding-y-lg !default;\n$custom-select-padding-x-lg:        $input-padding-x-lg !default;\n$custom-select-font-size-lg:        $input-font-size-lg !default;\n$custom-select-height-lg:           $input-height-lg !default;\n\n$custom-range-track-width:          100% !default;\n$custom-range-track-height:         .5rem !default;\n$custom-range-track-cursor:         pointer !default;\n$custom-range-track-bg:             $gray-300 !default;\n$custom-range-track-border-radius:  1rem !default;\n$custom-range-track-box-shadow:     inset 0 .25rem .25rem rgba($black, .1) !default;\n\n$custom-range-thumb-width:                   1rem !default;\n$custom-range-thumb-height:                  $custom-range-thumb-width !default;\n$custom-range-thumb-bg:                      $component-active-bg !default;\n$custom-range-thumb-border:                  0 !default;\n$custom-range-thumb-border-radius:           1rem !default;\n$custom-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$custom-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in IE/Edge\n$custom-range-thumb-active-bg:               lighten($component-active-bg, 35%) !default;\n$custom-range-thumb-disabled-bg:             $gray-500 !default;\n\n$custom-file-height:                $input-height !default;\n$custom-file-height-inner:          $input-height-inner !default;\n$custom-file-focus-border-color:    $input-focus-border-color !default;\n$custom-file-focus-box-shadow:      $input-focus-box-shadow !default;\n$custom-file-disabled-bg:           $input-disabled-bg !default;\n\n$custom-file-padding-y:             $input-padding-y !default;\n$custom-file-padding-x:             $input-padding-x !default;\n$custom-file-line-height:           $input-line-height !default;\n$custom-file-font-family:           $input-font-family !default;\n$custom-file-font-weight:           $input-font-weight !default;\n$custom-file-color:                 $input-color !default;\n$custom-file-bg:                    $input-bg !default;\n$custom-file-border-width:          $input-border-width !default;\n$custom-file-border-color:          $input-border-color !default;\n$custom-file-border-radius:         $input-border-radius !default;\n$custom-file-box-shadow:            $input-box-shadow !default;\n$custom-file-button-color:          $custom-file-color !default;\n$custom-file-button-bg:             $input-group-addon-bg !default;\n$custom-file-text: (\n  en: \"Browse\"\n) !default;\n\n\n// Form validation\n\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-valid-color:         theme-color(\"success\") !default;\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}' viewBox='0 0 12 12'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n\n$form-validation-states: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$form-validation-states: map-merge(\n  (\n    \"valid\": (\n      \"color\": $form-feedback-valid-color,\n      \"icon\": $form-feedback-icon-valid\n    ),\n    \"invalid\": (\n      \"color\": $form-feedback-invalid-color,\n      \"icon\": $form-feedback-icon-invalid\n    ),\n  ),\n  $form-validation-states\n);\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n$nav-divider-color:                 $gray-200 !default;\n$nav-divider-margin-y:              $spacer / 2 !default;\n\n\n// Navbar\n\n$navbar-padding-y:                  $spacer / 2 !default;\n$navbar-padding-x:                  $spacer !default;\n\n$navbar-nav-link-padding-x:         .5rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white, .5) !default;\n$navbar-dark-hover-color:           rgba($white, .75) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .5) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n$navbar-light-brand-color:                $navbar-light-active-color !default;\n$navbar-light-brand-hover-color:          $navbar-light-active-color !default;\n$navbar-dark-brand-color:                 $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-color:                    $body-color !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-inner-border-radius:      subtract($dropdown-border-radius, $dropdown-border-width) !default;\n$dropdown-divider-bg:               $gray-200 !default;\n$dropdown-divider-margin-y:         $nav-divider-margin-y !default;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           .25rem !default;\n$dropdown-item-padding-x:           1.5rem !default;\n\n$dropdown-header-color:             $gray-600 !default;\n\n\n// Pagination\n\n$pagination-padding-y:              .5rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n$pagination-line-height:            1.25 !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n$pagination-focus-outline:          0 !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-color:                   null !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     .75rem !default;\n$card-spacer-x:                     1.25rem !default;\n$card-border-width:                 $border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-cap-color:                    null !default;\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          1.25rem !default;\n\n$card-group-margin:                 $grid-gutter-width / 2 !default;\n$card-deck-margin:                  $card-group-margin !default;\n\n$card-columns-count:                3 !default;\n$card-columns-gap:                  1.25rem !default;\n$card-columns-margin:               $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-max-width:                 200px !default;\n$tooltip-color:                     $white !default;\n$tooltip-bg:                        $black !default;\n$tooltip-border-radius:             $border-radius !default;\n$tooltip-opacity:                   .9 !default;\n$tooltip-padding-y:                 .25rem !default;\n$tooltip-padding-x:                 .5rem !default;\n$tooltip-margin:                    0 !default;\n\n$tooltip-arrow-width:               .8rem !default;\n$tooltip-arrow-height:              .4rem !default;\n$tooltip-arrow-color:               $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   $line-height-base !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width) !default;\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\n\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          .75rem !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $popover-header-padding-y !default;\n$popover-body-padding-x:            $popover-header-padding-x !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Toasts\n\n$toast-max-width:                   350px !default;\n$toast-padding-x:                   .75rem !default;\n$toast-padding-y:                   .25rem !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       null !default;\n$toast-background-color:            rgba($white, .85) !default;\n$toast-border-width:                1px !default;\n$toast-border-color:                rgba(0, 0, 0, .1) !default;\n$toast-border-radius:               .25rem !default;\n$toast-box-shadow:                  0 .25rem .75rem rgba($black, .1) !default;\n\n$toast-header-color:                $gray-600 !default;\n$toast-header-background-color:     rgba($white, .85) !default;\n$toast-header-border-color:         rgba(0, 0, 0, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   75% !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-padding-y:                   .25em !default;\n$badge-padding-x:                   .4em !default;\n$badge-border-radius:               $border-radius !default;\n\n$badge-transition:                  $btn-transition !default;\n$badge-focus-width:                 $input-btn-focus-width !default;\n\n$badge-pill-padding-x:              .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:               1rem !default;\n\n// Margin between elements in footer, must be lower than or equal to 2 * $modal-inner-padding\n$modal-footer-margin-between:       .5rem !default;\n\n$modal-dialog-margin:               .5rem !default;\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-color:               null !default;\n$modal-content-bg:                  $white !default;\n$modal-content-border-color:        rgba($black, .2) !default;\n$modal-content-border-width:        $border-width !default;\n$modal-content-border-radius:       $border-radius-lg !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs:       0 .25rem .5rem rgba($black, .5) !default;\n$modal-content-box-shadow-sm-up:    0 .5rem 1rem rgba($black, .5) !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n$modal-header-border-color:         $border-color !default;\n$modal-footer-border-color:         $modal-header-border-color !default;\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-footer-border-width:         $modal-header-border-width !default;\n$modal-header-padding-y:            1rem !default;\n$modal-header-padding-x:            1rem !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-xl:                          1140px !default;\n$modal-lg:                          800px !default;\n$modal-md:                          500px !default;\n$modal-sm:                          300px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   .75rem !default;\n$alert-padding-x:                   1.25rem !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-level:                    -10 !default;\n$alert-border-level:                -9 !default;\n$alert-color-level:                 6 !default;\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                $font-size-base * .75 !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   theme-color(\"primary\") !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n\n// List group\n\n$list-group-color:                  null !default;\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\n\n\n// Figures\n\n$figure-caption-font-size:          90% !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-font-size:              null !default;\n\n$breadcrumb-padding-y:              .75rem !default;\n$breadcrumb-padding-x:              1rem !default;\n$breadcrumb-item-padding:           .5rem !default;\n\n$breadcrumb-margin-bottom:          1rem !default;\n\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                quote(\"/\") !default;\n\n$breadcrumb-border-radius:          $border-radius !default;\n\n\n// Carousel\n\n$carousel-control-color:             $white !default;\n$carousel-control-width:             15% !default;\n$carousel-control-opacity:           .5 !default;\n$carousel-control-hover-opacity:     .9 !default;\n$carousel-control-transition:        opacity .15s ease !default;\n\n$carousel-indicator-width:           30px !default;\n$carousel-indicator-height:          3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer:          3px !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-transition:      opacity .6s ease !default;\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             $white !default;\n\n$carousel-control-icon-width:        20px !default;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' width='8' height='8' viewBox='0 0 8 8'><path d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' width='8' height='8' viewBox='0 0 8 8'><path d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/></svg>\") !default;\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n\n// Spinners\n\n$spinner-width:         2rem !default;\n$spinner-height:        $spinner-width !default;\n$spinner-border-width:  .25em !default;\n\n$spinner-width-sm:        1rem !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n\n\n// Close\n\n$close-font-size:                   $font-size-base * 1.5 !default;\n$close-font-weight:                 $font-weight-bold !default;\n$close-color:                       $black !default;\n$close-text-shadow:                 0 1px 0 $white !default;\n\n\n// Code\n\n$code-font-size:                    87.5% !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         $gray-900 !default;\n$pre-scrollable-max-height:         340px !default;\n\n\n// Utilities\n\n$displays: none, inline, inline-block, block, table, table-row, table-cell, flex, inline-flex !default;\n$overflows: auto, hidden !default;\n$positions: static, relative, absolute, fixed, sticky !default;\n\n\n// Printing\n\n$print-page-size:                   a3 !default;\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated font-resizing\n//\n// See https://github.com/twbs/rfs\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n// Resize font-size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1 / $rfs-rem-value);\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\n}\n\n// Responsive font-size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Variables for storing static and fluid rescaling\n    $rfs-static: null;\n    $rfs-fluid: null;\n\n    // Remove px-unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: $fs / ($fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: $fs / ($fs * 0 + 1 / $rfs-rem-value);\n    }\n\n    // Set default font-size\n    @if $rfs-font-size-unit == rem {\n      $rfs-static: #{$fs / $rfs-rem-value}rem#{$rfs-suffix};\n    }\n    @else if $rfs-font-size-unit == px {\n      $rfs-static: #{$fs}px#{$rfs-suffix};\n    }\n    @else {\n      @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n    }\n\n    // Only add media query if font-size is bigger as the minimum font-size\n    // If $rfs-factor == 1, no rescaling will take place\n    @if $fs > $rfs-base-font-size and $enable-responsive-font-sizes {\n      $min-width: null;\n      $variable-unit: null;\n\n      // Calculate minimum font-size for given font-size\n      $fs-min: $rfs-base-font-size + ($fs - $rfs-base-font-size) / $rfs-factor;\n\n      // Calculate difference between given font-size and minimum font-size for given font-size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      // No need to check if the unit is valid, because we did that before\n      $min-width: if($rfs-font-size-unit == rem, #{$fs-min / $rfs-rem-value}rem, #{$fs-min}px);\n\n      // If two-dimensional, use smallest of screen width and height\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{$fs-diff * 100 / $rfs-breakpoint}#{$variable-unit};\n\n      // Set the calculated font-size.\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n    }\n\n    // Rendering\n    @if $rfs-fluid == null {\n      // Only render static font-size if no fluid font-size is available\n      font-size: $rfs-static;\n    }\n    @else {\n      $mq-value: null;\n\n      // RFS breakpoint formatting\n      @if $rfs-breakpoint-unit == em or $rfs-breakpoint-unit == rem {\n        $mq-value: #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit};\n      }\n      @else if $rfs-breakpoint-unit == px {\n        $mq-value: #{$rfs-breakpoint}px;\n      }\n      @else {\n        @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n      }\n\n      @if $rfs-class == \"disable\" {\n        // Adding an extra class increases specificity,\n        // which prevents the media query to override the font size\n        &,\n        .disable-responsive-font-size &,\n        &.disable-responsive-font-size {\n          font-size: $rfs-static;\n        }\n      }\n      @else {\n        font-size: $rfs-static;\n      }\n\n      @if $rfs-two-dimensional {\n        @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n      @else {\n        @media (max-width: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixin uses RFS to rescale font sizes\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "// Hover mixin and `$enable-hover-media-query` are deprecated.\n//\n// Originally added during our alphas and maintained during betas, this mixin was\n// designed to prevent `:hover` stickiness on iOS-an issue where hover styles\n// would persist after initial touch.\n//\n// For backward compatibility, we've kept these mixins and updated them to\n// always return their regular pseudo-classes instead of a shimmed media query.\n//\n// Issue: https://github.com/twbs/bootstrap/issues/25195\n\n@mixin hover() {\n  &:hover { @content; }\n}\n\n@mixin hover-focus() {\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin plain-hover-focus() {\n  &,\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin hover-focus-active() {\n  &:hover,\n  &:focus,\n  &:active {\n    @content;\n  }\n}\n", "// stylelint-disable declaration-no-important, selector-list-comma-newline-after\n\n//\n// Headings\n//\n\nh1, h2, h3, h4, h5, h6,\n.h1, .h2, .h3, .h4, .h5, .h6 {\n  margin-bottom: $headings-margin-bottom;\n  font-family: $headings-font-family;\n  font-weight: $headings-font-weight;\n  line-height: $headings-line-height;\n  color: $headings-color;\n}\n\nh1, .h1 { @include font-size($h1-font-size); }\nh2, .h2 { @include font-size($h2-font-size); }\nh3, .h3 { @include font-size($h3-font-size); }\nh4, .h4 { @include font-size($h4-font-size); }\nh5, .h5 { @include font-size($h5-font-size); }\nh6, .h6 { @include font-size($h6-font-size); }\n\n.lead {\n  @include font-size($lead-font-size);\n  font-weight: $lead-font-weight;\n}\n\n// Type display classes\n.display-1 {\n  @include font-size($display1-size);\n  font-weight: $display1-weight;\n  line-height: $display-line-height;\n}\n.display-2 {\n  @include font-size($display2-size);\n  font-weight: $display2-weight;\n  line-height: $display-line-height;\n}\n.display-3 {\n  @include font-size($display3-size);\n  font-weight: $display3-weight;\n  line-height: $display-line-height;\n}\n.display-4 {\n  @include font-size($display4-size);\n  font-weight: $display4-weight;\n  line-height: $display-line-height;\n}\n\n\n//\n// Horizontal rules\n//\n\nhr {\n  margin-top: $hr-margin-y;\n  margin-bottom: $hr-margin-y;\n  border: 0;\n  border-top: $hr-border-width solid $hr-border-color;\n}\n\n\n//\n// Emphasis\n//\n\nsmall,\n.small {\n  @include font-size($small-font-size);\n  font-weight: $font-weight-normal;\n}\n\nmark,\n.mark {\n  padding: $mark-padding;\n  background-color: $mark-bg;\n}\n\n\n//\n// Lists\n//\n\n.list-unstyled {\n  @include list-unstyled();\n}\n\n// Inline turns list items into inline-block\n.list-inline {\n  @include list-unstyled();\n}\n.list-inline-item {\n  display: inline-block;\n\n  &:not(:last-child) {\n    margin-right: $list-inline-padding;\n  }\n}\n\n\n//\n// Misc\n//\n\n// Builds on `abbr`\n.initialism {\n  @include font-size(90%);\n  text-transform: uppercase;\n}\n\n// Blockquotes\n.blockquote {\n  margin-bottom: $spacer;\n  @include font-size($blockquote-font-size);\n}\n\n.blockquote-footer {\n  display: block;\n  @include font-size($blockquote-small-font-size);\n  color: $blockquote-small-color;\n\n  &::before {\n    content: \"\\2014\\00A0\"; // em dash, nbsp\n  }\n}\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled() {\n  padding-left: 0;\n  list-style: none;\n}\n", "// Responsive images (ensure images don't scale beyond their parents)\n//\n// This is purposefully opt-in via an explicit class rather than being the default for all `<img>`s.\n// We previously tried the \"images are responsive by default\" approach in Bootstrap v2,\n// and abandoned it in Bootstrap v3 because it breaks lots of third-party widgets (including Google Maps)\n// which weren't expecting the images within themselves to be involuntarily resized.\n// See also https://github.com/twbs/bootstrap/issues/18178\n.img-fluid {\n  @include img-fluid();\n}\n\n\n// Image thumbnails\n.img-thumbnail {\n  padding: $thumbnail-padding;\n  background-color: $thumbnail-bg;\n  border: $thumbnail-border-width solid $thumbnail-border-color;\n  @include border-radius($thumbnail-border-radius);\n  @include box-shadow($thumbnail-box-shadow);\n\n  // Keep them at most 100% wide\n  @include img-fluid();\n}\n\n//\n// Figures\n//\n\n.figure {\n  // Ensures the caption's text aligns with the image.\n  display: inline-block;\n}\n\n.figure-img {\n  margin-bottom: $spacer / 2;\n  line-height: 1;\n}\n\n.figure-caption {\n  @include font-size($figure-caption-font-size);\n  color: $figure-caption-color;\n}\n", "// Image Mixins\n// - Responsive image\n// - Retina image\n\n\n// Responsive image\n//\n// Keep images from scaling beyond the width of their parents.\n\n@mixin img-fluid() {\n  // Part 1: Set a maximum relative to the parent\n  max-width: 100%;\n  // Part 2: Override the height to auto, otherwise images will be stretched\n  // when setting a width and height attribute on the img element.\n  height: auto;\n}\n\n\n// Retina image\n//\n// Short retina mixin for setting background-image and -size.\n\n@mixin img-retina($file-1x, $file-2x, $width-1x, $height-1x) {\n  background-image: url($file-1x);\n\n  // Autoprefixer takes care of adding -webkit-min-device-pixel-ratio and -o-min-device-pixel-ratio,\n  // but doesn't convert dppx=>dpi.\n  // There's no such thing as unprefixed min-device-pixel-ratio since it's nonstandard.\n  // Compatibility info: https://caniuse.com/#feat=css-media-resolution\n  @media only screen and (min-resolution: 192dpi), // IE9-11 don't support dppx\n    only screen and (min-resolution: 2dppx) { // Standardized\n    background-image: url($file-2x);\n    background-size: $width-1x $height-1x;\n  }\n  @include deprecate(\"`img-retina()`\", \"v4.3.0\", \"v5\");\n}\n", "// stylelint-disable property-blacklist\n// Single side border-radius\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: $radius;\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-top-right-radius: $radius;\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: $radius;\n    border-bottom-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: $radius;\n  }\n}\n", "// Inline code\ncode {\n  @include font-size($code-font-size);\n  color: $code-color;\n  word-wrap: break-word;\n\n  // Streamline the style when inside anchors to avoid broken underline and more\n  a > & {\n    color: inherit;\n  }\n}\n\n// User input typically entered via keyboard\nkbd {\n  padding: $kbd-padding-y $kbd-padding-x;\n  @include font-size($kbd-font-size);\n  color: $kbd-color;\n  background-color: $kbd-bg;\n  @include border-radius($border-radius-sm);\n  @include box-shadow($kbd-box-shadow);\n\n  kbd {\n    padding: 0;\n    @include font-size(100%);\n    font-weight: $nested-kbd-font-weight;\n    @include box-shadow(none);\n  }\n}\n\n// Blocks of code\npre {\n  display: block;\n  @include font-size($code-font-size);\n  color: $pre-color;\n\n  // Account for some code outputs that place code tags in pre tags\n  code {\n    @include font-size(inherit);\n    color: inherit;\n    word-break: normal;\n  }\n}\n\n// Enable scrollable blocks of code\n.pre-scrollable {\n  max-height: $pre-scrollable-max-height;\n  overflow-y: scroll;\n}\n", "// Container widths\n//\n// Set the container width, and override it for fixed navbars in media queries.\n\n@if $enable-grid-classes {\n  // Single container class with breakpoint max-widths\n  .container {\n    @include make-container();\n    @include make-container-max-widths();\n  }\n\n  // 100% wide container at all breakpoints\n  .container-fluid {\n    @include make-container();\n  }\n\n  // Responsive containers that are 100% wide until a breakpoint\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    .container-#{$breakpoint} {\n      @extend .container-fluid;\n    }\n\n    @include media-breakpoint-up($breakpoint, $grid-breakpoints) {\n      %responsive-container-#{$breakpoint} {\n        max-width: $container-max-width;\n      }\n\n      @each $name, $width in $grid-breakpoints {\n        @if ($container-max-width > $width or $breakpoint == $name) {\n          .container#{breakpoint-infix($name, $grid-breakpoints)} {\n            @extend %responsive-container-#{$breakpoint};\n          }\n        }\n      }\n    }\n  }\n}\n\n\n// Row\n//\n// Rows contain your columns.\n\n@if $enable-grid-classes {\n  .row {\n    @include make-row();\n  }\n\n  // Remove the negative margin from default .row, then the horizontal padding\n  // from all immediate children columns (to prevent runaway style inheritance).\n  .no-gutters {\n    margin-right: 0;\n    margin-left: 0;\n\n    > .col,\n    > [class*=\"col-\"] {\n      padding-right: 0;\n      padding-left: 0;\n    }\n  }\n}\n\n// Columns\n//\n// Common styles for small and large grid columns\n\n@if $enable-grid-classes {\n  @include make-grid-columns();\n}\n", "/// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-container($gutter: $grid-gutter-width) {\n  width: 100%;\n  padding-right: $gutter / 2;\n  padding-left: $gutter / 2;\n  margin-right: auto;\n  margin-left: auto;\n}\n\n\n// For each breakpoint, define the maximum width of the container in a media query\n@mixin make-container-max-widths($max-widths: $container-max-widths, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint, $container-max-width in $max-widths {\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      max-width: $container-max-width;\n    }\n  }\n}\n\n@mixin make-row($gutter: $grid-gutter-width) {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -$gutter / 2;\n  margin-left: -$gutter / 2;\n}\n\n@mixin make-col-ready($gutter: $grid-gutter-width) {\n  position: relative;\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we use `flex` values\n  // later on to override this initial width.\n  width: 100%;\n  padding-right: $gutter / 2;\n  padding-left: $gutter / 2;\n}\n\n@mixin make-col($size, $columns: $grid-columns) {\n  flex: 0 0 percentage($size / $columns);\n  // Add a `max-width` to ensure content within each column does not blow out\n  // the width of the column. Applies to IE10+ and Firefox. Chrome and Safari\n  // do not appear to require this.\n  max-width: percentage($size / $columns);\n}\n\n@mixin make-col-auto() {\n  flex: 0 0 auto;\n  width: auto;\n  max-width: 100%; // Reset earlier grid tiers\n}\n\n@mixin make-col-offset($size, $columns: $grid-columns) {\n  $num: $size / $columns;\n  margin-left: if($num == 0, 0, percentage($num));\n}\n\n// Row columns\n//\n// Specify on a parent element(e.g., .row) to force immediate children into NN\n// numberof columns. Supports wrapping to new lines, but does not do a Masonry\n// style grid.\n@mixin row-cols($count) {\n  & > * {\n    flex: 0 0 100% / $count;\n    max-width: 100% / $count;\n  }\n}\n", "// Framework grid generation\n//\n// Used only by <PERSON><PERSON><PERSON> to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\n  // Common properties for all breakpoints\n  %grid-column {\n    position: relative;\n    width: 100%;\n    padding-right: $gutter / 2;\n    padding-left: $gutter / 2;\n  }\n\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    // Allow columns to stretch full width below their breakpoints\n    @for $i from 1 through $columns {\n      .col#{$infix}-#{$i} {\n        @extend %grid-column;\n      }\n    }\n    .col#{$infix},\n    .col#{$infix}-auto {\n      @extend %grid-column;\n    }\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\n      .col#{$infix} {\n        flex-basis: 0;\n        flex-grow: 1;\n        max-width: 100%;\n      }\n\n      @for $i from 1 through $grid-row-columns {\n        .row-cols#{$infix}-#{$i} {\n          @include row-cols($i);\n        }\n      }\n\n      .col#{$infix}-auto {\n        @include make-col-auto();\n      }\n\n      @for $i from 1 through $columns {\n        .col#{$infix}-#{$i} {\n          @include make-col($i, $columns);\n        }\n      }\n\n      .order#{$infix}-first { order: -1; }\n\n      .order#{$infix}-last { order: $columns + 1; }\n\n      @for $i from 0 through $columns {\n        .order#{$infix}-#{$i} { order: $i; }\n      }\n\n      // `$columns - 1` because offsetting by the width of an entire row isn't possible\n      @for $i from 0 through ($columns - 1) {\n        @if not ($infix == \"\" and $i == 0) { // Avoid emitting useless .offset-0\n          .offset#{$infix}-#{$i} {\n            @include make-col-offset($i, $columns);\n          }\n        }\n      }\n    }\n  }\n}\n", "//\n// Basic Bootstrap table\n//\n\n.table {\n  width: 100%;\n  margin-bottom: $spacer;\n  color: $table-color;\n  background-color: $table-bg; // Reset for nesting within parents with `background-color`.\n\n  th,\n  td {\n    padding: $table-cell-padding;\n    vertical-align: top;\n    border-top: $table-border-width solid $table-border-color;\n  }\n\n  thead th {\n    vertical-align: bottom;\n    border-bottom: (2 * $table-border-width) solid $table-border-color;\n  }\n\n  tbody + tbody {\n    border-top: (2 * $table-border-width) solid $table-border-color;\n  }\n}\n\n\n//\n// Condensed table w/ half padding\n//\n\n.table-sm {\n  th,\n  td {\n    padding: $table-cell-padding-sm;\n  }\n}\n\n\n// Border versions\n//\n// Add or remove borders all around the table and between all the columns.\n\n.table-bordered {\n  border: $table-border-width solid $table-border-color;\n\n  th,\n  td {\n    border: $table-border-width solid $table-border-color;\n  }\n\n  thead {\n    th,\n    td {\n      border-bottom-width: 2 * $table-border-width;\n    }\n  }\n}\n\n.table-borderless {\n  th,\n  td,\n  thead th,\n  tbody + tbody {\n    border: 0;\n  }\n}\n\n// Zebra-striping\n//\n// Default zebra-stripe styles (alternating gray and transparent backgrounds)\n\n.table-striped {\n  tbody tr:nth-of-type(#{$table-striped-order}) {\n    background-color: $table-accent-bg;\n  }\n}\n\n\n// Hover effect\n//\n// Placed here since it has to come after the potential zebra striping\n\n.table-hover {\n  tbody tr {\n    @include hover() {\n      color: $table-hover-color;\n      background-color: $table-hover-bg;\n    }\n  }\n}\n\n\n// Table backgrounds\n//\n// Exact selectors below required to override `.table-striped` and prevent\n// inheritance to nested tables.\n\n@each $color, $value in $theme-colors {\n  @include table-row-variant($color, theme-color-level($color, $table-bg-level), theme-color-level($color, $table-border-level));\n}\n\n@include table-row-variant(active, $table-active-bg);\n\n\n// Dark styles\n//\n// Same table markup, but inverted color scheme: dark background and light text.\n\n// stylelint-disable-next-line no-duplicate-selectors\n.table {\n  .thead-dark {\n    th {\n      color: $table-dark-color;\n      background-color: $table-dark-bg;\n      border-color: $table-dark-border-color;\n    }\n  }\n\n  .thead-light {\n    th {\n      color: $table-head-color;\n      background-color: $table-head-bg;\n      border-color: $table-border-color;\n    }\n  }\n}\n\n.table-dark {\n  color: $table-dark-color;\n  background-color: $table-dark-bg;\n\n  th,\n  td,\n  thead th {\n    border-color: $table-dark-border-color;\n  }\n\n  &.table-bordered {\n    border: 0;\n  }\n\n  &.table-striped {\n    tbody tr:nth-of-type(#{$table-striped-order}) {\n      background-color: $table-dark-accent-bg;\n    }\n  }\n\n  &.table-hover {\n    tbody tr {\n      @include hover() {\n        color: $table-dark-hover-color;\n        background-color: $table-dark-hover-bg;\n      }\n    }\n  }\n}\n\n\n// Responsive tables\n//\n// Generate series of `.table-responsive-*` classes for configuring the screen\n// size of where your table will overflow.\n\n.table-responsive {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $next: breakpoint-next($breakpoint, $grid-breakpoints);\n    $infix: breakpoint-infix($next, $grid-breakpoints);\n\n    &#{$infix} {\n      @include media-breakpoint-down($breakpoint) {\n        display: block;\n        width: 100%;\n        overflow-x: auto;\n        -webkit-overflow-scrolling: touch;\n\n        // Prevent double border on horizontal scroll due to use of `display: block;`\n        > .table-bordered {\n          border: 0;\n        }\n      }\n    }\n  }\n}\n", "// Tables\n\n@mixin table-row-variant($state, $background, $border: null) {\n  // Exact selectors below required to override `.table-striped` and prevent\n  // inheritance to nested tables.\n  .table-#{$state} {\n    &,\n    > th,\n    > td {\n      background-color: $background;\n    }\n\n    @if $border != null {\n      th,\n      td,\n      thead th,\n      tbody + tbody {\n        border-color: $border;\n      }\n    }\n  }\n\n  // Hover states for `.table-hover`\n  // Note: this is not available for cells or rows within `thead` or `tfoot`.\n  .table-hover {\n    $hover-background: darken($background, 5%);\n\n    .table-#{$state} {\n      @include hover() {\n        background-color: $hover-background;\n\n        > td,\n        > th {\n          background-color: $hover-background;\n        }\n      }\n    }\n  }\n}\n", "// Bootstrap functions\n//\n// Utility mixins and functions for evaluating source code across our variables, maps, and mixins.\n\n// Ascending\n// Used to evaluate Sass maps like our grid breakpoints.\n@mixin _assert-ascending($map, $map-name) {\n  $prev-key: null;\n  $prev-num: null;\n  @each $key, $num in $map {\n    @if $prev-num == null or unit($num) == \"%\" or unit($prev-num) == \"%\" {\n      // Do nothing\n    } @else if not comparable($prev-num, $num) {\n      @warn \"Potentially invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} whose unit makes it incomparable to #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    } @else if $prev-num >= $num {\n      @warn \"Invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} which isn't greater than #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    }\n    $prev-key: $key;\n    $prev-num: $num;\n  }\n}\n\n// Starts at zero\n// Used to ensure the min-width of the lowest breakpoint starts at 0.\n@mixin _assert-starts-at-zero($map, $map-name: \"$grid-breakpoints\") {\n  $values: map-values($map);\n  $first-value: nth($values, 1);\n  @if $first-value != 0 {\n    @warn \"First breakpoint in #{$map-name} must start at 0, but starts at #{$first-value}.\";\n  }\n}\n\n// Replace `$search` with `$replace` in `$string`\n// Used on our SVG icon backgrounds for custom forms.\n//\n// <AUTHOR> Giraudel\n// @param {String} $string - Initial string\n// @param {String} $search - Substring to replace\n// @param {String} $replace ('') - New value\n// @return {String} - Updated string\n@function str-replace($string, $search, $replace: \"\") {\n  $index: str-index($string, $search);\n\n  @if $index {\n    @return str-slice($string, 1, $index - 1) + $replace + str-replace(str-slice($string, $index + str-length($search)), $search, $replace);\n  }\n\n  @return $string;\n}\n\n// See https://codepen.io/kevinweber/pen/dXWoRw\n@function escape-svg($string) {\n  @if str-index($string, \"data:image/svg+xml\") {\n    @each $char, $encoded in $escaped-characters {\n      $string: str-replace($string, $char, $encoded);\n    }\n  }\n\n  @return $string;\n}\n\n// Color contrast\n@function color-yiq($color, $dark: $yiq-text-dark, $light: $yiq-text-light) {\n  $r: red($color);\n  $g: green($color);\n  $b: blue($color);\n\n  $yiq: (($r * 299) + ($g * 587) + ($b * 114)) / 1000;\n\n  @if ($yiq >= $yiq-contrasted-threshold) {\n    @return $dark;\n  } @else {\n    @return $light;\n  }\n}\n\n// Retrieve color Sass maps\n@function color($key: \"blue\") {\n  @return map-get($colors, $key);\n}\n\n@function theme-color($key: \"primary\") {\n  @return map-get($theme-colors, $key);\n}\n\n@function gray($key: \"100\") {\n  @return map-get($grays, $key);\n}\n\n// Request a theme color level\n@function theme-color-level($color-name: \"primary\", $level: 0) {\n  $color: theme-color($color-name);\n  $color-base: if($level > 0, $black, $white);\n  $level: abs($level);\n\n  @return mix($color-base, $color, $level * $theme-color-interval);\n}\n\n// Return valid calc\n@function add($value1, $value2, $return-calc: true) {\n  @if $value1 == null {\n    @return $value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 + $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} + #{$value2}), $value1 + unquote(\" + \") + $value2);\n}\n\n@function subtract($value1, $value2, $return-calc: true) {\n  @if $value1 == null and $value2 == null {\n    @return null;\n  }\n\n  @if $value1 == null {\n    @return -$value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 - $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} - #{$value2}), $value1 + unquote(\" - \") + $value2);\n}\n", "// stylelint-disable selector-no-qualifying-type\n\n//\n// Textual form controls\n//\n\n.form-control {\n  display: block;\n  width: 100%;\n  height: $input-height;\n  padding: $input-padding-y $input-padding-x;\n  font-family: $input-font-family;\n  @include font-size($input-font-size);\n  font-weight: $input-font-weight;\n  line-height: $input-line-height;\n  color: $input-color;\n  background-color: $input-bg;\n  background-clip: padding-box;\n  border: $input-border-width solid $input-border-color;\n\n  // Note: This has no effect on <select>s in some browsers, due to the limited stylability of `<select>`s in CSS.\n  @include border-radius($input-border-radius, 0);\n\n  @include box-shadow($input-box-shadow);\n  @include transition($input-transition);\n\n  // Unstyle the caret on `<select>`s in IE10+.\n  &::-ms-expand {\n    background-color: transparent;\n    border: 0;\n  }\n\n  // Remove select outline from select box in FF\n  &:-moz-focusring {\n    color: transparent;\n    text-shadow: 0 0 0 $input-color;\n  }\n\n  // Customize the `:focus` state to imitate native WebKit styles.\n  @include form-control-focus($ignore-warning: true);\n\n  // Placeholder\n  &::placeholder {\n    color: $input-placeholder-color;\n    // Override Firefox's unusual default opacity; see https://github.com/twbs/bootstrap/pull/11526.\n    opacity: 1;\n  }\n\n  // Disabled and read-only inputs\n  //\n  // HTML5 says that controls under a fieldset > legend:first-child won't be\n  // disabled if the fieldset is disabled. Due to implementation difficulty, we\n  // don't honor that edge case; we style them as disabled anyway.\n  &:disabled,\n  &[readonly] {\n    background-color: $input-disabled-bg;\n    // iOS fix for unreadable disabled content; see https://github.com/twbs/bootstrap/issues/11655.\n    opacity: 1;\n  }\n}\n\nselect.form-control {\n  &:focus::-ms-value {\n    // Suppress the nested default white text on blue background highlight given to\n    // the selected option text when the (still closed) <select> receives focus\n    // in IE and (under certain conditions) Edge, as it looks bad and cannot be made to\n    // match the appearance of the native widget.\n    // See https://github.com/twbs/bootstrap/issues/19398.\n    color: $input-color;\n    background-color: $input-bg;\n  }\n}\n\n// Make file inputs better match text inputs by forcing them to new lines.\n.form-control-file,\n.form-control-range {\n  display: block;\n  width: 100%;\n}\n\n\n//\n// Labels\n//\n\n// For use with horizontal and inline forms, when you need the label (or legend)\n// text to align with the form controls.\n.col-form-label {\n  padding-top: add($input-padding-y, $input-border-width);\n  padding-bottom: add($input-padding-y, $input-border-width);\n  margin-bottom: 0; // Override the `<label>/<legend>` default\n  @include font-size(inherit); // Override the `<legend>` default\n  line-height: $input-line-height;\n}\n\n.col-form-label-lg {\n  padding-top: add($input-padding-y-lg, $input-border-width);\n  padding-bottom: add($input-padding-y-lg, $input-border-width);\n  @include font-size($input-font-size-lg);\n  line-height: $input-line-height-lg;\n}\n\n.col-form-label-sm {\n  padding-top: add($input-padding-y-sm, $input-border-width);\n  padding-bottom: add($input-padding-y-sm, $input-border-width);\n  @include font-size($input-font-size-sm);\n  line-height: $input-line-height-sm;\n}\n\n\n// Readonly controls as plain text\n//\n// Apply class to a readonly input to make it appear like regular plain\n// text (without any border, background color, focus indicator)\n\n.form-control-plaintext {\n  display: block;\n  width: 100%;\n  padding: $input-padding-y 0;\n  margin-bottom: 0; // match inputs if this class comes on inputs with default margins\n  @include font-size($input-font-size);\n  line-height: $input-line-height;\n  color: $input-plaintext-color;\n  background-color: transparent;\n  border: solid transparent;\n  border-width: $input-border-width 0;\n\n  &.form-control-sm,\n  &.form-control-lg {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n\n// Form control sizing\n//\n// Build on `.form-control` with modifier classes to decrease or increase the\n// height and font-size of form controls.\n//\n// Repeated in `_input_group.scss` to avoid Sass extend issues.\n\n.form-control-sm {\n  height: $input-height-sm;\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  line-height: $input-line-height-sm;\n  @include border-radius($input-border-radius-sm);\n}\n\n.form-control-lg {\n  height: $input-height-lg;\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  line-height: $input-line-height-lg;\n  @include border-radius($input-border-radius-lg);\n}\n\n// stylelint-disable-next-line no-duplicate-selectors\nselect.form-control {\n  &[size],\n  &[multiple] {\n    height: auto;\n  }\n}\n\ntextarea.form-control {\n  height: auto;\n}\n\n// Form groups\n//\n// Designed to help with the organization and spacing of vertical forms. For\n// horizontal forms, use the predefined grid classes.\n\n.form-group {\n  margin-bottom: $form-group-margin-bottom;\n}\n\n.form-text {\n  display: block;\n  margin-top: $form-text-margin-top;\n}\n\n\n// Form grid\n//\n// Special replacement for our grid system's `.row` for tighter form layouts.\n\n.form-row {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -$form-grid-gutter-width / 2;\n  margin-left: -$form-grid-gutter-width / 2;\n\n  > .col,\n  > [class*=\"col-\"] {\n    padding-right: $form-grid-gutter-width / 2;\n    padding-left: $form-grid-gutter-width / 2;\n  }\n}\n\n\n// Checkboxes and radios\n//\n// Indent the labels to position radios/checkboxes as hanging controls.\n\n.form-check {\n  position: relative;\n  display: block;\n  padding-left: $form-check-input-gutter;\n}\n\n.form-check-input {\n  position: absolute;\n  margin-top: $form-check-input-margin-y;\n  margin-left: -$form-check-input-gutter;\n\n  // Use [disabled] and :disabled for workaround https://github.com/twbs/bootstrap/issues/28247\n  &[disabled] ~ .form-check-label,\n  &:disabled ~ .form-check-label {\n    color: $text-muted;\n  }\n}\n\n.form-check-label {\n  margin-bottom: 0; // Override default `<label>` bottom margin\n}\n\n.form-check-inline {\n  display: inline-flex;\n  align-items: center;\n  padding-left: 0; // Override base .form-check\n  margin-right: $form-check-inline-margin-x;\n\n  // Undo .form-check-input defaults and add some `margin-right`.\n  .form-check-input {\n    position: static;\n    margin-top: 0;\n    margin-right: $form-check-inline-input-margin-x;\n    margin-left: 0;\n  }\n}\n\n\n// Form validation\n//\n// Provide feedback to users when form field values are valid or invalid. Works\n// primarily for client-side validation via scoped `:invalid` and `:valid`\n// pseudo-classes but also includes `.is-invalid` and `.is-valid` classes for\n// server side validation.\n\n@each $state, $data in $form-validation-states {\n  @include form-validation-state($state, map-get($data, color), map-get($data, icon));\n}\n\n// Inline forms\n//\n// Make forms appear inline(-block) by adding the `.form-inline` class. Inline\n// forms begin stacked on extra small (mobile) devices and then go inline when\n// viewports reach <768px.\n//\n// Requires wrapping inputs and labels with `.form-group` for proper display of\n// default HTML form controls and our custom form controls (e.g., input groups).\n\n.form-inline {\n  display: flex;\n  flex-flow: row wrap;\n  align-items: center; // Prevent shorter elements from growing to same height as others (e.g., small buttons growing to normal sized button height)\n\n  // Because we use flex, the initial sizing of checkboxes is collapsed and\n  // doesn't occupy the full-width (which is what we want for xs grid tier),\n  // so we force that here.\n  .form-check {\n    width: 100%;\n  }\n\n  // Kick in the inline\n  @include media-breakpoint-up(sm) {\n    label {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 0;\n    }\n\n    // Inline-block all the things for \"inline\"\n    .form-group {\n      display: flex;\n      flex: 0 0 auto;\n      flex-flow: row wrap;\n      align-items: center;\n      margin-bottom: 0;\n    }\n\n    // Allow folks to *not* use `.form-group`\n    .form-control {\n      display: inline-block;\n      width: auto; // Prevent labels from stacking above inputs in `.form-group`\n      vertical-align: middle;\n    }\n\n    // Make static controls behave like regular ones\n    .form-control-plaintext {\n      display: inline-block;\n    }\n\n    .input-group,\n    .custom-select {\n      width: auto;\n    }\n\n    // Remove default margin on radios/checkboxes that were used for stacking, and\n    // then undo the floating of radios and checkboxes to match.\n    .form-check {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: auto;\n      padding-left: 0;\n    }\n    .form-check-input {\n      position: relative;\n      flex-shrink: 0;\n      margin-top: 0;\n      margin-right: $form-check-input-margin-x;\n      margin-left: 0;\n    }\n\n    .custom-control {\n      align-items: center;\n      justify-content: center;\n    }\n    .custom-control-label {\n      margin-bottom: 0;\n    }\n  }\n}\n", "// stylelint-disable property-blacklist\n@mixin transition($transition...) {\n  @if $enable-transitions {\n    @if length($transition) == 0 {\n      transition: $transition-base;\n    } @else {\n      transition: $transition;\n    }\n  }\n\n  @if $enable-prefers-reduced-motion-media-query {\n    @media (prefers-reduced-motion: reduce) {\n      transition: none;\n    }\n  }\n}\n", "// Form control focus state\n//\n// Generate a customized focus state and for any input with the specified color,\n// which defaults to the `$input-focus-border-color` variable.\n//\n// We highly encourage you to not customize the default value, but instead use\n// this to tweak colors on an as-needed basis. This aesthetic change is based on\n// WebKit's default styles, but applicable to a wider range of browsers. Its\n// usability and accessibility should be taken into account with any change.\n//\n// Example usage: change the default blue border and shadow to white for better\n// contrast against a dark gray background.\n@mixin form-control-focus($ignore-warning: false) {\n  &:focus {\n    color: $input-focus-color;\n    background-color: $input-focus-bg;\n    border-color: $input-focus-border-color;\n    outline: 0;\n    // Avoid using mixin so we can pass custom focus shadow properly\n    @if $enable-shadows {\n      box-shadow: $input-box-shadow, $input-focus-box-shadow;\n    } @else {\n      box-shadow: $input-focus-box-shadow;\n    }\n  }\n  @include deprecate(\"The `form-control-focus()` mixin\", \"v4.4.0\", \"v5\", $ignore-warning);\n}\n\n// This mixin uses an `if()` technique to be compatible with Dart Sass\n// See https://github.com/sass/sass/issues/1873#issuecomment-********* for more details\n@mixin form-validation-state-selector($state) {\n  @if ($state == \"valid\" or $state == \"invalid\") {\n    .was-validated #{if(&, \"&\", \"\")}:#{$state},\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  } @else {\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  }\n}\n\n@mixin form-validation-state($state, $color, $icon) {\n  .#{$state}-feedback {\n    display: none;\n    width: 100%;\n    margin-top: $form-feedback-margin-top;\n    @include font-size($form-feedback-font-size);\n    color: $color;\n  }\n\n  .#{$state}-tooltip {\n    position: absolute;\n    top: 100%;\n    z-index: 5;\n    display: none;\n    max-width: 100%; // Contain to parent when possible\n    padding: $form-feedback-tooltip-padding-y $form-feedback-tooltip-padding-x;\n    margin-top: .1rem;\n    @include font-size($form-feedback-tooltip-font-size);\n    line-height: $form-feedback-tooltip-line-height;\n    color: color-yiq($color);\n    background-color: rgba($color, $form-feedback-tooltip-opacity);\n    @include border-radius($form-feedback-tooltip-border-radius);\n  }\n\n  @include form-validation-state-selector($state) {\n    ~ .#{$state}-feedback,\n    ~ .#{$state}-tooltip {\n      display: block;\n    }\n  }\n\n  .form-control {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-image: escape-svg($icon);\n        background-repeat: no-repeat;\n        background-position: right $input-height-inner-quarter center;\n        background-size: $input-height-inner-half $input-height-inner-half;\n      }\n\n      &:focus {\n        border-color: $color;\n        box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n      }\n    }\n  }\n\n  // stylelint-disable-next-line selector-no-qualifying-type\n  textarea.form-control {\n    @include form-validation-state-selector($state) {\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-position: top $input-height-inner-quarter right $input-height-inner-quarter;\n      }\n    }\n  }\n\n  .custom-select {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      @if $enable-validation-icons {\n        padding-right: $custom-select-feedback-icon-padding-right;\n        background: $custom-select-background, escape-svg($icon) $custom-select-bg no-repeat $custom-select-feedback-icon-position / $custom-select-feedback-icon-size;\n      }\n\n      &:focus {\n        border-color: $color;\n        box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n      }\n    }\n  }\n\n  .form-check-input {\n    @include form-validation-state-selector($state) {\n      ~ .form-check-label {\n        color: $color;\n      }\n\n      ~ .#{$state}-feedback,\n      ~ .#{$state}-tooltip {\n        display: block;\n      }\n    }\n  }\n\n  .custom-control-input {\n    @include form-validation-state-selector($state) {\n      ~ .custom-control-label {\n        color: $color;\n\n        &::before {\n          border-color: $color;\n        }\n      }\n\n      &:checked {\n        ~ .custom-control-label::before {\n          border-color: lighten($color, 10%);\n          @include gradient-bg(lighten($color, 10%));\n        }\n      }\n\n      &:focus {\n        ~ .custom-control-label::before {\n          box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n        }\n\n        &:not(:checked) ~ .custom-control-label::before {\n          border-color: $color;\n        }\n      }\n    }\n  }\n\n  // custom file\n  .custom-file-input {\n    @include form-validation-state-selector($state) {\n      ~ .custom-file-label {\n        border-color: $color;\n      }\n\n      &:focus {\n        ~ .custom-file-label {\n          border-color: $color;\n          box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n        }\n      }\n    }\n  }\n}\n", "// Gradients\n\n@mixin gradient-bg($color) {\n  @if $enable-gradients {\n    background: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x;\n  } @else {\n    background-color: $color;\n  }\n}\n\n// Horizontal gradient, from left to right\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-x($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n// Vertical gradient, from top to bottom\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-y($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n@mixin gradient-directional($start-color: $gray-700, $end-color: $gray-800, $deg: 45deg) {\n  background-image: linear-gradient($deg, $start-color, $end-color);\n  background-repeat: repeat-x;\n}\n@mixin gradient-x-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-y-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-radial($inner-color: $gray-700, $outer-color: $gray-800) {\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-striped($color: rgba($white, .15), $angle: 45deg) {\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n}\n", "// stylelint-disable selector-no-qualifying-type\n\n//\n// Base styles\n//\n\n.btn {\n  display: inline-block;\n  font-family: $btn-font-family;\n  font-weight: $btn-font-weight;\n  color: $body-color;\n  text-align: center;\n  white-space: $btn-white-space;\n  vertical-align: middle;\n  cursor: if($enable-pointer-cursor-for-buttons, pointer, null);\n  user-select: none;\n  background-color: transparent;\n  border: $btn-border-width solid transparent;\n  @include button-size($btn-padding-y, $btn-padding-x, $btn-font-size, $btn-line-height, $btn-border-radius);\n  @include transition($btn-transition);\n\n  @include hover() {\n    color: $body-color;\n    text-decoration: none;\n  }\n\n  &:focus,\n  &.focus {\n    outline: 0;\n    box-shadow: $btn-focus-box-shadow;\n  }\n\n  // Disabled comes first so active can properly restyle\n  &.disabled,\n  &:disabled {\n    opacity: $btn-disabled-opacity;\n    @include box-shadow(none);\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active {\n    @include box-shadow($btn-active-box-shadow);\n\n    &:focus {\n      @include box-shadow($btn-focus-box-shadow, $btn-active-box-shadow);\n    }\n  }\n}\n\n// Future-proof disabling of clicks on `<a>` elements\na.btn.disabled,\nfieldset:disabled a.btn {\n  pointer-events: none;\n}\n\n\n//\n// Alternate buttons\n//\n\n@each $color, $value in $theme-colors {\n  .btn-#{$color} {\n    @include button-variant($value, $value);\n  }\n}\n\n@each $color, $value in $theme-colors {\n  .btn-outline-#{$color} {\n    @include button-outline-variant($value);\n  }\n}\n\n\n//\n// Link buttons\n//\n\n// Make a button look and behave like a link\n.btn-link {\n  font-weight: $font-weight-normal;\n  color: $link-color;\n  text-decoration: $link-decoration;\n\n  @include hover() {\n    color: $link-hover-color;\n    text-decoration: $link-hover-decoration;\n  }\n\n  &:focus,\n  &.focus {\n    text-decoration: $link-hover-decoration;\n    box-shadow: none;\n  }\n\n  &:disabled,\n  &.disabled {\n    color: $btn-link-disabled-color;\n    pointer-events: none;\n  }\n\n  // No need for an active state here\n}\n\n\n//\n// Button Sizes\n//\n\n.btn-lg {\n  @include button-size($btn-padding-y-lg, $btn-padding-x-lg, $btn-font-size-lg, $btn-line-height-lg, $btn-border-radius-lg);\n}\n\n.btn-sm {\n  @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $btn-font-size-sm, $btn-line-height-sm, $btn-border-radius-sm);\n}\n\n\n//\n// Block button\n//\n\n.btn-block {\n  display: block;\n  width: 100%;\n\n  // Vertically space out multiple block buttons\n  + .btn-block {\n    margin-top: $btn-block-spacing-y;\n  }\n}\n\n// Specificity overrides\ninput[type=\"submit\"],\ninput[type=\"reset\"],\ninput[type=\"button\"] {\n  &.btn-block {\n    width: 100%;\n  }\n}\n", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n@mixin button-variant($background, $border, $hover-background: darken($background, 7.5%), $hover-border: darken($border, 10%), $active-background: darken($background, 10%), $active-border: darken($border, 12.5%)) {\n  color: color-yiq($background);\n  @include gradient-bg($background);\n  border-color: $border;\n  @include box-shadow($btn-box-shadow);\n\n  @include hover() {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n  }\n\n  &:focus,\n  &.focus {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n    // Avoid using mixin so we can pass custom focus shadow properly\n    @if $enable-shadows {\n      box-shadow: $btn-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n    } @else {\n      box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n    }\n  }\n\n  // Disabled comes first so active can properly restyle\n  &.disabled,\n  &:disabled {\n    color: color-yiq($background);\n    background-color: $background;\n    border-color: $border;\n    // Remove CSS gradients if they're enabled\n    @if $enable-gradients {\n      background-image: none;\n    }\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    @if $enable-gradients {\n      background-image: none; // Remove the gradient for the pressed/active state\n    }\n    border-color: $active-border;\n\n    &:focus {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        box-shadow: $btn-active-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n      } @else {\n        box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n      }\n    }\n  }\n}\n\n@mixin button-outline-variant($color, $color-hover: color-yiq($color), $active-background: $color, $active-border: $color) {\n  color: $color;\n  border-color: $color;\n\n  @include hover() {\n    color: $color-hover;\n    background-color: $active-background;\n    border-color: $active-border;\n  }\n\n  &:focus,\n  &.focus {\n    box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $color;\n    background-color: transparent;\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    border-color: $active-border;\n\n    &:focus {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        box-shadow: $btn-active-box-shadow, 0 0 0 $btn-focus-width rgba($color, .5);\n      } @else {\n        box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n      }\n    }\n  }\n}\n\n// Button sizes\n@mixin button-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\n  padding: $padding-y $padding-x;\n  @include font-size($font-size);\n  line-height: $line-height;\n  // Manually declare to provide an override to the browser default\n  @include border-radius($border-radius, 0);\n}\n", ".fade {\n  @include transition($transition-fade);\n\n  &:not(.show) {\n    opacity: 0;\n  }\n}\n\n.collapse {\n  &:not(.show) {\n    display: none;\n  }\n}\n\n.collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  @include transition($transition-collapse);\n}\n", "// The dropdown wrapper (`<div>`)\n.dropup,\n.dropright,\n.dropdown,\n.dropleft {\n  position: relative;\n}\n\n.dropdown-toggle {\n  white-space: nowrap;\n\n  // Generate the caret automatically\n  @include caret();\n}\n\n// The dropdown menu\n.dropdown-menu {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  z-index: $zindex-dropdown;\n  display: none; // none by default, but block on \"open\" of the menu\n  float: left;\n  min-width: $dropdown-min-width;\n  padding: $dropdown-padding-y 0;\n  margin: $dropdown-spacer 0 0; // override default ul\n  @include font-size($dropdown-font-size);\n  color: $dropdown-color;\n  text-align: left; // Ensures proper alignment if parent has it changed (e.g., modal footer)\n  list-style: none;\n  background-color: $dropdown-bg;\n  background-clip: padding-box;\n  border: $dropdown-border-width solid $dropdown-border-color;\n  @include border-radius($dropdown-border-radius);\n  @include box-shadow($dropdown-box-shadow);\n}\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .dropdown-menu#{$infix}-left {\n      right: auto;\n      left: 0;\n    }\n\n    .dropdown-menu#{$infix}-right {\n      right: 0;\n      left: auto;\n    }\n  }\n}\n\n// Allow for dropdowns to go bottom up (aka, dropup-menu)\n// Just add .dropup after the standard .dropdown class and you're set.\n.dropup {\n  .dropdown-menu {\n    top: auto;\n    bottom: 100%;\n    margin-top: 0;\n    margin-bottom: $dropdown-spacer;\n  }\n\n  .dropdown-toggle {\n    @include caret(up);\n  }\n}\n\n.dropright {\n  .dropdown-menu {\n    top: 0;\n    right: auto;\n    left: 100%;\n    margin-top: 0;\n    margin-left: $dropdown-spacer;\n  }\n\n  .dropdown-toggle {\n    @include caret(right);\n    &::after {\n      vertical-align: 0;\n    }\n  }\n}\n\n.dropleft {\n  .dropdown-menu {\n    top: 0;\n    right: 100%;\n    left: auto;\n    margin-top: 0;\n    margin-right: $dropdown-spacer;\n  }\n\n  .dropdown-toggle {\n    @include caret(left);\n    &::before {\n      vertical-align: 0;\n    }\n  }\n}\n\n// When enabled Popper.js, reset basic dropdown position\n// stylelint-disable-next-line no-duplicate-selectors\n.dropdown-menu {\n  &[x-placement^=\"top\"],\n  &[x-placement^=\"right\"],\n  &[x-placement^=\"bottom\"],\n  &[x-placement^=\"left\"] {\n    right: auto;\n    bottom: auto;\n  }\n}\n\n// Dividers (basically an `<hr>`) within the dropdown\n.dropdown-divider {\n  @include nav-divider($dropdown-divider-bg, $dropdown-divider-margin-y, true);\n}\n\n// Links, buttons, and more within the dropdown menu\n//\n// `<button>`-specific styles are denoted with `// For <button>s`\n.dropdown-item {\n  display: block;\n  width: 100%; // For `<button>`s\n  padding: $dropdown-item-padding-y $dropdown-item-padding-x;\n  clear: both;\n  font-weight: $font-weight-normal;\n  color: $dropdown-link-color;\n  text-align: inherit; // For `<button>`s\n  white-space: nowrap; // prevent links from randomly breaking onto new lines\n  background-color: transparent; // For `<button>`s\n  border: 0; // For `<button>`s\n\n  // Prevent dropdown overflow if there's no padding\n  // See https://github.com/twbs/bootstrap/pull/27703\n  @if $dropdown-padding-y == 0 {\n    &:first-child {\n      @include border-top-radius($dropdown-inner-border-radius);\n    }\n\n    &:last-child {\n      @include border-bottom-radius($dropdown-inner-border-radius);\n    }\n  }\n\n  @include hover-focus() {\n    color: $dropdown-link-hover-color;\n    text-decoration: none;\n    @include gradient-bg($dropdown-link-hover-bg);\n  }\n\n  &.active,\n  &:active {\n    color: $dropdown-link-active-color;\n    text-decoration: none;\n    @include gradient-bg($dropdown-link-active-bg);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $dropdown-link-disabled-color;\n    pointer-events: none;\n    background-color: transparent;\n    // Remove CSS gradients if they're enabled\n    @if $enable-gradients {\n      background-image: none;\n    }\n  }\n}\n\n.dropdown-menu.show {\n  display: block;\n}\n\n// Dropdown section headers\n.dropdown-header {\n  display: block;\n  padding: $dropdown-padding-y $dropdown-item-padding-x;\n  margin-bottom: 0; // for use with heading elements\n  @include font-size($font-size-sm);\n  color: $dropdown-header-color;\n  white-space: nowrap; // as with > li > a\n}\n\n// Dropdown text\n.dropdown-item-text {\n  display: block;\n  padding: $dropdown-item-padding-y $dropdown-item-padding-x;\n  color: $dropdown-link-color;\n}\n", "@mixin caret-down() {\n  border-top: $caret-width solid;\n  border-right: $caret-width solid transparent;\n  border-bottom: 0;\n  border-left: $caret-width solid transparent;\n}\n\n@mixin caret-up() {\n  border-top: 0;\n  border-right: $caret-width solid transparent;\n  border-bottom: $caret-width solid;\n  border-left: $caret-width solid transparent;\n}\n\n@mixin caret-right() {\n  border-top: $caret-width solid transparent;\n  border-right: 0;\n  border-bottom: $caret-width solid transparent;\n  border-left: $caret-width solid;\n}\n\n@mixin caret-left() {\n  border-top: $caret-width solid transparent;\n  border-right: $caret-width solid;\n  border-bottom: $caret-width solid transparent;\n}\n\n@mixin caret($direction: down) {\n  @if $enable-caret {\n    &::after {\n      display: inline-block;\n      margin-left: $caret-spacing;\n      vertical-align: $caret-vertical-align;\n      content: \"\";\n      @if $direction == down {\n        @include caret-down();\n      } @else if $direction == up {\n        @include caret-up();\n      } @else if $direction == right {\n        @include caret-right();\n      }\n    }\n\n    @if $direction == left {\n      &::after {\n        display: none;\n      }\n\n      &::before {\n        display: inline-block;\n        margin-right: $caret-spacing;\n        vertical-align: $caret-vertical-align;\n        content: \"\";\n        @include caret-left();\n      }\n    }\n\n    &:empty::after {\n      margin-left: 0;\n    }\n  }\n}\n", "// Horizontal dividers\n//\n// Dividers (basically an hr) within dropdowns and nav lists\n\n@mixin nav-divider($color: $nav-divider-color, $margin-y: $nav-divider-margin-y, $ignore-warning: false) {\n  height: 0;\n  margin: $margin-y 0;\n  overflow: hidden;\n  border-top: 1px solid $color;\n  @include deprecate(\"The `nav-divider()` mixin\", \"v4.4.0\", \"v5\", $ignore-warning);\n}\n", "// stylelint-disable selector-no-qualifying-type\n\n// Make the div behave like a button\n.btn-group,\n.btn-group-vertical {\n  position: relative;\n  display: inline-flex;\n  vertical-align: middle; // match .btn alignment given font-size hack above\n\n  > .btn {\n    position: relative;\n    flex: 1 1 auto;\n\n    // Bring the hover, focused, and \"active\" buttons to the front to overlay\n    // the borders properly\n    @include hover() {\n      z-index: 1;\n    }\n    &:focus,\n    &:active,\n    &.active {\n      z-index: 1;\n    }\n  }\n}\n\n// Optional: Group multiple button groups together for a toolbar\n.btn-toolbar {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n\n  .input-group {\n    width: auto;\n  }\n}\n\n.btn-group {\n  // Prevent double borders when buttons are next to each other\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) {\n    margin-left: -$btn-border-width;\n  }\n\n  // Reset rounded corners\n  > .btn:not(:last-child):not(.dropdown-toggle),\n  > .btn-group:not(:last-child) > .btn {\n    @include border-right-radius(0);\n  }\n\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) > .btn {\n    @include border-left-radius(0);\n  }\n}\n\n// Sizing\n//\n// Remix the default button sizing classes into new ones for easier manipulation.\n\n.btn-group-sm > .btn { @extend .btn-sm; }\n.btn-group-lg > .btn { @extend .btn-lg; }\n\n\n//\n// Split button dropdowns\n//\n\n.dropdown-toggle-split {\n  padding-right: $btn-padding-x * .75;\n  padding-left: $btn-padding-x * .75;\n\n  &::after,\n  .dropup &::after,\n  .dropright &::after {\n    margin-left: 0;\n  }\n\n  .dropleft &::before {\n    margin-right: 0;\n  }\n}\n\n.btn-sm + .dropdown-toggle-split {\n  padding-right: $btn-padding-x-sm * .75;\n  padding-left: $btn-padding-x-sm * .75;\n}\n\n.btn-lg + .dropdown-toggle-split {\n  padding-right: $btn-padding-x-lg * .75;\n  padding-left: $btn-padding-x-lg * .75;\n}\n\n\n// The clickable button for toggling the menu\n// Set the same inset shadow as the :active state\n.btn-group.show .dropdown-toggle {\n  @include box-shadow($btn-active-box-shadow);\n\n  // Show no shadow for `.btn-link` since it has no other button styles.\n  &.btn-link {\n    @include box-shadow(none);\n  }\n}\n\n\n//\n// Vertical button groups\n//\n\n.btn-group-vertical {\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center;\n\n  > .btn,\n  > .btn-group {\n    width: 100%;\n  }\n\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) {\n    margin-top: -$btn-border-width;\n  }\n\n  // Reset rounded corners\n  > .btn:not(:last-child):not(.dropdown-toggle),\n  > .btn-group:not(:last-child) > .btn {\n    @include border-bottom-radius(0);\n  }\n\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) > .btn {\n    @include border-top-radius(0);\n  }\n}\n\n\n// Checkbox and radio options\n//\n// In order to support the browser's form validation feedback, powered by the\n// `required` attribute, we have to \"hide\" the inputs via `clip`. We cannot use\n// `display: none;` or `visibility: hidden;` as that also hides the popover.\n// Simply visually hiding the inputs via `opacity` would leave them clickable in\n// certain cases which is prevented by using `clip` and `pointer-events`.\n// This way, we ensure a DOM element is visible to position the popover from.\n//\n// See https://github.com/twbs/bootstrap/pull/12794 and\n// https://github.com/twbs/bootstrap/pull/14559 for more information.\n\n.btn-group-toggle {\n  > .btn,\n  > .btn-group > .btn {\n    margin-bottom: 0; // Override default `<label>` value\n\n    input[type=\"radio\"],\n    input[type=\"checkbox\"] {\n      position: absolute;\n      clip: rect(0, 0, 0, 0);\n      pointer-events: none;\n    }\n  }\n}\n", "// stylelint-disable selector-no-qualifying-type\n\n//\n// Base styles\n//\n\n.input-group {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // For form validation feedback\n  align-items: stretch;\n  width: 100%;\n\n  > .form-control,\n  > .form-control-plaintext,\n  > .custom-select,\n  > .custom-file {\n    position: relative; // For focus state's z-index\n    flex: 1 1 0%;\n    min-width: 0; // https://stackoverflow.com/questions/36247140/why-dont-flex-items-shrink-past-content-size\n    margin-bottom: 0;\n\n    + .form-control,\n    + .custom-select,\n    + .custom-file {\n      margin-left: -$input-border-width;\n    }\n  }\n\n  // Bring the \"active\" form control to the top of surrounding elements\n  > .form-control:focus,\n  > .custom-select:focus,\n  > .custom-file .custom-file-input:focus ~ .custom-file-label {\n    z-index: 3;\n  }\n\n  // Bring the custom file input above the label\n  > .custom-file .custom-file-input:focus {\n    z-index: 4;\n  }\n\n  > .form-control,\n  > .custom-select {\n    &:not(:last-child) { @include border-right-radius(0); }\n    &:not(:first-child) { @include border-left-radius(0); }\n  }\n\n  // Custom file inputs have more complex markup, thus requiring different\n  // border-radius overrides.\n  > .custom-file {\n    display: flex;\n    align-items: center;\n\n    &:not(:last-child) .custom-file-label,\n    &:not(:last-child) .custom-file-label::after { @include border-right-radius(0); }\n    &:not(:first-child) .custom-file-label { @include border-left-radius(0); }\n  }\n}\n\n\n// Prepend and append\n//\n// While it requires one extra layer of HTML for each, dedicated prepend and\n// append elements allow us to 1) be less clever, 2) simplify our selectors, and\n// 3) support HTML5 form validation.\n\n.input-group-prepend,\n.input-group-append {\n  display: flex;\n\n  // Ensure buttons are always above inputs for more visually pleasing borders.\n  // This isn't needed for `.input-group-text` since it shares the same border-color\n  // as our inputs.\n  .btn {\n    position: relative;\n    z-index: 2;\n\n    &:focus {\n      z-index: 3;\n    }\n  }\n\n  .btn + .btn,\n  .btn + .input-group-text,\n  .input-group-text + .input-group-text,\n  .input-group-text + .btn {\n    margin-left: -$input-border-width;\n  }\n}\n\n.input-group-prepend { margin-right: -$input-border-width; }\n.input-group-append { margin-left: -$input-border-width; }\n\n\n// Textual addons\n//\n// Serves as a catch-all element for any text or radio/checkbox input you wish\n// to prepend or append to an input.\n\n.input-group-text {\n  display: flex;\n  align-items: center;\n  padding: $input-padding-y $input-padding-x;\n  margin-bottom: 0; // Allow use of <label> elements by overriding our default margin-bottom\n  @include font-size($input-font-size); // Match inputs\n  font-weight: $font-weight-normal;\n  line-height: $input-line-height;\n  color: $input-group-addon-color;\n  text-align: center;\n  white-space: nowrap;\n  background-color: $input-group-addon-bg;\n  border: $input-border-width solid $input-group-addon-border-color;\n  @include border-radius($input-border-radius);\n\n  // Nuke default margins from checkboxes and radios to vertically center within.\n  input[type=\"radio\"],\n  input[type=\"checkbox\"] {\n    margin-top: 0;\n  }\n}\n\n\n// Sizing\n//\n// Remix the default form control sizing classes into new ones for easier\n// manipulation.\n\n.input-group-lg > .form-control:not(textarea),\n.input-group-lg > .custom-select {\n  height: $input-height-lg;\n}\n\n.input-group-lg > .form-control,\n.input-group-lg > .custom-select,\n.input-group-lg > .input-group-prepend > .input-group-text,\n.input-group-lg > .input-group-append > .input-group-text,\n.input-group-lg > .input-group-prepend > .btn,\n.input-group-lg > .input-group-append > .btn {\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  line-height: $input-line-height-lg;\n  @include border-radius($input-border-radius-lg);\n}\n\n.input-group-sm > .form-control:not(textarea),\n.input-group-sm > .custom-select {\n  height: $input-height-sm;\n}\n\n.input-group-sm > .form-control,\n.input-group-sm > .custom-select,\n.input-group-sm > .input-group-prepend > .input-group-text,\n.input-group-sm > .input-group-append > .input-group-text,\n.input-group-sm > .input-group-prepend > .btn,\n.input-group-sm > .input-group-append > .btn {\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  line-height: $input-line-height-sm;\n  @include border-radius($input-border-radius-sm);\n}\n\n.input-group-lg > .custom-select,\n.input-group-sm > .custom-select {\n  padding-right: $custom-select-padding-x + $custom-select-indicator-padding;\n}\n\n\n// Prepend and append rounded corners\n//\n// These rulesets must come after the sizing ones to properly override sm and lg\n// border-radius values when extending. They're more specific than we'd like\n// with the `.input-group >` part, but without it, we cannot override the sizing.\n\n\n.input-group > .input-group-prepend > .btn,\n.input-group > .input-group-prepend > .input-group-text,\n.input-group > .input-group-append:not(:last-child) > .btn,\n.input-group > .input-group-append:not(:last-child) > .input-group-text,\n.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),\n.input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {\n  @include border-right-radius(0);\n}\n\n.input-group > .input-group-append > .btn,\n.input-group > .input-group-append > .input-group-text,\n.input-group > .input-group-prepend:not(:first-child) > .btn,\n.input-group > .input-group-prepend:not(:first-child) > .input-group-text,\n.input-group > .input-group-prepend:first-child > .btn:not(:first-child),\n.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {\n  @include border-left-radius(0);\n}\n", "// Embedded icons from Open Iconic.\n// Released under MIT and copyright 2014 Waybury.\n// https://useiconic.com/open\n\n\n// Checkboxes and radios\n//\n// Base class takes care of all the key behavioral aspects.\n\n.custom-control {\n  position: relative;\n  display: block;\n  min-height: $font-size-base * $line-height-base;\n  padding-left: $custom-control-gutter + $custom-control-indicator-size;\n}\n\n.custom-control-inline {\n  display: inline-flex;\n  margin-right: $custom-control-spacer-x;\n}\n\n.custom-control-input {\n  position: absolute;\n  left: 0;\n  z-index: -1; // Put the input behind the label so it doesn't overlay text\n  width: $custom-control-indicator-size;\n  height: ($font-size-base * $line-height-base + $custom-control-indicator-size) / 2;\n  opacity: 0;\n\n  &:checked ~ .custom-control-label::before {\n    color: $custom-control-indicator-checked-color;\n    border-color: $custom-control-indicator-checked-border-color;\n    @include gradient-bg($custom-control-indicator-checked-bg);\n    @include box-shadow($custom-control-indicator-checked-box-shadow);\n  }\n\n  &:focus ~ .custom-control-label::before {\n    // the mixin is not used here to make sure there is feedback\n    @if $enable-shadows {\n      box-shadow: $input-box-shadow, $input-focus-box-shadow;\n    } @else {\n      box-shadow: $custom-control-indicator-focus-box-shadow;\n    }\n  }\n\n  &:focus:not(:checked) ~ .custom-control-label::before {\n    border-color: $custom-control-indicator-focus-border-color;\n  }\n\n  &:not(:disabled):active ~ .custom-control-label::before {\n    color: $custom-control-indicator-active-color;\n    background-color: $custom-control-indicator-active-bg;\n    border-color: $custom-control-indicator-active-border-color;\n    @include box-shadow($custom-control-indicator-active-box-shadow);\n  }\n\n  // Use [disabled] and :disabled to work around https://github.com/twbs/bootstrap/issues/28247\n  &[disabled],\n  &:disabled {\n    ~ .custom-control-label {\n      color: $custom-control-label-disabled-color;\n\n      &::before {\n        background-color: $custom-control-indicator-disabled-bg;\n      }\n    }\n  }\n}\n\n// Custom control indicators\n//\n// Build the custom controls out of pseudo-elements.\n\n.custom-control-label {\n  position: relative;\n  margin-bottom: 0;\n  color: $custom-control-label-color;\n  vertical-align: top;\n  cursor: $custom-control-cursor;\n\n  // Background-color and (when enabled) gradient\n  &::before {\n    position: absolute;\n    top: ($font-size-base * $line-height-base - $custom-control-indicator-size) / 2;\n    left: -($custom-control-gutter + $custom-control-indicator-size);\n    display: block;\n    width: $custom-control-indicator-size;\n    height: $custom-control-indicator-size;\n    pointer-events: none;\n    content: \"\";\n    background-color: $custom-control-indicator-bg;\n    border: $custom-control-indicator-border-color solid $custom-control-indicator-border-width;\n    @include box-shadow($custom-control-indicator-box-shadow);\n  }\n\n  // Foreground (icon)\n  &::after {\n    position: absolute;\n    top: ($font-size-base * $line-height-base - $custom-control-indicator-size) / 2;\n    left: -($custom-control-gutter + $custom-control-indicator-size);\n    display: block;\n    width: $custom-control-indicator-size;\n    height: $custom-control-indicator-size;\n    content: \"\";\n    background: no-repeat 50% / #{$custom-control-indicator-bg-size};\n  }\n}\n\n\n// Checkboxes\n//\n// Tweak just a few things for checkboxes.\n\n.custom-checkbox {\n  .custom-control-label::before {\n    @include border-radius($custom-checkbox-indicator-border-radius);\n  }\n\n  .custom-control-input:checked ~ .custom-control-label {\n    &::after {\n      background-image: escape-svg($custom-checkbox-indicator-icon-checked);\n    }\n  }\n\n  .custom-control-input:indeterminate ~ .custom-control-label {\n    &::before {\n      border-color: $custom-checkbox-indicator-indeterminate-border-color;\n      @include gradient-bg($custom-checkbox-indicator-indeterminate-bg);\n      @include box-shadow($custom-checkbox-indicator-indeterminate-box-shadow);\n    }\n    &::after {\n      background-image: escape-svg($custom-checkbox-indicator-icon-indeterminate);\n    }\n  }\n\n  .custom-control-input:disabled {\n    &:checked ~ .custom-control-label::before {\n      background-color: $custom-control-indicator-checked-disabled-bg;\n    }\n    &:indeterminate ~ .custom-control-label::before {\n      background-color: $custom-control-indicator-checked-disabled-bg;\n    }\n  }\n}\n\n// Radios\n//\n// Tweak just a few things for radios.\n\n.custom-radio {\n  .custom-control-label::before {\n    // stylelint-disable-next-line property-blacklist\n    border-radius: $custom-radio-indicator-border-radius;\n  }\n\n  .custom-control-input:checked ~ .custom-control-label {\n    &::after {\n      background-image: escape-svg($custom-radio-indicator-icon-checked);\n    }\n  }\n\n  .custom-control-input:disabled {\n    &:checked ~ .custom-control-label::before {\n      background-color: $custom-control-indicator-checked-disabled-bg;\n    }\n  }\n}\n\n\n// switches\n//\n// Tweak a few things for switches\n\n.custom-switch {\n  padding-left: $custom-switch-width + $custom-control-gutter;\n\n  .custom-control-label {\n    &::before {\n      left: -($custom-switch-width + $custom-control-gutter);\n      width: $custom-switch-width;\n      pointer-events: all;\n      // stylelint-disable-next-line property-blacklist\n      border-radius: $custom-switch-indicator-border-radius;\n    }\n\n    &::after {\n      top: add(($font-size-base * $line-height-base - $custom-control-indicator-size) / 2, $custom-control-indicator-border-width * 2);\n      left: add(-($custom-switch-width + $custom-control-gutter), $custom-control-indicator-border-width * 2);\n      width: $custom-switch-indicator-size;\n      height: $custom-switch-indicator-size;\n      background-color: $custom-control-indicator-border-color;\n      // stylelint-disable-next-line property-blacklist\n      border-radius: $custom-switch-indicator-border-radius;\n      @include transition(transform .15s ease-in-out, $custom-forms-transition);\n    }\n  }\n\n  .custom-control-input:checked ~ .custom-control-label {\n    &::after {\n      background-color: $custom-control-indicator-bg;\n      transform: translateX($custom-switch-width - $custom-control-indicator-size);\n    }\n  }\n\n  .custom-control-input:disabled {\n    &:checked ~ .custom-control-label::before {\n      background-color: $custom-control-indicator-checked-disabled-bg;\n    }\n  }\n}\n\n\n// Select\n//\n// Replaces the browser default select with a custom one, mostly pulled from\n// https://primer.github.io/.\n//\n\n.custom-select {\n  display: inline-block;\n  width: 100%;\n  height: $custom-select-height;\n  padding: $custom-select-padding-y ($custom-select-padding-x + $custom-select-indicator-padding) $custom-select-padding-y $custom-select-padding-x;\n  font-family: $custom-select-font-family;\n  @include font-size($custom-select-font-size);\n  font-weight: $custom-select-font-weight;\n  line-height: $custom-select-line-height;\n  color: $custom-select-color;\n  vertical-align: middle;\n  background: $custom-select-bg $custom-select-background;\n  border: $custom-select-border-width solid $custom-select-border-color;\n  @include border-radius($custom-select-border-radius, 0);\n  @include box-shadow($custom-select-box-shadow);\n  appearance: none;\n\n  &:focus {\n    border-color: $custom-select-focus-border-color;\n    outline: 0;\n    @if $enable-shadows {\n      box-shadow: $custom-select-box-shadow, $custom-select-focus-box-shadow;\n    } @else {\n      box-shadow: $custom-select-focus-box-shadow;\n    }\n\n    &::-ms-value {\n      // For visual consistency with other platforms/browsers,\n      // suppress the default white text on blue background highlight given to\n      // the selected option text when the (still closed) <select> receives focus\n      // in IE and (under certain conditions) Edge.\n      // See https://github.com/twbs/bootstrap/issues/19398.\n      color: $input-color;\n      background-color: $input-bg;\n    }\n  }\n\n  &[multiple],\n  &[size]:not([size=\"1\"]) {\n    height: auto;\n    padding-right: $custom-select-padding-x;\n    background-image: none;\n  }\n\n  &:disabled {\n    color: $custom-select-disabled-color;\n    background-color: $custom-select-disabled-bg;\n  }\n\n  // Hides the default caret in IE11\n  &::-ms-expand {\n    display: none;\n  }\n\n  // Remove outline from select box in FF\n  &:-moz-focusring {\n    color: transparent;\n    text-shadow: 0 0 0 $custom-select-color;\n  }\n}\n\n.custom-select-sm {\n  height: $custom-select-height-sm;\n  padding-top: $custom-select-padding-y-sm;\n  padding-bottom: $custom-select-padding-y-sm;\n  padding-left: $custom-select-padding-x-sm;\n  @include font-size($custom-select-font-size-sm);\n}\n\n.custom-select-lg {\n  height: $custom-select-height-lg;\n  padding-top: $custom-select-padding-y-lg;\n  padding-bottom: $custom-select-padding-y-lg;\n  padding-left: $custom-select-padding-x-lg;\n  @include font-size($custom-select-font-size-lg);\n}\n\n\n// File\n//\n// Custom file input.\n\n.custom-file {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  height: $custom-file-height;\n  margin-bottom: 0;\n}\n\n.custom-file-input {\n  position: relative;\n  z-index: 2;\n  width: 100%;\n  height: $custom-file-height;\n  margin: 0;\n  opacity: 0;\n\n  &:focus ~ .custom-file-label {\n    border-color: $custom-file-focus-border-color;\n    box-shadow: $custom-file-focus-box-shadow;\n  }\n\n  // Use [disabled] and :disabled to work around https://github.com/twbs/bootstrap/issues/28247\n  &[disabled] ~ .custom-file-label,\n  &:disabled ~ .custom-file-label {\n    background-color: $custom-file-disabled-bg;\n  }\n\n  @each $lang, $value in $custom-file-text {\n    &:lang(#{$lang}) ~ .custom-file-label::after {\n      content: $value;\n    }\n  }\n\n  ~ .custom-file-label[data-browse]::after {\n    content: attr(data-browse);\n  }\n}\n\n.custom-file-label {\n  position: absolute;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1;\n  height: $custom-file-height;\n  padding: $custom-file-padding-y $custom-file-padding-x;\n  font-family: $custom-file-font-family;\n  font-weight: $custom-file-font-weight;\n  line-height: $custom-file-line-height;\n  color: $custom-file-color;\n  background-color: $custom-file-bg;\n  border: $custom-file-border-width solid $custom-file-border-color;\n  @include border-radius($custom-file-border-radius);\n  @include box-shadow($custom-file-box-shadow);\n\n  &::after {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 3;\n    display: block;\n    height: $custom-file-height-inner;\n    padding: $custom-file-padding-y $custom-file-padding-x;\n    line-height: $custom-file-line-height;\n    color: $custom-file-button-color;\n    content: \"Browse\";\n    @include gradient-bg($custom-file-button-bg);\n    border-left: inherit;\n    @include border-radius(0 $custom-file-border-radius $custom-file-border-radius 0);\n  }\n}\n\n// Range\n//\n// Style range inputs the same across browsers. Vendor-specific rules for pseudo\n// elements cannot be mixed. As such, there are no shared styles for focus or\n// active states on prefixed selectors.\n\n.custom-range {\n  width: 100%;\n  height: add($custom-range-thumb-height, $custom-range-thumb-focus-box-shadow-width * 2);\n  padding: 0; // Need to reset padding\n  background-color: transparent;\n  appearance: none;\n\n  &:focus {\n    outline: none;\n\n    // Pseudo-elements must be split across multiple rulesets to have an effect.\n    // No box-shadow() mixin for focus accessibility.\n    &::-webkit-slider-thumb { box-shadow: $custom-range-thumb-focus-box-shadow; }\n    &::-moz-range-thumb     { box-shadow: $custom-range-thumb-focus-box-shadow; }\n    &::-ms-thumb            { box-shadow: $custom-range-thumb-focus-box-shadow; }\n  }\n\n  &::-moz-focus-outer {\n    border: 0;\n  }\n\n  &::-webkit-slider-thumb {\n    width: $custom-range-thumb-width;\n    height: $custom-range-thumb-height;\n    margin-top: ($custom-range-track-height - $custom-range-thumb-height) / 2; // Webkit specific\n    @include gradient-bg($custom-range-thumb-bg);\n    border: $custom-range-thumb-border;\n    @include border-radius($custom-range-thumb-border-radius);\n    @include box-shadow($custom-range-thumb-box-shadow);\n    @include transition($custom-forms-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($custom-range-thumb-active-bg);\n    }\n  }\n\n  &::-webkit-slider-runnable-track {\n    width: $custom-range-track-width;\n    height: $custom-range-track-height;\n    color: transparent; // Why?\n    cursor: $custom-range-track-cursor;\n    background-color: $custom-range-track-bg;\n    border-color: transparent;\n    @include border-radius($custom-range-track-border-radius);\n    @include box-shadow($custom-range-track-box-shadow);\n  }\n\n  &::-moz-range-thumb {\n    width: $custom-range-thumb-width;\n    height: $custom-range-thumb-height;\n    @include gradient-bg($custom-range-thumb-bg);\n    border: $custom-range-thumb-border;\n    @include border-radius($custom-range-thumb-border-radius);\n    @include box-shadow($custom-range-thumb-box-shadow);\n    @include transition($custom-forms-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($custom-range-thumb-active-bg);\n    }\n  }\n\n  &::-moz-range-track {\n    width: $custom-range-track-width;\n    height: $custom-range-track-height;\n    color: transparent;\n    cursor: $custom-range-track-cursor;\n    background-color: $custom-range-track-bg;\n    border-color: transparent; // Firefox specific?\n    @include border-radius($custom-range-track-border-radius);\n    @include box-shadow($custom-range-track-box-shadow);\n  }\n\n  &::-ms-thumb {\n    width: $custom-range-thumb-width;\n    height: $custom-range-thumb-height;\n    margin-top: 0; // Edge specific\n    margin-right: $custom-range-thumb-focus-box-shadow-width; // Workaround that overflowed box-shadow is hidden.\n    margin-left: $custom-range-thumb-focus-box-shadow-width;  // Workaround that overflowed box-shadow is hidden.\n    @include gradient-bg($custom-range-thumb-bg);\n    border: $custom-range-thumb-border;\n    @include border-radius($custom-range-thumb-border-radius);\n    @include box-shadow($custom-range-thumb-box-shadow);\n    @include transition($custom-forms-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($custom-range-thumb-active-bg);\n    }\n  }\n\n  &::-ms-track {\n    width: $custom-range-track-width;\n    height: $custom-range-track-height;\n    color: transparent;\n    cursor: $custom-range-track-cursor;\n    background-color: transparent;\n    border-color: transparent;\n    border-width: $custom-range-thumb-height / 2;\n    @include box-shadow($custom-range-track-box-shadow);\n  }\n\n  &::-ms-fill-lower {\n    background-color: $custom-range-track-bg;\n    @include border-radius($custom-range-track-border-radius);\n  }\n\n  &::-ms-fill-upper {\n    margin-right: 15px; // arbitrary?\n    background-color: $custom-range-track-bg;\n    @include border-radius($custom-range-track-border-radius);\n  }\n\n  &:disabled {\n    &::-webkit-slider-thumb {\n      background-color: $custom-range-thumb-disabled-bg;\n    }\n\n    &::-webkit-slider-runnable-track {\n      cursor: default;\n    }\n\n    &::-moz-range-thumb {\n      background-color: $custom-range-thumb-disabled-bg;\n    }\n\n    &::-moz-range-track {\n      cursor: default;\n    }\n\n    &::-ms-thumb {\n      background-color: $custom-range-thumb-disabled-bg;\n    }\n  }\n}\n\n.custom-control-label::before,\n.custom-file-label,\n.custom-select {\n  @include transition($custom-forms-transition);\n}\n", "// Base class\n//\n// Kickstart any navigation component with a set of style resets. Works with\n// `<nav>`s, `<ul>`s or `<ol>`s.\n\n.nav {\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n}\n\n.nav-link {\n  display: block;\n  padding: $nav-link-padding-y $nav-link-padding-x;\n\n  @include hover-focus() {\n    text-decoration: none;\n  }\n\n  // Disabled state lightens text\n  &.disabled {\n    color: $nav-link-disabled-color;\n    pointer-events: none;\n    cursor: default;\n  }\n}\n\n//\n// Tabs\n//\n\n.nav-tabs {\n  border-bottom: $nav-tabs-border-width solid $nav-tabs-border-color;\n\n  .nav-item {\n    margin-bottom: -$nav-tabs-border-width;\n  }\n\n  .nav-link {\n    border: $nav-tabs-border-width solid transparent;\n    @include border-top-radius($nav-tabs-border-radius);\n\n    @include hover-focus() {\n      border-color: $nav-tabs-link-hover-border-color;\n    }\n\n    &.disabled {\n      color: $nav-link-disabled-color;\n      background-color: transparent;\n      border-color: transparent;\n    }\n  }\n\n  .nav-link.active,\n  .nav-item.show .nav-link {\n    color: $nav-tabs-link-active-color;\n    background-color: $nav-tabs-link-active-bg;\n    border-color: $nav-tabs-link-active-border-color;\n  }\n\n  .dropdown-menu {\n    // Make dropdown border overlap tab border\n    margin-top: -$nav-tabs-border-width;\n    // Remove the top rounded corners here since there is a hard edge above the menu\n    @include border-top-radius(0);\n  }\n}\n\n\n//\n// Pills\n//\n\n.nav-pills {\n  .nav-link {\n    @include border-radius($nav-pills-border-radius);\n  }\n\n  .nav-link.active,\n  .show > .nav-link {\n    color: $nav-pills-link-active-color;\n    background-color: $nav-pills-link-active-bg;\n  }\n}\n\n\n//\n// Justified variants\n//\n\n.nav-fill {\n  .nav-item {\n    flex: 1 1 auto;\n    text-align: center;\n  }\n}\n\n.nav-justified {\n  .nav-item {\n    flex-basis: 0;\n    flex-grow: 1;\n    text-align: center;\n  }\n}\n\n\n// Tabbable tabs\n//\n// Hide tabbable panes to start, show them when `.active`\n\n.tab-content {\n  > .tab-pane {\n    display: none;\n  }\n  > .active {\n    display: block;\n  }\n}\n", "// Contents\n//\n// Navbar\n// Navbar brand\n// Navbar nav\n// Navbar text\n// Navbar divider\n// Responsive navbar\n// Navbar position\n// Navbar themes\n\n\n// Navbar\n//\n// Provide a static navbar from which we expand to create full-width, fixed, and\n// other navbar variations.\n\n.navbar {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // allow us to do the line break for collapsing content\n  align-items: center;\n  justify-content: space-between; // space out brand from logo\n  padding: $navbar-padding-y $navbar-padding-x;\n\n  // Because flex properties aren't inherited, we need to redeclare these first\n  // few properties so that content nested within behave properly.\n  %container-flex-properties {\n    display: flex;\n    flex-wrap: wrap;\n    align-items: center;\n    justify-content: space-between;\n  }\n\n  .container,\n  .container-fluid {\n    @extend %container-flex-properties;\n  }\n\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    > .container#{breakpoint-infix($breakpoint, $container-max-widths)} {\n      @extend %container-flex-properties;\n    }\n  }\n}\n\n\n// Navbar brand\n//\n// Used for brand, project, or site names.\n\n.navbar-brand {\n  display: inline-block;\n  padding-top: $navbar-brand-padding-y;\n  padding-bottom: $navbar-brand-padding-y;\n  margin-right: $navbar-padding-x;\n  @include font-size($navbar-brand-font-size);\n  line-height: inherit;\n  white-space: nowrap;\n\n  @include hover-focus() {\n    text-decoration: none;\n  }\n}\n\n\n// Navbar nav\n//\n// Custom navbar navigation (doesn't require `.nav`, but does make use of `.nav-link`).\n\n.navbar-nav {\n  display: flex;\n  flex-direction: column; // cannot use `inherit` to get the `.navbar`s value\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n\n  .nav-link {\n    padding-right: 0;\n    padding-left: 0;\n  }\n\n  .dropdown-menu {\n    position: static;\n    float: none;\n  }\n}\n\n\n// Navbar text\n//\n//\n\n.navbar-text {\n  display: inline-block;\n  padding-top: $nav-link-padding-y;\n  padding-bottom: $nav-link-padding-y;\n}\n\n\n// Responsive navbar\n//\n// Custom styles for responsive collapsing and toggling of navbar contents.\n// Powered by the collapse Bootstrap JavaScript plugin.\n\n// When collapsed, prevent the toggleable navbar contents from appearing in\n// the default flexbox row orientation. Requires the use of `flex-wrap: wrap`\n// on the `.navbar` parent.\n.navbar-collapse {\n  flex-basis: 100%;\n  flex-grow: 1;\n  // For always expanded or extra full navbars, ensure content aligns itself\n  // properly vertically. Can be easily overridden with flex utilities.\n  align-items: center;\n}\n\n// Button for toggling the navbar when in its collapsed state\n.navbar-toggler {\n  padding: $navbar-toggler-padding-y $navbar-toggler-padding-x;\n  @include font-size($navbar-toggler-font-size);\n  line-height: 1;\n  background-color: transparent; // remove default button style\n  border: $border-width solid transparent; // remove default button style\n  @include border-radius($navbar-toggler-border-radius);\n\n  @include hover-focus() {\n    text-decoration: none;\n  }\n}\n\n// Keep as a separate element so folks can easily override it with another icon\n// or image file as needed.\n.navbar-toggler-icon {\n  display: inline-block;\n  width: 1.5em;\n  height: 1.5em;\n  vertical-align: middle;\n  content: \"\";\n  background: no-repeat center center;\n  background-size: 100% 100%;\n}\n\n// Generate series of `.navbar-expand-*` responsive classes for configuring\n// where your navbar collapses.\n.navbar-expand {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $next: breakpoint-next($breakpoint, $grid-breakpoints);\n    $infix: breakpoint-infix($next, $grid-breakpoints);\n\n    &#{$infix} {\n      @include media-breakpoint-down($breakpoint) {\n        %container-navbar-expand-#{$breakpoint} {\n          padding-right: 0;\n          padding-left: 0;\n        }\n\n        > .container,\n        > .container-fluid {\n          @extend %container-navbar-expand-#{$breakpoint};\n        }\n\n        @each $size, $container-max-width in $container-max-widths {\n          > .container#{breakpoint-infix($size, $container-max-widths)} {\n            @extend %container-navbar-expand-#{$breakpoint};\n          }\n        }\n      }\n\n      @include media-breakpoint-up($next) {\n        flex-flow: row nowrap;\n        justify-content: flex-start;\n\n        .navbar-nav {\n          flex-direction: row;\n\n          .dropdown-menu {\n            position: absolute;\n          }\n\n          .nav-link {\n            padding-right: $navbar-nav-link-padding-x;\n            padding-left: $navbar-nav-link-padding-x;\n          }\n        }\n\n        // For nesting containers, have to redeclare for alignment purposes\n        %container-nesting-#{$breakpoint} {\n          flex-wrap: nowrap;\n        }\n\n        > .container,\n        > .container-fluid {\n          @extend %container-nesting-#{$breakpoint};\n        }\n\n        @each $size, $container-max-width in $container-max-widths {\n          > .container#{breakpoint-infix($size, $container-max-widths)} {\n            @extend %container-nesting-#{$breakpoint};\n          }\n        }\n\n        .navbar-collapse {\n          display: flex !important; // stylelint-disable-line declaration-no-important\n\n          // Changes flex-bases to auto because of an IE10 bug\n          flex-basis: auto;\n        }\n\n        .navbar-toggler {\n          display: none;\n        }\n      }\n    }\n  }\n}\n\n\n// Navbar themes\n//\n// Styles for switching between navbars with light or dark background.\n\n// Dark links against a light background\n.navbar-light {\n  .navbar-brand {\n    color: $navbar-light-brand-color;\n\n    @include hover-focus() {\n      color: $navbar-light-brand-hover-color;\n    }\n  }\n\n  .navbar-nav {\n    .nav-link {\n      color: $navbar-light-color;\n\n      @include hover-focus() {\n        color: $navbar-light-hover-color;\n      }\n\n      &.disabled {\n        color: $navbar-light-disabled-color;\n      }\n    }\n\n    .show > .nav-link,\n    .active > .nav-link,\n    .nav-link.show,\n    .nav-link.active {\n      color: $navbar-light-active-color;\n    }\n  }\n\n  .navbar-toggler {\n    color: $navbar-light-color;\n    border-color: $navbar-light-toggler-border-color;\n  }\n\n  .navbar-toggler-icon {\n    background-image: escape-svg($navbar-light-toggler-icon-bg);\n  }\n\n  .navbar-text {\n    color: $navbar-light-color;\n    a {\n      color: $navbar-light-active-color;\n\n      @include hover-focus() {\n        color: $navbar-light-active-color;\n      }\n    }\n  }\n}\n\n// White links against a dark background\n.navbar-dark {\n  .navbar-brand {\n    color: $navbar-dark-brand-color;\n\n    @include hover-focus() {\n      color: $navbar-dark-brand-hover-color;\n    }\n  }\n\n  .navbar-nav {\n    .nav-link {\n      color: $navbar-dark-color;\n\n      @include hover-focus() {\n        color: $navbar-dark-hover-color;\n      }\n\n      &.disabled {\n        color: $navbar-dark-disabled-color;\n      }\n    }\n\n    .show > .nav-link,\n    .active > .nav-link,\n    .nav-link.show,\n    .nav-link.active {\n      color: $navbar-dark-active-color;\n    }\n  }\n\n  .navbar-toggler {\n    color: $navbar-dark-color;\n    border-color: $navbar-dark-toggler-border-color;\n  }\n\n  .navbar-toggler-icon {\n    background-image: escape-svg($navbar-dark-toggler-icon-bg);\n  }\n\n  .navbar-text {\n    color: $navbar-dark-color;\n    a {\n      color: $navbar-dark-active-color;\n\n      @include hover-focus() {\n        color: $navbar-dark-active-color;\n      }\n    }\n  }\n}\n", "//\n// Base styles\n//\n\n.card {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  min-width: 0; // See https://github.com/twbs/bootstrap/pull/22740#issuecomment-305868106\n  height: $card-height;\n  word-wrap: break-word;\n  background-color: $card-bg;\n  background-clip: border-box;\n  border: $card-border-width solid $card-border-color;\n  @include border-radius($card-border-radius);\n\n  > hr {\n    margin-right: 0;\n    margin-left: 0;\n  }\n\n  > .list-group:first-child {\n    .list-group-item:first-child {\n      @include border-top-radius($card-border-radius);\n    }\n  }\n\n  > .list-group:last-child {\n    .list-group-item:last-child {\n      @include border-bottom-radius($card-border-radius);\n    }\n  }\n}\n\n.card-body {\n  // Enable `flex-grow: 1` for decks and groups so that card blocks take up\n  // as much space as possible, ensuring footers are aligned to the bottom.\n  flex: 1 1 auto;\n  // Workaround for the image size bug in IE\n  // See: https://github.com/twbs/bootstrap/pull/28855\n  min-height: 1px;\n  padding: $card-spacer-x;\n  color: $card-color;\n}\n\n.card-title {\n  margin-bottom: $card-spacer-y;\n}\n\n.card-subtitle {\n  margin-top: -$card-spacer-y / 2;\n  margin-bottom: 0;\n}\n\n.card-text:last-child {\n  margin-bottom: 0;\n}\n\n.card-link {\n  @include hover() {\n    text-decoration: none;\n  }\n\n  + .card-link {\n    margin-left: $card-spacer-x;\n  }\n}\n\n//\n// Optional textual caps\n//\n\n.card-header {\n  padding: $card-spacer-y $card-spacer-x;\n  margin-bottom: 0; // Removes the default margin-bottom of <hN>\n  color: $card-cap-color;\n  background-color: $card-cap-bg;\n  border-bottom: $card-border-width solid $card-border-color;\n\n  &:first-child {\n    @include border-radius($card-inner-border-radius $card-inner-border-radius 0 0);\n  }\n\n  + .list-group {\n    .list-group-item:first-child {\n      border-top: 0;\n    }\n  }\n}\n\n.card-footer {\n  padding: $card-spacer-y $card-spacer-x;\n  background-color: $card-cap-bg;\n  border-top: $card-border-width solid $card-border-color;\n\n  &:last-child {\n    @include border-radius(0 0 $card-inner-border-radius $card-inner-border-radius);\n  }\n}\n\n\n//\n// Header navs\n//\n\n.card-header-tabs {\n  margin-right: -$card-spacer-x / 2;\n  margin-bottom: -$card-spacer-y;\n  margin-left: -$card-spacer-x / 2;\n  border-bottom: 0;\n}\n\n.card-header-pills {\n  margin-right: -$card-spacer-x / 2;\n  margin-left: -$card-spacer-x / 2;\n}\n\n// Card image\n.card-img-overlay {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  padding: $card-img-overlay-padding;\n}\n\n.card-img,\n.card-img-top,\n.card-img-bottom {\n  flex-shrink: 0; // For IE: https://github.com/twbs/bootstrap/issues/29396\n  width: 100%; // Required because we use flexbox and this inherently applies align-self: stretch\n}\n\n.card-img,\n.card-img-top {\n  @include border-top-radius($card-inner-border-radius);\n}\n\n.card-img,\n.card-img-bottom {\n  @include border-bottom-radius($card-inner-border-radius);\n}\n\n\n// Card deck\n\n.card-deck {\n  .card {\n    margin-bottom: $card-deck-margin;\n  }\n\n  @include media-breakpoint-up(sm) {\n    display: flex;\n    flex-flow: row wrap;\n    margin-right: -$card-deck-margin;\n    margin-left: -$card-deck-margin;\n\n    .card {\n      // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      flex: 1 0 0%;\n      margin-right: $card-deck-margin;\n      margin-bottom: 0; // Override the default\n      margin-left: $card-deck-margin;\n    }\n  }\n}\n\n\n//\n// Card groups\n//\n\n.card-group {\n  // The child selector allows nested `.card` within `.card-group`\n  // to display properly.\n  > .card {\n    margin-bottom: $card-group-margin;\n  }\n\n  @include media-breakpoint-up(sm) {\n    display: flex;\n    flex-flow: row wrap;\n    // The child selector allows nested `.card` within `.card-group`\n    // to display properly.\n    > .card {\n      // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      flex: 1 0 0%;\n      margin-bottom: 0;\n\n      + .card {\n        margin-left: 0;\n        border-left: 0;\n      }\n\n      // Handle rounded corners\n      @if $enable-rounded {\n        &:not(:last-child) {\n          @include border-right-radius(0);\n\n          .card-img-top,\n          .card-header {\n            // stylelint-disable-next-line property-blacklist\n            border-top-right-radius: 0;\n          }\n          .card-img-bottom,\n          .card-footer {\n            // stylelint-disable-next-line property-blacklist\n            border-bottom-right-radius: 0;\n          }\n        }\n\n        &:not(:first-child) {\n          @include border-left-radius(0);\n\n          .card-img-top,\n          .card-header {\n            // stylelint-disable-next-line property-blacklist\n            border-top-left-radius: 0;\n          }\n          .card-img-bottom,\n          .card-footer {\n            // stylelint-disable-next-line property-blacklist\n            border-bottom-left-radius: 0;\n          }\n        }\n      }\n    }\n  }\n}\n\n\n//\n// Columns\n//\n\n.card-columns {\n  .card {\n    margin-bottom: $card-columns-margin;\n  }\n\n  @include media-breakpoint-up(sm) {\n    column-count: $card-columns-count;\n    column-gap: $card-columns-gap;\n    orphans: 1;\n    widows: 1;\n\n    .card {\n      display: inline-block; // Don't let them vertically span multiple columns\n      width: 100%; // Don't let their width change\n    }\n  }\n}\n\n\n//\n// Accordion\n//\n\n.accordion {\n  > .card {\n    overflow: hidden;\n\n    &:not(:last-of-type) {\n      border-bottom: 0;\n      @include border-bottom-radius(0);\n    }\n\n    &:not(:first-of-type) {\n      @include border-top-radius(0);\n    }\n\n    > .card-header {\n      @include border-radius(0);\n      margin-bottom: -$card-border-width;\n    }\n  }\n}\n", ".breadcrumb {\n  display: flex;\n  flex-wrap: wrap;\n  padding: $breadcrumb-padding-y $breadcrumb-padding-x;\n  margin-bottom: $breadcrumb-margin-bottom;\n  @include font-size($breadcrumb-font-size);\n  list-style: none;\n  background-color: $breadcrumb-bg;\n  @include border-radius($breadcrumb-border-radius);\n}\n\n.breadcrumb-item {\n  // The separator between breadcrumbs (by default, a forward-slash: \"/\")\n  + .breadcrumb-item {\n    padding-left: $breadcrumb-item-padding;\n\n    &::before {\n      display: inline-block; // Suppress underlining of the separator in modern browsers\n      padding-right: $breadcrumb-item-padding;\n      color: $breadcrumb-divider-color;\n      content: escape-svg($breadcrumb-divider);\n    }\n  }\n\n  // IE9-11 hack to properly handle hyperlink underlines for breadcrumbs built\n  // without `<ul>`s. The `::before` pseudo-element generates an element\n  // *within* the .breadcrumb-item and thereby inherits the `text-decoration`.\n  //\n  // To trick <PERSON><PERSON> into suppressing the underline, we give the pseudo-element an\n  // underline and then immediately remove it.\n  + .breadcrumb-item:hover::before {\n    text-decoration: underline;\n  }\n  // stylelint-disable-next-line no-duplicate-selectors\n  + .breadcrumb-item:hover::before {\n    text-decoration: none;\n  }\n\n  &.active {\n    color: $breadcrumb-active-color;\n  }\n}\n", ".pagination {\n  display: flex;\n  @include list-unstyled();\n  @include border-radius();\n}\n\n.page-link {\n  position: relative;\n  display: block;\n  padding: $pagination-padding-y $pagination-padding-x;\n  margin-left: -$pagination-border-width;\n  line-height: $pagination-line-height;\n  color: $pagination-color;\n  background-color: $pagination-bg;\n  border: $pagination-border-width solid $pagination-border-color;\n\n  &:hover {\n    z-index: 2;\n    color: $pagination-hover-color;\n    text-decoration: none;\n    background-color: $pagination-hover-bg;\n    border-color: $pagination-hover-border-color;\n  }\n\n  &:focus {\n    z-index: 3;\n    outline: $pagination-focus-outline;\n    box-shadow: $pagination-focus-box-shadow;\n  }\n}\n\n.page-item {\n  &:first-child {\n    .page-link {\n      margin-left: 0;\n      @include border-left-radius($border-radius);\n    }\n  }\n  &:last-child {\n    .page-link {\n      @include border-right-radius($border-radius);\n    }\n  }\n\n  &.active .page-link {\n    z-index: 3;\n    color: $pagination-active-color;\n    background-color: $pagination-active-bg;\n    border-color: $pagination-active-border-color;\n  }\n\n  &.disabled .page-link {\n    color: $pagination-disabled-color;\n    pointer-events: none;\n    // Opinionated: remove the \"hand\" cursor set previously for .page-link\n    cursor: auto;\n    background-color: $pagination-disabled-bg;\n    border-color: $pagination-disabled-border-color;\n  }\n}\n\n\n//\n// Sizing\n//\n\n.pagination-lg {\n  @include pagination-size($pagination-padding-y-lg, $pagination-padding-x-lg, $font-size-lg, $line-height-lg, $border-radius-lg);\n}\n\n.pagination-sm {\n  @include pagination-size($pagination-padding-y-sm, $pagination-padding-x-sm, $font-size-sm, $line-height-sm, $border-radius-sm);\n}\n", "// Pagination\n\n@mixin pagination-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\n  .page-link {\n    padding: $padding-y $padding-x;\n    @include font-size($font-size);\n    line-height: $line-height;\n  }\n\n  .page-item {\n    &:first-child {\n      .page-link {\n        @include border-left-radius($border-radius);\n      }\n    }\n    &:last-child {\n      .page-link {\n        @include border-right-radius($border-radius);\n      }\n    }\n  }\n}\n", "// Base class\n//\n// Requires one of the contextual, color modifier classes for `color` and\n// `background-color`.\n\n.badge {\n  display: inline-block;\n  padding: $badge-padding-y $badge-padding-x;\n  @include font-size($badge-font-size);\n  font-weight: $badge-font-weight;\n  line-height: 1;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: baseline;\n  @include border-radius($badge-border-radius);\n  @include transition($badge-transition);\n\n  @at-root a#{&} {\n    @include hover-focus() {\n      text-decoration: none;\n    }\n  }\n\n  // Empty badges collapse automatically\n  &:empty {\n    display: none;\n  }\n}\n\n// Quick fix for badges in buttons\n.btn .badge {\n  position: relative;\n  top: -1px;\n}\n\n// Pill badges\n//\n// Make them extra rounded with a modifier to replace v3's badges.\n\n.badge-pill {\n  padding-right: $badge-pill-padding-x;\n  padding-left: $badge-pill-padding-x;\n  @include border-radius($badge-pill-border-radius);\n}\n\n// Colors\n//\n// Contextual variations (linked badges get darker on :hover).\n\n@each $color, $value in $theme-colors {\n  .badge-#{$color} {\n    @include badge-variant($value);\n  }\n}\n", "@mixin badge-variant($bg) {\n  color: color-yiq($bg);\n  background-color: $bg;\n\n  @at-root a#{&} {\n    @include hover-focus() {\n      color: color-yiq($bg);\n      background-color: darken($bg, 10%);\n    }\n\n    &:focus,\n    &.focus {\n      outline: 0;\n      box-shadow: 0 0 0 $badge-focus-width rgba($bg, .5);\n    }\n  }\n}\n", ".jumbotron {\n  padding: $jumbotron-padding ($jumbotron-padding / 2);\n  margin-bottom: $jumbotron-padding;\n  color: $jumbotron-color;\n  background-color: $jumbotron-bg;\n  @include border-radius($border-radius-lg);\n\n  @include media-breakpoint-up(sm) {\n    padding: ($jumbotron-padding * 2) $jumbotron-padding;\n  }\n}\n\n.jumbotron-fluid {\n  padding-right: 0;\n  padding-left: 0;\n  @include border-radius(0);\n}\n", "//\n// Base styles\n//\n\n.alert {\n  position: relative;\n  padding: $alert-padding-y $alert-padding-x;\n  margin-bottom: $alert-margin-bottom;\n  border: $alert-border-width solid transparent;\n  @include border-radius($alert-border-radius);\n}\n\n// Headings for larger alerts\n.alert-heading {\n  // Specified to prevent conflicts of changing $headings-color\n  color: inherit;\n}\n\n// Provide class for links that match alerts\n.alert-link {\n  font-weight: $alert-link-font-weight;\n}\n\n\n// Dismissible alerts\n//\n// Expand the right padding and account for the close button's positioning.\n\n.alert-dismissible {\n  padding-right: $close-font-size + $alert-padding-x * 2;\n\n  // Adjust close link position\n  .close {\n    position: absolute;\n    top: 0;\n    right: 0;\n    padding: $alert-padding-y $alert-padding-x;\n    color: inherit;\n  }\n}\n\n\n// Alternate styles\n//\n// Generate contextual modifier classes for colorizing the alert.\n\n@each $color, $value in $theme-colors {\n  .alert-#{$color} {\n    @include alert-variant(theme-color-level($color, $alert-bg-level), theme-color-level($color, $alert-border-level), theme-color-level($color, $alert-color-level));\n  }\n}\n", "@mixin alert-variant($background, $border, $color) {\n  color: $color;\n  @include gradient-bg($background);\n  border-color: $border;\n\n  hr {\n    border-top-color: darken($border, 5%);\n  }\n\n  .alert-link {\n    color: darken($color, 10%);\n  }\n}\n", "// Disable animation if transitions are disabled\n@if $enable-transitions {\n  @keyframes progress-bar-stripes {\n    from { background-position: $progress-height 0; }\n    to { background-position: 0 0; }\n  }\n}\n\n.progress {\n  display: flex;\n  height: $progress-height;\n  overflow: hidden; // force rounded corners by cropping it\n  @include font-size($progress-font-size);\n  background-color: $progress-bg;\n  @include border-radius($progress-border-radius);\n  @include box-shadow($progress-box-shadow);\n}\n\n.progress-bar {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  overflow: hidden;\n  color: $progress-bar-color;\n  text-align: center;\n  white-space: nowrap;\n  background-color: $progress-bar-bg;\n  @include transition($progress-bar-transition);\n}\n\n.progress-bar-striped {\n  @include gradient-striped();\n  background-size: $progress-height $progress-height;\n}\n\n@if $enable-transitions {\n  .progress-bar-animated {\n    animation: progress-bar-stripes $progress-bar-animation-timing;\n\n    @if $enable-prefers-reduced-motion-media-query {\n      @media (prefers-reduced-motion: reduce) {\n        animation: none;\n      }\n    }\n  }\n}\n", ".media {\n  display: flex;\n  align-items: flex-start;\n}\n\n.media-body {\n  flex: 1;\n}\n", "// Base class\n//\n// Easily usable on <ul>, <ol>, or <div>.\n\n.list-group {\n  display: flex;\n  flex-direction: column;\n\n  // No need to set list-style: none; since .list-group-item is block level\n  padding-left: 0; // reset padding because ul and ol\n  margin-bottom: 0;\n}\n\n\n// Interactive list items\n//\n// Use anchor or button elements instead of `li`s or `div`s to create interactive\n// list items. Includes an extra `.active` modifier class for selected items.\n\n.list-group-item-action {\n  width: 100%; // For `<button>`s (anchors become 100% by default though)\n  color: $list-group-action-color;\n  text-align: inherit; // For `<button>`s (anchors inherit)\n\n  // Hover state\n  @include hover-focus() {\n    z-index: 1; // Place hover/focus items above their siblings for proper border styling\n    color: $list-group-action-hover-color;\n    text-decoration: none;\n    background-color: $list-group-hover-bg;\n  }\n\n  &:active {\n    color: $list-group-action-active-color;\n    background-color: $list-group-action-active-bg;\n  }\n}\n\n\n// Individual list items\n//\n// Use on `li`s or `div`s within the `.list-group` parent.\n\n.list-group-item {\n  position: relative;\n  display: block;\n  padding: $list-group-item-padding-y $list-group-item-padding-x;\n  color: $list-group-color;\n  background-color: $list-group-bg;\n  border: $list-group-border-width solid $list-group-border-color;\n\n  &:first-child {\n    @include border-top-radius($list-group-border-radius);\n  }\n\n  &:last-child {\n    @include border-bottom-radius($list-group-border-radius);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $list-group-disabled-color;\n    pointer-events: none;\n    background-color: $list-group-disabled-bg;\n  }\n\n  // Include both here for `<a>`s and `<button>`s\n  &.active {\n    z-index: 2; // Place active items above their siblings for proper border styling\n    color: $list-group-active-color;\n    background-color: $list-group-active-bg;\n    border-color: $list-group-active-border-color;\n  }\n\n  & + & {\n    border-top-width: 0;\n\n    &.active {\n      margin-top: -$list-group-border-width;\n      border-top-width: $list-group-border-width;\n    }\n  }\n}\n\n\n// Horizontal\n//\n// Change the layout of list group items from vertical (default) to horizontal.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .list-group-horizontal#{$infix} {\n      flex-direction: row;\n\n      .list-group-item {\n        &:first-child {\n          @include border-bottom-left-radius($list-group-border-radius);\n          @include border-top-right-radius(0);\n        }\n\n        &:last-child {\n          @include border-top-right-radius($list-group-border-radius);\n          @include border-bottom-left-radius(0);\n        }\n\n        &.active {\n          margin-top: 0;\n        }\n\n        & + .list-group-item {\n          border-top-width: $list-group-border-width;\n          border-left-width: 0;\n\n          &.active {\n            margin-left: -$list-group-border-width;\n            border-left-width: $list-group-border-width;\n          }\n        }\n      }\n    }\n  }\n}\n\n\n// Flush list items\n//\n// Remove borders and border-radius to keep list group items edge-to-edge. Most\n// useful within other components (e.g., cards).\n\n.list-group-flush {\n  .list-group-item {\n    border-right-width: 0;\n    border-left-width: 0;\n    @include border-radius(0);\n\n    &:first-child {\n      border-top-width: 0;\n    }\n  }\n\n  &:last-child {\n    .list-group-item:last-child {\n      border-bottom-width: 0;\n    }\n  }\n}\n\n\n// Contextual variants\n//\n// Add modifier classes to change text and background color on individual items.\n// Organizationally, this must come after the `:hover` states.\n\n@each $color, $value in $theme-colors {\n  @include list-group-item-variant($color, theme-color-level($color, -9), theme-color-level($color, 6));\n}\n", "// List Groups\n\n@mixin list-group-item-variant($state, $background, $color) {\n  .list-group-item-#{$state} {\n    color: $color;\n    background-color: $background;\n\n    &.list-group-item-action {\n      @include hover-focus() {\n        color: $color;\n        background-color: darken($background, 5%);\n      }\n\n      &.active {\n        color: $white;\n        background-color: $color;\n        border-color: $color;\n      }\n    }\n  }\n}\n", ".close {\n  float: right;\n  @include font-size($close-font-size);\n  font-weight: $close-font-weight;\n  line-height: 1;\n  color: $close-color;\n  text-shadow: $close-text-shadow;\n  opacity: .5;\n\n  // Override <a>'s hover style\n  @include hover() {\n    color: $close-color;\n    text-decoration: none;\n  }\n\n  &:not(:disabled):not(.disabled) {\n    @include hover-focus() {\n      opacity: .75;\n    }\n  }\n}\n\n// Additional properties for button version\n// iOS requires the button element instead of an anchor tag.\n// If you want the anchor version, it requires `href=\"#\"`.\n// See https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile\n\n// stylelint-disable-next-line selector-no-qualifying-type\nbutton.close {\n  padding: 0;\n  background-color: transparent;\n  border: 0;\n  appearance: none;\n}\n\n// Future-proof disabling of clicks on `<a>` elements\n\n// stylelint-disable-next-line selector-no-qualifying-type\na.close.disabled {\n  pointer-events: none;\n}\n", ".toast {\n  max-width: $toast-max-width;\n  overflow: hidden; // cheap rounded corners on nested items\n  @include font-size($toast-font-size);\n  color: $toast-color;\n  background-color: $toast-background-color;\n  background-clip: padding-box;\n  border: $toast-border-width solid $toast-border-color;\n  box-shadow: $toast-box-shadow;\n  backdrop-filter: blur(10px);\n  opacity: 0;\n  @include border-radius($toast-border-radius);\n\n  &:not(:last-child) {\n    margin-bottom: $toast-padding-x;\n  }\n\n  &.showing {\n    opacity: 1;\n  }\n\n  &.show {\n    display: block;\n    opacity: 1;\n  }\n\n  &.hide {\n    display: none;\n  }\n}\n\n.toast-header {\n  display: flex;\n  align-items: center;\n  padding: $toast-padding-y $toast-padding-x;\n  color: $toast-header-color;\n  background-color: $toast-header-background-color;\n  background-clip: padding-box;\n  border-bottom: $toast-border-width solid $toast-header-border-color;\n}\n\n.toast-body {\n  padding: $toast-padding-x; // apply to both vertical and horizontal\n}\n", "// .modal-open      - body class for killing the scroll\n// .modal           - container to scroll within\n// .modal-dialog    - positioning shell for the actual modal\n// .modal-content   - actual modal w/ bg and corners and stuff\n\n\n.modal-open {\n  // Kill the scroll on the body\n  overflow: hidden;\n\n  .modal {\n    overflow-x: hidden;\n    overflow-y: auto;\n  }\n}\n\n// Container that the modal scrolls within\n.modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: $zindex-modal;\n  display: none;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n  // Prevent Chrome on Windows from adding a focus outline. For details, see\n  // https://github.com/twbs/bootstrap/pull/10951.\n  outline: 0;\n  // We deliberately don't use `-webkit-overflow-scrolling: touch;` due to a\n  // gnarly iOS Safari bug: https://bugs.webkit.org/show_bug.cgi?id=158342\n  // See also https://github.com/twbs/bootstrap/issues/17695\n}\n\n// Shell div to position the modal with bottom padding\n.modal-dialog {\n  position: relative;\n  width: auto;\n  margin: $modal-dialog-margin;\n  // allow clicks to pass through for custom click handling to close modal\n  pointer-events: none;\n\n  // When fading in the modal, animate it to slide down\n  .modal.fade & {\n    @include transition($modal-transition);\n    transform: $modal-fade-transform;\n  }\n  .modal.show & {\n    transform: $modal-show-transform;\n  }\n\n  // When trying to close, animate focus to scale\n  .modal.modal-static & {\n    transform: $modal-scale-transform;\n  }\n}\n\n.modal-dialog-scrollable {\n  display: flex; // IE10/11\n  max-height: subtract(100%, $modal-dialog-margin * 2);\n\n  .modal-content {\n    max-height: subtract(100vh, $modal-dialog-margin * 2); // IE10/11\n    overflow: hidden;\n  }\n\n  .modal-header,\n  .modal-footer {\n    flex-shrink: 0;\n  }\n\n  .modal-body {\n    overflow-y: auto;\n  }\n}\n\n.modal-dialog-centered {\n  display: flex;\n  align-items: center;\n  min-height: subtract(100%, $modal-dialog-margin * 2);\n\n  // Ensure `modal-dialog-centered` extends the full height of the view (IE10/11)\n  &::before {\n    display: block; // IE10\n    height: subtract(100vh, $modal-dialog-margin * 2);\n    content: \"\";\n  }\n\n  // Ensure `.modal-body` shows scrollbar (IE10/11)\n  &.modal-dialog-scrollable {\n    flex-direction: column;\n    justify-content: center;\n    height: 100%;\n\n    .modal-content {\n      max-height: none;\n    }\n\n    &::before {\n      content: none;\n    }\n  }\n}\n\n// Actual modal\n.modal-content {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  width: 100%; // Ensure `.modal-content` extends the full width of the parent `.modal-dialog`\n  // counteract the pointer-events: none; in the .modal-dialog\n  color: $modal-content-color;\n  pointer-events: auto;\n  background-color: $modal-content-bg;\n  background-clip: padding-box;\n  border: $modal-content-border-width solid $modal-content-border-color;\n  @include border-radius($modal-content-border-radius);\n  @include box-shadow($modal-content-box-shadow-xs);\n  // Remove focus outline from opened modal\n  outline: 0;\n}\n\n// Modal background\n.modal-backdrop {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: $zindex-modal-backdrop;\n  width: 100vw;\n  height: 100vh;\n  background-color: $modal-backdrop-bg;\n\n  // Fade for backdrop\n  &.fade { opacity: 0; }\n  &.show { opacity: $modal-backdrop-opacity; }\n}\n\n// Modal header\n// Top section of the modal w/ title and dismiss\n.modal-header {\n  display: flex;\n  align-items: flex-start; // so the close btn always stays on the upper right corner\n  justify-content: space-between; // Put modal header elements (title and dismiss) on opposite ends\n  padding: $modal-header-padding;\n  border-bottom: $modal-header-border-width solid $modal-header-border-color;\n  @include border-top-radius($modal-content-inner-border-radius);\n\n  .close {\n    padding: $modal-header-padding;\n    // auto on the left force icon to the right even when there is no .modal-title\n    margin: (-$modal-header-padding-y) (-$modal-header-padding-x) (-$modal-header-padding-y) auto;\n  }\n}\n\n// Title text within header\n.modal-title {\n  margin-bottom: 0;\n  line-height: $modal-title-line-height;\n}\n\n// Modal body\n// Where all modal content resides (sibling of .modal-header and .modal-footer)\n.modal-body {\n  position: relative;\n  // Enable `flex-grow: 1` so that the body take up as much space as possible\n  // when there should be a fixed height on `.modal-dialog`.\n  flex: 1 1 auto;\n  padding: $modal-inner-padding;\n}\n\n// Footer (for actions)\n.modal-footer {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center; // vertically center\n  justify-content: flex-end; // Right align buttons with flex property because text-align doesn't work on flex items\n  padding: $modal-inner-padding - $modal-footer-margin-between / 2;\n  border-top: $modal-footer-border-width solid $modal-footer-border-color;\n  @include border-bottom-radius($modal-content-inner-border-radius);\n\n  // Place margin between footer elements\n  // This solution is far from ideal because of the universal selector usage,\n  // but is needed to fix https://github.com/twbs/bootstrap/issues/24800\n  // stylelint-disable-next-line selector-max-universal\n  > * {\n    margin: $modal-footer-margin-between / 2;\n  }\n}\n\n// Measure scrollbar width for padding body during modal show/hide\n.modal-scrollbar-measure {\n  position: absolute;\n  top: -9999px;\n  width: 50px;\n  height: 50px;\n  overflow: scroll;\n}\n\n// Scale up the modal\n@include media-breakpoint-up(sm) {\n  // Automatically set modal's width for larger viewports\n  .modal-dialog {\n    max-width: $modal-md;\n    margin: $modal-dialog-margin-y-sm-up auto;\n  }\n\n  .modal-dialog-scrollable {\n    max-height: subtract(100%, $modal-dialog-margin-y-sm-up * 2);\n\n    .modal-content {\n      max-height: subtract(100vh, $modal-dialog-margin-y-sm-up * 2);\n    }\n  }\n\n  .modal-dialog-centered {\n    min-height: subtract(100%, $modal-dialog-margin-y-sm-up * 2);\n\n    &::before {\n      height: subtract(100vh, $modal-dialog-margin-y-sm-up * 2);\n    }\n  }\n\n  .modal-content {\n    @include box-shadow($modal-content-box-shadow-sm-up);\n  }\n\n  .modal-sm { max-width: $modal-sm; }\n}\n\n@include media-breakpoint-up(lg) {\n  .modal-lg,\n  .modal-xl {\n    max-width: $modal-lg;\n  }\n}\n\n@include media-breakpoint-up(xl) {\n  .modal-xl { max-width: $modal-xl; }\n}\n", "// Base class\n.tooltip {\n  position: absolute;\n  z-index: $zindex-tooltip;\n  display: block;\n  margin: $tooltip-margin;\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text();\n  @include font-size($tooltip-font-size);\n  // Allow breaking very long words so they don't overflow the tooltip's bounds\n  word-wrap: break-word;\n  opacity: 0;\n\n  &.show { opacity: $tooltip-opacity; }\n\n  .arrow {\n    position: absolute;\n    display: block;\n    width: $tooltip-arrow-width;\n    height: $tooltip-arrow-height;\n\n    &::before {\n      position: absolute;\n      content: \"\";\n      border-color: transparent;\n      border-style: solid;\n    }\n  }\n}\n\n.bs-tooltip-top {\n  padding: $tooltip-arrow-height 0;\n\n  .arrow {\n    bottom: 0;\n\n    &::before {\n      top: 0;\n      border-width: $tooltip-arrow-height ($tooltip-arrow-width / 2) 0;\n      border-top-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-right {\n  padding: 0 $tooltip-arrow-height;\n\n  .arrow {\n    left: 0;\n    width: $tooltip-arrow-height;\n    height: $tooltip-arrow-width;\n\n    &::before {\n      right: 0;\n      border-width: ($tooltip-arrow-width / 2) $tooltip-arrow-height ($tooltip-arrow-width / 2) 0;\n      border-right-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-bottom {\n  padding: $tooltip-arrow-height 0;\n\n  .arrow {\n    top: 0;\n\n    &::before {\n      bottom: 0;\n      border-width: 0 ($tooltip-arrow-width / 2) $tooltip-arrow-height;\n      border-bottom-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-left {\n  padding: 0 $tooltip-arrow-height;\n\n  .arrow {\n    right: 0;\n    width: $tooltip-arrow-height;\n    height: $tooltip-arrow-width;\n\n    &::before {\n      left: 0;\n      border-width: ($tooltip-arrow-width / 2) 0 ($tooltip-arrow-width / 2) $tooltip-arrow-height;\n      border-left-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-auto {\n  &[x-placement^=\"top\"] {\n    @extend .bs-tooltip-top;\n  }\n  &[x-placement^=\"right\"] {\n    @extend .bs-tooltip-right;\n  }\n  &[x-placement^=\"bottom\"] {\n    @extend .bs-tooltip-bottom;\n  }\n  &[x-placement^=\"left\"] {\n    @extend .bs-tooltip-left;\n  }\n}\n\n// Wrapper for the tooltip content\n.tooltip-inner {\n  max-width: $tooltip-max-width;\n  padding: $tooltip-padding-y $tooltip-padding-x;\n  color: $tooltip-color;\n  text-align: center;\n  background-color: $tooltip-bg;\n  @include border-radius($tooltip-border-radius);\n}\n", "@mixin reset-text() {\n  font-family: $font-family-base;\n  // We deliberately do NOT reset font-size or word-wrap.\n  font-style: normal;\n  font-weight: $font-weight-normal;\n  line-height: $line-height-base;\n  text-align: left; // Fallback for where `start` is not supported\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n}\n", ".popover {\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: $zindex-popover;\n  display: block;\n  max-width: $popover-max-width;\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text();\n  @include font-size($popover-font-size);\n  // Allow breaking very long words so they don't overflow the popover's bounds\n  word-wrap: break-word;\n  background-color: $popover-bg;\n  background-clip: padding-box;\n  border: $popover-border-width solid $popover-border-color;\n  @include border-radius($popover-border-radius);\n  @include box-shadow($popover-box-shadow);\n\n  .arrow {\n    position: absolute;\n    display: block;\n    width: $popover-arrow-width;\n    height: $popover-arrow-height;\n    margin: 0 $popover-border-radius;\n\n    &::before,\n    &::after {\n      position: absolute;\n      display: block;\n      content: \"\";\n      border-color: transparent;\n      border-style: solid;\n    }\n  }\n}\n\n.bs-popover-top {\n  margin-bottom: $popover-arrow-height;\n\n  > .arrow {\n    bottom: subtract(-$popover-arrow-height, $popover-border-width);\n\n    &::before {\n      bottom: 0;\n      border-width: $popover-arrow-height ($popover-arrow-width / 2) 0;\n      border-top-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      bottom: $popover-border-width;\n      border-width: $popover-arrow-height ($popover-arrow-width / 2) 0;\n      border-top-color: $popover-arrow-color;\n    }\n  }\n}\n\n.bs-popover-right {\n  margin-left: $popover-arrow-height;\n\n  > .arrow {\n    left: subtract(-$popover-arrow-height, $popover-border-width);\n    width: $popover-arrow-height;\n    height: $popover-arrow-width;\n    margin: $popover-border-radius 0; // make sure the arrow does not touch the popover's rounded corners\n\n    &::before {\n      left: 0;\n      border-width: ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2) 0;\n      border-right-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      left: $popover-border-width;\n      border-width: ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2) 0;\n      border-right-color: $popover-arrow-color;\n    }\n  }\n}\n\n.bs-popover-bottom {\n  margin-top: $popover-arrow-height;\n\n  > .arrow {\n    top: subtract(-$popover-arrow-height, $popover-border-width);\n\n    &::before {\n      top: 0;\n      border-width: 0 ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2);\n      border-bottom-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      top: $popover-border-width;\n      border-width: 0 ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2);\n      border-bottom-color: $popover-arrow-color;\n    }\n  }\n\n  // This will remove the popover-header's border just below the arrow\n  .popover-header::before {\n    position: absolute;\n    top: 0;\n    left: 50%;\n    display: block;\n    width: $popover-arrow-width;\n    margin-left: -$popover-arrow-width / 2;\n    content: \"\";\n    border-bottom: $popover-border-width solid $popover-header-bg;\n  }\n}\n\n.bs-popover-left {\n  margin-right: $popover-arrow-height;\n\n  > .arrow {\n    right: subtract(-$popover-arrow-height, $popover-border-width);\n    width: $popover-arrow-height;\n    height: $popover-arrow-width;\n    margin: $popover-border-radius 0; // make sure the arrow does not touch the popover's rounded corners\n\n    &::before {\n      right: 0;\n      border-width: ($popover-arrow-width / 2) 0 ($popover-arrow-width / 2) $popover-arrow-height;\n      border-left-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      right: $popover-border-width;\n      border-width: ($popover-arrow-width / 2) 0 ($popover-arrow-width / 2) $popover-arrow-height;\n      border-left-color: $popover-arrow-color;\n    }\n  }\n}\n\n.bs-popover-auto {\n  &[x-placement^=\"top\"] {\n    @extend .bs-popover-top;\n  }\n  &[x-placement^=\"right\"] {\n    @extend .bs-popover-right;\n  }\n  &[x-placement^=\"bottom\"] {\n    @extend .bs-popover-bottom;\n  }\n  &[x-placement^=\"left\"] {\n    @extend .bs-popover-left;\n  }\n}\n\n\n// Offset the popover to account for the popover arrow\n.popover-header {\n  padding: $popover-header-padding-y $popover-header-padding-x;\n  margin-bottom: 0; // Reset the default from Reboot\n  @include font-size($font-size-base);\n  color: $popover-header-color;\n  background-color: $popover-header-bg;\n  border-bottom: $popover-border-width solid darken($popover-header-bg, 5%);\n  @include border-top-radius($popover-inner-border-radius);\n\n  &:empty {\n    display: none;\n  }\n}\n\n.popover-body {\n  padding: $popover-body-padding-y $popover-body-padding-x;\n  color: $popover-body-color;\n}\n", "// Notes on the classes:\n//\n// 1. .carousel.pointer-event should ideally be pan-y (to allow for users to scroll vertically)\n//    even when their scroll action started on a carousel, but for compatibility (with Firefox)\n//    we're preventing all actions instead\n// 2. The .carousel-item-left and .carousel-item-right is used to indicate where\n//    the active slide is heading.\n// 3. .active.carousel-item is the current slide.\n// 4. .active.carousel-item-left and .active.carousel-item-right is the current\n//    slide in its in-transition state. Only one of these occurs at a time.\n// 5. .carousel-item-next.carousel-item-left and .carousel-item-prev.carousel-item-right\n//    is the upcoming slide in transition.\n\n.carousel {\n  position: relative;\n}\n\n.carousel.pointer-event {\n  touch-action: pan-y;\n}\n\n.carousel-inner {\n  position: relative;\n  width: 100%;\n  overflow: hidden;\n  @include clearfix();\n}\n\n.carousel-item {\n  position: relative;\n  display: none;\n  float: left;\n  width: 100%;\n  margin-right: -100%;\n  backface-visibility: hidden;\n  @include transition($carousel-transition);\n}\n\n.carousel-item.active,\n.carousel-item-next,\n.carousel-item-prev {\n  display: block;\n}\n\n.carousel-item-next:not(.carousel-item-left),\n.active.carousel-item-right {\n  transform: translateX(100%);\n}\n\n.carousel-item-prev:not(.carousel-item-right),\n.active.carousel-item-left {\n  transform: translateX(-100%);\n}\n\n\n//\n// Alternate transitions\n//\n\n.carousel-fade {\n  .carousel-item {\n    opacity: 0;\n    transition-property: opacity;\n    transform: none;\n  }\n\n  .carousel-item.active,\n  .carousel-item-next.carousel-item-left,\n  .carousel-item-prev.carousel-item-right {\n    z-index: 1;\n    opacity: 1;\n  }\n\n  .active.carousel-item-left,\n  .active.carousel-item-right {\n    z-index: 0;\n    opacity: 0;\n    @include transition(opacity 0s $carousel-transition-duration);\n  }\n}\n\n\n//\n// Left/right controls for nav\n//\n\n.carousel-control-prev,\n.carousel-control-next {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  z-index: 1;\n  // Use flex for alignment (1-3)\n  display: flex; // 1. allow flex styles\n  align-items: center; // 2. vertically center contents\n  justify-content: center; // 3. horizontally center contents\n  width: $carousel-control-width;\n  color: $carousel-control-color;\n  text-align: center;\n  opacity: $carousel-control-opacity;\n  @include transition($carousel-control-transition);\n\n  // Hover/focus state\n  @include hover-focus() {\n    color: $carousel-control-color;\n    text-decoration: none;\n    outline: 0;\n    opacity: $carousel-control-hover-opacity;\n  }\n}\n.carousel-control-prev {\n  left: 0;\n  @if $enable-gradients {\n    background-image: linear-gradient(90deg, rgba($black, .25), rgba($black, .001));\n  }\n}\n.carousel-control-next {\n  right: 0;\n  @if $enable-gradients {\n    background-image: linear-gradient(270deg, rgba($black, .25), rgba($black, .001));\n  }\n}\n\n// Icons for within\n.carousel-control-prev-icon,\n.carousel-control-next-icon {\n  display: inline-block;\n  width: $carousel-control-icon-width;\n  height: $carousel-control-icon-width;\n  background: no-repeat 50% / 100% 100%;\n}\n.carousel-control-prev-icon {\n  background-image: escape-svg($carousel-control-prev-icon-bg);\n}\n.carousel-control-next-icon {\n  background-image: escape-svg($carousel-control-next-icon-bg);\n}\n\n\n// Optional indicator pips\n//\n// Add an ordered list with the following class and add a list item for each\n// slide your carousel holds.\n\n.carousel-indicators {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 15;\n  display: flex;\n  justify-content: center;\n  padding-left: 0; // override <ol> default\n  // Use the .carousel-control's width as margin so we don't overlay those\n  margin-right: $carousel-control-width;\n  margin-left: $carousel-control-width;\n  list-style: none;\n\n  li {\n    box-sizing: content-box;\n    flex: 0 1 auto;\n    width: $carousel-indicator-width;\n    height: $carousel-indicator-height;\n    margin-right: $carousel-indicator-spacer;\n    margin-left: $carousel-indicator-spacer;\n    text-indent: -999px;\n    cursor: pointer;\n    background-color: $carousel-indicator-active-bg;\n    background-clip: padding-box;\n    // Use transparent borders to increase the hit area by 10px on top and bottom.\n    border-top: $carousel-indicator-hit-area-height solid transparent;\n    border-bottom: $carousel-indicator-hit-area-height solid transparent;\n    opacity: .5;\n    @include transition($carousel-indicator-transition);\n  }\n\n  .active {\n    opacity: 1;\n  }\n}\n\n\n// Optional captions\n//\n//\n\n.carousel-caption {\n  position: absolute;\n  right: (100% - $carousel-caption-width) / 2;\n  bottom: 20px;\n  left: (100% - $carousel-caption-width) / 2;\n  z-index: 10;\n  padding-top: 20px;\n  padding-bottom: 20px;\n  color: $carousel-caption-color;\n  text-align: center;\n}\n", "@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n", "//\n// Rotating border\n//\n\n@keyframes spinner-border {\n  to { transform: rotate(360deg); }\n}\n\n.spinner-border {\n  display: inline-block;\n  width: $spinner-width;\n  height: $spinner-height;\n  vertical-align: text-bottom;\n  border: $spinner-border-width solid currentColor;\n  border-right-color: transparent;\n  // stylelint-disable-next-line property-blacklist\n  border-radius: 50%;\n  animation: spinner-border .75s linear infinite;\n}\n\n.spinner-border-sm {\n  width: $spinner-width-sm;\n  height: $spinner-height-sm;\n  border-width: $spinner-border-width-sm;\n}\n\n//\n// Growing circle\n//\n\n@keyframes spinner-grow {\n  0% {\n    transform: scale(0);\n  }\n  50% {\n    opacity: 1;\n  }\n}\n\n.spinner-grow {\n  display: inline-block;\n  width: $spinner-width;\n  height: $spinner-height;\n  vertical-align: text-bottom;\n  background-color: currentColor;\n  // stylelint-disable-next-line property-blacklist\n  border-radius: 50%;\n  opacity: 0;\n  animation: spinner-grow .75s linear infinite;\n}\n\n.spinner-grow-sm {\n  width: $spinner-width-sm;\n  height: $spinner-height-sm;\n}\n", "// stylelint-disable declaration-no-important\n\n.align-baseline    { vertical-align: baseline !important; } // Browser default\n.align-top         { vertical-align: top !important; }\n.align-middle      { vertical-align: middle !important; }\n.align-bottom      { vertical-align: bottom !important; }\n.align-text-bottom { vertical-align: text-bottom !important; }\n.align-text-top    { vertical-align: text-top !important; }\n", "// stylelint-disable declaration-no-important\n\n// Contextual backgrounds\n\n@mixin bg-variant($parent, $color, $ignore-warning: false) {\n  #{$parent} {\n    background-color: $color !important;\n  }\n  a#{$parent},\n  button#{$parent} {\n    @include hover-focus() {\n      background-color: darken($color, 10%) !important;\n    }\n  }\n  @include deprecate(\"The `bg-variant` mixin\", \"v4.4.0\", \"v5\", $ignore-warning);\n}\n\n@mixin bg-gradient-variant($parent, $color) {\n  #{$parent} {\n    background: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x !important;\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n@each $color, $value in $theme-colors {\n  @include bg-variant(\".bg-#{$color}\", $value, true);\n}\n\n@if $enable-gradients {\n  @each $color, $value in $theme-colors {\n    @include bg-gradient-variant(\".bg-gradient-#{$color}\", $value);\n  }\n}\n\n.bg-white {\n  background-color: $white !important;\n}\n\n.bg-transparent {\n  background-color: transparent !important;\n}\n", "// stylelint-disable property-blacklist, declaration-no-important\n\n//\n// Border\n//\n\n.border         { border: $border-width solid $border-color !important; }\n.border-top     { border-top: $border-width solid $border-color !important; }\n.border-right   { border-right: $border-width solid $border-color !important; }\n.border-bottom  { border-bottom: $border-width solid $border-color !important; }\n.border-left    { border-left: $border-width solid $border-color !important; }\n\n.border-0        { border: 0 !important; }\n.border-top-0    { border-top: 0 !important; }\n.border-right-0  { border-right: 0 !important; }\n.border-bottom-0 { border-bottom: 0 !important; }\n.border-left-0   { border-left: 0 !important; }\n\n@each $color, $value in $theme-colors {\n  .border-#{$color} {\n    border-color: $value !important;\n  }\n}\n\n.border-white {\n  border-color: $white !important;\n}\n\n//\n// Border-radius\n//\n\n.rounded-sm {\n  border-radius: $border-radius-sm !important;\n}\n\n.rounded {\n  border-radius: $border-radius !important;\n}\n\n.rounded-top {\n  border-top-left-radius: $border-radius !important;\n  border-top-right-radius: $border-radius !important;\n}\n\n.rounded-right {\n  border-top-right-radius: $border-radius !important;\n  border-bottom-right-radius: $border-radius !important;\n}\n\n.rounded-bottom {\n  border-bottom-right-radius: $border-radius !important;\n  border-bottom-left-radius: $border-radius !important;\n}\n\n.rounded-left {\n  border-top-left-radius: $border-radius !important;\n  border-bottom-left-radius: $border-radius !important;\n}\n\n.rounded-lg {\n  border-radius: $border-radius-lg !important;\n}\n\n.rounded-circle {\n  border-radius: 50% !important;\n}\n\n.rounded-pill {\n  border-radius: $rounded-pill !important;\n}\n\n.rounded-0 {\n  border-radius: 0 !important;\n}\n", "// stylelint-disable declaration-no-important\n\n//\n// Utilities for common `display` values\n//\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @each $value in $displays {\n      .d#{$infix}-#{$value} { display: $value !important; }\n    }\n  }\n}\n\n\n//\n// Utilities for toggling `display` in print\n//\n\n@media print {\n  @each $value in $displays {\n    .d-print-#{$value} { display: $value !important; }\n  }\n}\n", "// Credit: <PERSON> and <PERSON><PERSON><PERSON> CSS.\n\n.embed-responsive {\n  position: relative;\n  display: block;\n  width: 100%;\n  padding: 0;\n  overflow: hidden;\n\n  &::before {\n    display: block;\n    content: \"\";\n  }\n\n  .embed-responsive-item,\n  iframe,\n  embed,\n  object,\n  video {\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    border: 0;\n  }\n}\n\n@each $embed-responsive-aspect-ratio in $embed-responsive-aspect-ratios {\n  $embed-responsive-aspect-ratio-x: nth($embed-responsive-aspect-ratio, 1);\n  $embed-responsive-aspect-ratio-y: nth($embed-responsive-aspect-ratio, 2);\n\n  .embed-responsive-#{$embed-responsive-aspect-ratio-x}by#{$embed-responsive-aspect-ratio-y} {\n    &::before {\n      padding-top: percentage($embed-responsive-aspect-ratio-y / $embed-responsive-aspect-ratio-x);\n    }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n// Flex variation\n//\n// Custom styles for additional flex alignment options.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .flex#{$infix}-row            { flex-direction: row !important; }\n    .flex#{$infix}-column         { flex-direction: column !important; }\n    .flex#{$infix}-row-reverse    { flex-direction: row-reverse !important; }\n    .flex#{$infix}-column-reverse { flex-direction: column-reverse !important; }\n\n    .flex#{$infix}-wrap         { flex-wrap: wrap !important; }\n    .flex#{$infix}-nowrap       { flex-wrap: nowrap !important; }\n    .flex#{$infix}-wrap-reverse { flex-wrap: wrap-reverse !important; }\n    .flex#{$infix}-fill         { flex: 1 1 auto !important; }\n    .flex#{$infix}-grow-0       { flex-grow: 0 !important; }\n    .flex#{$infix}-grow-1       { flex-grow: 1 !important; }\n    .flex#{$infix}-shrink-0     { flex-shrink: 0 !important; }\n    .flex#{$infix}-shrink-1     { flex-shrink: 1 !important; }\n\n    .justify-content#{$infix}-start   { justify-content: flex-start !important; }\n    .justify-content#{$infix}-end     { justify-content: flex-end !important; }\n    .justify-content#{$infix}-center  { justify-content: center !important; }\n    .justify-content#{$infix}-between { justify-content: space-between !important; }\n    .justify-content#{$infix}-around  { justify-content: space-around !important; }\n\n    .align-items#{$infix}-start    { align-items: flex-start !important; }\n    .align-items#{$infix}-end      { align-items: flex-end !important; }\n    .align-items#{$infix}-center   { align-items: center !important; }\n    .align-items#{$infix}-baseline { align-items: baseline !important; }\n    .align-items#{$infix}-stretch  { align-items: stretch !important; }\n\n    .align-content#{$infix}-start   { align-content: flex-start !important; }\n    .align-content#{$infix}-end     { align-content: flex-end !important; }\n    .align-content#{$infix}-center  { align-content: center !important; }\n    .align-content#{$infix}-between { align-content: space-between !important; }\n    .align-content#{$infix}-around  { align-content: space-around !important; }\n    .align-content#{$infix}-stretch { align-content: stretch !important; }\n\n    .align-self#{$infix}-auto     { align-self: auto !important; }\n    .align-self#{$infix}-start    { align-self: flex-start !important; }\n    .align-self#{$infix}-end      { align-self: flex-end !important; }\n    .align-self#{$infix}-center   { align-self: center !important; }\n    .align-self#{$infix}-baseline { align-self: baseline !important; }\n    .align-self#{$infix}-stretch  { align-self: stretch !important; }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .float#{$infix}-left  { float: left !important; }\n    .float#{$infix}-right { float: right !important; }\n    .float#{$infix}-none  { float: none !important; }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n@each $value in $overflows {\n  .overflow-#{$value} { overflow: $value !important; }\n}\n", "// stylelint-disable declaration-no-important\n\n// Common values\n@each $position in $positions {\n  .position-#{$position} { position: $position !important; }\n}\n\n// Shorthand\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.sticky-top {\n  @supports (position: sticky) {\n    position: sticky;\n    top: 0;\n    z-index: $zindex-sticky;\n  }\n}\n", "//\n// Screenreaders\n//\n\n.sr-only {\n  @include sr-only();\n}\n\n.sr-only-focusable {\n  @include sr-only-focusable();\n}\n", "// Only display content to screen readers\n//\n// See: https://a11yproject.com/posts/how-to-hide-content/\n// See: https://hugogiraudel.com/2016/10/13/css-hide-and-seek/\n\n@mixin sr-only() {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px; // Fix for https://github.com/twbs/bootstrap/issues/25686\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n\n// Use in conjunction with .sr-only to only display content when it's focused.\n//\n// Useful for \"Skip to main content\" links; see https://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n//\n// Credit: HTML5 Boilerplate\n\n@mixin sr-only-focusable() {\n  &:active,\n  &:focus {\n    position: static;\n    width: auto;\n    height: auto;\n    overflow: visible;\n    clip: auto;\n    white-space: normal;\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n.shadow-sm { box-shadow: $box-shadow-sm !important; }\n.shadow { box-shadow: $box-shadow !important; }\n.shadow-lg { box-shadow: $box-shadow-lg !important; }\n.shadow-none { box-shadow: none !important; }\n", "// stylelint-disable declaration-no-important\n\n// Width and height\n\n@each $prop, $abbrev in (width: w, height: h) {\n  @each $size, $length in $sizes {\n    .#{$abbrev}-#{$size} { #{$prop}: $length !important; }\n  }\n}\n\n.mw-100 { max-width: 100% !important; }\n.mh-100 { max-height: 100% !important; }\n\n// Viewport additional helpers\n\n.min-vw-100 { min-width: 100vw !important; }\n.min-vh-100 { min-height: 100vh !important; }\n\n.vw-100 { width: 100vw !important; }\n.vh-100 { height: 100vh !important; }\n", "//\n// Stretched link\n//\n\n.stretched-link {\n  &::after {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: 1;\n    // Just in case `pointer-events: none` is set on a parent\n    pointer-events: auto;\n    content: \"\";\n    // IE10 bugfix, see https://stackoverflow.com/questions/16947967/ie10-hover-pseudo-class-doesnt-work-without-background-color\n    background-color: rgba(0, 0, 0, 0);\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n// Mar<PERSON> and Padding\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @each $prop, $abbrev in (margin: m, padding: p) {\n      @each $size, $length in $spacers {\n        .#{$abbrev}#{$infix}-#{$size} { #{$prop}: $length !important; }\n        .#{$abbrev}t#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-top: $length !important;\n        }\n        .#{$abbrev}r#{$infix}-#{$size},\n        .#{$abbrev}x#{$infix}-#{$size} {\n          #{$prop}-right: $length !important;\n        }\n        .#{$abbrev}b#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-bottom: $length !important;\n        }\n        .#{$abbrev}l#{$infix}-#{$size},\n        .#{$abbrev}x#{$infix}-#{$size} {\n          #{$prop}-left: $length !important;\n        }\n      }\n    }\n\n    // Negative margins (e.g., where `.mb-n1` is negative version of `.mb-1`)\n    @each $size, $length in $spacers {\n      @if $size != 0 {\n        .m#{$infix}-n#{$size} { margin: -$length !important; }\n        .mt#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-top: -$length !important;\n        }\n        .mr#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-right: -$length !important;\n        }\n        .mb#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-bottom: -$length !important;\n        }\n        .ml#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-left: -$length !important;\n        }\n      }\n    }\n\n    // Some special margin utils\n    .m#{$infix}-auto { margin: auto !important; }\n    .mt#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-top: auto !important;\n    }\n    .mr#{$infix}-auto,\n    .mx#{$infix}-auto {\n      margin-right: auto !important;\n    }\n    .mb#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-bottom: auto !important;\n    }\n    .ml#{$infix}-auto,\n    .mx#{$infix}-auto {\n      margin-left: auto !important;\n    }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n//\n// Text\n//\n\n.text-monospace { font-family: $font-family-monospace !important; }\n\n// Alignment\n\n.text-justify  { text-align: justify !important; }\n.text-wrap     { white-space: normal !important; }\n.text-nowrap   { white-space: nowrap !important; }\n.text-truncate { @include text-truncate(); }\n\n// Responsive alignment\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .text#{$infix}-left   { text-align: left !important; }\n    .text#{$infix}-right  { text-align: right !important; }\n    .text#{$infix}-center { text-align: center !important; }\n  }\n}\n\n// Transformation\n\n.text-lowercase  { text-transform: lowercase !important; }\n.text-uppercase  { text-transform: uppercase !important; }\n.text-capitalize { text-transform: capitalize !important; }\n\n// Weight and italics\n\n.font-weight-light   { font-weight: $font-weight-light !important; }\n.font-weight-lighter { font-weight: $font-weight-lighter !important; }\n.font-weight-normal  { font-weight: $font-weight-normal !important; }\n.font-weight-bold    { font-weight: $font-weight-bold !important; }\n.font-weight-bolder  { font-weight: $font-weight-bolder !important; }\n.font-italic         { font-style: italic !important; }\n\n// Contextual colors\n\n.text-white { color: $white !important; }\n\n@each $color, $value in $theme-colors {\n  @include text-emphasis-variant(\".text-#{$color}\", $value, true);\n}\n\n.text-body { color: $body-color !important; }\n.text-muted { color: $text-muted !important; }\n\n.text-black-50 { color: rgba($black, .5) !important; }\n.text-white-50 { color: rgba($white, .5) !important; }\n\n// Misc\n\n.text-hide {\n  @include text-hide($ignore-warning: true);\n}\n\n.text-decoration-none { text-decoration: none !important; }\n\n.text-break {\n  word-break: break-word !important; // IE & < Edge 18\n  overflow-wrap: break-word !important;\n}\n\n// Reset\n\n.text-reset { color: inherit !important; }\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", "// stylelint-disable declaration-no-important\n\n// Typography\n\n@mixin text-emphasis-variant($parent, $color, $ignore-warning: false) {\n  #{$parent} {\n    color: $color !important;\n  }\n  @if $emphasized-link-hover-darken-percentage != 0 {\n    a#{$parent} {\n      @include hover-focus() {\n        color: darken($color, $emphasized-link-hover-darken-percentage) !important;\n      }\n    }\n  }\n  @include deprecate(\"`text-emphasis-variant()`\", \"v4.4.0\", \"v5\", $ignore-warning);\n}\n", "// CSS image replacement\n@mixin text-hide($ignore-warning: false) {\n  // stylelint-disable-next-line font-family-no-missing-generic-family-keyword\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0;\n\n  @include deprecate(\"`text-hide()`\", \"v4.1.0\", \"v5\", $ignore-warning);\n}\n", "// stylelint-disable declaration-no-important\n\n//\n// Visibility utilities\n//\n\n.visible {\n  visibility: visible !important;\n}\n\n.invisible {\n  visibility: hidden !important;\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// stylelint-disable declaration-no-important, selector-no-qualifying-type\n\n// Source: https://github.com/h5bp/main.css/blob/master/src/_print.css\n\n// ==========================================================================\n// Print styles.\n// Inlined to avoid the additional HTTP request:\n// https://www.phpied.com/delay-loading-your-print-css/\n// ==========================================================================\n\n@if $enable-print-styles {\n  @media print {\n    *,\n    *::before,\n    *::after {\n      // Bootstrap specific; comment out `color` and `background`\n      //color: $black !important; // Black prints faster\n      text-shadow: none !important;\n      //background: transparent !important;\n      box-shadow: none !important;\n    }\n\n    a {\n      &:not(.btn) {\n        text-decoration: underline;\n      }\n    }\n\n    // Bootstrap specific; comment the following selector out\n    //a[href]::after {\n    //  content: \" (\" attr(href) \")\";\n    //}\n\n    abbr[title]::after {\n      content: \" (\" attr(title) \")\";\n    }\n\n    // Bootstrap specific; comment the following selector out\n    //\n    // Don't show links that are fragment identifiers,\n    // or use the `javascript:` pseudo protocol\n    //\n\n    //a[href^=\"#\"]::after,\n    //a[href^=\"javascript:\"]::after {\n    // content: \"\";\n    //}\n\n    pre {\n      white-space: pre-wrap !important;\n    }\n    pre,\n    blockquote {\n      border: $border-width solid $gray-500; // Bootstrap custom code; using `$border-width` instead of 1px\n      page-break-inside: avoid;\n    }\n\n    //\n    // Printing Tables:\n    // https://web.archive.org/web/20180815150934/http://css-discuss.incutio.com/wiki/Printing_Tables\n    //\n\n    thead {\n      display: table-header-group;\n    }\n\n    tr,\n    img {\n      page-break-inside: avoid;\n    }\n\n    p,\n    h2,\n    h3 {\n      orphans: 3;\n      widows: 3;\n    }\n\n    h2,\n    h3 {\n      page-break-after: avoid;\n    }\n\n    // Bootstrap specific changes start\n\n    // Specify a size and min-width to make printing closer across browsers.\n    // We don't set margin here because it breaks `size` in Chrome. We also\n    // don't use `!important` on `size` as it breaks in Chrome.\n    @page {\n      size: $print-page-size;\n    }\n    body {\n      min-width: $print-body-min-width !important;\n    }\n    .container {\n      min-width: $print-body-min-width !important;\n    }\n\n    // Bootstrap components\n    .navbar {\n      display: none;\n    }\n    .badge {\n      border: $border-width solid $black;\n    }\n\n    .table {\n      border-collapse: collapse !important;\n\n      td,\n      th {\n        background-color: $white !important;\n      }\n    }\n\n    .table-bordered {\n      th,\n      td {\n        border: 1px solid $gray-300 !important;\n      }\n    }\n\n    .table-dark {\n      color: inherit;\n\n      th,\n      td,\n      thead th,\n      tbody + tbody {\n        border-color: $table-border-color;\n      }\n    }\n\n    .table .thead-dark th {\n      color: inherit;\n      border-color: $table-border-color;\n    }\n\n    // Bootstrap specific changes end\n  }\n}\n"]}