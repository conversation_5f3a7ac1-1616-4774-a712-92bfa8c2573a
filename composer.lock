{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "54f828784a479f284e3802f7e7ddfab1", "packages": [{"name": "composer/ca-bundle", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "558f321c52faeb4828c03e7dc0cfe39a09e09a2d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/558f321c52faeb4828c03e7dc0cfe39a09e09a2d", "reference": "558f321c52faeb4828c03e7dc0cfe39a09e09a2d", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5", "psr/log": "^1.0", "symfony/process": "^2.5 || ^3.0 || ^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "time": "2019-01-28T09:30:10+00:00"}, {"name": "composer/composer", "version": "1.8.5", "source": {"type": "git", "url": "https://github.com/composer/composer.git", "reference": "949b116f9e7d98d8d276594fed74b580d125c0e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/composer/zipball/949b116f9e7d98d8d276594fed74b580d125c0e6", "reference": "949b116f9e7d98d8d276594fed74b580d125c0e6", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0", "composer/semver": "^1.0", "composer/spdx-licenses": "^1.2", "composer/xdebug-handler": "^1.1", "justinrainbow/json-schema": "^3.0 || ^4.0 || ^5.0", "php": "^5.3.2 || ^7.0", "psr/log": "^1.0", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.0", "symfony/console": "^2.7 || ^3.0 || ^4.0", "symfony/filesystem": "^2.7 || ^3.0 || ^4.0", "symfony/finder": "^2.7 || ^3.0 || ^4.0", "symfony/process": "^2.7 || ^3.0 || ^4.0"}, "conflict": {"symfony/console": "2.8.38"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7", "phpunit/phpunit-mock-objects": "^2.3 || ^3.0"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "bin": ["bin/composer"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"Composer\\": "src/Composer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Composer helps you declare, manage and install dependencies of PHP projects, ensuring you have the right stack everywhere.", "homepage": "https://getcomposer.org/", "keywords": ["autoload", "dependency", "package"], "time": "2019-04-09T15:46:48+00:00"}, {"name": "composer/semver", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "46d9139568ccb8d9e7cdd4539cab7347568a5e2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/46d9139568ccb8d9e7cdd4539cab7347568a5e2e", "reference": "46d9139568ccb8d9e7cdd4539cab7347568a5e2e", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.5 || ^5.0.5", "phpunit/phpunit-mock-objects": "2.3.0 || ^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "time": "2019-03-19T17:25:45+00:00"}, {"name": "composer/spdx-licenses", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/composer/spdx-licenses.git", "reference": "a1aa51cf3ab838b83b0867b14e56fc20fbd55b3d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/spdx-licenses/zipball/a1aa51cf3ab838b83b0867b14e56fc20fbd55b3d", "reference": "a1aa51cf3ab838b83b0867b14e56fc20fbd55b3d", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "time": "2019-03-26T10:23:26+00:00"}, {"name": "composer/xdebug-handler", "version": "1.3.2", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "d17708133b6c276d6e42ef887a877866b909d892"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/d17708133b6c276d6e42ef887a877866b909d892", "reference": "d17708133b6c276d6e42ef887a877866b909d892", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0", "psr/log": "^1.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without xdebug.", "keywords": ["Xdebug", "performance"], "time": "2019-01-28T20:25:53+00:00"}, {"name": "doctrine/annotations", "version": "v1.6.1", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "53120e0eb10355388d6ccbe462f1fea34ddadb24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/53120e0eb10355388d6ccbe462f1fea34ddadb24", "reference": "53120e0eb10355388d6ccbe462f1fea34ddadb24", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "php": "^7.1"}, "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "http://www.doctrine-project.org", "keywords": ["annotations", "doc<PERSON>", "parser"], "time": "2019-03-25T19:12:02+00:00"}, {"name": "doctrine/cache", "version": "v1.8.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "d768d58baee9a4862ca783840eca1b9add7a7f57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/d768d58baee9a4862ca783840eca1b9add7a7f57", "reference": "d768d58baee9a4862ca783840eca1b9add7a7f57", "shasum": ""}, "require": {"php": "~7.1"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "doctrine/coding-standard": "^4.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0", "predis/predis": "~1.0"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Caching library offering an object-oriented API for many cache backends", "homepage": "https://www.doctrine-project.org", "keywords": ["cache", "caching"], "time": "2018-08-21T18:01:43+00:00"}, {"name": "doctrine/collections", "version": "v1.6.1", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "d2ae4ef05e25197343b6a39bae1d3c427a2f6956"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/d2ae4ef05e25197343b6a39bae1d3c427a2f6956", "reference": "d2ae4ef05e25197343b6a39bae1d3c427a2f6956", "shasum": ""}, "require": {"php": "^7.1.3"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan-shim": "^0.9.2", "phpunit/phpunit": "^7.0", "vimeo/psalm": "^3.2.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "time": "2019-03-25T19:03:48+00:00"}, {"name": "doctrine/common", "version": "v2.10.0", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "30e33f60f64deec87df728c02b107f82cdafad9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/30e33f60f64deec87df728c02b107f82cdafad9d", "reference": "30e33f60f64deec87df728c02b107f82cdafad9d", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/inflector": "^1.0", "doctrine/lexer": "^1.0", "doctrine/persistence": "^1.1", "doctrine/reflection": "^1.0", "php": "^7.1"}, "require-dev": {"doctrine/coding-standard": "^1.0", "phpunit/phpunit": "^6.3", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.10.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, persistence interfaces, proxies, event system and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "time": "2018-11-21T01:24:55+00:00"}, {"name": "doctrine/data-fixtures", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/doctrine/data-fixtures.git", "reference": "3a1e2c3c600e615a2dffe56d4ca0875cc5233e0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/data-fixtures/zipball/3a1e2c3c600e615a2dffe56d4ca0875cc5233e0a", "reference": "3a1e2c3c600e615a2dffe56d4ca0875cc5233e0a", "shasum": ""}, "require": {"doctrine/common": "~2.2", "php": "^7.1"}, "conflict": {"doctrine/phpcr-odm": "<1.3.0"}, "require-dev": {"doctrine/dbal": "^2.5.4", "doctrine/orm": "^2.5.4", "phpunit/phpunit": "^7.0"}, "suggest": {"alcaeus/mongo-php-adapter": "For using MongoDB ODM with PHP 7", "doctrine/mongodb-odm": "For loading MongoDB ODM fixtures", "doctrine/orm": "For loading ORM fixtures", "doctrine/phpcr-odm": "For loading PHPCR ODM fixtures"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\DataFixtures\\": "lib/Doctrine/Common/DataFixtures"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Data Fixtures for all Doctrine Object Managers", "homepage": "http://www.doctrine-project.org", "keywords": ["database"], "time": "2018-03-20T09:06:36+00:00"}, {"name": "doctrine/dbal", "version": "v2.9.2", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "22800bd651c1d8d2a9719e2a3dc46d5108ebfcc9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/22800bd651c1d8d2a9719e2a3dc46d5108ebfcc9", "reference": "22800bd651c1d8d2a9719e2a3dc46d5108ebfcc9", "shasum": ""}, "require": {"doctrine/cache": "^1.0", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.1"}, "require-dev": {"doctrine/coding-standard": "^5.0", "jetbrains/phpstorm-stubs": "^2018.1.2", "phpstan/phpstan": "^0.10.1", "phpunit/phpunit": "^7.4", "symfony/console": "^2.0.5|^3.0|^4.0", "symfony/phpunit-bridge": "^3.4.5|^4.0.5"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.9.x-dev", "dev-develop": "3.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "dbal", "mysql", "persistence", "pgsql", "php", "queryobject"], "time": "2018-12-31T03:27:51+00:00"}, {"name": "doctrine/doctrine-bundle", "version": "1.10.2", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineBundle.git", "reference": "1f99e6645030542079c57d4680601a4a8778a1bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineBundle/zipball/1f99e6645030542079c57d4680601a4a8778a1bd", "reference": "1f99e6645030542079c57d4680601a4a8778a1bd", "shasum": ""}, "require": {"doctrine/dbal": "^2.5.12", "doctrine/doctrine-cache-bundle": "~1.2", "jdorn/sql-formatter": "^1.2.16", "php": "^5.5.9|^7.0", "symfony/console": "~2.7|~3.0|~4.0", "symfony/dependency-injection": "~2.7|~3.0|~4.0", "symfony/doctrine-bridge": "~2.7|~3.0|~4.0", "symfony/framework-bundle": "^2.7.22|~3.0|~4.0"}, "conflict": {"symfony/http-foundation": "<2.6"}, "require-dev": {"doctrine/orm": "~2.4", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^4.8.36|^5.7|^6.4", "symfony/phpunit-bridge": "~2.7|~3.0|~4.0", "symfony/property-info": "~2.8|~3.0|~4.0", "symfony/validator": "~2.7|~3.0|~4.0", "symfony/web-profiler-bundle": "~2.7|~3.0|~4.0", "symfony/yaml": "~2.7|~3.0|~4.0", "twig/twig": "~1.26|~2.0"}, "suggest": {"doctrine/orm": "The Doctrine ORM integration is optional in the bundle.", "symfony/web-profiler-bundle": "To use the data collector."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony DoctrineBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["database", "dbal", "orm", "persistence"], "time": "2019-02-06T13:18:04+00:00"}, {"name": "doctrine/doctrine-cache-bundle", "version": "1.3.5", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineCacheBundle.git", "reference": "5514c90d9fb595e1095e6d66ebb98ce9ef049927"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineCacheBundle/zipball/5514c90d9fb595e1095e6d66ebb98ce9ef049927", "reference": "5514c90d9fb595e1095e6d66ebb98ce9ef049927", "shasum": ""}, "require": {"doctrine/cache": "^1.4.2", "doctrine/inflector": "~1.0", "php": ">=5.3.2", "symfony/doctrine-bridge": "~2.7|~3.3|~4.0"}, "require-dev": {"instaclick/coding-standard": "~1.1", "instaclick/object-calisthenics-sniffs": "dev-master", "instaclick/symfony2-coding-standard": "dev-remaster", "phpunit/phpunit": "~4.8.36|~5.6|~6.5|~7.0", "predis/predis": "~0.8", "satooshi/php-coveralls": "^1.0", "squizlabs/php_codesniffer": "~1.5", "symfony/console": "~2.7|~3.3|~4.0", "symfony/finder": "~2.7|~3.3|~4.0", "symfony/framework-bundle": "~2.7|~3.3|~4.0", "symfony/phpunit-bridge": "~2.7|~3.3|~4.0", "symfony/security-acl": "~2.7|~3.3", "symfony/validator": "~2.7|~3.3|~4.0", "symfony/yaml": "~2.7|~3.3|~4.0"}, "suggest": {"symfony/security-acl": "For using this bundle to cache ACLs"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineCacheBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON><PERSON><EMAIL>"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony Bundle for Doctrine Cache", "homepage": "https://www.doctrine-project.org", "keywords": ["cache", "caching"], "time": "2018-11-09T06:25:35+00:00"}, {"name": "doctrine/doctrine-fixtures-bundle", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineFixturesBundle.git", "reference": "f016565b251c2dfa32a8d6da44d1650dc9ec1498"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineFixturesBundle/zipball/f016565b251c2dfa32a8d6da44d1650dc9ec1498", "reference": "f016565b251c2dfa32a8d6da44d1650dc9ec1498", "shasum": ""}, "require": {"doctrine/data-fixtures": "^1.3", "doctrine/doctrine-bundle": "^1.6", "php": "^7.1", "symfony/doctrine-bridge": "~3.4|^4.1", "symfony/framework-bundle": "^3.4|^4.1"}, "require-dev": {"doctrine/coding-standard": "^5.0", "phpunit/phpunit": "^7.4", "symfony/phpunit-bridge": "^4.1"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\FixturesBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony DoctrineFixturesBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["Fixture", "persistence"], "time": "2018-12-21T10:10:51+00:00"}, {"name": "doctrine/doctrine-migrations-bundle", "version": "v1.3.2", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineMigrationsBundle.git", "reference": "49fa399181db4bf4f9f725126bd1cb65c4398dce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineMigrationsBundle/zipball/49fa399181db4bf4f9f725126bd1cb65c4398dce", "reference": "49fa399181db4bf4f9f725126bd1cb65c4398dce", "shasum": ""}, "require": {"doctrine/doctrine-bundle": "~1.0", "doctrine/migrations": "^1.1", "php": ">=5.4.0", "symfony/framework-bundle": "~2.7|~3.3|~4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^7.4"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\MigrationsBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony DoctrineMigrationsBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["dbal", "migrations", "schema"], "time": "2018-12-03T11:55:33+00:00"}, {"name": "doctrine/event-manager", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "a520bc093a0170feeb6b14e9d83f3a14452e64b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/a520bc093a0170feeb6b14e9d83f3a14452e64b3", "reference": "a520bc093a0170feeb6b14e9d83f3a14452e64b3", "shasum": ""}, "require": {"php": "^7.1"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^4.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Doctrine Event Manager component", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "eventdispatcher", "eventmanager"], "time": "2018-06-11T11:59:03+00:00"}, {"name": "doctrine/inflector", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "5527a48b7313d15261292c149e55e26eae771b0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/5527a48b7313d15261292c149e55e26eae771b0a", "reference": "5527a48b7313d15261292c149e55e26eae771b0a", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Common String Manipulations with regard to casing and singular/plural rules.", "homepage": "http://www.doctrine-project.org", "keywords": ["inflection", "pluralize", "singularize", "string"], "time": "2018-01-09T20:05:19+00:00"}, {"name": "doctrine/instantiator", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "a2c590166b2133a4633738648b6b064edae0814a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/a2c590166b2133a4633738648b6b064edae0814a", "reference": "a2c590166b2133a4633738648b6b064edae0814a", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"doctrine/coding-standard": "^6.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-shim": "^0.11", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "time": "2019-03-17T17:37:11+00:00"}, {"name": "doctrine/lexer", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "83893c552fd2045dd78aef794c31e694c37c0b8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/83893c552fd2045dd78aef794c31e694c37c0b8c", "reference": "83893c552fd2045dd78aef794c31e694c37c0b8c", "shasum": ""}, "require": {"php": ">=5.3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\Lexer\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Base library for a lexer that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "http://www.doctrine-project.org", "keywords": ["lexer", "parser"], "time": "2014-09-09T13:34:57+00:00"}, {"name": "doctrine/migrations", "version": "v1.8.1", "source": {"type": "git", "url": "https://github.com/doctrine/migrations.git", "reference": "215438c0eef3e5f9b7da7d09c6b90756071b43e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/migrations/zipball/215438c0eef3e5f9b7da7d09c6b90756071b43e6", "reference": "215438c0eef3e5f9b7da7d09c6b90756071b43e6", "shasum": ""}, "require": {"doctrine/dbal": "~2.6", "ocramius/proxy-manager": "^1.0|^2.0", "php": "^7.1", "symfony/console": "~3.3|^4.0"}, "require-dev": {"doctrine/coding-standard": "^1.0", "doctrine/orm": "~2.5", "jdorn/sql-formatter": "~1.1", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": "~7.0", "squizlabs/php_codesniffer": "^3.0", "symfony/yaml": "~3.3|^4.0"}, "suggest": {"jdorn/sql-formatter": "Allows to generate formatted SQL with the diff command.", "symfony/yaml": "Allows the use of yaml for migration configuration files."}, "bin": ["bin/doctrine-migrations"], "type": "library", "extra": {"branch-alias": {"dev-master": "v1.8.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\DBAL\\Migrations\\": "lib/Doctrine/DBAL/Migrations", "Doctrine\\Migrations\\": "lib/Doctrine/Migrations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Database Schema migrations using Doctrine DBAL", "homepage": "https://www.doctrine-project.org/projects/migrations.html", "keywords": ["database", "migrations"], "time": "2018-06-06T21:00:30+00:00"}, {"name": "doctrine/orm", "version": "v2.6.3", "source": {"type": "git", "url": "https://github.com/doctrine/orm.git", "reference": "434820973cadf2da2d66e7184be370084cc32ca8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/orm/zipball/434820973cadf2da2d66e7184be370084cc32ca8", "reference": "434820973cadf2da2d66e7184be370084cc32ca8", "shasum": ""}, "require": {"doctrine/annotations": "~1.5", "doctrine/cache": "~1.6", "doctrine/collections": "^1.4", "doctrine/common": "^2.7.1", "doctrine/dbal": "^2.6", "doctrine/instantiator": "~1.1", "ext-pdo": "*", "php": "^7.1", "symfony/console": "~3.0|~4.0"}, "require-dev": {"doctrine/coding-standard": "^1.0", "phpunit/phpunit": "^6.5", "squizlabs/php_codesniffer": "^3.2", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"symfony/yaml": "If you want to use YAML Metadata Mapping Driver"}, "bin": ["bin/doctrine"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.6.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\ORM\\": "lib/Doctrine/ORM"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Object-Relational-Mapper for PHP", "homepage": "http://www.doctrine-project.org", "keywords": ["database", "orm"], "time": "2018-11-20T23:46:46+00:00"}, {"name": "doctrine/persistence", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "3da7c9d125591ca83944f477e65ed3d7b4617c48"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/3da7c9d125591ca83944f477e65ed3d7b4617c48", "reference": "3da7c9d125591ca83944f477e65ed3d7b4617c48", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.0", "php": "^7.1"}, "conflict": {"doctrine/common": "<2.10@dev"}, "require-dev": {"doctrine/coding-standard": "^5.0", "phpstan/phpstan": "^0.8", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "time": "2019-04-23T08:28:24+00:00"}, {"name": "doctrine/reflection", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/reflection.git", "reference": "02538d3f95e88eb397a5f86274deb2c6175c2ab6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/reflection/zipball/02538d3f95e88eb397a5f86274deb2c6175c2ab6", "reference": "02538d3f95e88eb397a5f86274deb2c6175c2ab6", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "ext-tokenizer": "*", "php": "^7.1"}, "require-dev": {"doctrine/coding-standard": "^4.0", "doctrine/common": "^2.8", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpunit/phpunit": "^7.0", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Doctrine Reflection component", "homepage": "https://www.doctrine-project.org/projects/reflection.html", "keywords": ["reflection"], "time": "2018-06-14T14:45:07+00:00"}, {"name": "easycorp/easy-log-handler", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/EasyCorp/easy-log-handler.git", "reference": "5f95717248d20684f88cfb878d8bf3d78aadcbba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/EasyCorp/easy-log-handler/zipball/5f95717248d20684f88cfb878d8bf3d78aadcbba", "reference": "5f95717248d20684f88cfb878d8bf3d78aadcbba", "shasum": ""}, "require": {"monolog/monolog": "~1.6", "php": ">=5.3.0", "symfony/yaml": "~2.3|~3.0|~4.0"}, "type": "library", "autoload": {"psr-4": {"EasyCorp\\EasyLog\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "javieregu<PERSON><EMAIL>"}, {"name": "Project Contributors", "homepage": "https://github.com/EasyCorp/easy-log-handler"}], "description": "A handler for Monolog that optimizes log messages to be processed by humans instead of software. Improve your productivity with logs that are easy to understand.", "homepage": "https://github.com/EasyCorp/easy-log-handler", "keywords": ["easy", "log", "logging", "monolog", "productivity"], "time": "2018-07-27T15:41:37+00:00"}, {"name": "ec-cube/plugin-installer", "version": "0.0.8", "source": {"type": "git", "url": "https://github.com/EC-CUBE/eccube-plugin-installer.git", "reference": "6185cd43ae0dd0423bebc9bb900d096356340f80"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/EC-CUBE/eccube-plugin-installer/zipball/6185cd43ae0dd0423bebc9bb900d096356340f80", "reference": "6185cd43ae0dd0423bebc9bb900d096356340f80", "shasum": ""}, "require": {"composer-plugin-api": "^1.0"}, "type": "composer-plugin", "extra": {"class": "Eccube\\Composer\\EccubePluginInstallerPlugin"}, "autoload": {"psr-0": {"Eccube": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "EC-CUBE plugin installer.", "time": "2018-12-13T08:14:58+00:00"}, {"name": "egulias/email-validator", "version": "2.1.7", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "709f21f92707308cdf8f9bcfa1af4cb26586521e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/709f21f92707308cdf8f9bcfa1af4cb26586521e", "reference": "709f21f92707308cdf8f9bcfa1af4cb26586521e", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">= 5.5"}, "require-dev": {"dominicsayers/isemail": "dev-master", "phpunit/phpunit": "^4.8.35||^5.7||^6.0", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "EmailValidator"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "time": "2018-12-04T22:38:24+00:00"}, {"name": "friendsofphp/php-cs-fixer", "version": "v2.14.2", "source": {"type": "git", "url": "https://github.com/FriendsOfPHP/PHP-CS-Fixer.git", "reference": "ff401e58261ffc5934a58f795b3f95b355e276cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfPHP/PHP-CS-Fixer/zipball/ff401e58261ffc5934a58f795b3f95b355e276cb", "reference": "ff401e58261ffc5934a58f795b3f95b355e276cb", "shasum": ""}, "require": {"composer/semver": "^1.4", "composer/xdebug-handler": "^1.2", "doctrine/annotations": "^1.2", "ext-json": "*", "ext-tokenizer": "*", "php": "^5.6 || ^7.0", "php-cs-fixer/diff": "^1.3", "symfony/console": "^3.4.17 || ^4.1.6", "symfony/event-dispatcher": "^3.0 || ^4.0", "symfony/filesystem": "^3.0 || ^4.0", "symfony/finder": "^3.0 || ^4.0", "symfony/options-resolver": "^3.0 || ^4.0", "symfony/polyfill-php70": "^1.0", "symfony/polyfill-php72": "^1.4", "symfony/process": "^3.0 || ^4.0", "symfony/stopwatch": "^3.0 || ^4.0"}, "require-dev": {"johnkary/phpunit-speedtrap": "^1.1 || ^2.0 || ^3.0", "justinrainbow/json-schema": "^5.0", "keradus/cli-executor": "^1.2", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.1", "php-cs-fixer/accessible-object": "^1.0", "php-cs-fixer/phpunit-constraint-isidenticalstring": "^1.0.1", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "^1.0.1", "phpunit/phpunit": "^5.7.27 || ^6.5.8 || ^7.1", "phpunitgoodpractices/traits": "^1.5.1", "symfony/phpunit-bridge": "^4.0"}, "suggest": {"ext-mbstring": "For handling non-UTF8 characters in cache signature.", "php-cs-fixer/phpunit-constraint-isidenticalstring": "For IsIdenticalString constraint.", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "For XmlMatchesXsd constraint.", "symfony/polyfill-mbstring": "When enabling `ext-mbstring` is not possible."}, "bin": ["php-cs-fixer"], "type": "application", "autoload": {"psr-4": {"PhpCsFixer\\": "src/"}, "classmap": ["tests/Test/AbstractFixerTestCase.php", "tests/Test/AbstractIntegrationCaseFactory.php", "tests/Test/AbstractIntegrationTestCase.php", "tests/Test/Assert/AssertTokensTrait.php", "tests/Test/IntegrationCase.php", "tests/Test/IntegrationCaseFactory.php", "tests/Test/IntegrationCaseFactoryInterface.php", "tests/Test/InternalIntegrationCaseFactory.php", "tests/TestCase.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A tool to automatically fix PHP code style", "time": "2019-02-17T17:44:13+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.3.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/407b0cb880ace85c9b63c5f9551db498cb2d50ba", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba", "shasum": ""}, "require": {"guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.4", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.3-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2018-04-22T15:46:56+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.5.2", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "9f83dded91781a01c63574e387eaa769be769115"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/9f83dded91781a01c63574e387eaa769be769115", "reference": "9f83dded91781a01c63574e387eaa769be769115", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2018-12-04T20:46:45+00:00"}, {"name": "jdorn/sql-formatter", "version": "v1.2.17", "source": {"type": "git", "url": "https://github.com/jdorn/sql-formatter.git", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jdorn/sql-formatter/zipball/64990d96e0959dff8e059dfcdc1af130728d92bc", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["lib"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/jdorn/sql-formatter/", "keywords": ["highlight", "sql"], "time": "2014-01-12T16:20:24+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "5.2.8", "source": {"type": "git", "url": "https://github.com/justinrainbow/json-schema.git", "reference": "dcb6e1006bb5fd1e392b4daa68932880f37550d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/justinrainbow/json-schema/zipball/dcb6e1006bb5fd1e392b4daa68932880f37550d4", "reference": "dcb6e1006bb5fd1e392b4daa68932880f37550d4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.2.20", "json-schema/json-schema-test-suite": "1.2.0", "phpunit/phpunit": "^4.8.35"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "time": "2019-01-14T23:55:14+00:00"}, {"name": "knplabs/knp-components", "version": "v1.3.10", "source": {"type": "git", "url": "https://github.com/KnpLabs/knp-components.git", "reference": "fc1755ba2b77f83a3d3c99e21f3026ba2a1429be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/knp-components/zipball/fc1755ba2b77f83a3d3c99e21f3026ba2a1429be", "reference": "fc1755ba2b77f83a3d3c99e21f3026ba2a1429be", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"doctrine/mongodb-odm": "~1.0@beta", "doctrine/orm": "~2.4", "doctrine/phpcr-odm": "~1.2", "jackalope/jackalope-doctrine-dbal": "~1.2", "phpunit/phpunit": "~4.2", "ruflin/elastica": "~1.0", "symfony/event-dispatcher": "~2.5", "symfony/property-access": ">=2.3"}, "suggest": {"symfony/property-access": "To allow sorting arrays"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Knp\\Component": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/knp-components/contributors"}], "description": "Knplabs component library", "homepage": "http://github.com/KnpLabs/knp-components", "keywords": ["components", "knp", "knplabs", "pager", "paginator"], "time": "2018-09-11T07:54:48+00:00"}, {"name": "knplabs/knp-paginator-bundle", "version": "v2.8.0", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpPaginatorBundle.git", "reference": "f4ece5b347121b9fe13166264f197f90252d4bd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpPaginatorBundle/zipball/f4ece5b347121b9fe13166264f197f90252d4bd2", "reference": "f4ece5b347121b9fe13166264f197f90252d4bd2", "shasum": ""}, "require": {"knplabs/knp-components": "~1.2", "php": ">=5.3.3", "symfony/framework-bundle": "~2.7|~3.0|~4.0", "twig/twig": "~1.12|~2"}, "require-dev": {"phpunit/phpunit": "~4.8.35|~5.4.3|~6.4", "symfony/expression-language": "~2.7|~3.0|~4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.8.x-dev"}}, "autoload": {"psr-4": {"Knp\\Bundle\\PaginatorBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/KnpPaginatorBundle/contributors"}], "description": "Paginator bundle for Symfony to automate pagination and simplify sorting and other features", "homepage": "http://github.com/KnpLabs/KnpPaginatorBundle", "keywords": ["bundle", "knp", "knplabs", "pager", "pagination", "paginator", "symfony"], "time": "2018-05-16T12:15:58+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.33", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "cd385290f9a0d609d2eddd165a1e44ec1bf12102"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/cd385290f9a0d609d2eddd165a1e44ec1bf12102", "reference": "cd385290f9a0d609d2eddd165a1e44ec1bf12102", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8.35||~5.7"}, "type": "library", "autoload": {"classmap": ["Mobile_Detect.php"], "psr-0": {"Detection": "namespaced/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "time": "2018-09-01T15:05:15+00:00"}, {"name": "monolog/monolog", "version": "1.24.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "bfc9ebb28f97e7a24c45bdc3f0ff482e47bb0266"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/bfc9ebb28f97e7a24c45bdc3f0ff482e47bb0266", "reference": "bfc9ebb28f97e7a24c45bdc3f0ff482e47bb0266", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "jakub-onderka/php-parallel-lint": "0.9", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpunit/phpunit": "~4.5", "phpunit/phpunit-mock-objects": "2.3.0", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2018-11-05T09:00:11+00:00"}, {"name": "nesbot/carbon", "version": "1.36.2", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "cd324b98bc30290f233dd0e75e6ce49f7ab2a6c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/cd324b98bc30290f233dd0e75e6ce49f7ab2a6c9", "reference": "cd324b98bc30290f233dd0e75e6ce49f7ab2a6c9", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7"}, "suggest": {"friendsofphp/php-cs-fixer": "Needed for the `composer phpcs` command. Allow to automatically fix code style.", "phpstan/phpstan": "Needed for the `composer phpstan` command. Allow to detect potential errors."}, "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}}, "autoload": {"psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}], "description": "A simple API extension for DateTime.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "time": "2018-12-28T10:07:33+00:00"}, {"name": "nikic/php-parser", "version": "v4.2.1", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "5221f49a608808c1e4d436df32884cbc1b821ac0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/5221f49a608808c1e4d436df32884cbc1b821ac0", "reference": "5221f49a608808c1e4d436df32884cbc1b821ac0", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.5 || ^7.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2019-02-16T20:54:15+00:00"}, {"name": "ocramius/package-versions", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/Ocramius/PackageVersions.git", "reference": "a4d4b60d0e60da2487bd21a2c6ac089f85570dbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/a4d4b60d0e60da2487bd21a2c6ac089f85570dbb", "reference": "a4d4b60d0e60da2487bd21a2c6ac089f85570dbb", "shasum": ""}, "require": {"composer-plugin-api": "^1.0.0", "php": "^7.1.0"}, "require-dev": {"composer/composer": "^1.6.3", "doctrine/coding-standard": "^5.0.1", "ext-zip": "*", "infection/infection": "^0.7.1", "phpunit/phpunit": "^7.0.0"}, "type": "composer-plugin", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "time": "2019-02-21T12:16:21+00:00"}, {"name": "ocramius/proxy-manager", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/Ocramius/ProxyManager.git", "reference": "e18ac876b2e4819c76349de8f78ccc8ef1554cd7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/e18ac876b2e4819c76349de8f78ccc8ef1554cd7", "reference": "e18ac876b2e4819c76349de8f78ccc8ef1554cd7", "shasum": ""}, "require": {"ocramius/package-versions": "^1.1.1", "php": "^7.1.0", "zendframework/zend-code": "^3.1.0"}, "require-dev": {"couscous/couscous": "^1.5.2", "ext-phar": "*", "humbug/humbug": "dev-master@DEV", "nikic/php-parser": "^3.0.4", "phpbench/phpbench": "^0.12.2", "phpstan/phpstan": "^0.6.4", "phpunit/phpunit": "^5.6.4", "phpunit/phpunit-mock-objects": "^3.4.1", "squizlabs/php_codesniffer": "^2.7.0"}, "suggest": {"ocramius/generated-hydrator": "To have very fast object to array to object conversion for ghost objects", "zendframework/zend-json": "To have the JsonRpc adapter (Remote Object feature)", "zendframework/zend-soap": "To have the Soap adapter (Remote Object feature)", "zendframework/zend-xmlrpc": "To have the XmlRpc adapter (Remote Object feature)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-0": {"ProxyManager\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.io/"}], "description": "A library providing utilities to generate, instantiate and generally operate with Object Proxies", "homepage": "https://github.com/Ocramius/ProxyManager", "keywords": ["aop", "lazy loading", "proxy", "proxy pattern", "service proxies"], "time": "2017-05-04T11:12:50+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.99", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "shasum": ""}, "require": {"php": "^7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2018-07-02T15:55:56+00:00"}, {"name": "php-cs-fixer/diff", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/PHP-CS-Fixer/diff.git", "reference": "78bb099e9c16361126c86ce82ec4405ebab8e756"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-CS-Fixer/diff/zipball/78bb099e9c16361126c86ce82ec4405ebab8e756", "reference": "78bb099e9c16361126c86ce82ec4405ebab8e756", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7.23 || ^6.4.3", "symfony/process": "^3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "SpacePossum"}], "description": "sebastian/diff v2 backport support for PHP5.6", "homepage": "https://github.com/PHP-CS-Fixer", "keywords": ["diff"], "time": "2018-02-15T16:58:55+00:00"}, {"name": "pimple/pimple", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/silexphp/Pimple.git", "reference": "2019c145fe393923f3441b23f29bbdfaa5c58c4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silexphp/Pimple/zipball/2019c145fe393923f3441b23f29bbdfaa5c58c4d", "reference": "2019c145fe393923f3441b23f29bbdfaa5c58c4d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-0": {"Pimple": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Pimple is a simple Dependency Injection Container for PHP 5.3", "homepage": "http://pimple.sensiolabs.org", "keywords": ["container", "dependency injection"], "time": "2013-11-22T08:30:29+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "6c001f1daafa3a3ac1d8ff69ee4db8e799a654dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/6c001f1daafa3a3ac1d8ff69ee4db8e799a654dd", "reference": "6c001f1daafa3a3ac1d8ff69ee4db8e799a654dd", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2018-11-20T15:27:04+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/5601c8a83fbba7ef674a7369456d12f1e0d0eafa", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~3.7.0", "satooshi/php-coveralls": ">=1.0"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2016-02-11T07:05:27+00:00"}, {"name": "seld/jsonlint", "version": "1.7.1", "source": {"type": "git", "url": "https://github.com/Seldaek/jsonlint.git", "reference": "d15f59a67ff805a44c50ea0516d2341740f81a38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/d15f59a67ff805a44c50ea0516d2341740f81a38", "reference": "d15f59a67ff805a44c50ea0516d2341740f81a38", "shasum": ""}, "require": {"php": "^5.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "time": "2018-01-24T12:46:19+00:00"}, {"name": "seld/phar-utils", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/Seldaek/phar-utils.git", "reference": "7009b5139491975ef6486545a39f3e6dad5ac30a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/phar-utils/zipball/7009b5139491975ef6486545a39f3e6dad5ac30a", "reference": "7009b5139491975ef6486545a39f3e6dad5ac30a", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\PharUtils\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "PHAR file format utilities, for when PHP phars you up", "keywords": ["phra"], "time": "2015-10-13T18:44:15+00:00"}, {"name": "sensio/framework-extra-bundle", "version": "v5.3.1", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioFrameworkExtraBundle.git", "reference": "5f75c4658b03301cba17baa15a840b57b72b4262"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioFrameworkExtraBundle/zipball/5f75c4658b03301cba17baa15a840b57b72b4262", "reference": "5f75c4658b03301cba17baa15a840b57b72b4262", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/persistence": "^1.0", "php": ">=7.1.3", "symfony/config": "^3.4|^4.2", "symfony/dependency-injection": "^3.4|^4.2", "symfony/framework-bundle": "^3.4|^4.2", "symfony/http-kernel": "^3.4|^4.2"}, "require-dev": {"doctrine/doctrine-bundle": "^1.6", "doctrine/orm": "^2.5", "nyholm/psr7": "^1.1", "symfony/browser-kit": "^3.4|^4.2", "symfony/dom-crawler": "^3.4|^4.2", "symfony/expression-language": "^3.4|^4.2", "symfony/finder": "^3.4|^4.2", "symfony/monolog-bridge": "^3.0|^4.0", "symfony/monolog-bundle": "^3.2", "symfony/phpunit-bridge": "^3.4.19|^4.1.8", "symfony/psr-http-message-bridge": "^1.1", "symfony/security-bundle": "^3.4|^4.2", "symfony/twig-bundle": "^3.4|^4.2", "symfony/yaml": "^3.4|^4.2", "twig/twig": "~1.12|~2.0"}, "suggest": {"symfony/expression-language": "", "symfony/psr-http-message-bridge": "To use the PSR-7 converters", "symfony/security-bundle": ""}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "5.3.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\FrameworkExtraBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle provides a way to configure your controllers with annotations", "keywords": ["annotations", "controllers"], "time": "2019-04-10T06:00:20+00:00"}, {"name": "sensio/generator-bundle", "version": "v3.1.7", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioGeneratorBundle.git", "reference": "28cbaa244bd0816fd8908b93f90380bcd7b67a65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioGeneratorBundle/zipball/28cbaa244bd0816fd8908b93f90380bcd7b67a65", "reference": "28cbaa244bd0816fd8908b93f90380bcd7b67a65", "shasum": ""}, "require": {"symfony/console": "~2.7|~3.0", "symfony/framework-bundle": "~2.7|~3.0", "symfony/process": "~2.7|~3.0", "symfony/yaml": "~2.7|~3.0", "twig/twig": "^1.28.2|^2.0"}, "require-dev": {"doctrine/orm": "~2.4", "symfony/doctrine-bridge": "~2.7|~3.0", "symfony/filesystem": "~2.7|~3.0", "symfony/phpunit-bridge": "^3.3"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\GeneratorBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle generates code for you", "time": "2017-12-07T15:36:41+00:00"}, {"name": "setasign/fpdi", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "3c266002f8044f61b17329f7cd702d44d73f0f7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/3c266002f8044f61b17329f7cd702d44d73f0f7f", "reference": "3c266002f8044f61b17329f7cd702d44d73f0f7f", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "~5.7", "setasign/fpdf": "~1.8", "setasign/tfpdf": "1.25", "tecnickcom/tcpdf": "~6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured.", "setasign/fpdi-fpdf": "Use this package to automatically evaluate dependencies to FPDF.", "setasign/fpdi-tcpdf": "Use this package to automatically evaluate dependencies to TCPDF.", "setasign/fpdi-tfpdf": "Use this package to automatically evaluate dependencies to tFPDF."}, "type": "library", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "time": "2019-01-30T14:11:19+00:00"}, {"name": "setasign/fpdi-tcpdf", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI-TCPDF.git", "reference": "95778baea02e5da76acb9f6dc1c946be380a22d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI-TCPDF/zipball/95778baea02e5da76acb9f6dc1c946be380a22d8", "reference": "95778baea02e5da76acb9f6dc1c946be380a22d8", "shasum": ""}, "require": {"setasign/fpdi": "^2.2", "tecnickcom/tcpdf": "^6.2"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "Kind of metadata package for dependencies of the latest versions of FPDI and TCPDF.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["TCPDF", "fpdi", "pdf"], "time": "2019-01-30T14:39:33+00:00"}, {"name": "suncat/mobile-detect-bundle", "version": "v1.1.1", "target-dir": "SunCat/MobileDetectBundle", "source": {"type": "git", "url": "https://github.com/suncat2000/MobileDetectBundle.git", "reference": "06007fec624587fd90e8963b796fc84fff64d4d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/suncat2000/MobileDetectBundle/zipball/06007fec624587fd90e8963b796fc84fff64d4d8", "reference": "06007fec624587fd90e8963b796fc84fff64d4d8", "shasum": ""}, "require": {"mobiledetect/mobiledetectlib": "~2.8", "php": ">=5.5.9", "symfony/framework-bundle": "~2.7|~3.3|~4.0", "twig/twig": "~1.26|~2.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3|^6.0", "satooshi/php-coveralls": "^2.0.0", "symfony/phpunit-bridge": "~2.7|~3.3|~4.0"}, "type": "symfony-bundle", "autoload": {"psr-0": {"SunCat\\MobileDetectBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "netmikey", "homepage": "https://github.com/netmikey"}], "description": "Symfony2/3/4 bundle for detect mobile devices, managing mobile view types, redirect to mobile version.", "homepage": "https://github.com/suncat2000/MobileDetectBundle", "keywords": ["mobile", "mobile detect", "mobile redirect", "mobile view managing", "symfony mobile"], "time": "2018-05-02T10:20:57+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.2.1", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "5397cd05b0a0f7937c47b0adcb4c60e5ab936b6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/5397cd05b0a0f7937c47b0adcb4c60e5ab936b6a", "reference": "5397cd05b0a0f7937c47b0adcb4c60e5ab936b6a", "shasum": ""}, "require": {"egulias/email-validator": "~2.0", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "~0.9.1", "symfony/phpunit-bridge": "^3.4.19|^4.1.8"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses", "true/punycode": "Needed to support internationalized email addresses, if ext-intl is not installed"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "time": "2019-04-21T09:21:45+00:00"}, {"name": "symfony/asset", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/asset.git", "reference": "30e3b424c4b8c5b640eb672ab57f52b8ed217124"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/asset/zipball/30e3b424c4b8c5b640eb672ab57f52b8ed217124", "reference": "30e3b424c4b8c5b640eb672ab57f52b8ed217124", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "require-dev": {"symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/http-kernel": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/http-foundation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Asset\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Asset Component", "homepage": "https://symfony.com", "time": "2019-01-16T09:39:14+00:00"}, {"name": "symfony/cache", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "380b8395b43f60e7d26a32f84f80c0a7ba93e7c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/380b8395b43f60e7d26a32f84f80c0a7ba93e7c5", "reference": "380b8395b43f60e7d26a32f84f80c0a7ba93e7c5", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/cache": "~1.0", "psr/log": "~1.0", "psr/simple-cache": "^1.0", "symfony/polyfill-apcu": "~1.1"}, "conflict": {"symfony/var-dumper": "<3.3"}, "provide": {"psr/cache-implementation": "1.0", "psr/simple-cache-implementation": "1.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "~1.6", "doctrine/dbal": "~2.4", "predis/predis": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Cache component with PSR-6, PSR-16, and tags", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "time": "2019-04-16T09:03:16+00:00"}, {"name": "symfony/class-loader", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/class-loader.git", "reference": "4459eef5298dedfb69f771186a580062b8516497"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/class-loader/zipball/4459eef5298dedfb69f771186a580062b8516497", "reference": "4459eef5298dedfb69f771186a580062b8516497", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "require-dev": {"symfony/finder": "~2.8|~3.0|~4.0", "symfony/polyfill-apcu": "~1.1"}, "suggest": {"symfony/polyfill-apcu": "For using ApcClassLoader on HHVM"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\ClassLoader\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ClassLoader Component", "homepage": "https://symfony.com", "time": "2019-01-16T09:39:14+00:00"}, {"name": "symfony/config", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "177a276c01575253c95cefe0866e3d1b57637fe0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/177a276c01575253c95cefe0866e3d1b57637fe0", "reference": "177a276c01575253c95cefe0866e3d1b57637fe0", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/filesystem": "~2.8|~3.0|~4.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/dependency-injection": "<3.3", "symfony/finder": "<3.3"}, "require-dev": {"symfony/dependency-injection": "~3.3|~4.0", "symfony/event-dispatcher": "~3.3|~4.0", "symfony/finder": "~3.3|~4.0", "symfony/yaml": "~3.0|~4.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Config Component", "homepage": "https://symfony.com", "time": "2019-02-23T15:06:07+00:00"}, {"name": "symfony/console", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "15a9104356436cb26e08adab97706654799d31d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/15a9104356436cb26e08adab97706654799d31d8", "reference": "15a9104356436cb26e08adab97706654799d31d8", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/debug": "~2.8|~3.0|~4.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.3|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/lock": "~3.4|~4.0", "symfony/process": "~3.3|~4.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2019-04-08T09:29:13+00:00"}, {"name": "symfony/css-selector", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "8ca29297c29b64fb3a1a135e71cb25f67f9fdccf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/8ca29297c29b64fb3a1a135e71cb25f67f9fdccf", "reference": "8ca29297c29b64fb3a1a135e71cb25f67f9fdccf", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony CssSelector Component", "homepage": "https://symfony.com", "time": "2019-01-16T09:39:14+00:00"}, {"name": "symfony/debug", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "681afbb26488903c5ac15e63734f1d8ac430c9b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/681afbb26488903c5ac15e63734f1d8ac430c9b9", "reference": "681afbb26488903c5ac15e63734f1d8ac430c9b9", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": ">=2.3,<2.3.24|~2.4.0|>=2.5,<2.5.9|>=2.6,<2.6.2"}, "require-dev": {"symfony/http-kernel": "~2.8|~3.0|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "time": "2019-04-11T09:48:14+00:00"}, {"name": "symfony/debug-bundle", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/debug-bundle.git", "reference": "04ccc8dc856fbba1ca1a325341f59e5ee6e02f73"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug-bundle/zipball/04ccc8dc856fbba1ca1a325341f59e5ee6e02f73", "reference": "04ccc8dc856fbba1ca1a325341f59e5ee6e02f73", "shasum": ""}, "require": {"ext-xml": "*", "php": "^5.5.9|>=7.0.8", "symfony/http-kernel": "~2.8|~3.0|~4.0", "symfony/twig-bridge": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~3.4|~4.0"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "require-dev": {"symfony/config": "~3.3|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/web-profiler-bundle": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/config": "For service container configuration", "symfony/dependency-injection": "For using as a service from the container"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\DebugBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DebugBundle", "homepage": "https://symfony.com", "time": "2019-03-04T10:06:18+00:00"}, {"name": "symfony/debug-pack", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/symfony/debug-pack.git", "reference": "09a4a1e9bf2465987d4f79db0ad6c11cc632bc79"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug-pack/zipball/09a4a1e9bf2465987d4f79db0ad6c11cc632bc79", "reference": "09a4a1e9bf2465987d4f79db0ad6c11cc632bc79", "shasum": ""}, "require": {"easycorp/easy-log-handler": "^1.0.7", "php": "^7.0", "symfony/debug-bundle": "*", "symfony/monolog-bundle": "^3.0", "symfony/profiler-pack": "*", "symfony/var-dumper": "*"}, "type": "symfony-pack", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A debug pack for Symfony projects", "time": "2018-12-10T12:11:11+00:00"}, {"name": "symfony/dependency-injection", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "dee85a9148399cdb2731603802842bcfd8afe5ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/dee85a9148399cdb2731603802842bcfd8afe5ab", "reference": "dee85a9148399cdb2731603802842bcfd8afe5ab", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/container": "^1.0"}, "conflict": {"symfony/config": "<3.3.7", "symfony/finder": "<3.3", "symfony/proxy-manager-bridge": "<3.4", "symfony/yaml": "<3.4"}, "provide": {"psr/container-implementation": "1.0"}, "require-dev": {"symfony/config": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DependencyInjection Component", "homepage": "https://symfony.com", "time": "2019-04-16T11:13:42+00:00"}, {"name": "symfony/doctrine-bridge", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-bridge.git", "reference": "10d950628067b814f1b95db3693a64eb7e4fa431"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-bridge/zipball/10d950628067b814f1b95db3693a64eb7e4fa431", "reference": "10d950628067b814f1b95db3693a64eb7e4fa431", "shasum": ""}, "require": {"doctrine/common": "~2.4", "php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.4"}, "require-dev": {"doctrine/data-fixtures": "1.0.*", "doctrine/dbal": "~2.4", "doctrine/orm": "^2.4.5", "symfony/dependency-injection": "~3.4|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/form": "^3.3.10|~4.0", "symfony/http-kernel": "~2.8|~3.0|~4.0", "symfony/property-access": "~2.8|~3.0|~4.0", "symfony/property-info": "~2.8|3.0|~4.0", "symfony/proxy-manager-bridge": "~2.8|~3.0|~4.0", "symfony/security": "^2.8.31|^3.3.13|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0", "symfony/translation": "~2.8|~3.0|~4.0", "symfony/validator": "^3.2.5|~4.0"}, "suggest": {"doctrine/data-fixtures": "", "doctrine/dbal": "", "doctrine/orm": "", "symfony/form": "", "symfony/property-info": "", "symfony/validator": ""}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Doctrine Bridge", "homepage": "https://symfony.com", "time": "2019-02-23T15:06:07+00:00"}, {"name": "symfony/dom-crawler", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "d40023c057393fb25f7ca80af2a56ed948c45a09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/d40023c057393fb25f7ca80af2a56ed948c45a09", "reference": "d40023c057393fb25f7ca80af2a56ed948c45a09", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/css-selector": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DomCrawler Component", "homepage": "https://symfony.com", "time": "2019-02-23T15:06:07+00:00"}, {"name": "symfony/dotenv", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "7b33c7b6f497898a173e4b9d6a7698cd789d54ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/7b33c7b6f497898a173e4b9d6a7698cd789d54ce", "reference": "7b33c7b6f497898a173e4b9d6a7698cd789d54ce", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "require-dev": {"symfony/process": "~3.2|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "time": "2019-04-01T07:08:40+00:00"}, {"name": "symfony/event-dispatcher", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "a088aafcefb4eef2520a290ed82e4374092a6dff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/a088aafcefb4eef2520a290ed82e4374092a6dff", "reference": "a088aafcefb4eef2520a290ed82e4374092a6dff", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"symfony/dependency-injection": "<3.3"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "time": "2019-04-02T08:51:52+00:00"}, {"name": "symfony/expression-language", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/expression-language.git", "reference": "74631d47774cfa59bfb4a0de18cdf700fb98d658"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/74631d47774cfa59bfb4a0de18cdf700fb98d658", "reference": "74631d47774cfa59bfb4a0de18cdf700fb98d658", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/cache": "~3.1|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ExpressionLanguage Component", "homepage": "https://symfony.com", "time": "2019-01-16T12:52:19+00:00"}, {"name": "symfony/filesystem", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "acf99758b1df8e9295e6b85aa69f294565c9fedb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/acf99758b1df8e9295e6b85aa69f294565c9fedb", "reference": "acf99758b1df8e9295e6b85aa69f294565c9fedb", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "time": "2019-02-04T21:34:32+00:00"}, {"name": "symfony/finder", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "61af5ce0b34b942d414fe8f1b11950d0e9a90e98"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/61af5ce0b34b942d414fe8f1b11950d0e9a90e98", "reference": "61af5ce0b34b942d414fe8f1b11950d0e9a90e98", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2019-04-02T19:54:57+00:00"}, {"name": "symfony/flex", "version": "v1.2.3", "source": {"type": "git", "url": "https://github.com/symfony/flex.git", "reference": "d65041a4c9b1dbcd606f8be3a5bae2bee4534f6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/flex/zipball/d65041a4c9b1dbcd606f8be3a5bae2bee4534f6a", "reference": "d65041a4c9b1dbcd606f8be3a5bae2bee4534f6a", "shasum": ""}, "require": {"composer-plugin-api": "^1.0", "php": "^7.0"}, "require-dev": {"composer/composer": "^1.0.2", "symfony/dotenv": "^3.4|^4.0", "symfony/phpunit-bridge": "^3.4.19|^4.1.8", "symfony/process": "^2.7|^3.0|^4.0"}, "type": "composer-plugin", "extra": {"branch-alias": {"dev-master": "1.2-dev"}, "class": "Symfony\\Flex\\Flex"}, "autoload": {"psr-4": {"Symfony\\Flex\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin for Symfony", "time": "2019-04-16T10:04:15+00:00"}, {"name": "symfony/form", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/form.git", "reference": "16f9919b034d4c2c80aa3b47ba3b44d7a71ac24a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/form/zipball/16f9919b034d4c2c80aa3b47ba3b44d7a71ac24a", "reference": "16f9919b034d4c2c80aa3b47ba3b44d7a71ac24a", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/intl": "^2.8.18|^3.2.5|~4.0", "symfony/options-resolver": "~3.4|~4.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/property-access": "~2.8|~3.0|~4.0"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.3", "symfony/doctrine-bridge": "<2.7", "symfony/framework-bundle": "<3.4", "symfony/http-kernel": "<3.3.5", "symfony/twig-bridge": "<3.4.5|<4.0.5,>=4.0"}, "require-dev": {"doctrine/collections": "~1.0", "symfony/config": "~2.7|~3.0|~4.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/http-kernel": "^3.3.5|~4.0", "symfony/security-csrf": "^2.8.31|^3.3.13|~4.0", "symfony/translation": "~2.8|~3.0|~4.0", "symfony/validator": "^3.2.5|~4.0", "symfony/var-dumper": "~3.3.11|~3.4|~4.0"}, "suggest": {"symfony/framework-bundle": "For templating with PHP.", "symfony/security-csrf": "For protecting forms against CSRF attacks.", "symfony/twig-bridge": "For templating with <PERSON><PERSON>.", "symfony/validator": "For form validation."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Form\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Form Component", "homepage": "https://symfony.com", "time": "2019-04-15T13:23:09+00:00"}, {"name": "symfony/framework-bundle", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "172b76d504b5472761dce6a5887ed77a88eee7ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/172b76d504b5472761dce6a5887ed77a88eee7ec", "reference": "172b76d504b5472761dce6a5887ed77a88eee7ec", "shasum": ""}, "require": {"ext-xml": "*", "php": "^5.5.9|>=7.0.8", "symfony/cache": "~3.4|~4.0", "symfony/class-loader": "~3.2", "symfony/config": "~3.4|~4.0", "symfony/dependency-injection": "^3.4.24|^4.2.5", "symfony/event-dispatcher": "~3.4|~4.0", "symfony/filesystem": "~2.8|~3.0|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/http-foundation": "^3.4.24|^4.2.5", "symfony/http-kernel": "~3.4|~4.0", "symfony/polyfill-mbstring": "~1.0", "symfony/routing": "^3.4.5|^4.0.5"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.0", "phpdocumentor/type-resolver": "<0.2.1", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/asset": "<3.3", "symfony/console": "<3.4", "symfony/form": "<3.4", "symfony/property-info": "<3.3", "symfony/serializer": "<3.3", "symfony/stopwatch": "<3.4", "symfony/translation": "<3.4", "symfony/validator": "<3.4", "symfony/workflow": "<3.3"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "fig/link-util": "^1.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/asset": "~3.3|~4.0", "symfony/browser-kit": "~2.8|~3.0|~4.0", "symfony/console": "~3.4|~4.0", "symfony/css-selector": "~2.8|~3.0|~4.0", "symfony/dom-crawler": "~2.8|~3.0|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/form": "^3.4.22|~4.1.11|^4.2.3", "symfony/lock": "~3.4|~4.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "~2.8|~3.0|~4.0", "symfony/property-info": "~3.3|~4.0", "symfony/security-core": "~3.2|~4.0", "symfony/security-csrf": "^2.8.31|^3.3.13|~4.0", "symfony/serializer": "~3.3|~4.0", "symfony/stopwatch": "~3.4|~4.0", "symfony/templating": "~2.8|~3.0|~4.0", "symfony/translation": "~3.4|~4.0", "symfony/validator": "~3.4|~4.0", "symfony/var-dumper": "~3.3|~4.0", "symfony/web-link": "~3.3|~4.0", "symfony/workflow": "~3.3|~4.0", "symfony/yaml": "~3.2|~4.0", "twig/twig": "~1.34|~2.4"}, "suggest": {"ext-apcu": "For best performance of the system caches", "symfony/console": "For using the console commands", "symfony/form": "For using forms", "symfony/property-info": "For using the property_info service", "symfony/serializer": "For using the serializer service", "symfony/validator": "For using validation", "symfony/web-link": "For using web links, features such as preloading, prefetching or prerendering", "symfony/yaml": "For using the debug:config and lint:yaml commands"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony FrameworkBundle", "homepage": "https://symfony.com", "time": "2019-04-17T14:42:57+00:00"}, {"name": "symfony/http-foundation", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "90454ad44c95d75faf3507d56388056001b74baf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/90454ad44c95d75faf3507d56388056001b74baf", "reference": "90454ad44c95d75faf3507d56388056001b74baf", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php70": "~1.6"}, "require-dev": {"symfony/expression-language": "~2.8|~3.0|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "time": "2019-04-17T14:51:18+00:00"}, {"name": "symfony/http-kernel", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "14fa41ccd38570b5e3120a3754bbaa144a15f311"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/14fa41ccd38570b5e3120a3754bbaa144a15f311", "reference": "14fa41ccd38570b5e3120a3754bbaa144a15f311", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0", "symfony/debug": "^3.3.3|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~3.4.12|~4.0.12|^4.1.1", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/config": "<2.8", "symfony/dependency-injection": "<3.4.10|<4.0.10,>=4", "symfony/var-dumper": "<3.3", "twig/twig": "<1.34|<2.4,>=2"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/cache": "~1.0", "symfony/browser-kit": "~2.8|~3.0|~4.0", "symfony/class-loader": "~2.8|~3.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/console": "~2.8|~3.0|~4.0", "symfony/css-selector": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "^3.4.10|^4.0.10", "symfony/dom-crawler": "~2.8|~3.0|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/process": "~2.8|~3.0|~4.0", "symfony/routing": "~3.4|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0", "symfony/templating": "~2.8|~3.0|~4.0", "symfony/translation": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~3.3|~4.0"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": "", "symfony/finder": "", "symfony/var-dumper": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpKernel Component", "homepage": "https://symfony.com", "time": "2019-04-17T15:57:07+00:00"}, {"name": "symfony/inflector", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/inflector.git", "reference": "4a7d5c4ad3edeba3fe4a27d26ece6a012eee46b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/inflector/zipball/4a7d5c4ad3edeba3fe4a27d26ece6a012eee46b1", "reference": "4a7d5c4ad3edeba3fe4a27d26ece6a012eee46b1", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Inflector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Inflector Component", "homepage": "https://symfony.com", "keywords": ["inflection", "pluralize", "singularize", "string", "symfony", "words"], "time": "2019-01-16T13:27:11+00:00"}, {"name": "symfony/intl", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/intl.git", "reference": "d2ac83703951bc3206e9ea3f6114b355de14e3ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/intl/zipball/d2ac83703951bc3206e9ea3f6114b355de14e3ce", "reference": "d2ac83703951bc3206e9ea3f6114b355de14e3ce", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-intl-icu": "~1.0"}, "require-dev": {"symfony/filesystem": "~2.8|~3.0|~4.0"}, "suggest": {"ext-intl": "to use the component with locales other than \"en\""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Intl\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A PHP replacement layer for the C intl extension that includes additional data from the ICU library.", "homepage": "https://symfony.com", "keywords": ["i18n", "icu", "internationalization", "intl", "l10n", "localization"], "time": "2019-03-31T16:47:37+00:00"}, {"name": "symfony/maker-bundle", "version": "v1.11.6", "source": {"type": "git", "url": "https://github.com/symfony/maker-bundle.git", "reference": "d262c2cace4d9bca99137a84f6fc6ba909a17e02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/maker-bundle/zipball/d262c2cace4d9bca99137a84f6fc6ba909a17e02", "reference": "d262c2cace4d9bca99137a84f6fc6ba909a17e02", "shasum": ""}, "require": {"doctrine/inflector": "^1.2", "nikic/php-parser": "^4.0", "php": "^7.0.8", "symfony/config": "^3.4|^4.0", "symfony/console": "^3.4|^4.0", "symfony/dependency-injection": "^3.4|^4.0", "symfony/filesystem": "^3.4|^4.0", "symfony/finder": "^3.4|^4.0", "symfony/framework-bundle": "^3.4|^4.0", "symfony/http-kernel": "^3.4|^4.0"}, "require-dev": {"allocine/twigcs": "^3.0", "doctrine/doctrine-bundle": "^1.8", "doctrine/orm": "^2.3", "friendsofphp/php-cs-fixer": "^2.8", "symfony/phpunit-bridge": "^3.4|^4.0", "symfony/process": "^3.4|^4.0", "symfony/yaml": "^3.4|^4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MakerBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Maker helps you create empty commands, controllers, form classes, tests and more so you can forget about writing boilerplate code.", "homepage": "https://symfony.com/doc/current/bundles/SymfonyMakerBundle/index.html", "keywords": ["code generator", "generator", "scaffold", "scaffolding"], "time": "2019-04-19T17:26:45+00:00"}, {"name": "symfony/monolog-bridge", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bridge.git", "reference": "8a9482e9f0900f5ebc3eae15b1ac4cf67f3db2fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bridge/zipball/8a9482e9f0900f5ebc3eae15b1ac4cf67f3db2fc", "reference": "8a9482e9f0900f5ebc3eae15b1ac4cf67f3db2fc", "shasum": ""}, "require": {"monolog/monolog": "~1.19", "php": "^5.5.9|>=7.0.8", "symfony/http-kernel": "~2.8|~3.0|~4.0"}, "conflict": {"symfony/console": "<2.8", "symfony/http-foundation": "<3.3"}, "require-dev": {"symfony/console": "~2.8|~3.0|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/security-core": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~3.3|~4.0"}, "suggest": {"symfony/console": "For the possibility to show log messages in console commands depending on verbosity settings. You need version ^2.8 of the console for it.", "symfony/event-dispatcher": "Needed when using log messages in console commands.", "symfony/http-kernel": "For using the debugging handlers together with the response life cycle of the HTTP kernel.", "symfony/var-dumper": "For using the debugging handlers like the console handler or the log server handler."}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Monolog\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Monolog Bridge", "homepage": "https://symfony.com", "time": "2019-01-16T09:39:14+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.3.1", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "572e143afc03419a75ab002c80a2fd99299195ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/572e143afc03419a75ab002c80a2fd99299195ff", "reference": "572e143afc03419a75ab002c80a2fd99299195ff", "shasum": ""}, "require": {"monolog/monolog": "~1.22", "php": ">=5.6", "symfony/config": "~2.7|~3.3|~4.0", "symfony/dependency-injection": "~2.7|~3.4.10|^4.0.10", "symfony/http-kernel": "~2.7|~3.3|~4.0", "symfony/monolog-bridge": "~2.7|~3.3|~4.0"}, "require-dev": {"symfony/console": "~2.7|~3.3|~4.0", "symfony/phpunit-bridge": "^3.3|^4.0", "symfony/yaml": "~2.7|~3.3|~4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony MonologBundle", "homepage": "http://symfony.com", "keywords": ["log", "logging"], "time": "2018-11-04T09:58:13+00:00"}, {"name": "symfony/options-resolver", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "ed3b397f9c07c8ca388b2a1ef744403b4d4ecc44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/ed3b397f9c07c8ca388b2a1ef744403b4d4ecc44", "reference": "ed3b397f9c07c8ca388b2a1ef744403b4d4ecc44", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony OptionsResolver Component", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "time": "2019-04-10T16:00:48+00:00"}, {"name": "symfony/orm-pack", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/symfony/orm-pack.git", "reference": "36c2a928482dc5f05c5c1c1b947242ae03ff1335"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/orm-pack/zipball/36c2a928482dc5f05c5c1c1b947242ae03ff1335", "reference": "36c2a928482dc5f05c5c1c1b947242ae03ff1335", "shasum": ""}, "require": {"doctrine/doctrine-bundle": "^1.6.10", "doctrine/doctrine-migrations-bundle": "^1.3|^2.0", "doctrine/orm": "^2.5.11", "php": "^7.0"}, "type": "symfony-pack", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A pack for the Doctrine ORM", "time": "2019-01-16T09:49:15+00:00"}, {"name": "symfony/polyfill-apcu", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-apcu.git", "reference": "a502face1da6a53289480166f24de2c3c68e5c3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-apcu/zipball/a502face1da6a53289480166f24de2c3c68e5c3c", "reference": "a502face1da6a53289480166f24de2c3c68e5c3c", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Apcu\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting apcu_* functions to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["apcu", "compatibility", "polyfill", "portable", "shim"], "time": "2019-02-06T07:57:58+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "82ebae02209c21113908c229e9883c419720738a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/82ebae02209c21113908c229e9883c419720738a", "reference": "82ebae02209c21113908c229e9883c419720738a", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "time": "2019-02-06T07:57:58+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "f037ea22acfaee983e271dd9c3b8bb4150bd8ad7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/f037ea22acfaee983e271dd9c3b8bb4150bd8ad7", "reference": "f037ea22acfaee983e271dd9c3b8bb4150bd8ad7", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "time": "2019-02-06T07:57:58+00:00"}, {"name": "symfony/polyfill-intl-icu", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-icu.git", "reference": "999878a3a09d73cae157b0cf89bb6fb2cc073057"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-icu/zipball/999878a3a09d73cae157b0cf89bb6fb2cc073057", "reference": "999878a3a09d73cae157b0cf89bb6fb2cc073057", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/intl": "~2.3|~3.0|~4.0"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's ICU-related data and classes", "homepage": "https://symfony.com", "keywords": ["compatibility", "icu", "intl", "polyfill", "portable", "shim"], "time": "2019-01-07T19:39:47+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "c766e95bec706cdd89903b1eda8afab7d7a6b7af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c766e95bec706cdd89903b1eda8afab7d7a6b7af", "reference": "c766e95bec706cdd89903b1eda8afab7d7a6b7af", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php72": "^1.9"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "time": "2019-03-04T13:44:35+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "fe5e94c604826c35a32fa832f35bd036b6799609"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/fe5e94c604826c35a32fa832f35bd036b6799609", "reference": "fe5e94c604826c35a32fa832f35bd036b6799609", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2019-02-06T07:57:58+00:00"}, {"name": "symfony/polyfill-php56", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php56.git", "reference": "f4dddbc5c3471e1b700a147a20ae17cdb72dbe42"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php56/zipball/f4dddbc5c3471e1b700a147a20ae17cdb72dbe42", "reference": "f4dddbc5c3471e1b700a147a20ae17cdb72dbe42", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/polyfill-util": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php56\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.6+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2019-02-06T07:57:58+00:00"}, {"name": "symfony/polyfill-php70", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php70.git", "reference": "bc4858fb611bda58719124ca079baff854149c89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php70/zipball/bc4858fb611bda58719124ca079baff854149c89", "reference": "bc4858fb611bda58719124ca079baff854149c89", "shasum": ""}, "require": {"paragonie/random_compat": "~1.0|~2.0|~9.99", "php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php70\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2019-02-06T07:57:58+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "ab50dcf166d5f577978419edd37aa2bb8eabce0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/ab50dcf166d5f577978419edd37aa2bb8eabce0c", "reference": "ab50dcf166d5f577978419edd37aa2bb8eabce0c", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2019-02-06T07:57:58+00:00"}, {"name": "symfony/polyfill-util", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-util.git", "reference": "b46c6cae28a3106735323f00a0c38eccf2328897"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-util/zipball/b46c6cae28a3106735323f00a0c38eccf2328897", "reference": "b46c6cae28a3106735323f00a0c38eccf2328897", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Util\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony utilities for portability of PHP codes", "homepage": "https://symfony.com", "keywords": ["compat", "compatibility", "polyfill", "shim"], "time": "2019-02-08T14:16:39+00:00"}, {"name": "symfony/process", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "a9c4dfbf653023b668c282e4e02609d131f4057a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/a9c4dfbf653023b668c282e4e02609d131f4057a", "reference": "a9c4dfbf653023b668c282e4e02609d131f4057a", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "time": "2019-04-08T16:15:54+00:00"}, {"name": "symfony/profiler-pack", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/symfony/profiler-pack.git", "reference": "99c4370632c2a59bb0444852f92140074ef02209"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/profiler-pack/zipball/99c4370632c2a59bb0444852f92140074ef02209", "reference": "99c4370632c2a59bb0444852f92140074ef02209", "shasum": ""}, "require": {"php": "^7.0", "symfony/stopwatch": "*", "symfony/twig-bundle": "*", "symfony/web-profiler-bundle": "*"}, "type": "symfony-pack", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A pack for the Symfony web profiler", "time": "2018-12-10T12:11:44+00:00"}, {"name": "symfony/property-access", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "9b1c9df96a00c14445bef4cf37ad85e7239d8a4a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/9b1c9df96a00c14445bef4cf37ad85e7239d8a4a", "reference": "9b1c9df96a00c14445bef4cf37ad85e7239d8a4a", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/inflector": "~3.1|~4.0", "symfony/polyfill-php70": "~1.0"}, "require-dev": {"symfony/cache": "~3.1|~4.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony PropertyAccess Component", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property path", "reflection"], "time": "2019-03-04T06:36:31+00:00"}, {"name": "symfony/proxy-manager-bridge", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/proxy-manager-bridge.git", "reference": "f534483997cdfb1984b6fb2130faa61da84771f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/proxy-manager-bridge/zipball/f534483997cdfb1984b6fb2130faa61da84771f7", "reference": "f534483997cdfb1984b6fb2130faa61da84771f7", "shasum": ""}, "require": {"ocramius/proxy-manager": "~0.4|~1.0|~2.0", "php": "^5.5.9|>=7.0.8", "symfony/dependency-injection": "~3.4|~4.0"}, "require-dev": {"symfony/config": "~2.8|~3.0|~4.0"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\ProxyManager\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ProxyManager Bridge", "homepage": "https://symfony.com", "time": "2019-04-16T11:13:42+00:00"}, {"name": "symfony/routing", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "ff11aac46d6cb8a65f2855687bb9a1ac9d860eec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/ff11aac46d6cb8a65f2855687bb9a1ac9d860eec", "reference": "ff11aac46d6cb8a65f2855687bb9a1ac9d860eec", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"symfony/config": "<3.3.1", "symfony/dependency-injection": "<3.3", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "psr/log": "~1.0", "symfony/config": "^3.3.1|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Routing Component", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "time": "2019-03-29T21:58:42+00:00"}, {"name": "symfony/security", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/security.git", "reference": "fdbff3de6a0486aa10fcc3f0cbfe336d46534f27"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security/zipball/fdbff3de6a0486aa10fcc3f0cbfe336d46534f27", "reference": "fdbff3de6a0486aa10fcc3f0cbfe336d46534f27", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/http-foundation": "^2.8.31|~3.3.13|~3.4|~4.0", "symfony/http-kernel": "~3.3|~4.0", "symfony/polyfill-php56": "~1.0", "symfony/polyfill-php70": "~1.0", "symfony/property-access": "~2.8|~3.0|~4.0"}, "replace": {"symfony/security-core": "self.version", "symfony/security-csrf": "self.version", "symfony/security-guard": "self.version", "symfony/security-http": "self.version"}, "require-dev": {"psr/container": "^1.0", "psr/log": "~1.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/ldap": "~3.1|~4.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-icu": "~1.0", "symfony/routing": "~2.8|~3.0|~4.0", "symfony/validator": "^3.2.5|~4.0"}, "suggest": {"psr/container-implementation": "To instantiate the Security class", "symfony/expression-language": "For using the expression voter", "symfony/form": "", "symfony/ldap": "For using the LDAP user and authentication providers", "symfony/routing": "For using the HttpUtils class to create sub-requests, redirect the user, and match URLs", "symfony/validator": "For using the user password constraint"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Security\\": ""}, "exclude-from-classmap": ["/Core/Tests/", "/Csrf/Tests/", "/Guard/Tests/", "/Http/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component", "homepage": "https://symfony.com", "time": "2019-04-17T14:49:35+00:00"}, {"name": "symfony/security-bundle", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/security-bundle.git", "reference": "ae2d003a6d48558bb5cde7ea2879d05a24da3ba5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-bundle/zipball/ae2d003a6d48558bb5cde7ea2879d05a24da3ba5", "reference": "ae2d003a6d48558bb5cde7ea2879d05a24da3ba5", "shasum": ""}, "require": {"ext-xml": "*", "php": "^5.5.9|>=7.0.8", "symfony/dependency-injection": "^3.4.3|^4.0.3", "symfony/http-kernel": "~3.4|~4.0", "symfony/polyfill-php70": "~1.0", "symfony/security": "~3.4.15|~4.0.15|^4.1.4"}, "conflict": {"symfony/console": "<3.4", "symfony/event-dispatcher": "<3.4", "symfony/framework-bundle": "<3.4", "symfony/var-dumper": "<3.3"}, "require-dev": {"doctrine/doctrine-bundle": "~1.5", "symfony/asset": "~2.8|~3.0|~4.0", "symfony/browser-kit": "~2.8|~3.0|~4.0", "symfony/console": "~3.4|~4.0", "symfony/css-selector": "~2.8|~3.0|~4.0", "symfony/dom-crawler": "~2.8|~3.0|~4.0", "symfony/event-dispatcher": "~3.4|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/form": "^3.4|~4.0", "symfony/framework-bundle": "~3.4|~4.0", "symfony/http-foundation": "~3.3|~4.0", "symfony/process": "~3.3|~4.0", "symfony/security-acl": "~2.8|~3.0", "symfony/translation": "~3.4|~4.0", "symfony/twig-bridge": "~3.4|~4.0", "symfony/twig-bundle": "~3.4|~4.0", "symfony/validator": "^3.4|~4.0", "symfony/var-dumper": "~3.3|~4.0", "symfony/yaml": "~3.4|~4.0", "twig/twig": "~1.34|~2.4"}, "suggest": {"symfony/security-acl": "For using the ACL functionality of this bundle"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\SecurityBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony SecurityBundle", "homepage": "https://symfony.com", "time": "2019-04-10T16:00:48+00:00"}, {"name": "symfony/serializer", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "14b3221cc41dcfef404205f0060cda873f43a534"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/14b3221cc41dcfef404205f0060cda873f43a534", "reference": "14b3221cc41dcfef404205f0060cda873f43a534", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"phpdocumentor/type-resolver": "<0.2.1", "symfony/dependency-injection": "<3.2", "symfony/property-access": ">=3.0,<3.0.4|>=2.8,<2.8.4", "symfony/property-info": "<3.1", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/cache": "~3.1|~4.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.2|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/property-access": "~2.8|~3.0|~4.0", "symfony/property-info": "~3.1|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader and metadata cache.", "psr/cache-implementation": "For using the metadata cache.", "symfony/config": "For using the XML mapping loader.", "symfony/http-foundation": "To use the DataUriNormalizer.", "symfony/property-access": "For using the ObjectNormalizer.", "symfony/property-info": "To deserialize relations.", "symfony/yaml": "For using the default YAML mapping loader."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Serializer Component", "homepage": "https://symfony.com", "time": "2019-04-11T05:44:34+00:00"}, {"name": "symfony/stopwatch", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "2a651c2645c10bbedd21170771f122d935e0dd58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/2a651c2645c10bbedd21170771f122d935e0dd58", "reference": "2a651c2645c10bbedd21170771f122d935e0dd58", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Stopwatch Component", "homepage": "https://symfony.com", "time": "2019-01-16T09:39:14+00:00"}, {"name": "symfony/swiftmailer-bundle", "version": "v3.2.6", "source": {"type": "git", "url": "https://github.com/symfony/swiftmailer-bundle.git", "reference": "7a83160b50a2479d37eb74ba71577380b9afe4f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/swiftmailer-bundle/zipball/7a83160b50a2479d37eb74ba71577380b9afe4f5", "reference": "7a83160b50a2479d37eb74ba71577380b9afe4f5", "shasum": ""}, "require": {"php": ">=7.0.0", "swiftmailer/swiftmailer": "^6.1.3", "symfony/config": "~2.8|~3.3|~4.0", "symfony/dependency-injection": "~2.7|~3.3|~4.0", "symfony/http-kernel": "~2.7|~3.3|~4.0"}, "require-dev": {"symfony/console": "~2.7|~3.3|~4.0", "symfony/framework-bundle": "~2.7|~3.3|~4.0", "symfony/phpunit-bridge": "~3.3|~4.0", "symfony/yaml": "~2.7|~3.3|~4.0"}, "suggest": {"psr/log": "Allows logging"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\SwiftmailerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony SwiftmailerBundle", "homepage": "http://symfony.com", "time": "2019-04-18T15:52:54+00:00"}, {"name": "symfony/templating", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/templating.git", "reference": "1ea7458a960cf6b8818f24c62ff0db2a75835656"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/templating/zipball/1ea7458a960cf6b8818f24c62ff0db2a75835656", "reference": "1ea7458a960cf6b8818f24c62ff0db2a75835656", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "require-dev": {"psr/log": "~1.0"}, "suggest": {"psr/log-implementation": "For using debug logging in loaders"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Templating\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Templating Component", "homepage": "https://symfony.com", "time": "2019-02-23T15:06:07+00:00"}, {"name": "symfony/translation", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "aae26f143da71adc8707eb489f1dc86aef7d376b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/aae26f143da71adc8707eb489f1dc86aef7d376b", "reference": "aae26f143da71adc8707eb489f1dc86aef7d376b", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/config": "<2.8", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/http-kernel": "~3.4|~4.0", "symfony/intl": "^2.8.18|^3.2.5|~4.0", "symfony/var-dumper": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "time": "2019-04-10T16:00:48+00:00"}, {"name": "symfony/twig-bridge", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "de91817e436ac4fb27c58c05d6063c9214d6f37c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/de91817e436ac4fb27c58c05d6063c9214d6f37c", "reference": "de91817e436ac4fb27c58c05d6063c9214d6f37c", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "twig/twig": "^1.37.1|^2.6.2"}, "conflict": {"symfony/console": "<3.4", "symfony/form": "<3.4.13|>=4.0,<4.0.13|>=4.1,<4.1.2"}, "require-dev": {"symfony/asset": "~2.8|~3.0|~4.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~2.8|~3.0|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/form": "^3.4.23|^4.2.4", "symfony/http-foundation": "^3.3.11|~4.0", "symfony/http-kernel": "~3.2|~4.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/routing": "~2.8|~3.0|~4.0", "symfony/security": "^2.8.31|^3.3.13|~4.0", "symfony/security-acl": "~2.8|~3.0", "symfony/stopwatch": "~2.8|~3.0|~4.0", "symfony/templating": "~2.8|~3.0|~4.0", "symfony/translation": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~2.8.10|~3.1.4|~3.2|~4.0", "symfony/web-link": "~3.3|~4.0", "symfony/workflow": "~3.3|~4.0", "symfony/yaml": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/asset": "For using the AssetExtension", "symfony/expression-language": "For using the ExpressionExtension", "symfony/finder": "", "symfony/form": "For using the FormExtension", "symfony/http-kernel": "For using the HttpKernelExtension", "symfony/routing": "For using the RoutingExtension", "symfony/security": "For using the SecurityExtension", "symfony/stopwatch": "For using the StopwatchExtension", "symfony/templating": "For using the TwigEngine", "symfony/translation": "For using the TranslationExtension", "symfony/var-dumper": "For using the DumpExtension", "symfony/web-link": "For using the WebLinkExtension", "symfony/yaml": "For using the YamlExtension"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Twig Bridge", "homepage": "https://symfony.com", "time": "2019-04-12T13:39:20+00:00"}, {"name": "symfony/twig-bundle", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/twig-bundle.git", "reference": "57d8b5b97d754a24b2eaeb8bf02b8f7533d0df24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bundle/zipball/57d8b5b97d754a24b2eaeb8bf02b8f7533d0df24", "reference": "57d8b5b97d754a24b2eaeb8bf02b8f7533d0df24", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/config": "~3.2|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/http-kernel": "^3.3|~4.0", "symfony/polyfill-ctype": "~1.8", "symfony/twig-bridge": "^3.4.3|^4.0.3", "twig/twig": "~1.34|~2.4"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<3.3.1"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "symfony/asset": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.4.24|^4.2.5", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/form": "~2.8|~3.0|~4.0", "symfony/framework-bundle": "^3.3.11|~4.0", "symfony/routing": "~2.8|~3.0|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0", "symfony/templating": "~2.8|~3.0|~4.0", "symfony/web-link": "~3.3|~4.0", "symfony/yaml": "~2.8|~3.0|~4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\TwigBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony TwigBundle", "homepage": "https://symfony.com", "time": "2019-04-11T09:48:14+00:00"}, {"name": "symfony/validator", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "83da5259779aaf9dde220130e62b785f74e2ac49"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/83da5259779aaf9dde220130e62b785f74e2ac49", "reference": "83da5259779aaf9dde220130e62b785f74e2ac49", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/translation": "~2.8|~3.0|~4.0"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.3", "symfony/http-kernel": "<3.3.5", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "egulias/email-validator": "^1.2.8|~2.0", "symfony/cache": "~3.1|~4.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/http-kernel": "^3.3.5|~4.0", "symfony/intl": "^2.8.18|^3.2.5|~4.0", "symfony/property-access": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~3.3|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader and metadata cache.", "egulias/email-validator": "Strict (RFC compliant) email validation", "psr/cache-implementation": "For using the metadata cache.", "symfony/config": "", "symfony/expression-language": "For using the Expression validator", "symfony/http-foundation": "", "symfony/intl": "", "symfony/property-access": "For accessing properties within comparison constraints", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Validator Component", "homepage": "https://symfony.com", "time": "2019-04-16T11:21:44+00:00"}, {"name": "symfony/var-dumper", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "f0883812642a6d6583a9e2ae6aec4ba134436f40"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/f0883812642a6d6583a9e2ae6aec4ba134436f40", "reference": "f0883812642a6d6583a9e2ae6aec4ba134436f40", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0"}, "require-dev": {"ext-iconv": "*", "twig/twig": "~1.34|~2.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "ext-symfony_debug": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony mechanism for exploring and dumping PHP variables", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "time": "2019-04-16T13:58:17+00:00"}, {"name": "symfony/web-profiler-bundle", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/web-profiler-bundle.git", "reference": "4e76752191f9a453722f379f18ef65fbdc55cee4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-profiler-bundle/zipball/4e76752191f9a453722f379f18ef65fbdc55cee4", "reference": "4e76752191f9a453722f379f18ef65fbdc55cee4", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/http-kernel": "~3.4.25|^4.2.6", "symfony/polyfill-php70": "~1.0", "symfony/routing": "~2.8|~3.0|~4.0", "symfony/twig-bridge": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~3.3|~4.0", "twig/twig": "~1.34|~2.4"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<3.3.1", "symfony/var-dumper": "<3.3"}, "require-dev": {"symfony/config": "~3.4|~4.0", "symfony/console": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\WebProfilerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony WebProfilerBundle", "homepage": "https://symfony.com", "time": "2019-04-11T09:48:14+00:00"}, {"name": "symfony/web-server-bundle", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/web-server-bundle.git", "reference": "71efb580e6e7d11bf1fad18b783350b1ebb21646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-server-bundle/zipball/71efb580e6e7d11bf1fad18b783350b1ebb21646", "reference": "71efb580e6e7d11bf1fad18b783350b1ebb21646", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/config": "~3.4|~4.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/http-kernel": "~3.3|~4.0", "symfony/polyfill-ctype": "~1.8", "symfony/process": "~3.3.14|^3.4.2|^4.0.2"}, "suggest": {"symfony/expression-language": "For using the filter option of the log server.", "symfony/monolog-bridge": "For using the log server."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\WebServerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony WebServerBundle", "homepage": "https://symfony.com", "time": "2019-03-04T10:06:18+00:00"}, {"name": "symfony/workflow", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/workflow.git", "reference": "d4aac685731562b9b8648bb7653d9ae3cacd8523"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/workflow/zipball/d4aac685731562b9b8648bb7653d9ae3cacd8523", "reference": "d4aac685731562b9b8648bb7653d9ae3cacd8523", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/property-access": "~2.3|~3.0|~4.0"}, "require-dev": {"psr/log": "~1.0", "symfony/dependency-injection": "~2.8|~3.0|~4.0", "symfony/event-dispatcher": "~2.1|~3.0|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/security-core": "~2.8|~3.0|~4.0", "symfony/validator": "~2.8|~3.4|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Workflow\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Workflow Component", "homepage": "https://symfony.com", "keywords": ["petrinet", "place", "state", "statemachine", "transition", "workflow"], "time": "2019-04-01T07:08:40+00:00"}, {"name": "symfony/yaml", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "212a27b731e5bfb735679d1ffaac82bd6a1dc996"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/212a27b731e5bfb735679d1ffaac82bd6a1dc996", "reference": "212a27b731e5bfb735679d1ffaac82bd6a1dc996", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "~3.4|~4.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2019-03-25T07:48:46+00:00"}, {"name": "tecnickcom/tcpdf", "version": "6.2.26", "source": {"type": "git", "url": "https://github.com/tecnickcom/TCPDF.git", "reference": "367241059ca166e3a76490f4448c284e0a161f15"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tecnickcom/TCPDF/zipball/367241059ca166e3a76490f4448c284e0a161f15", "reference": "367241059ca166e3a76490f4448c284e0a161f15", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"classmap": ["config", "include", "tcpdf.php", "tcpdf_parser.php", "tcpdf_import.php", "tcpdf_barcodes_1d.php", "tcpdf_barcodes_2d.php", "include/tcpdf_colors.php", "include/tcpdf_filters.php", "include/tcpdf_font_data.php", "include/tcpdf_fonts.php", "include/tcpdf_images.php", "include/tcpdf_static.php", "include/barcodes/datamatrix.php", "include/barcodes/pdf417.php", "include/barcodes/qrcode.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "TCPDF is a PHP class for generating PDF documents and barcodes.", "homepage": "http://www.tcpdf.org/", "keywords": ["PDFD32000-2008", "TCPDF", "barcodes", "datamatrix", "pdf", "pdf417", "qrcode"], "time": "2018-10-16T17:24:05+00:00"}, {"name": "twig/extensions", "version": "v1.5.4", "source": {"type": "git", "url": "https://github.com/twigphp/Twig-extensions.git", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig-extensions/zipball/57873c8b0c1be51caa47df2cdb824490beb16202", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202", "shasum": ""}, "require": {"twig/twig": "^1.27|^2.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.4", "symfony/translation": "^2.7|^3.4"}, "suggest": {"symfony/translation": "Allow the time_diff output to be translated"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-0": {"Twig_Extensions_": "lib/"}, "psr-4": {"Twig\\Extensions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common additional features for Twig that do not directly belong in core", "keywords": ["i18n", "text"], "time": "2018-12-05T18:34:18+00:00"}, {"name": "twig/twig", "version": "v2.8.1", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "91cc2594d3143761ce0399c1caffd0b500ffe5b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/91cc2594d3143761ce0399c1caffd0b500ffe5b9", "reference": "91cc2594d3143761ce0399c1caffd0b500ffe5b9", "shasum": ""}, "require": {"php": "^7.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"psr/container": "^1.0", "symfony/debug": "^2.7", "symfony/phpunit-bridge": "^3.4.19|^4.1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}, {"name": "Twig Team", "homepage": "https://twig.symfony.com/contributors", "role": "Contributors"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "time": "2019-04-16T17:14:24+00:00"}, {"name": "vlucas/phpdotenv", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "3cc116adbe4b11be5ec557bf1d24dc5e3a21d18c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/3cc116adbe4b11be5ec557bf1d24dc5e3a21d18c", "reference": "3cc116adbe4b11be5ec557bf1d24dc5e3a21d18c", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "^4.8 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-Clause-Attribution"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.vancelucas.com"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "time": "2016-09-01T10:05:43+00:00"}, {"name": "zendframework/zend-code", "version": "3.3.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-code.git", "reference": "c21db169075c6ec4b342149f446e7b7b724f95eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-code/zipball/c21db169075c6ec4b342149f446e7b7b724f95eb", "reference": "c21db169075c6ec4b342149f446e7b7b724f95eb", "shasum": ""}, "require": {"php": "^7.1", "zendframework/zend-eventmanager": "^2.6 || ^3.0"}, "require-dev": {"doctrine/annotations": "~1.0", "ext-phar": "*", "phpunit/phpunit": "^6.2.3", "zendframework/zend-coding-standard": "^1.0.0", "zendframework/zend-stdlib": "^2.7 || ^3.0"}, "suggest": {"doctrine/annotations": "Doctrine\\Common\\Annotations >=1.0 for annotation features", "zendframework/zend-stdlib": "Zend\\Stdlib component"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3.x-dev", "dev-develop": "3.4.x-dev"}}, "autoload": {"psr-4": {"Zend\\Code\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides facilities to generate arbitrary code using an object oriented interface", "homepage": "https://github.com/zendframework/zend-code", "keywords": ["code", "zf2"], "time": "2018-08-13T20:36:59+00:00"}, {"name": "zendframework/zend-eventmanager", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-eventmanager.git", "reference": "a5e2583a211f73604691586b8406ff7296a946dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-eventmanager/zipball/a5e2583a211f73604691586b8406ff7296a946dd", "reference": "a5e2583a211f73604691586b8406ff7296a946dd", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"athletic/athletic": "^0.1", "container-interop/container-interop": "^1.1.0", "phpunit/phpunit": "^5.7.27 || ^6.5.8 || ^7.1.2", "zendframework/zend-coding-standard": "~1.0.0", "zendframework/zend-stdlib": "^2.7.3 || ^3.0"}, "suggest": {"container-interop/container-interop": "^1.1.0, to use the lazy listeners feature", "zendframework/zend-stdlib": "^2.7.3 || ^3.0, to use the FilterChain feature"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev", "dev-develop": "3.3-dev"}}, "autoload": {"psr-4": {"Zend\\EventManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Trigger and listen to events within a PHP application", "homepage": "https://github.com/zendframework/zend-eventmanager", "keywords": ["event", "eventmanager", "events", "zf2"], "time": "2018-04-25T15:33:34+00:00"}], "packages-dev": [{"name": "behat/gherkin", "version": "v4.6.0", "source": {"type": "git", "url": "https://github.com/Behat/Gherkin.git", "reference": "ab0a02ea14893860bca00f225f5621d351a3ad07"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Gherkin/zipball/ab0a02ea14893860bca00f225f5621d351a3ad07", "reference": "ab0a02ea14893860bca00f225f5621d351a3ad07", "shasum": ""}, "require": {"php": ">=5.3.1"}, "require-dev": {"phpunit/phpunit": "~4.5|~5", "symfony/phpunit-bridge": "~2.7|~3|~4", "symfony/yaml": "~2.3|~3|~4"}, "suggest": {"symfony/yaml": "If you want to parse features, represented in YAML files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-0": {"Behat\\Gherkin": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Gherkin DSL parser for PHP 5.3", "homepage": "http://behat.org/", "keywords": ["BDD", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>ber", "DSL", "g<PERSON>kin", "parser"], "time": "2019-01-16T14:22:17+00:00"}, {"name": "bheller/images-generator", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/bruceheller/images-generator.git", "reference": "50b61fe1dcf1b72b6a830debec4db22afd1e8ee1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bruceheller/images-generator/zipball/50b61fe1dcf1b72b6a830debec4db22afd1e8ee1", "reference": "50b61fe1dcf1b72b6a830debec4db22afd1e8ee1", "shasum": ""}, "require": {"fzaninotto/faker": "~1.4", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "*", "squizlabs/php_codesniffer": "2.*"}, "type": "library", "autoload": {"psr-4": {"bheller\\ImagesGenerator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.bheller.com"}], "description": "Generator of placeholder images for Faker", "homepage": "https://github.com/bruceheller/images-generator", "keywords": ["data", "faker", "fixtures", "images-generator", "imagesgenerator"], "time": "2016-03-03T08:40:48+00:00"}, {"name": "captbaritone/mailcatcher-codeception-module", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/captbaritone/codeception-mailcatcher-module.git", "reference": "75ba9aa803d81780ee7e9b5c36bb5b8f9139d972"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/captbaritone/codeception-mailcatcher-module/zipball/75ba9aa803d81780ee7e9b5c36bb5b8f9139d972", "reference": "75ba9aa803d81780ee7e9b5c36bb5b8f9139d972", "shasum": ""}, "require": {"guzzlehttp/guzzle": "*"}, "type": "library", "autoload": {"psr-4": {"Codeception\\Module\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Test emails in your Codeception acceptance tests", "time": "2016-08-16T21:35:06+00:00"}, {"name": "codeception/codeception", "version": "2.4.5", "source": {"type": "git", "url": "https://github.com/Codeception/Codeception.git", "reference": "5fee32d5c82791548931cbc34806b4de6aa1abfc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Codeception/zipball/5fee32d5c82791548931cbc34806b4de6aa1abfc", "reference": "5fee32d5c82791548931cbc34806b4de6aa1abfc", "shasum": ""}, "require": {"behat/gherkin": "^4.4.0", "codeception/phpunit-wrapper": "^6.0.9|^7.0.6", "codeception/stub": "^2.0", "ext-json": "*", "ext-mbstring": "*", "facebook/webdriver": ">=1.1.3 <2.0", "guzzlehttp/guzzle": ">=4.1.4 <7.0", "guzzlehttp/psr7": "~1.0", "php": ">=5.6.0 <8.0", "symfony/browser-kit": ">=2.7 <5.0", "symfony/console": ">=2.7 <5.0", "symfony/css-selector": ">=2.7 <5.0", "symfony/dom-crawler": ">=2.7 <5.0", "symfony/event-dispatcher": ">=2.7 <5.0", "symfony/finder": ">=2.7 <5.0", "symfony/yaml": ">=2.7 <5.0"}, "require-dev": {"codeception/specify": "~0.3", "facebook/graph-sdk": "~5.3", "flow/jsonpath": "~0.2", "monolog/monolog": "~1.8", "pda/pheanstalk": "~3.0", "php-amqplib/php-amqplib": "~2.4", "predis/predis": "^1.0", "squizlabs/php_codesniffer": "~2.0", "symfony/process": ">=2.7 <5.0", "vlucas/phpdotenv": "^2.4.0"}, "suggest": {"aws/aws-sdk-php": "For using AWS Auth in REST module and Queue module", "codeception/phpbuiltinserver": "Start and stop PHP built-in web server for your tests", "codeception/specify": "BDD-style code blocks", "codeception/verify": "BDD-style assertions", "flow/jsonpath": "For using JSONPath in REST module", "league/factory-muffin": "For DataFactory module", "league/factory-muffin-faker": "For Faker support in DataFactory module", "phpseclib/phpseclib": "for SFTP option in FTP Module", "stecman/symfony-console-completion": "For BASH autocompletion", "symfony/phpunit-bridge": "For phpunit-bridge support"}, "bin": ["codecept"], "type": "library", "extra": {"branch-alias": []}, "autoload": {"psr-4": {"Codeception\\": "src\\Codeception", "Codeception\\Extension\\": "ext"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://codegyre.com"}], "description": "BDD-style testing framework", "homepage": "http://codeception.com/", "keywords": ["BDD", "TDD", "acceptance testing", "functional testing", "unit testing"], "time": "2018-08-01T07:21:49+00:00"}, {"name": "codeception/phpunit-wrapper", "version": "6.6.1", "source": {"type": "git", "url": "https://github.com/Codeception/phpunit-wrapper.git", "reference": "d0da25a98bcebeb15d97c2ad3b2de6166b6e7a0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/phpunit-wrapper/zipball/d0da25a98bcebeb15d97c2ad3b2de6166b6e7a0c", "reference": "d0da25a98bcebeb15d97c2ad3b2de6166b6e7a0c", "shasum": ""}, "require": {"phpunit/php-code-coverage": ">=4.0.4 <6.0", "phpunit/phpunit": ">=6.5.13 <7.0", "sebastian/comparator": ">=1.2.4 <3.0", "sebastian/diff": ">=1.4 <4.0"}, "replace": {"codeception/phpunit-wrapper": "*"}, "require-dev": {"codeception/specify": "*", "vlucas/phpdotenv": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Codeception\\PHPUnit\\": "src\\"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHPUnit classes used by Codeception", "time": "2019-02-26T20:47:39+00:00"}, {"name": "codeception/stub", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/Codeception/Stub.git", "reference": "853657f988942f7afb69becf3fd0059f192c705a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Stub/zipball/853657f988942f7afb69becf3fd0059f192c705a", "reference": "853657f988942f7afb69becf3fd0059f192c705a", "shasum": ""}, "require": {"codeception/phpunit-wrapper": ">6.0.15 <6.1.0 | ^6.6.1 | ^7.7.1 | ^8.0.3"}, "type": "library", "autoload": {"psr-4": {"Codeception\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Flexible Stub wrapper for PHPUnit's Mock Builder", "time": "2019-03-02T15:35:10+00:00"}, {"name": "dama/doctrine-test-bundle", "version": "v4.0.2", "source": {"type": "git", "url": "https://github.com/dmaicher/doctrine-test-bundle.git", "reference": "438346b3380cc7675e37fbcdca912fdc33471d32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dmaicher/doctrine-test-bundle/zipball/438346b3380cc7675e37fbcdca912fdc33471d32", "reference": "438346b3380cc7675e37fbcdca912fdc33471d32", "shasum": ""}, "require": {"doctrine/dbal": "~2.5", "doctrine/doctrine-bundle": "~1.4", "php": ">=5.5.0", "symfony/framework-bundle": "~2.7|~3.0|~4.0"}, "require-dev": {"phpunit/phpunit": "^5.4.4|~6.0", "symfony/yaml": "~2.7|~3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.1.x-dev"}}, "autoload": {"psr-4": {"DAMA\\DoctrineTestBundle\\": "src/DAMA/DoctrineTestBundle"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Symfony 2/3 bundle to isolate doctrine database tests and improve test performance", "keywords": ["Symfony 3", "doctrine", "isolation", "performance", "symfony", "symfony 2", "tests"], "time": "2018-01-13T13:14:27+00:00"}, {"name": "facebook/webdriver", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/facebook/php-webdriver.git", "reference": "bd8c740097eb9f2fc3735250fc1912bc811a954e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/facebook/php-webdriver/zipball/bd8c740097eb9f2fc3735250fc1912bc811a954e", "reference": "bd8c740097eb9f2fc3735250fc1912bc811a954e", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-zip": "*", "php": "^5.6 || ~7.0", "symfony/process": "^2.8 || ^3.1 || ^4.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "jakub-onderka/php-parallel-lint": "^0.9.2", "php-coveralls/php-coveralls": "^2.0", "php-mock/php-mock-phpunit": "^1.1", "phpunit/phpunit": "^5.7", "sebastian/environment": "^1.3.4 || ^2.0 || ^3.0", "squizlabs/php_codesniffer": "^2.6", "symfony/var-dumper": "^3.3 || ^4.0"}, "suggest": {"ext-SimpleXML": "For Firefox profile creation"}, "type": "library", "extra": {"branch-alias": {"dev-community": "1.5-dev"}}, "autoload": {"psr-4": {"Facebook\\WebDriver\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "A PHP client for Selenium WebDriver", "homepage": "https://github.com/facebook/php-webdriver", "keywords": ["facebook", "php", "selenium", "webdriver"], "time": "2018-05-16T17:37:13+00:00"}, {"name": "fzaninotto/faker", "version": "v1.8.0", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "f72816b43e74063c8b10357394b6bba8cb1c10de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/f72816b43e74063c8b10357394b6bba8cb1c10de", "reference": "f72816b43e74063c8b10357394b6bba8cb1c10de", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^1.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "time": "2018-07-12T10:23:15+00:00"}, {"name": "mikey179/vfsstream", "version": "v1.6.6", "source": {"type": "git", "url": "https://github.com/bovigo/vfsStream.git", "reference": "095238a0711c974ae5b4ebf4c4534a23f3f6c99d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bovigo/vfsStream/zipball/095238a0711c974ae5b4ebf4c4534a23f3f6c99d", "reference": "095238a0711c974ae5b4ebf4c4534a23f3f6c99d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-0": {"org\\bovigo\\vfs\\": "src/main/php"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://frankkleine.de/", "role": "Developer"}], "description": "Virtual file system to mock the real file system in unit tests.", "homepage": "http://vfs.bovigo.org/", "time": "2019-04-08T13:54:32+00:00"}, {"name": "myclabs/deep-copy", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "e6828efaba2c9b79f4499dae1d66ef8bfa7b2b72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/e6828efaba2c9b79f4499dae1d66ef8bfa7b2b72", "reference": "e6828efaba2c9b79f4499dae1d66ef8bfa7b2b72", "shasum": ""}, "require": {"php": "^7.1"}, "replace": {"myclabs/deep-copy": "self.version"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "time": "2019-04-07T13:18:21+00:00"}, {"name": "phar-io/manifest", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "2df402786ab5368a0169091f61a7c1e0eb6852d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/2df402786ab5368a0169091f61a7c1e0eb6852d0", "reference": "2df402786ab5368a0169091f61a7c1e0eb6852d0", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "phar-io/version": "^1.0.1", "php": "^5.6 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "time": "2017-03-05T18:14:27+00:00"}, {"name": "phar-io/version", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "a70c0ced4be299a63d32fa96d9281d03e94041df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/a70c0ced4be299a63d32fa96d9281d03e94041df", "reference": "a70c0ced4be299a63d32fa96d9281d03e94041df", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "time": "2017-03-05T17:38:23+00:00"}, {"name": "php-coveralls/php-coveralls", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/php-coveralls/php-coveralls.git", "reference": "3b00c229726f892bfdadeaf01ea430ffd04a939d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-coveralls/php-coveralls/zipball/3b00c229726f892bfdadeaf01ea430ffd04a939d", "reference": "3b00c229726f892bfdadeaf01ea430ffd04a939d", "shasum": ""}, "require": {"ext-json": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.0", "php": "^5.5 || ^7.0", "psr/log": "^1.0", "symfony/config": "^2.1 || ^3.0 || ^4.0", "symfony/console": "^2.1 || ^3.0 || ^4.0", "symfony/stopwatch": "^2.0 || ^3.0 || ^4.0", "symfony/yaml": "^2.0 || ^3.0 || ^4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.4.3 || ^6.0"}, "suggest": {"symfony/http-kernel": "Allows Symfony integration"}, "bin": ["bin/php-coveralls"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"psr-4": {"PhpCoveralls\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.facebook.com/satooshi.jp", "role": "Original creator"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Google Inc"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/keradus"}, {"name": "Contributors", "homepage": "https://github.com/php-coveralls/php-coveralls/graphs/contributors"}], "description": "PHP client library for Coveralls API", "homepage": "https://github.com/php-coveralls/php-coveralls", "keywords": ["ci", "coverage", "github", "test"], "time": "2018-05-22T23:11:08+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2017-09-11T18:02:19+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "4.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "94fd0001232e47129dd3504189fa1c7225010d08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/94fd0001232e47129dd3504189fa1c7225010d08", "reference": "94fd0001232e47129dd3504189fa1c7225010d08", "shasum": ""}, "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0.0", "phpdocumentor/type-resolver": "^0.4.0", "webmozart/assert": "^1.0"}, "require-dev": {"doctrine/instantiator": "~1.0.5", "mockery/mockery": "^1.0", "phpunit/phpunit": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2017-11-30T07:14:17+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "0.4.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "9c977708995954784726e25d0cd1dddf4e65b0f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/9c977708995954784726e25d0cd1dddf4e65b0f7", "reference": "9c977708995954784726e25d0cd1dddf4e65b0f7", "shasum": ""}, "require": {"php": "^5.5 || ^7.0", "phpdocumentor/reflection-common": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^5.2||^4.8.24"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2017-07-14T14:27:02+00:00"}, {"name": "phpspec/prophecy", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "4ba436b55987b4bf311cb7c6ba82aa528aac0a06"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/4ba436b55987b4bf311cb7c6ba82aa528aac0a06", "reference": "4ba436b55987b4bf311cb7c6ba82aa528aac0a06", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0", "sebastian/comparator": "^1.1|^2.0|^3.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0"}, "require-dev": {"phpspec/phpspec": "^2.5|^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5 || ^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "autoload": {"psr-0": {"Prophecy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2018-08-05T17:53:17+00:00"}, {"name": "phpunit/php-code-coverage", "version": "5.3.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "c89677919c5dd6d3b3852f230a663118762218ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/c89677919c5dd6d3b3852f230a663118762218ac", "reference": "c89677919c5dd6d3b3852f230a663118762218ac", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^7.0", "phpunit/php-file-iterator": "^1.4.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^2.0.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.0", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "suggest": {"ext-xdebug": "^2.5.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2018-04-06T15:36:58+00:00"}, {"name": "phpunit/php-file-iterator", "version": "1.4.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/730b01bc3e867237eaac355e06a36b85dd93a8b4", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2017-11-27T13:52:08+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "1.0.9", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2017-02-26T11:10:40+00:00"}, {"name": "phpunit/php-token-stream", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "791198a2c6254db10131eecfe8c06670700904db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/791198a2c6254db10131eecfe8c06670700904db", "reference": "791198a2c6254db10131eecfe8c06670700904db", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "time": "2017-11-27T05:48:46+00:00"}, {"name": "phpunit/phpunit", "version": "6.5.14", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "bac23fe7ff13dbdb461481f706f0e9fe746334b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/bac23fe7ff13dbdb461481f706f0e9fe746334b7", "reference": "bac23fe7ff13dbdb461481f706f0e9fe746334b7", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "^1.6.1", "phar-io/manifest": "^1.0.1", "phar-io/version": "^1.0", "php": "^7.0", "phpspec/prophecy": "^1.7", "phpunit/php-code-coverage": "^5.3", "phpunit/php-file-iterator": "^1.4.3", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^1.0.9", "phpunit/phpunit-mock-objects": "^5.0.9", "sebastian/comparator": "^2.1", "sebastian/diff": "^2.0", "sebastian/environment": "^3.1", "sebastian/exporter": "^3.1", "sebastian/global-state": "^2.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^1.0", "sebastian/version": "^2.0.1"}, "conflict": {"phpdocumentor/reflection-docblock": "3.0.2", "phpunit/dbunit": "<3.0"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-xdebug": "*", "phpunit/php-invoker": "^1.1"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "6.5.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2019-02-01T05:22:47+00:00"}, {"name": "phpunit/phpunit-mock-objects", "version": "5.0.10", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "cd1cf05c553ecfec36b170070573e540b67d3f1f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/cd1cf05c553ecfec36b170070573e540b67d3f1f", "reference": "cd1cf05c553ecfec36b170070573e540b67d3f1f", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.5", "php": "^7.0", "phpunit/php-text-template": "^1.2.1", "sebastian/exporter": "^3.1"}, "conflict": {"phpunit/phpunit": "<6.0"}, "require-dev": {"phpunit/phpunit": "^6.5.11"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "abandoned": true, "time": "2018-08-09T05:50:03+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "time": "2017-03-04T06:30:41+00:00"}, {"name": "sebastian/comparator", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "****************************************"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/****************************************", "reference": "****************************************", "shasum": ""}, "require": {"php": "^7.0", "sebastian/diff": "^2.0 || ^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2018-02-01T13:46:46+00:00"}, {"name": "sebastian/diff", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "347c1d8b49c5c3ee30c7040ea6fc446790e6bddd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/347c1d8b49c5c3ee30c7040ea6fc446790e6bddd", "reference": "347c1d8b49c5c3ee30c7040ea6fc446790e6bddd", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "time": "2017-08-03T08:09:46+00:00"}, {"name": "sebastian/environment", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "cd0871b3975fb7fc44d11314fd1ee20925fce4f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/cd0871b3975fb7fc44d11314fd1ee20925fce4f5", "reference": "cd0871b3975fb7fc44d11314fd1ee20925fce4f5", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2017-07-01T08:51:00+00:00"}, {"name": "sebastian/exporter", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "68609e1261d215ea5b21b7987539cbfbe156ec3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/68609e1261d215ea5b21b7987539cbfbe156ec3e", "reference": "68609e1261d215ea5b21b7987539cbfbe156ec3e", "shasum": ""}, "require": {"php": "^7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2019-09-14T09:02:43+00:00"}, {"name": "sebastian/global-state", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2017-04-27T15:39:26+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/7cfd9e65d11ffb5af41198476395774d4c8a84c5", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5", "shasum": ""}, "require": {"php": "^7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "time": "2017-08-03T12:35:26+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "773f97c67f28de00d397be301821b06708fca0be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/773f97c67f28de00d397be301821b06708fca0be", "reference": "773f97c67f28de00d397be301821b06708fca0be", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "time": "2017-03-29T09:07:27+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2017-03-03T06:23:57+00:00"}, {"name": "sebastian/resource-operations", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "shasum": ""}, "require": {"php": ">=5.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "time": "2015-07-28T20:34:47+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2016-10-03T07:35:21+00:00"}, {"name": "symfony/browser-kit", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "7f2b0843d5045468225f9a9b27a0cb171ae81828"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/7f2b0843d5045468225f9a9b27a0cb171ae81828", "reference": "7f2b0843d5045468225f9a9b27a0cb171ae81828", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/dom-crawler": "~2.8|~3.0|~4.0"}, "require-dev": {"symfony/css-selector": "~2.8|~3.0|~4.0", "symfony/process": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony BrowserKit Component", "homepage": "https://symfony.com", "time": "2019-04-06T19:33:58+00:00"}, {"name": "symfony/phpunit-bridge", "version": "v3.4.26", "source": {"type": "git", "url": "https://github.com/symfony/phpunit-bridge.git", "reference": "a43a2f6c465a2d99635fea0addbebddc3864ad97"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/phpunit-bridge/zipball/a43a2f6c465a2d99635fea0addbebddc3864ad97", "reference": "a43a2f6c465a2d99635fea0addbebddc3864ad97", "shasum": ""}, "require": {"php": ">=5.3.3"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0"}, "suggest": {"symfony/debug": "For tracking deprecated interfaces usages at runtime with DebugClassLoader"}, "bin": ["bin/simple-phpunit"], "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "3.4-dev"}, "thanks": {"name": "phpunit/phpunit", "url": "https://github.com/sebastian<PERSON>mann/phpunit"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Bridge\\PhpUnit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony PHPUnit Bridge", "homepage": "https://symfony.com", "time": "2019-04-16T09:03:16+00:00"}, {"name": "theseer/tokenizer", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "1c42705be2b6c1de5904f8afacef5895cab44bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/1c42705be2b6c1de5904f8afacef5895cab44bf8", "reference": "1c42705be2b6c1de5904f8afacef5895cab44bf8", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "time": "2019-04-04T09:56:43+00:00"}, {"name": "webmozart/assert", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/webmozart/assert.git", "reference": "83e253c8e0be5b0257b881e1827274667c5c17a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/assert/zipball/83e253c8e0be5b0257b881e1827274667c5c17a9", "reference": "83e253c8e0be5b0257b881e1827274667c5c17a9", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2018-12-25T11:19:39+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^7.1.3", "ext-intl": "*", "ext-mbstring": "*"}, "platform-dev": [], "platform-overrides": {"php": "7.1.3"}}