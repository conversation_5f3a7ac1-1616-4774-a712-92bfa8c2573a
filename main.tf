module "vpc" {
  source              = "./modules/vpc"
  name                = "${var.env}-${var.project_name}-vpc"
  cidr_block          = var.vpc_cidr
  public_subnet_cidrs = var.public_subnet_cidrs
  public_subnet_azs   = var.public_subnet_azs
  private_subnet_cidrs = var.private_subnet_cidrs
  private_subnet_azs   = var.private_subnet_azs
  env                 = var.env
  project_name        = var.project_name
}

module "security_groups" {
  source = "./modules/security-groups"
  vpc_id = module.vpc.vpc_id
  env    = var.env
  project_name = var.project_name
}

module "efs" {
  source                = "./modules/efs"
  vpc_id                = module.vpc.vpc_id
  private_subnet_ids    = module.vpc.private_subnet_ids
  ecs_security_group_id = module.security_groups.ecs_sg_id
  env                   = var.env
  project_name          = var.project_name
}

module "rds" {
  source            = "./modules/rds"
  public_subnet_ids = module.vpc.public_subnet_ids
  engine_version    = var.rds_engine_version
  username          = var.rds_username
  password          = var.rds_password
  security_group_id = module.security_groups.rds_sg_id
  instance_class    = var.rds_instance_class
  env               = var.env
  project_name      = var.project_name
}

module "alb" {
  source            = "./modules/alb"
  vpc_id            = module.vpc.vpc_id
  public_subnet_ids  = module.vpc.public_subnet_ids
  security_group_id = module.security_groups.alb_sg_id
  web_port          = var.web_port
  env               = var.env
  project_name      = var.project_name
}

# module "route53" {
#   source      = "./modules/route53"
#   domain_name = var.domain_name
#   alb_dns_name = module.alb.alb_dns_name
#   alb_zone_id  = module.alb.alb_zone_id
#   env          = var.env
#   project_name = var.project_name
# }

module "ecs" {
  source              = "./modules/ecs"
  private_subnet_ids  = module.vpc.private_subnet_ids
  security_group_id   = module.security_groups.ecs_sg_id
  web_image           = var.web_image
  web_port            = var.web_port
  web_tg_arn          = module.alb.web_tg_arn
  db_host             = module.rds.address
  db_user             = var.rds_username
  db_pass             = var.rds_password
  web_cpu             = var.web_cpu
  web_memory          = var.web_memory
  efs_file_system_id  = module.efs.efs_id
  efs_access_point_id = module.efs.efs_access_point_id
  efs_mount_path      = "/mnt/${var.env}"
  env                 = var.env
  project_name        = var.project_name
}
