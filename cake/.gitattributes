# Define the line ending behavior of the different file extensions
# Set default behavior, in case users don't have core.autocrlf set.
* text=auto
* text eol=lf

# Explicitly declare text files we want to always be normalized and converted
# to native line endings on checkout.
*.php text
*.default text
*.ctp text
*.sql text
*.md text
*.po text
*.js text
*.css text
*.ini text
*.properties text
*.txt text
*.xml text
*.svg text
*.yml text
.htaccess text

# Declare files that will always have CRLF line endings on checkout.
*.bat eol=crlf

# Declare files that will always have LF line endings on checkout.
*.pem eol=lf

# Denote all files that are truly binary and should not be modified.
*.png binary
*.jpg binary
*.gif binary
*.ico binary
*.mo binary
*.pdf binary
*.phar binary
*.woff binary
*.woff2 binary
*.ttf binary
*.otf binary
*.eot binary
*.gz binary
*.bz2 binary
*.7z binary
*.zip binary
