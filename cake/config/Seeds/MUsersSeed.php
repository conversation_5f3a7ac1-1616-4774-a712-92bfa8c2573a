<?php

use <PERSON>ake\Auth\DefaultPasswordHasher;
use Faker\Factory;
use Migrations\AbstractSeed;

/**
 * MUsersSeed
 * 利用者マスタ
 */
class MUsersSeed extends AbstractSeed
{
    /**
     * Run Method.
     *
     * Write your database seeder using this method.
     *
     * More information on writing seeds is available here:
     * http://docs.phinx.org/en/latest/seeding.html
     *
     * @return void
     */
    public function run()
    {
        $datetime = date('Y-m-d H:i:s');

        // fakerインスタンスを生成する
        $faker = Faker\Factory::create('ja_JP');

        // password用のhash関数
        $hash = new DefaultPasswordHasher;

        $data = [
            [
                'login_id' => 'user1',
                'customer_id' => '1',
                'mail_address' => '<EMAIL>',
                'password' => $hash->hash('123'),
                'name' => 'テストユーザ１',
                'secure_code' => 'aaaaaaaaaaaaaaaa',
                'tmp_password' => '',
                'tmp_password_time' => null,
                'is_approved' => '1',
                'is_active' => '1',
                'order_view_type' => '0',
                'order_category_type' => '0',
                'is_demo' => '0',
                'customer_code_type' => '1',
                'is_deleted' => '0',
                'last_login_date'=>$datetime,
                'last_order_date'=>$datetime,
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ],
            [
                'login_id' => 'user2',
                'customer_id' => '1',
                'mail_address' => '<EMAIL>',
                'password' => '',
                'name' => 'テストユーザ２',
                'secure_code' => 'bbbbbbbbbbbbbbbbb',
                'tmp_password' => '123456',
                'tmp_password_time' => $datetime,
                'is_approved' => '1',
                'is_active' => '1',
                'order_view_type' => '0',
                'order_category_type' => '0',
                'is_demo' => '0',
                'customer_code_type' => '1',
                'is_deleted' => '0',
                'last_login_date'=>$datetime,
                'last_order_date'=>$datetime,
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ],
            [
                'login_id' => 'user3',
                'customer_id' => '1',
                'mail_address' => '<EMAIL>',
                'password' => $hash->hash('123'),
                'name' => 'テストユーザ３',
                'secure_code' => 'bbbbbbbbbbbbbbbbb',
                'tmp_password' => '',
                'tmp_password_time' => null,
                'is_approved' => '1',
                'is_active' => '1',
                'order_view_type' => '0',
                'order_category_type' => '0',
                'is_demo' => '0',
                'customer_code_type' => '1',
                'is_deleted' => '1',
                'last_login_date'=>$datetime,
                'last_order_date'=>$datetime,
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ],
            [
                'login_id' => 'user4',
                'customer_id' => '2',
                'mail_address' => '<EMAIL>',
                'password' => $hash->hash('123'),
                'name' => '取引先１－２ユーザ',
                'secure_code' => 'bbbbbbbbbbbbbbbbb',
                'tmp_password' => '',
                'tmp_password_time' => null,
                'is_approved' => '1',
                'is_active' => '1',
                'order_view_type' => '0',
                'order_category_type' => '0',
                'is_demo' => '0',
                'customer_code_type' => '1',
                'is_deleted' => '0',
                'last_login_date'=>$datetime,
                'last_order_date'=>$datetime,
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ],
            [
                'login_id' => 'user5',
                'customer_id' => '3',
                'mail_address' => '<EMAIL>',
                'password' => $hash->hash('123'),
                'name' => '取引先１－３ユーザ',
                'secure_code' => 'bbbbbbbbbbbbbbbbb',
                'tmp_password' => '',
                'tmp_password_time' => null,
                'is_approved' => '1',
                'is_active' => '1',
                'order_view_type' => '0',
                'order_category_type' => '0',
                'is_demo' => '0',
                'customer_code_type' => '1',
                'is_deleted' => '0',
                'last_login_date'=>$datetime,
                'last_order_date'=>$datetime,
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ],
            [
                'login_id' => 'user6',
                'customer_id' => '4',
                'mail_address' => '<EMAIL>',
                'password' => $hash->hash('123'),
                'name' => '取引先２ユーザ',
                'secure_code' => 'bbbbbbbbbbbbbbbbb',
                'tmp_password' => '',
                'tmp_password_time' => null,
                'is_approved' => '1',
                'is_active' => '1',
                'order_view_type' => '0',
                'order_category_type' => '0',
                'is_demo' => '0',
                'customer_code_type' => '1',
                'is_deleted' => '0',
                'last_login_date'=>$datetime,
                'last_order_date'=>$datetime,
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ],
            [
                'login_id' => 'user',
                'customer_id' => '5',
                'mail_address' => '<EMAIL>',
                'password' => $hash->hash('123'),
                'name' => 'テストユーザ',
                'secure_code' => 'bbbbbbbbbbbbbbbbb',
                'tmp_password' => '',
                'tmp_password_time' => null,
                'is_approved' => '1',
                'is_active' => '1',
                'order_view_type' => '0',
                'order_category_type' => '0',
                'is_demo' => '0',
                'customer_code_type' => '1',
                'is_deleted' => '0',
                'last_login_date'=>$datetime,
                'last_order_date'=>$datetime,
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ],
            [
                'login_id' => 'test1',
                'customer_id' => '1',
                'mail_address' => '<EMAIL>',
                'password' => $hash->hash('123'),
                'name' => 'テストユーザ１',
                'secure_code' => 'aaaaaaaaaaaaaaaa',
                'tmp_password' => '',
                'tmp_password_time' => null,
                'is_approved' => '1',
                'is_active' => '1',
                'order_view_type' => '0',
                'order_category_type' => '0',
                'is_demo' => '0',
                'customer_code_type' => '1',
                'is_deleted' => '0',
                'last_login_date'=>$datetime,
                'last_order_date'=>$datetime,
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ],
            [
                'login_id' => 'test2',
                'customer_id' => '1',
                'mail_address' => '<EMAIL>',
                'password' => $hash->hash('123'),
                'name' => 'テストユーザ１',
                'secure_code' => 'aaaaaaaaaaaaaaaa',
                'tmp_password' => '',
                'tmp_password_time' => null,
                'is_approved' => '1',
                'is_active' => '1',
                'order_view_type' => '0',
                'order_category_type' => '0',
                'is_demo' => '0',
                'customer_code_type' => '1',
                'is_deleted' => '0',
                'last_login_date'=>$datetime,
                'last_order_date'=>$datetime,
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ],
            [
                'login_id' => 'test3',
                'customer_id' => '5',
                'mail_address' => '<EMAIL>',
                'password' => $hash->hash('123'),
                'name' => 'テストユーザ３',
                'secure_code' => 'aaaaaaaaaaaaaaaa',
                'tmp_password' => '',
                'tmp_password_time' => null,
                'is_approved' => '1',
                'is_active' => '1',
                'order_view_type' => '0',
                'order_category_type' => '0',
                'is_demo' => '0',
                'customer_code_type' => '1',
                'is_deleted' => '0',
                'last_login_date'=>$datetime,
                'last_order_date'=>$datetime,
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ],
        ];

        //ダミーデータ挿入

        for ($i = 0; $i < 100; $i++) {

            $data[] = [
                'login_id' => $faker->unique()->userName,
                'customer_id' => $faker->numberBetween(1, 100),
                'mail_address' => 	"<EMAIL>",//$faker->safeEmail,
                'password' => $hash->hash('123'),
                'name' => $faker->name(),
                'secure_code' => $faker->password(10, 10),
                'tmp_password' => '',
                'tmp_password_time' => null,
                'is_approved' => $faker->numberBetween(0, 1),
                'is_active' => $faker->numberBetween(0, 1),
                'order_view_type' => '0',
                'order_category_type' => '0',
                'is_demo' => $faker->numberBetween(0, 1),
                'customer_code_type' => '1',
                'is_deleted' => $faker->numberBetween(0, 1),
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ];

        }

        $this->execute('SET FOREIGN_KEY_CHECKS = 0');

        $table = $this->table('m_users');
        $table->insert($data)->save();

        $this->execute('SET FOREIGN_KEY_CHECKS = 1');
    }
}
