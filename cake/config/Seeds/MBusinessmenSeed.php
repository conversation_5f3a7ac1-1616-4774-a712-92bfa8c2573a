<?php

use Faker\Factory;
use Migrations\AbstractSeed;

/**
 * MBusinessmen seed.
 */
class MBusinessmenSeed extends AbstractSeed
{
    /**
     * Run Method.
     *
     * Write your database seeder using this method.
     *
     * More information on writing seeds is available here:
     * http://docs.phinx.org/en/latest/seeding.html
     *
     * @return void
     */
    public function run()
    {
        $datetime = date('Y-m-d H:i:s');
        $datetime2 = date('Y-m-d H:i:s');
        $time = date('Hi');

        // fakerインスタンスを生成する
        $faker = Faker\Factory::create('ja_JP');

        $data = [
            [
                'company_code' => '001',
                'login_id' => 'admin1',
                'password' => '123',
                'name' => '上杉鷹山',
                'bu_code' => '999',
                'ka_code' => '999',
                'kakari_code' => '999',
                'is_orderstop' => '1',
                'last_order_date' => $datetime2,
                'use_businessman_close_time' => '1',
                'businessman_close_time' => '1700',
                'created' => $datetime,
                'created_user' => 'system',
                'updated' => $datetime,
                'updated_user' => 'system',
            ],
            [
                'company_code' => '001',
                'login_id' => 'admin2',
                'password' => '123',
                'name' => '真田幸村',
                'bu_code' => '999',
                'ka_code' => '999',
                'kakari_code' => '999',
                'is_orderstop' => '1',
                'last_order_date' => $datetime2,
                'use_businessman_close_time' => '0',
                'businessman_close_time' => '0000',
                'created' => $datetime,
                'created_user' => 'system',
                'updated' => $datetime,
                'updated_user' => 'system',
            ],
            [
                'company_code' => '001',
                'login_id' => 'admin11',
                'password' => '123',
                'name' => '伊達政宗',
                'bu_code' => '999',
                'ka_code' => '999',
                'kakari_code' => '999',
                'is_orderstop' => '0',
                'last_order_date' => $datetime2,
                'use_businessman_close_time' => '1',
                'businessman_close_time' => '1700',
                'created' => $datetime,
                'created_user' => 'system',
                'updated' => $datetime,
                'updated_user' => 'system',
            ],
            [
                'company_code' => '001',
                'login_id' => 'admin22',
                'password' => '123',
                'name' => '毛利元就',
                'bu_code' => '999',
                'ka_code' => '999',
                'kakari_code' => '999',
                'is_orderstop' => '0',
                'last_order_date' => $datetime2,
                'use_businessman_close_time' => '0',
                'businessman_close_time' => '0000',
                'created' => $datetime,
                'created_user' => 'system',
                'updated' => $datetime,
                'updated_user' => 'system',
            ],
        ];

        //ダミーデータ挿入
        for ($i = 0; $i < 1000; $i++) {
            $data[] = [
                'company_code' => $faker->numberBetween(101, 999),
                'login_id' => $faker->lexify('???????????????'),
                'password' => '123',
                'name' => $faker->name,
                'bu_code' => $faker->numberBetween(101, 999),
                'ka_code' => $faker->numberBetween(101, 999),
                'kakari_code' => $faker->numberBetween(101, 999),
                'is_orderstop' => $faker->randomElement(['0', '1']),
                'last_order_date' => $datetime2,
                'use_businessman_close_time' => $faker->randomElement(['0', '1']),
                'businessman_close_time' => $faker->numberBetween(10, 23) . '00',
                'created' => $datetime,
                'created_user' => 'system',
                'updated' => $datetime,
                'updated_user' => 'system',
            ];
        }

        $this->execute('SET FOREIGN_KEY_CHECKS = 0');

        $table = $this->table('m_businessmen');
        $table->insert($data)->save();

        $this->execute('SET FOREIGN_KEY_CHECKS = 1');
    }
}
