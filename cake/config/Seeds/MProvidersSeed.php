<?php

use Migrations\AbstractSeed;

/**
 * MProviders seed.
 */
class MProvidersSeed extends AbstractSeed
{
    /**
     * Run Method.
     *
     * Write your database seeder using this method.
     *
     * More information on writing seeds is available here:
     * http://docs.phinx.org/en/latest/seeding.html
     *
     * @return void
     */
    public function run()
    {
        $datetime = date('Y-m-d H:i:s');

        // ユーザ追加
        $data = [
            [
                'login_id' => 'pro1',
                'password' => '123',
                'name' => 'プロバイダ1',
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ],
            [
                'login_id' => 'pro2',
                'password' => '123',
                'name' => 'プロバイダ2',
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ],
            [
                'login_id' => 'pro3',
                'password' => '123',
                'name' => 'プロバイダ3',
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ],
        ];

        $this->execute('SET FOREIGN_KEY_CHECKS = 0');

        $table = $this->table('m_providers');
        $table->insert($data)->save();

        $this->execute('SET FOREIGN_KEY_CHECKS = 1');
    }
}
