<?php

use Faker\Factory;
use Migrations\AbstractSeed;

/**
 * MCompanies seed.
 */
class MCompaniesSeed extends AbstractSeed
{
    /**
     * Run Method.
     *
     * Write your database seeder using this method.
     *
     * More information on writing seeds is available here:
     * http://docs.phinx.org/en/latest/seeding.html
     *
     * @return void
     */
    public function run()
    {
        $datetime = date('Y-m-d H:i:s');

        // fakerインスタンスを生成する
        $faker = Faker\Factory::create('ja_JP');

        $data = [
            [
                'code' => '001',
                'name' => '企業１',
                'postcode' => '700-0000',
                'address1' => '広島市中区',
                'address2' => '',
                'address3' => '',
                'tel_no' => '************',
                'fax_no' => '',
                'tanto_name' => 'テスト担当者',
                'tanto_email' => '<EMAIL>',
                'download_id' => '0',
                'download_state' => '0',
                'close_time' => '1700',
                'sender_name' => '送信元名称1',
                'plan_code' => '99999',
                'is_maintenance' => '0',
                'is_orderzan' => '0',
                'orderzan_emails' => '',
                'is_order_code' => '1',
                'self_name1' => '',
                'self_name2' => '',
                'self_name3' => '',
                'order_type_0' => '通常',
                'order_type_1' => '直送',
                'order_type_2' => '時価',
                'order_type_3' => '受発',
                'order_type_4' => '',
                'order_type_5' => '',
                'order_type_6' => '',
                'order_type_7' => '',
                'order_type_8' => '',
                'order_type_9' => '',
                'customer_code_type' => '1',
                'terminal_order_type' => '1',
                'terminal_tab_type' => '1',
                'use_businessman' => '1',
                'terminal_price_type' => '1',
                'terminal_picture_type' => '1',
                'use_inquiry' => '1',
                'use_fixed_phrase' => '0',
                'is_deleted' => '0',
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ],
        ];

        //ダミーデータ挿入

        for ($i = 2; $i <= 999; $i++) {
            $companyCode = sprintf('%03d', $i);

            $data[] = [
                'code' => $companyCode,
                'name' => $faker->company,
                'postcode' => $faker->postcode,
                'address1' => $faker->prefecture,
                'address2' => $faker->city,
                'address3' => $faker->streetAddress,
                'tel_no' => $faker->phoneNumber,
                'fax_no' => '',
                'tanto_name' => $faker->name,
                'tanto_email' => $faker->email,
                'download_id' => '0',
                'download_state' => '0',
                'close_time' => '1700',
                'plan_code' => $faker->randomElement(['99999', '00001', '00002']),
                'is_maintenance' => '0',
                'is_orderzan' => '0',
                'orderzan_emails' => '',
                'is_order_code' => '0',
                'self_name1' => '',
                'self_name2' => '',
                'self_name3' => '',
                'order_type_0' => '通常',
                'order_type_1' => '直送',
                'order_type_2' => '時価',
                'order_type_3' => '受発',
                'order_type_4' => '',
                'order_type_5' => '',
                'order_type_6' => '',
                'order_type_7' => '',
                'order_type_8' => '',
                'order_type_9' => '',
                'customer_code_type' => '1',
                'terminal_order_type' => '1',
                'terminal_tab_type' => '1',
                'use_businessman' => '0',
                'terminal_price_type' => '1',
                'terminal_picture_type' => '1',
                'use_inquiry' => '0',
                'use_fixed_phrase' => '0',
                'is_deleted' => '0',
                'created' => $datetime,
                'created_user' => 'admin',
                'updated' => $datetime,
                'updated_user' => 'admin',
            ];
        }

        $this->execute('SET FOREIGN_KEY_CHECKS = 0');

        $table = $this->table('m_companies');
        $table->insert($data)->save();

        $this->execute('SET FOREIGN_KEY_CHECKS = 1');
    }
}
