<?php
use Migrations\AbstractMigration;

/**
 * 統計情報テーブル
 * ・月ごとの発注数を記録する
 * ・購入数での並べ替えに使用
 * 
 * t_statistics
 */
class CreateTStatistics extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        $table = $this->table('t_statistics', [
            'id' => false, //idを主キーに設定しない設定を追加
            'primary_key' => ['id'],
            'comment' => '統計情報テーブル',
            'collation' => 'utf8mb4_0900_ai_ci',
        ]);

        // 統計情報ID：主キー
        $table->addColumn('id', 'integer', [
            'default' => null,
            'null' => false,
            'identity' => true,
            'signed' => false,
            'comment' => 'ID',
        ]);

        // 企業コード
        $table->addColumn('company_code', 'string', [
            'default' => null,
            'limit' => 3,
            'null' => false,
            'comment' => '企業コード',
        ]);

        $table->addColumn('customer_code', 'string', [
            'default' => '',
            'limit' => 10,
            'null' => false,
            'comment' => '取引先コード',
        ]);

        $table->addColumn('customer_edaban', 'string', [
            'default' => '',
            'limit' => 5,
            'null' => false,
            'comment' => '取引先枝番',
        ]);

        // 端末ID
        $table->addColumn('user_id', 'integer', [
            'default' => null,
            'null' => false,
            'signed' => false,
            'comment' => 'ユーザID',
        ]);

        // 自社管理商品コード
        $table->addColumn('management_product_code', 'string', [
            'default' => null,
            'limit' => 32,
            'null' => false,
            'comment' => '商品コード',
        ]);


        // TODO 計測年月(yyyymm形式)
        $table->addColumn('order_month', 'string', [
            'default' => '',
            'limit' => 6,
            'null' => false,
            'comment' => '計測年月',
        ]);

        // TODO 発注数量
        $table->addColumn('order_num', 'integer', [
            'default' => 0,
            'null' => false,
            'signed' => false,
            'comment' => '発注数量',
        ]);

        $table->addColumn('created', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '作成日',
        ]);
        $table->addColumn('created_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '作成者',
        ]);
        $table->addColumn('updated', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '更新日',
        ]);
        $table->addColumn('updated_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '更新者',
        ]);

        $table->create();
    }
}
