<?php

use Migrations\AbstractMigration;

/**
 * 販売ランク区分マスタテーブル作成
 * m_rank_division
 */
class CreateMRankDivision extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        /**
         * テーブル
         */
        $table = $this->table('m_rank_division', [
            'id' => false,
            'primary_key' => ['rank_category_1', 'rank_category_2'],
            'comment' => '販売ランク区分マスタ',
            'collation' => 'utf8mb4_0900_ai_ci',
        ]);

        ////////////////////////////////////////////////////////
        // カラムの設定
        ////////////////////////////////////////////////////////
        // 大分類：主キー
        $table->addColumn('rank_category_1', 'integer', [
            'default' => 0,
            'null' => false,
            'signed' => false,
            'comment' => '大分類',
        ]);

        // 中分類：主キー
        $table->addColumn('rank_category_2', 'integer', [
            'default' => 0,
            'null' => false,
            'signed' => false,
            'comment' => '中分類',
        ]);

        //分類名
        $table->addColumn('category_1_name', 'string', [
            'limit' => 20,
            'null' => false,
            'comment' => '分類名',
        ]);

        // 品種名
        $table->addColumn('category_2_name', 'string', [
            'limit' => 20,
            'null' => false,
            'comment' => '品種名',
        ]);

        // 販売ランク区分
        $table->addColumn('rank_division', 'integer', [
            'default' => 0,
            'null' => false,
            'signed' => false,
            'comment' => '販売ランク区分',
        ]);

        //作成日
        $table->addColumn('created', 'datetime', [
            'null' => false,
            'comment' => '作成日',
        ]);

        //作成者
        $table->addColumn('created_user', 'string', [
            'limit' => 20,
            'null' => false,
            'comment' => '作成者',
        ]);

        //更新日
        $table->addColumn('updated', 'datetime', [
            'null' => false,
            'comment' => '更新日',
        ]);

        //更新者
        $table->addColumn('updated_user', 'string', [
            'limit' => 20,
            'null' => false,
            'comment' => '更新者',
        ]);

        $table->create();
    }
}
