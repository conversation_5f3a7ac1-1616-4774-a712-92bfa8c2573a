<?php
use Migrations\AbstractMigration;

/**
 * サービス提供者マスタ（BMC）作成
 * m_providers
 */
class CreateMProviders extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        $table = $this->table('m_providers', [
            'id' => false,
            'primary_key' => ['id'],
            'comment' => 'サービス提供者マスタ(サービス提供者（BMC）情報の設定)',
            'collation' => 'utf8mb4_0900_ai_ci',
        ]);

        ////////////////////////////////////////////////////////
        // カラムの設定
        ////////////////////////////////////////////////////////
        // ID：主キー
        $table->addColumn('id', 'integer', [
            'default' => null,
            'null' => false,
            'identity' => true,
            'signed' => false,
            'comment' => 'プロバイダID(1以上の自然数)',
        ]);

        // ログインID：ユニークキー
        $table->addColumn('login_id', 'string', [
            'default' => null,
            'limit' => 15,
            'null' => false,
            'comment' => 'ログインID',
        ]);

        $table->addColumn('password', 'string', [
            'default' => null,
            'null' => false,
            'limit' => 255,
            'comment' => 'パスワード',
        ]);

        $table->addColumn('name', 'string', [
            'default' => '',
            'limit' => 50,
            'null' => false,
            'comment' => 'ユーザ名',
        ]);
        
        $table->addColumn('created', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '作成日',
        ]);
        $table->addColumn('created_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '作成者',
        ]);
        $table->addColumn('updated', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '更新日',
        ]);
        $table->addColumn('updated_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '更新者',
        ]);

        ////////////////////////////////////////////////////////
        // indexの設定
        ////////////////////////////////////////////////////////
        $table->addIndex(['name'], ['name' => 'idx_customer_name']);

        $table->addIndex(['login_id'], ['unique' => true, 'name' => 'idx_login_id']);

        $table->create();
    }
}
