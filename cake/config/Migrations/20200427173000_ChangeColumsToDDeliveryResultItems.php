<?php

use Migrations\AbstractMigration;

/**
 * 取引先マスタテーブル カラム追加
 * m_users
 */
class ChangeColumsToDDeliveryResultItems extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */

	public function change()
    {
        $table = $this->table('d_delivery_result_items');

        $table->changeColumn('invoice_cutoff_date', 'string', [
            'default' => null,
            'limit' => 8,
            'null' => true,
            'comment' => '請求締日(yyyymmdd)',
        ]);
        $table->update();
    }
}
