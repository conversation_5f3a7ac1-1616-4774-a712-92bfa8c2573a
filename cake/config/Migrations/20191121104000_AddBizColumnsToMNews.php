<?php

use Migrations\AbstractMigration;

class AddBizColumnsToMNews extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */
    public function up()
    {
        $table = $this->table('m_news');
        $table->addColumn('is_biz', 'boolean', [
            'default' => 0,
            'null' => false,
            'comment' => 'ログインユーザが営業マンの場合に表示するか否か(0:非表示、1:表示)',
            'after' => 'is_view_admin',
        ]);
        $table->update();
    }

    /**
     * rollback
     */
    public function down()
    {
        $table = $this->table('m_news');
        $table->removeColumn('is_biz');
        $table->update();
    }
}
