<?php
use Migrations\AbstractMigration;

/**
 * 拠点マスタ
 * 拠点情報を保存する
 */
class CreateMDeliveryBase extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        $table = $this->table('m_delivery_base', [
            'id' => false,
            'primary_key' => ['company_code', 'base_id'],
            'comment' => '拠点マスタ(取引先の所属する拠点（倉庫）を設定する)',
            'collation' => 'utf8mb4_0900_ai_ci',
        ]);

        ////////////////////////////////////////////////////////
        // カラムの設定
        ////////////////////////////////////////////////////////
        // 企業コード：主キー
        $table->addColumn('company_code', 'string', [
            'default' => null,
            'limit' => 3,
            'null' => false,
            'comment' => '企業コード',
        ]);

        // 拠点ID：主キー
        $table->addColumn('base_id', 'string', [
            'default' => null,
            'limit' => 3,
            'null' => false,
            'comment' => '拠点ID',
        ]);

        $table->addColumn('name', 'string', [
            'default' => '',
            'limit' => 200,
            'null' => false,
            'comment' => '拠点名',
        ]);

        $table->addColumn('created', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '作成日',
        ]);
        $table->addColumn('created_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '作成者',
        ]);
        $table->addColumn('updated', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '更新日',
        ]);
        $table->addColumn('updated_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '更新者',
        ]);

        ////////////////////////////////////////////////////////
        // indexの設定
        ////////////////////////////////////////////////////////
        $table->addIndex(['base_id'], ['name' => 'idx_base_id']);

        $table->create();
    }
}
