<?php

use Migrations\AbstractMigration;

/**
 * 商品マスタ（取引先別オーダーリスト） カラム追加
 * d_orderlist
 */
class AddColumnsToDOrderlistCase extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */

	public function change()
    {
        $table = $this->table('d_orderlist');

        $table->addColumn('case_price_before', 'decimal', [
            'precision' => 15,
            'scale' => 4,
            'null' => false,
            'default' => '0.00',
            'comment' => 'ケース単価（前）',
            'after' => 'price_after',
        ]);

        $table->addColumn('case_price_after', 'decimal', [
            'precision' => 15,
            'scale' => 4,
            'null' => true,
            'default' => '0.00',
            'comment' => 'ケース単価（後）',
            'after' => 'case_price_before',
        ]);

        $table->addColumn('case_separately', 'integer', [
            'null' => false,
            'default' => '0',
            'comment' => 'ケースバラ区分',
            'after' => 'case_price_after',
        ]);

        $table->update();
    }
}
