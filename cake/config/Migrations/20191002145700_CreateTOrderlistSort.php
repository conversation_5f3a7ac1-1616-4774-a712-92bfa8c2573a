<?php

use Migrations\AbstractMigration;

/**
 * オーダーリストの並び順テーブル作成
 * t_orderlist_ort
 */
class CreateTOrderlistSort extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        /**
         * テーブル
         */
        $table = $this->table('t_orderlist_sort', [
            'id' => false,
            'primary_key' => ['company_code', 'customer_code', 'customer_edaban', 'management_product_code', 'user_id'],
            'comment' => 'オーダーソート(オーダーリストの並び順管理)',
            'collation' => 'utf8mb4_0900_ai_ci',
        ]);

        ////////////////////////////////////////////////////////
        // カラムの設定
        ////////////////////////////////////////////////////////
        // 企業コード：主キー
        $table->addColumn('company_code', 'string', [
            'limit' => 3,
            'null' => false,
            'comment' => '企業コード',
        ]);

        // 取引先コード：主キー
        $table->addColumn('customer_code', 'string', [
            'limit' => 10,
            'null' => false,
            'comment' => '取引先コード',
        ]);

        //取引先枝番：主キー
        $table->addColumn('customer_edaban', 'string', [
            'limit' => 5,
            'null' => false,
            'comment' => '取引先枝番(先頭ゼロ埋め数値)',
        ]);

        // 自社管理商品コード：主キー
        $table->addColumn('management_product_code', 'string', [
            'limit' => 15,
            'null' => false,
            'comment' => '商品コード',
        ]);

        // ユーザーID：主キー
        $table->addColumn('user_id', 'integer', [
            'default' => null,
            'null' => false,
            'signed' => false,
            'comment' => '利用者ID',
        ]);

        $table->addColumn('sort_order_number', 'integer', [
            'default' => null,
            'null' => true,
            'signed' => false,
            'comment' => '並び順番号(小さい番号のものを上に表示させる。同じ値の場合場合は取込順で並び替える（自然数で設定。）)',
        ]);

        $table->addColumn('is_favorite', 'boolean', [
            'default' => false,
            'null' => false,
            'comment' => 'お気に入りフラグ(0: 通常商品、1:お気に入り商品)',
        ]);

        $table->addColumn('is_trash', 'boolean', [
            'default' => false,
            'null' => false,
            'comment' => 'ゴミ箱フラグ(0: 表示、1:非表示)',
        ]);

        $table->addColumn('created', 'datetime', [
            'null' => false,
            'comment' => '作成日',
        ]);
        $table->addColumn('created_user', 'string', [
            'limit' => 20,
            'null' => false,
            'comment' => '作成者',
        ]);
        $table->addColumn('updated', 'datetime', [
            'null' => false,
            'comment' => '更新日',
        ]);
        $table->addColumn('updated_user', 'string', [
            'limit' => 20,
            'null' => false,
            'comment' => '更新者',
        ]);

        ////////////////////////////////////////////////////////
        // indexの設定
        ////////////////////////////////////////////////////////
        $table->addIndex(['is_favorite'], ['name' => 'idx_is_favorite']);
        $table->addIndex(['is_trash'], ['name' => 'is_trash']);

        $table->create();
    }
}
