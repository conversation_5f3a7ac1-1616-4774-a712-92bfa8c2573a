<?php

use Migrations\AbstractMigration;

class CreateViewStatistical extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */
    public function up()
    {
        $sql = "create view v_statistical as"
            . " select company_code,"
            . "        customer_code,"
            . "        customer_edaban,"
            . "        user_id,"
            . "        management_product_code,"
            . "        sum("
            . "                case"
            . "                    when order_month = DATE_FORMAT(curdate(), '%Y%m')"
            . "                        then order_num"
            . "                    else 0"
            . "                    end"
            . "            )          as order_count_1,"
            . "        sum(case"
            . "                when order_month = DATE_FORMAT(ADDDATE(CURDATE(), INTERVAL -1 MONTH), '%Y%m')"
            . "                    then order_num"
            . "                else 0"
            . "            end"
            . "            )          as order_count_2,"
            . "        sum(case"
            . "                when order_month = DATE_FORMAT(ADDDATE(CURDATE(), INTERVAL -2 MONTH), '%Y%m')"
            . "                    then order_num"
            . "                else 0"
            . "            end"
            . "            )          as order_count_3,"
            . "        sum(order_num) as order_count_sum"
            . " from t_statistics"
            . " group by company_code,"
            . "          customer_code,"
            . "          customer_edaban,"
            . "          user_id,"
            . "          management_product_code";

        $this->execute($sql);
    }

    public function down()
    {
        $this->execute('drop view v_statistical;');
    }
}
