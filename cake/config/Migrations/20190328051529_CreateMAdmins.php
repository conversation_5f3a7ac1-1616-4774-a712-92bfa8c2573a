<?php
use Migrations\AbstractMigration;

/**
 * 管理者マスタの作成
 * m_admins
 * 管理画面のログインユーザを設定する
 */
class CreateMAdmins extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        $table = $this->table('m_admins', [
            'id' => false,
            'primary_key' => ['company_code', 'login_id'],
            'comment' => '管理者マスタ(管理画面操作ユーザの管理)',
            'collation' => 'utf8mb4_0900_ai_ci',
        ]);

        ////////////////////////////////////////////////////////
        // カラムの設定
        ////////////////////////////////////////////////////////
        // 企業コード：主キー
        $table->addColumn('company_code', 'string', [
            'default' => null,
            'limit' => 3,
            'null' => false,
            'comment' => '企業コード',
        ]);

        // 管理画面ログインID：主キー
        $table->addColumn('login_id', 'string', [
            'default' => null,
            'limit' => 15,
            'null' => false,
            'comment' => '管理画面ログインID',
        ]);

        $table->addColumn('password', 'string', [
            'default' => '',
            'limit' => 15,
            'null' => false,
            'comment' => 'パスワード',
        ]);
        $table->addColumn('name', 'string', [
            'default' => '',
            'limit' => 50,
            'null' => false,
            'comment' => 'ユーザ名',
        ]);
        $table->addColumn('authority_type', 'integer', [
            'default' => 1,
            'null' => false,
            'comment' => '権限区分(1:一般ユーザー（ﾏｽﾀﾒﾝﾃﾅﾝｽ不可）、9:管理者（ﾏｽﾀﾒﾝﾃﾅﾝｽ可）)',
        ]);


        $table->addColumn('created', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '作成日',
        ]);
        $table->addColumn('created_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '作成者',
        ]);
        $table->addColumn('updated', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '更新日',
        ]);
        $table->addColumn('updated_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '更新者',
        ]);

        ////////////////////////////////////////////////////////
        // indexの設定
        ////////////////////////////////////////////////////////
        $table->addIndex(['name'], ['name' => 'idx_name']);

        ////////////////////////////////////////////////////////
        // 外部キーの設定
        ////////////////////////////////////////////////////////
        /**
         * 企業コード： 更新時：変更、削除時：エラー
         */
        $table->addForeignKey(
            'company_code',
            'm_companies',
            'code',
            [
                'delete' => 'RESTRICT',
                'update' => 'CASCADE'
            ]
        );

        $table->create();
    }
}
