<?php

use Migrations\AbstractMigration;

/**
 * 発注明細テーブル
 * t_order_items
 */
class CreateTOrderItems extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        /**
         * テーブル
         */
        $table = $this->table('t_order_items', [
            'id' => false,
            'primary_key' => ['company_code', 'slip_no', 'slip_detail_no'],
            'comment' => '発注明細テーブル',
            'collation' => 'utf8mb4_0900_ai_ci',
        ]);

        ////////////////////////////////////////////////////////
        // カラムの設定
        ////////////////////////////////////////////////////////
        // 企業コード：主キー
        $table->addColumn('company_code', 'string', [
            'default' => null,
            'limit' => 3,
            'null' => false,
            'comment' => '企業コード',
        ]);

        $table->addColumn('customer_code', 'string', [
            'default' => '',
            'limit' => 10,
            'null' => false,
            'comment' => '取引先コード',
        ]);

        $table->addColumn('customer_edaban', 'string', [
            'default' => '',
            'limit' => 5,
            'null' => false,
            'comment' => '取引先枝番',
        ]);

        $table->addColumn('customer_name', 'string', [
            'limit' => 50,
            'null' => true,
            'default' => '',
            'comment' => '取引先名',
        ]);

        // 伝票番号：主キー
        $table->addColumn('slip_no', 'integer', [
            'default' => 0,
            'null' => false,
            'signed' => false,
            'comment' => '伝票番号',
        ]);

        // 伝票明細番号：主キー
        $table->addColumn('slip_detail_no', 'integer', [
            'default' => 0,
            'null' => false,
            'signed' => false,
            'comment' => '伝票明細番号',
        ]);

        $table->addColumn('management_product_code', 'string', [
            'default' => '',
            'limit' => 32,
            'null' => false,
            'comment' => '商品コード',
        ]);

        $table->addColumn('product_name', 'string', [
            'default' => '',
            'limit' => 100,
            'null' => false,
            'comment' => '商品名',
        ]);

        $table->addColumn('standard', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '規格',
        ]);

        $table->addColumn('in_numbers', 'decimal', [
            'precision' => 15,
            'scale' => 4,
            'null' => false,
            'comment' => '入数',
        ]);

        $table->addColumn('unit_code', 'string', [
            'null' => false,
            'limit' => 5,
            'comment' => '単位コード',
        ]);

        $table->addColumn('unit_name', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '単位名',
        ]);

        $table->addColumn('category_code', 'string', [
            'default' => '',
            'limit' => 15,
            'null' => false,
            'comment' => '分類コード',
        ]);

        $table->addColumn('category_name_1', 'string', [
            'default' => '',
            'limit' => 30,
            'null' => false,
            'comment' => '大分類',
        ]);

        $table->addColumn('category_name_2', 'string', [
            'default' => '',
            'limit' => 30,
            'comment' => '中分類',
        ]);

        $table->addColumn('category_name_3', 'string', [
            'default' => '',
            'limit' => 30,
            'comment' => '小分類',
        ]);

        $table->addColumn('price', 'decimal', [
            'default' => 0,
            'precision' => 10,
            'scale' => 2,
            'null' => false,
            'comment' => '単価',
        ]);

        $table->addColumn('order_num', 'decimal', [
            'default' => 0,
            'precision' => 10,
            'scale' => 2,
            'null' => false,
            'comment' => '数量',
        ]);

        $table->addColumn('order_amount', 'decimal', [
            'default' => 0,
            'precision' => 15,
            'null' => false,
            'comment' => '金額',
        ]);

        $table->addColumn('tax_type', 'integer', [
            'default' => 1,
            'null' => false,
            'comment' => '税区分(1:税抜、2:税込、3:非課税)',
        ]);

        $table->addColumn('tax_kbn', 'integer', [
            'default' => 0,
            'null' => false,
            'comment' => '消費税算出区分',
        ]);

        $table->addColumn('order_division', 'integer', [
            'default' => 0,
            'null' => false,
            'comment' => '受発注区分(0:通常商品、1:受注発注、2:直送)',
        ]);

        $table->addColumn('delivery_date', 'string', [
            'default' => '',
            'limit' => 8,
            'null' => false,
            'comment' => '納品日',
        ]);

        $table->addColumn('is_deleted', 'boolean', [
            'default' => false,
            'null' => false,
            'comment' => '削除フラグ(0: 未削除、1:削除済み)',
        ]);

        $table->addColumn('subtotal', 'integer', [
            'default' => 0,
            'null' => false,
            'signed' => false,
            'comment' => '小計',
        ]);

        $table->addColumn('bikou', 'string', [
            'default' => '',
            'limit' => 512,
            'null' => false,
            'comment' => '伝票明細備考',
        ]);

        $table->addColumn('created', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '作成日',
        ]);
        $table->addColumn('created_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '作成者',
        ]);
        $table->addColumn('updated', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '更新日',
        ]);
        $table->addColumn('updated_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '更新者',
        ]);

        ////////////////////////////////////////////////////////
        // 外部キーの設定
        ////////////////////////////////////////////////////////
        /**
         * 企業コード： 更新時：変更、削除時：エラー
         * 伝票番号： 更新時：変更、削除時：エラー
         */
        $table->addForeignKey(
            ['company_code', 'slip_no'],
            't_order_headers',
            ['company_code', 'slip_no'],
            [
                'delete' => 'RESTRICT',
                'update' => 'CASCADE',
                'constraint' => 'order_headers_id',
            ]
        );

        $table->create();
    }
}
