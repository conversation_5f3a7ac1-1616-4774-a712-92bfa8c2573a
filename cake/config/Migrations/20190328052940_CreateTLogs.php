<?php

use Migrations\AbstractMigration;

/**
 * ログテーブル
 * システム操作ログの保存
 * t_logs
 */
class CreateTLogs extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        $table = $this->table('t_logs', [
            'id' => false,
            'primary_key' => ['id'],
            'comment' => 'ログテーブル(システムログの保存)',
            'collation' => 'utf8mb4_0900_ai_ci',
        ]);

        ////////////////////////////////////////////////////////
        // カラムの設定
        ////////////////////////////////////////////////////////
        // ID：主キー
        $table->addColumn('id', 'integer', [
            'default' => null,
            'null' => false,
            'identity' => true,
            'signed' => false,
            'comment' => 'ID(自然数)',
        ]);

        $table->addColumn('created', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '作成日時',
        ]);

        $table->addColumn('user_type', 'integer', [
            'default' => 9,
            'null' => false,
            'comment' => 'ログ種別(0：システム、1：管理者、2：端末、3：営業マン、9：その他)',
        ]);

        $table->addColumn('log_type', 'integer', [
            'default' => 2,
            'null' => false,
            'comment' => 'ログ区分(0：操作ログ、1：DBエラー、2：その他)',
        ]);

        $table->addColumn('company_code', 'string', [
            'default' => null,
            'limit' => 3,
            'null' => false,
            'comment' => '企業コード',
        ]);

        $table->addColumn('company_name', 'string', [
            'default' => '',
            'limit' => 100,
            'null' => false,
            'comment' => '企業名',
        ]);

        $table->addColumn('customer_code', 'string', [
            'default' => '',
            'limit' => 10,
            'null' => false,
            'comment' => '取引先コード',
        ]);

        $table->addColumn('customer_edaban', 'string', [
            'default' => '',
            'limit' => 5,
            'null' => false,
            'comment' => '取引先枝番',
        ]);

        $table->addColumn('customer_name', 'string', [
            'default' => '',
            'limit' => 50,
            'null' => false,
            'comment' => '取引先名',
        ]);

        $table->addColumn('user_id', 'integer', [
            'default' => 0,
            'null' => false,
            'signed' => false,
            'comment' => '利用者ID',
        ]);

        $table->addColumn('user_name', 'string', [
            'default' => '',
            'limit' => 50,
            'null' => false,
            'comment' => 'ユーザー名',
        ]);

        $table->addColumn('log_text', 'string', [
            'default' => '',
            'limit' => 512,
            'null' => false,
            'comment' => '操作内容',
        ]);

        $table->addColumn('log_no', 'integer', [
            'default' => 0,
            'null' => false,
            'comment' => 'ログ連番(ログイン端末毎の連番)',
        ]);

        // TODO 担当者ID(ビジネスマン機能)
        $table->addColumn('businessman_id', 'integer', [
            'default' => null,
            'null' => false,
            'signed' => false,
            'comment' => '担当者ID(ビジネスマン機能)',
        ]);


        $table->create();
    }
}
