<?php

use Migrations\AbstractMigration;

class AddColumnsToMNews extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */
    public function up()
    {
        $table = $this->table('m_news');
        $table->addColumn('is_provider', 'boolean', [
            'default' => 0,
            'null' => false,
            'comment' => 'プロバイダお知らせか否か(0:プロバイダお知らせではない、1:プロバイダお知らせ)',
            'after' => 'is_view_admin',
        ]);
        $table->update();
    }

    /**
     * rollback
     */
    public function down()
    {
        $table = $this->table('m_news');
        $table->removeColumn('is_provider');
        $table->update();
    }
}
