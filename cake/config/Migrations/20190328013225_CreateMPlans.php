<?php
use Migrations\AbstractMigration;

/**
 * プランマスタ
 * m_plans
 */
class CreateMPlans extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        /**
         * テーブル
         */
        $table = $this->table('m_plans', [
            'id' => false, //idを主キーに設定しない設定を追加
            'primary_key' => ['code'],
            'comment' => 'プランマスタ(プラン情報の管理マスタ)',
            'collation' => 'utf8mb4_0900_ai_ci',
        ]);

      
        ////////////////////////////////////////////////////////
        // カラムの設定
        ////////////////////////////////////////////////////////
        // プランコード：主キー
        $table->addColumn('code', 'string', [
            'default' => null,
            'limit' => 5,
            'null' => false,
            'comment' => 'プランコード',
        ]);

        $table->addColumn('name', 'string', [
            'default' => null,
            'limit' => 100,
            'null' => false,
            'comment' => 'プラン名',
        ]);

        $table->addColumn('max_customers', 'integer', [
            'default' => 0,
            'null' => false,
            'comment' => '最大契約取引先数',
        ]);  
        

        $table->addColumn('created', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '作成日',
        ]);
        $table->addColumn('created_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '作成者',
        ]);
        $table->addColumn('updated', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '更新日',
        ]);
        $table->addColumn('updated_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '更新者',
        ]);



        $table->create();
    }
}
