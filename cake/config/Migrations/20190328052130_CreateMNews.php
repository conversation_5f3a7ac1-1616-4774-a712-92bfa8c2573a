<?php
use Migrations\AbstractMigration;

/**
 * お知らせ情報マスタ
 * m_news
 */
class CreateMNews extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        /**
         * テーブル
         */
        $table = $this->table('m_news', [
            'id' => false, //idを独自に定義
            'primary_key' => ['id'],
            'comment' => 'お知らせ情報マスタ',
            'collation' => 'utf8mb4_0900_ai_ci',
        ]);

        ////////////////////////////////////////////////////////
        // カラムの設定
        ////////////////////////////////////////////////////////
        // お知らせID：主キー
        $table->addColumn('id', 'integer', [
            'default' => null,
            'null' => false,
            'identity' => true,
            'signed' => false,
            'comment' => 'お知らせID',
        ]);

        $table->addColumn('company_code', 'string', [
            'default' => null,
            'limit' => 3,
            'null' => false,
            'comment' => '企業コード(ZZZの場合はプロバイダお知らせ)',
        ]);

        $table->addColumn('title', 'string', [
            'default' => '',
            'limit' => 50,
            'null' => false,
            'comment' => 'タイトル',
        ]);

        $table->addColumn('text', 'string', [
            'default' => '',
            'limit' => 1024,
            'null' => false,
            'comment' => 'お知らせ内容',
        ]);

        $table->addColumn('url', 'string', [
            'default' => '',
            'limit' => 1024,
            'null' => false,
            'comment' => 'リンク先URL',
        ]);

        $table->addColumn('category', 'integer', [
            'default' => 0,
            'null' => false,
            'comment' => 'カテゴリ(例 0:お知らせ、1:イベント、2:重要、3:メンテナンス)',
        ]);

        $table->addColumn('set_date', 'date', [
            'default' => null,
            'null' => false,
            'comment' => '記載日付(お知らせ一覧に表示される日付)',
        ]);

        $table->addColumn('date_from', 'date', [
            'default' => null,
            'null' => true,
            'comment' => '掲載期間From',
        ]);

        $table->addColumn('date_to', 'date', [
            'default' => null,
            'null' => true,
            'comment' => '掲載期間To(永続的に表示させたい場合は2100/01/01などを設定)',
        ]);

        $table->addColumn('is_visible', 'boolean', [
            'default' => 1,
            'null' => false,
            'comment' => '表示フラグ(0:非表示、1:表示)',
        ]);

        $table->addColumn('is_login', 'boolean', [
            'default' => 0,
            'null' => false,
            'comment' => 'ログイン画面表示(0:未設定、1:ログイン画面に表示)',
        ]);
        $table->addColumn('is_popup', 'boolean', [
            'default' => 0,
            'null' => false,
            'comment' => 'ポップアップ表示(0:未設定、1:ポップアップ表示させる)',
        ]);

        $table->addColumn('is_view_user', 'boolean', [
            'default' => 1,
            'null' => false,
            'comment' => '発注画面での表示(0:非表示、1:表示)',
        ]);
        $table->addColumn('is_view_admin', 'boolean', [
            'default' => 0,
            'null' => false,
            'comment' => '管理画面での表示(0:非表示、1:表示)',
        ]);
        
        $table->addColumn('priority', 'integer', [
            'default' => 10,
            'null' => false,
            'comment' => '優先度(掲載日が同じ場合、数値の小さいものが上に掲載されます)',
        ]);


        $table->addColumn('created', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '作成日',
        ]);
        $table->addColumn('created_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '作成者',
        ]);
        $table->addColumn('updated', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '更新日',
        ]);
        $table->addColumn('updated_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '更新者',
        ]);



        $table->create();
    }
}
