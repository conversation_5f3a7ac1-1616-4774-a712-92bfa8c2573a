
<?php
use Migrations\AbstractMigration;

/**
 * m_companies - m_customers 中間テーブルの作成
 */
class CreateCompanyCustomer extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        $table = $this->table('company_customer', [
            'id' => false, 
            'primary_key' => ['company_code', 'customer_id'],
            'comment' => 'm_companies - m_customers(中間テーブル)',
            'collation' => 'utf8mb4_0900_ai_ci',
        ]);

        // 企業コード：主キー
        $table->addColumn('company_code', 'string', [
            'default' => null,
            'limit' => 3,
            'null' => false,
            'comment' => '企業コード',
        ]);
        
        // 取引先ID：主キー
        $table->addColumn('customer_id', 'integer', [
            'default' => null,
            'null' => false,
            'signed' => false,
            'comment' => '取引先ID',
        ]);

        ////////////////////////////////////////////////////////
        // 外部キーの設定
        ////////////////////////////////////////////////////////
        /**
         * 企業コード： 更新時：変更、削除時：エラー
         * 取引先ID 更新時：変更、削除時：エラー
         */
        $table->addForeignKey(
            'company_code',
            'm_companies',
            'code',
            [
                'delete' => 'RESTRICT',
                'update' => 'CASCADE'
            ]
        );
        $table->addForeignKey(
            'customer_id',
            'm_customers',
            'id',
            [
                'delete' => 'RESTRICT',
                'update' => 'CASCADE'
            ]
        );

        $table->create();
    }
}
