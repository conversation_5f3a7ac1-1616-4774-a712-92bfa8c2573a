<?php
use Migrations\AbstractMigration;

/**
 * 拠点ごとの商品情報テーブル
 */
class CreateCProductInfoByBase extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        $table = $this->table('c_product_info_by_base', [
            'id' => false,
            'primary_key' => ['company_code','base_id','product_code'],
            'comment' => 'カタログ掲載用の拠点別商品データ',
            'collation' => 'utf8mb4_0900_ai_ci',
        ]);

        ////////////////////////////////////////////////////////
        // カラムの設定
        ////////////////////////////////////////////////////////
        // 企業コード：主キー
        $table->addColumn('company_code', 'string', [
            'limit' => 3,
            'null' => false,
            'comment' => '企業コード',
        ]);

        // 拠点ID：主キー
        $table->addColumn('base_id', 'string', [
            'default' => null,
            'limit' => 3,
            'null' => false,
            'comment' => '拠点ID',
        ]);

        // 商品コード：主キー
        $table->addColumn('product_code', 'string', [
            'limit' => 15,
            'null' => false,
            'comment' => '商品コード',
        ]);

        $table->addColumn('order_division', 'integer', [
            'null' => false,
            'comment' => '受発注区分(0:通常商品、1:受注発注、2:直送など、企業マスタの設定値を表示する)',
        ]);

        $table->addColumn('lead_time', 'integer', [
            'null' => false,
            'comment' => '納品リードタイム(カタログで拠点が存在すればこの値を利用する)',
        ]);

        $table->addColumn('price_switch_day', 'date', [
            'null' => true,
            'default' => null,
            'comment' => '単価切替日:未使用の場合NULL',
        ]);

        $table->addColumn('price_before', 'decimal', [
            'precision' => 15,
            'scale' => 4,
            'null' => true,
            'default' => null,
            'comment' => '単価（前）:未使用の場合NULL',
        ]);

        $table->addColumn('price_after', 'decimal', [
            'precision' => 15,
            'scale' => 4,
            'null' => true,
            'default' => null,
            'comment' => '単価（後）:未使用の場合NULL',
        ]);

        $table->addColumn('is_visible', 'boolean', [
            'default' => true,
            'null' => false,
            'comment' => 'カタログで利用するか(1:利用、0:利用しない)',
        ]);

        $table->addColumn('created', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '作成日',
        ]);
        $table->addColumn('created_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '作成者',
        ]);
        $table->addColumn('updated', 'datetime', [
            'default' => null,
            'null' => false,
            'comment' => '更新日',
        ]);
        $table->addColumn('updated_user', 'string', [
            'default' => '',
            'limit' => 20,
            'null' => false,
            'comment' => '更新者',
        ]);

        ////////////////////////////////////////////////////////
        // indexの設定
        ////////////////////////////////////////////////////////
        $table->addIndex(['product_code'], ['name' => 'idx_product_code']);
        $table->addIndex(['base_id'], ['name' => 'idx_base_id']);

        $table->create();
    }
}
