#!/usr/bin/env bash
# Used as a default to seed config/.env which
# enables you to use environment variables to configure
# the aspects of your application that vary by
# environment.
#
# Having this file in production is considered a **SECURITY RISK** and also decreases
# the boostrap performance of your application.
#
# To use this file, first copy it into `config/.env`. Also ensure the related
# code block for loading this file is uncommented in `config/boostrap.php`
#
# In development .env files are parsed by PHP
# and set into the environment. This provides a simpler
# development workflow over standard environment variables.
export APP_NAME="ACT_NETORDER_TEST"
export DEBUG=true
export DEBUG_SQL_LOG=true
export APP_ENCODING="UTF-8"
export APP_DEFAULT_LOCALE="ja_JP"
export APP_DEFAULT_TIMEZONE="Asia/Tokyo"
export SECURITY_SALT="9e34c367eea4cb779e7f273bc9f209fb92e580c946fbc84ac1b357aaa355da64"

export DB_HOST="***************"
export DB_SCHEMA="act_smart_order_test"
export DB_USERNAME="bmc_admin"
export DB_PASSWORD="Qq!DLOx_4"

# リリースバージョン(例：1とした場合はバージョン2以降の機能は使用できない)
export APP_VERSION="1"

# データ保存先ディレクトリ
# 下記ディレクトリの下に企業コードディレクトリを作成し、その配下に保存
export DIR_IMAGES="/var/www/html/act-test/smart_order/cake/webroot/img/" # 商品画像
export DIR_PRODUCT_CSV="/var/www/html/act-test/uploads/orderlist/" # オーダーリスト
export DIR_DELIVERY_CSV="/var/www/html/act-test/uploads/delivery/" # 納品ヘッダ
export DIR_BUSINESSMAN_CSV="/var/www/html/act-test/uploads/businessman/" # 営業マン

export DIR_DL_DELIVERY_PDF="files/pdf/delivery_pdf/" # 請求書PDF
export DIR_DLDATA_CSV="files/csv/download/" # 発注データのcsv保存先
export CATALOG_DIR="/var/www/html/catalog/" # カタログサイトのホームディレクトリ