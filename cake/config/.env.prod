#!/usr/bin/env bash
# Used as a default to seed config/.env which
# enables you to use environment variables to configure
# the aspects of your application that vary by
# environment.
#
# Having this file in production is considered a **SECURITY RISK** and also decreases
# the boostrap performance of your application.
#
# To use this file, first copy it into `config/.env`. Also ensure the related
# code block for loading this file is uncommented in `config/boostrap.php`
#
# In development .env files are parsed by PHP
# and set into the environment. This provides a simpler
# development workflow over standard environment variables.
export APP_NAME="ACT_NETORDER"
export DEBUG=false
export DEBUG_SQL_LOG=false
export APP_ENCODING="UTF-8"
export APP_DEFAULT_LOCALE="ja_JP"
export APP_DEFAULT_TIMEZONE="Asia/Tokyo"
export SECURITY_SALT="9e34c367eea4cb779e7f273bc9f209fb92e580c946fbc84ac1b357aaa355da64"

export DB_HOST="smart-order-database.cdeq2yae8x5o.ap-northeast-1.rds.amazonaws.com"
export DB_SCHEMA="act_smart_order"
export DB_USERNAME="admin"
export DB_PASSWORD="smart-order"

# リリースバージョン(例：1とした場合はバージョン2以降の機能は使用できない)
export APP_VERSION="2"

# データ保存先ディレクトリ
# 下記ディレクトリの下に企業コードディレクトリを作成し、その配下に保存
export DIR_IMAGES="/var/www/html/act/smart_order/cake/webroot/img/" # 商品画像
export DIR_PRODUCT_CSV="/var/www/html/act/uploads/orderlist/" # オーダーリスト
export DIR_DELIVERY_CSV="/var/www/html/act/uploads/delivery/" # 納品ヘッダ
export DIR_BUSINESSMAN_CSV="/var/www/html/act/uploads/businessman/" # 営業マン
export DIR_CATALOG_DATA_CSV="/var/www/html/act/uploads/catalog_upload/" # カタログ商品データ
export DIR_RANK_CSV="/var/www/html/act/uploads/rank/" # 取引先販売ランク情報

export DIR_DL_DELIVERY_PDF="files/pdf/delivery_pdf/" # 請求書PDF
export DIR_DLDATA_CSV="files/csv/download/" # 発注データのcsv保存先

# カタログサイトとの連携のため、カタログサイトのドメインを設定する
export CATALOG_URL="https://act.smart-order.jp/catalog" # カタログサイトのURL（
export CATALOG_LOGIN_PAGE="/catalog/login" # カタログサイトの連携用ログインページ名
export CATALOG_DIR="/var/www/html/act/catalog/" # カタログサイトのホームディレクトリ