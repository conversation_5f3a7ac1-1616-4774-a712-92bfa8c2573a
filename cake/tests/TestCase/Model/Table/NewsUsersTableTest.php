<?php
namespace App\Test\TestCase\Model\Table;

use App\Model\Table\NewsUsersTable;
use Cake\ORM\TableRegistry;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\NewsUsersTable Test Case
 */
class NewsUsersTableTest extends TestCase
{

    /**
     * Test subject
     *
     * @var \App\Model\Table\NewsUsersTable
     */
    public $NewsUsers;

    /**
     * Fixtures
     *
     * @var array
     */
    public $fixtures = [
        'app.NewsUsers',
        'app.MNews',
        'app.MUsers'
    ];

    /**
     * setUp method
     *
     * @return void
     */
    public function setUp()
    {
        parent::setUp();
        $config = TableRegistry::getTableLocator()->exists('NewsUsers') ? [] : ['className' => NewsUsersTable::class];
        $this->NewsUsers = TableRegistry::getTableLocator()->get('NewsUsers', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    public function tearDown()
    {
        unset($this->NewsUsers);

        parent::tearDown();
    }

    /**
     * Test initialize method
     *
     * @return void
     */
    public function testInitialize()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test buildRules method
     *
     * @return void
     */
    public function testBuildRules()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
