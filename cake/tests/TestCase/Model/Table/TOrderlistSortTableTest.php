<?php
namespace App\Test\TestCase\Model\Table;

use App\Model\Table\TOrderlistSortTable;
use Cake\ORM\TableRegistry;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\TOrderlistSortTable Test Case
 */
class TOrderlistSortTableTest extends TestCase
{

    /**
     * Test subject
     *
     * @var \App\Model\Table\TOrderlistSortTable
     */
    public $TOrderlistSort;

    /**
     * Fixtures
     *
     * @var array
     */
    public $fixtures = [
        'app.TOrderlistSort',
        'app.Users'
    ];

    /**
     * setUp method
     *
     * @return void
     */
    public function setUp()
    {
        parent::setUp();
        $config = TableRegistry::getTableLocator()->exists('TOrderlistSort') ? [] : ['className' => TOrderlistSortTable::class];
        $this->TOrderlistSort = TableRegistry::getTableLocator()->get('TOrderlistSort', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    public function tearDown()
    {
        unset($this->TOrderlistSort);

        parent::tearDown();
    }

    /**
     * Test initialize method
     *
     * @return void
     */
    public function testInitialize()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test validationDefault method
     *
     * @return void
     */
    public function testValidationDefault()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test buildRules method
     *
     * @return void
     */
    public function testBuildRules()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
