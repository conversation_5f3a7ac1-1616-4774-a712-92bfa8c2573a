<?php
namespace App\Test\TestCase\Model\Table;

use App\Model\Table\MBusinessmenTable;
use Cake\ORM\TableRegistry;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\MBusinessmenTable Test Case
 */
class MBusinessmenTableTest extends TestCase
{

    /**
     * Test subject
     *
     * @var \App\Model\Table\MBusinessmenTable
     */
    public $MBusinessmen;

    /**
     * Fixtures
     *
     * @var array
     */
    public $fixtures = [
        'app.MBusinessmen',
        'app.Logins'
    ];

    /**
     * setUp method
     *
     * @return void
     */
    public function setUp()
    {
        parent::setUp();
        $config = TableRegistry::getTableLocator()->exists('MBusinessmen') ? [] : ['className' => MBusinessmenTable::class];
        $this->MBusinessmen = TableRegistry::getTableLocator()->get('MBusinessmen', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    public function tearDown()
    {
        unset($this->MBusinessmen);

        parent::tearDown();
    }

    /**
     * Test initialize method
     *
     * @return void
     */
    public function testInitialize()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test validationDefault method
     *
     * @return void
     */
    public function testValidationDefault()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test buildRules method
     *
     * @return void
     */
    public function testBuildRules()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
