<?php
namespace App\Test\TestCase\Model\Table;

use App\Model\Table\MHolidayTable;
use Cake\ORM\TableRegistry;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\MHolidayTable Test Case
 */
class MHolidayTableTest extends TestCase
{

    /**
     * Test subject
     *
     * @var \App\Model\Table\MHolidayTable
     */
    public $MHoliday;

    /**
     * Fixtures
     *
     * @var array
     */
    public $fixtures = [
        'app.MHoliday'
    ];

    /**
     * setUp method
     *
     * @return void
     */
    public function setUp()
    {
        parent::setUp();
        $config = TableRegistry::getTableLocator()->exists('MHoliday') ? [] : ['className' => MHolidayTable::class];
        $this->MHoliday = TableRegistry::getTableLocator()->get('MHoliday', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    public function tearDown()
    {
        unset($this->MHoliday);

        parent::tearDown();
    }

    /**
     * Test initialize method
     *
     * @return void
     */
    public function testInitialize()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test validationDefault method
     *
     * @return void
     */
    public function testValidationDefault()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
