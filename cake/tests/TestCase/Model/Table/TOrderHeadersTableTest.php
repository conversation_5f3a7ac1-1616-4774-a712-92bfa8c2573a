<?php
namespace App\Test\TestCase\Model\Table;

use App\Model\Table\TOrderHeadersTable;
use Cake\ORM\TableRegistry;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\TOrderHeadersTable Test Case
 */
class TOrderHeadersTableTest extends TestCase
{

    /**
     * Test subject
     *
     * @var \App\Model\Table\TOrderHeadersTable
     */
    public $TOrderHeaders;

    /**
     * Fixtures
     *
     * @var array
     */
    public $fixtures = [
        'app.TOrderHeaders',
        'app.Customers',
        'app.Users',
        'app.Businessmen'
    ];

    /**
     * setUp method
     *
     * @return void
     */
    public function setUp()
    {
        parent::setUp();
        $config = TableRegistry::getTableLocator()->exists('TOrderHeaders') ? [] : ['className' => TOrderHeadersTable::class];
        $this->TOrderHeaders = TableRegistry::getTableLocator()->get('TOrderHeaders', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    public function tearDown()
    {
        unset($this->TOrderHeaders);

        parent::tearDown();
    }

    /**
     * Test initialize method
     *
     * @return void
     */
    public function testInitialize()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test validationDefault method
     *
     * @return void
     */
    public function testValidationDefault()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test buildRules method
     *
     * @return void
     */
    public function testBuildRules()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
