<?php
namespace App\Test\TestCase\Model\Table;

use App\Model\Table\MProvidersTable;
use Cake\ORM\TableRegistry;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\MProvidersTable Test Case
 */
class MProvidersTableTest extends TestCase
{

    /**
     * Test subject
     *
     * @var \App\Model\Table\MProvidersTable
     */
    public $MProviders;

    /**
     * Fixtures
     *
     * @var array
     */
    public $fixtures = [
        'app.MProviders'
    ];

    /**
     * setUp method
     *
     * @return void
     */
    public function setUp()
    {
        parent::setUp();
        $config = TableRegistry::getTableLocator()->exists('MProviders') ? [] : ['className' => MProvidersTable::class];
        $this->MProviders = TableRegistry::getTableLocator()->get('MProviders', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    public function tearDown()
    {
        unset($this->MProviders);

        parent::tearDown();
    }

    /**
     * Test initialize method
     *
     * @return void
     */
    public function testInitialize()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test validationDefault method
     *
     * @return void
     */
    public function testValidationDefault()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
