<?php
namespace App\Test\TestCase\Model\Table;

use App\Model\Table\CProductListTable;
use Cake\ORM\TableRegistry;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\CProductListTable Test Case
 */
class CProductListTableTest extends TestCase
{
    /**
     * Test subject
     *
     * @var \App\Model\Table\CProductListTable
     */
    public $CProductList;

    /**
     * Fixtures
     *
     * @var array
     */
    public $fixtures = [
        'app.CProductList'
    ];

    /**
     * setUp method
     *
     * @return void
     */
    public function setUp()
    {
        parent::setUp();
        $config = TableRegistry::getTableLocator()->exists('CProductList') ? [] : ['className' => CProductListTable::class];
        $this->CProductList = TableRegistry::getTableLocator()->get('CProductList', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    public function tearDown()
    {
        unset($this->CProductList);

        parent::tearDown();
    }

    /**
     * Test initialize method
     *
     * @return void
     */
    public function testInitialize()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test validationDefault method
     *
     * @return void
     */
    public function testValidationDefault()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
