<?php
namespace App\Test\TestCase\Model\Table;

use App\Model\Table\DOrderlistTable;
use Cake\ORM\TableRegistry;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\DOrderlistTable Test Case
 */
class DOrderlistTableTest extends TestCase
{

    /**
     * Test subject
     *
     * @var \App\Model\Table\DOrderlistTable
     */
    public $DOrderlist;

    /**
     * Fixtures
     *
     * @var array
     */
    public $fixtures = [
        'app.DOrderlist',
        'app.Customers'
    ];

    /**
     * setUp method
     *
     * @return void
     */
    public function setUp()
    {
        parent::setUp();
        $config = TableRegistry::getTableLocator()->exists('DOrderlist') ? [] : ['className' => DOrderlistTable::class];
        $this->DOrderlist = TableRegistry::getTableLocator()->get('DOrderlist', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    public function tearDown()
    {
        unset($this->DOrderlist);

        parent::tearDown();
    }

    /**
     * Test initialize method
     *
     * @return void
     */
    public function testInitialize()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test validationDefault method
     *
     * @return void
     */
    public function testValidationDefault()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test buildRules method
     *
     * @return void
     */
    public function testBuildRules()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
