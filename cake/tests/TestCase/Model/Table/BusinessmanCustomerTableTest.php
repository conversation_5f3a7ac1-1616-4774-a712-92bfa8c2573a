<?php
namespace App\Test\TestCase\Model\Table;

use App\Model\Table\BusinessmanCustomerTable;
use Cake\ORM\TableRegistry;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\BusinessmanCustomerTable Test Case
 */
class BusinessmanCustomerTableTest extends TestCase
{

    /**
     * Test subject
     *
     * @var \App\Model\Table\BusinessmanCustomerTable
     */
    public $BusinessmanCustomer;

    /**
     * Fixtures
     *
     * @var array
     */
    public $fixtures = [
        'app.BusinessmanCustomer',
        'app.MCustomers',
        'app.MBusinessmen'
    ];

    /**
     * setUp method
     *
     * @return void
     */
    public function setUp()
    {
        parent::setUp();
        $config = TableRegistry::getTableLocator()->exists('BusinessmanCustomer') ? [] : ['className' => BusinessmanCustomerTable::class];
        $this->BusinessmanCustomer = TableRegistry::getTableLocator()->get('BusinessmanCustomer', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    public function tearDown()
    {
        unset($this->BusinessmanCustomer);

        parent::tearDown();
    }

    /**
     * Test initialize method
     *
     * @return void
     */
    public function testInitialize()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test buildRules method
     *
     * @return void
     */
    public function testBuildRules()
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
