<?php
namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;

/**
 * MAdminsFixture
 *
 */
class MAdminsFixture extends TestFixture
{

    /**
     * Fields
     *
     * @var array
     */
    // @codingStandardsIgnoreStart
    public $fields = [
        'company_code' => ['type' => 'string', 'length' => 3, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '企業コード', 'precision' => null, 'fixed' => null],
        'login_id' => ['type' => 'string', 'length' => 15, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '管理画面ログインID', 'precision' => null, 'fixed' => null],
        'password' => ['type' => 'string', 'length' => 15, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => 'パスワード', 'precision' => null, 'fixed' => null],
        'name' => ['type' => 'string', 'length' => 50, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => 'ユーザ名', 'precision' => null, 'fixed' => null],
        'authority_type' => ['type' => 'integer', 'length' => 11, 'unsigned' => false, 'null' => false, 'default' => '1', 'comment' => '権限区分(1:一般ユーザー（ﾏｽﾀﾒﾝﾃﾅﾝｽ不可）、9:管理者（ﾏｽﾀﾒﾝﾃﾅﾝｽ可）)', 'precision' => null, 'autoIncrement' => null],
        'created' => ['type' => 'datetime', 'length' => null, 'null' => false, 'default' => null, 'comment' => '作成日', 'precision' => null],
        'created_user' => ['type' => 'string', 'length' => 20, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '作成者', 'precision' => null, 'fixed' => null],
        'updated' => ['type' => 'datetime', 'length' => null, 'null' => false, 'default' => null, 'comment' => '更新日', 'precision' => null],
        'updated_user' => ['type' => 'string', 'length' => 20, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '更新者', 'precision' => null, 'fixed' => null],
        '_indexes' => [
            'idx_name' => ['type' => 'index', 'columns' => ['name'], 'length' => []],
        ],
        '_constraints' => [
            'primary' => ['type' => 'primary', 'columns' => ['company_code', 'login_id'], 'length' => []],
            'm_admins_ibfk_1' => ['type' => 'foreign', 'columns' => ['company_code'], 'references' => ['m_companies', 'code'], 'update' => 'cascade', 'delete' => 'restrict', 'length' => []],
        ],
        '_options' => [
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_ja_0900_as_cs'
        ],
    ];
    // @codingStandardsIgnoreEnd

    /**
     * Init method
     *
     * @return void
     */
    public function init()
    {
        $this->records = [
            [
                'company_code' => '088f549b-f869-4182-9554-63d6d7135d6f',
                'login_id' => '9ab77353-ce69-4710-8163-8ab115a3d390',
                'password' => 'Lorem ipsum d',
                'name' => 'Lorem ipsum dolor sit amet',
                'authority_type' => 1,
                'created' => '2019-05-31 16:04:39',
                'created_user' => 'Lorem ipsum dolor ',
                'updated' => '2019-05-31 16:04:39',
                'updated_user' => 'Lorem ipsum dolor '
            ],
        ];
        parent::init();
    }
}
