<?php
namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;

/**
 * TOrderItemsFixture
 *
 */
class TOrderItemsFixture extends TestFixture
{

    /**
     * Fields
     *
     * @var array
     */
    // @codingStandardsIgnoreStart
    public $fields = [
        'company_code' => ['type' => 'string', 'length' => 3, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '企業コード', 'precision' => null, 'fixed' => null],
        'slip_no' => ['type' => 'integer', 'length' => 11, 'unsigned' => true, 'null' => false, 'default' => '0', 'comment' => '伝票番号', 'precision' => null, 'autoIncrement' => null],
        'slip_detail_no' => ['type' => 'integer', 'length' => 11, 'unsigned' => true, 'null' => false, 'default' => '0', 'comment' => '伝票明細番号', 'precision' => null, 'autoIncrement' => null],
        'management_product_code' => ['type' => 'string', 'length' => 32, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '商品コード', 'precision' => null, 'fixed' => null],
        'product_name' => ['type' => 'string', 'length' => 100, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '商品名', 'precision' => null, 'fixed' => null],
        'standard' => ['type' => 'string', 'length' => 20, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '規格', 'precision' => null, 'fixed' => null],
        'in_numbers' => ['type' => 'decimal', 'length' => 15, 'precision' => 4, 'unsigned' => false, 'null' => false, 'default' => '0.0000', 'comment' => '入数'],
        'unit_name' => ['type' => 'string', 'length' => 20, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '単位名', 'precision' => null, 'fixed' => null],
        'price' => ['type' => 'decimal', 'length' => 10, 'precision' => 2, 'unsigned' => false, 'null' => false, 'default' => '0.00', 'comment' => '単価'],
        'order_num' => ['type' => 'decimal', 'length' => 10, 'precision' => 2, 'unsigned' => false, 'null' => false, 'default' => '0.00', 'comment' => '数量'],
        'delivery_date' => ['type' => 'string', 'length' => 8, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '納品日', 'precision' => null, 'fixed' => null],
        'is_deleted' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => '0', 'comment' => '削除フラグ(0: 未削除、1:削除済み)', 'precision' => null],
        'created' => ['type' => 'datetime', 'length' => null, 'null' => false, 'default' => null, 'comment' => '作成日', 'precision' => null],
        'created_user' => ['type' => 'string', 'length' => 20, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '作成者', 'precision' => null, 'fixed' => null],
        'updated' => ['type' => 'datetime', 'length' => null, 'null' => false, 'default' => null, 'comment' => '更新日', 'precision' => null],
        'updated_user' => ['type' => 'string', 'length' => 20, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '更新者', 'precision' => null, 'fixed' => null],
        '_constraints' => [
            'primary' => ['type' => 'primary', 'columns' => ['company_code', 'slip_no', 'slip_detail_no'], 'length' => []],
            'order_headers_id' => ['type' => 'foreign', 'columns' => ['company_code', 'slip_no'], 'references' => ['t_order_headers', '1' => ['company_code', 'slip_no']], 'update' => 'cascade', 'delete' => 'restrict', 'length' => []],
        ],
        '_options' => [
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_ja_0900_as_cs'
        ],
    ];
    // @codingStandardsIgnoreEnd

    /**
     * Init method
     *
     * @return void
     */
    public function init()
    {
        $this->records = [
            [
                'company_code' => '4067ee5d-f92b-4d5d-8276-0aac9996e953',
                'slip_no' => 1,
                'slip_detail_no' => 1,
                'management_product_code' => 'Lorem ipsum dolor sit amet',
                'product_name' => 'Lorem ipsum dolor sit amet',
                'standard' => 'Lorem ipsum dolor ',
                'in_numbers' => 1.5,
                'unit_name' => 'Lorem ipsum dolor ',
                'price' => 1.5,
                'order_num' => 1.5,
                'delivery_date' => 'Lorem ',
                'is_deleted' => 1,
                'created' => '2019-05-31 16:04:42',
                'created_user' => 'Lorem ipsum dolor ',
                'updated' => '2019-05-31 16:04:42',
                'updated_user' => 'Lorem ipsum dolor '
            ],
        ];
        parent::init();
    }
}
