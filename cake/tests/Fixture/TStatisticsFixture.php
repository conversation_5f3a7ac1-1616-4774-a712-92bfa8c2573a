<?php
namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;

/**
 * TStatisticsFixture
 *
 */
class TStatisticsFixture extends TestFixture
{

    /**
     * Fields
     *
     * @var array
     */
    // @codingStandardsIgnoreStart
    public $fields = [
        'company_code' => ['type' => 'string', 'length' => 3, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '企業コード', 'precision' => null, 'fixed' => null],
        'user_id' => ['type' => 'integer', 'length' => 11, 'unsigned' => true, 'null' => false, 'default' => null, 'comment' => '端末ID', 'precision' => null, 'autoIncrement' => null],
        'management_product_code' => ['type' => 'string', 'length' => 32, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '商品コード', 'precision' => null, 'fixed' => null],
        'order_month' => ['type' => 'string', 'length' => 6, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '計測年月', 'precision' => null, 'fixed' => null],
        'order_num' => ['type' => 'integer', 'length' => 11, 'unsigned' => true, 'null' => false, 'default' => '0', 'comment' => '発注数量', 'precision' => null, 'autoIncrement' => null],
        'created' => ['type' => 'datetime', 'length' => null, 'null' => false, 'default' => null, 'comment' => '作成日', 'precision' => null],
        'created_user' => ['type' => 'string', 'length' => 20, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '作成者', 'precision' => null, 'fixed' => null],
        'updated' => ['type' => 'datetime', 'length' => null, 'null' => false, 'default' => null, 'comment' => '更新日', 'precision' => null],
        'updated_user' => ['type' => 'string', 'length' => 20, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '更新者', 'precision' => null, 'fixed' => null],
        '_constraints' => [
            'primary' => ['type' => 'primary', 'columns' => ['company_code', 'customer_id', 'user_id', 'management_product_code', 'created'], 'length' => []],
        ],
        '_options' => [
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_ja_0900_as_cs'
        ],
    ];
    // @codingStandardsIgnoreEnd

    /**
     * Init method
     *
     * @return void
     */
    public function init()
    {
        $this->records = [
            [
                'company_code' => '522fb927-27d4-4b10-b53f-17d8f89f9c45',
                'user_id' => 1,
                'management_product_code' => '9db81a10-45d4-4b47-afd8-0802d5f89690',
                'order_month' => 'Lore',
                'order_num' => 1,
                'created' => '2019-05-31 16:04:42',
                'created_user' => 'Lorem ipsum dolor ',
                'updated' => '2019-05-31 16:04:42',
                'updated_user' => 'Lorem ipsum dolor '
            ],
        ];
        parent::init();
    }
}
