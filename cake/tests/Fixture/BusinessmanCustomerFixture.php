<?php
namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;

/**
 * BusinessmanCustomerFixture
 *
 */
class BusinessmanCustomerFixture extends TestFixture
{

    /**
     * Table name
     *
     * @var string
     */
    public $table = 'businessman_customer';

    /**
     * Fields
     *
     * @var array
     */
    // @codingStandardsIgnoreStart
    public $fields = [
        'customer_id' => ['type' => 'integer', 'length' => 11, 'unsigned' => true, 'null' => false, 'default' => null, 'comment' => '取引先ID', 'precision' => null, 'autoIncrement' => null],
        'businessman_id' => ['type' => 'integer', 'length' => 11, 'unsigned' => true, 'null' => false, 'default' => null, 'comment' => '営業担当者ID(営業マンマスタ)', 'precision' => null, 'autoIncrement' => null],
        '_indexes' => [
            'businessman_id' => ['type' => 'index', 'columns' => ['businessman_id'], 'length' => []],
        ],
        '_constraints' => [
            'primary' => ['type' => 'primary', 'columns' => ['customer_id', 'businessman_id'], 'length' => []],
            'businessman_customer_ibfk_1' => ['type' => 'foreign', 'columns' => ['customer_id'], 'references' => ['m_customers', 'id'], 'update' => 'cascade', 'delete' => 'restrict', 'length' => []],
            'businessman_customer_ibfk_2' => ['type' => 'foreign', 'columns' => ['businessman_id'], 'references' => ['m_businessmen', 'id'], 'update' => 'cascade', 'delete' => 'cascade', 'length' => []],
        ],
        '_options' => [
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_ja_0900_as_cs'
        ],
    ];
    // @codingStandardsIgnoreEnd

    /**
     * Init method
     *
     * @return void
     */
    public function init()
    {
        $this->records = [
            [
                'customer_id' => 1,
                'businessman_id' => 1
            ],
        ];
        parent::init();
    }
}
