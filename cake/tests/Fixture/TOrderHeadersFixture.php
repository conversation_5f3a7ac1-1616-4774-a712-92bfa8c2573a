<?php
namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;

/**
 * TOrderHeadersFixture
 *
 */
class TOrderHeadersFixture extends TestFixture
{

    /**
     * Fields
     *
     * @var array
     */
    // @codingStandardsIgnoreStart
    public $fields = [
        'company_code' => ['type' => 'string', 'length' => 3, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '企業コード', 'precision' => null, 'fixed' => null],
        'slip_no' => ['type' => 'integer', 'length' => 11, 'unsigned' => true, 'null' => false, 'default' => '0', 'comment' => '伝票番号', 'precision' => null, 'autoIncrement' => null],
        'customer_id' => ['type' => 'integer', 'length' => 11, 'unsigned' => true, 'null' => false, 'default' => null, 'comment' => '取引先ID', 'precision' => null, 'autoIncrement' => null],
        'user_id' => ['type' => 'integer', 'length' => 11, 'unsigned' => true, 'null' => true, 'default' => null, 'comment' => '利用者ID(営業担当者が発注した場合はnull)', 'precision' => null, 'autoIncrement' => null],
        'businessman_id' => ['type' => 'integer', 'length' => 11, 'unsigned' => true, 'null' => true, 'default' => null, 'comment' => '営業マンID(利用者が発注した場合はnull)', 'precision' => null, 'autoIncrement' => null],
        'is_downloaded' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => '0', 'comment' => 'ダウンロードフラグ(0: 未DL、1:DL済み)', 'precision' => null],
        'is_deleted' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => '0', 'comment' => '削除フラグ(0: 未削除、1:削除済み)', 'precision' => null],
        'subtotal' => ['type' => 'integer', 'length' => 11, 'unsigned' => true, 'null' => false, 'default' => '0', 'comment' => '小計(未使用)', 'precision' => null, 'autoIncrement' => null],
        'tax_total' => ['type' => 'integer', 'length' => 11, 'unsigned' => true, 'null' => false, 'default' => '0', 'comment' => '消費税(未使用)', 'precision' => null, 'autoIncrement' => null],
        'bikou' => ['type' => 'string', 'length' => 512, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '伝票備考', 'precision' => null, 'fixed' => null],
        'created' => ['type' => 'datetime', 'length' => null, 'null' => false, 'default' => null, 'comment' => '作成日', 'precision' => null],
        'created_user' => ['type' => 'string', 'length' => 20, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '作成者', 'precision' => null, 'fixed' => null],
        'updated' => ['type' => 'datetime', 'length' => null, 'null' => false, 'default' => null, 'comment' => '更新日', 'precision' => null],
        'updated_user' => ['type' => 'string', 'length' => 20, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '更新者', 'precision' => null, 'fixed' => null],
        '_constraints' => [
            'primary' => ['type' => 'primary', 'columns' => ['company_code', 'slip_no'], 'length' => []],
            't_order_headers_ibfk_1' => ['type' => 'foreign', 'columns' => ['company_code'], 'references' => ['m_companies', 'code'], 'update' => 'cascade', 'delete' => 'restrict', 'length' => []],
        ],
        '_options' => [
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_ja_0900_as_cs'
        ],
    ];
    // @codingStandardsIgnoreEnd

    /**
     * Init method
     *
     * @return void
     */
    public function init()
    {
        $this->records = [
            [
                'company_code' => '84e00f92-cb73-450b-83c8-5057eabc5977',
                'slip_no' => 1,
                'customer_id' => 1,
                'user_id' => 1,
                'businessman_id' => 1,
                'is_downloaded' => 1,
                'is_deleted' => 1,
                'subtotal' => 1,
                'tax_total' => 1,
                'bikou' => 'Lorem ipsum dolor sit amet',
                'created' => '2019-05-31 16:04:42',
                'created_user' => 'Lorem ipsum dolor ',
                'updated' => '2019-05-31 16:04:42',
                'updated_user' => 'Lorem ipsum dolor '
            ],
        ];
        parent::init();
    }
}
