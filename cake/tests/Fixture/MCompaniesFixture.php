<?php
namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;

/**
 * MCompaniesFixture
 *
 */
class MCompaniesFixture extends TestFixture
{

    /**
     * Fields
     *
     * @var array
     */
    // @codingStandardsIgnoreStart
    public $fields = [
        'code' => ['type' => 'string', 'length' => 3, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '企業コード', 'precision' => null, 'fixed' => null],
        'name' => ['type' => 'string', 'length' => 100, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '企業名', 'precision' => null, 'fixed' => null],
        'postcode' => ['type' => 'string', 'length' => 8, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '郵便番号', 'precision' => null, 'fixed' => null],
        'address1' => ['type' => 'string', 'length' => 50, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '住所１', 'precision' => null, 'fixed' => null],
        'address2' => ['type' => 'string', 'length' => 50, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '住所２', 'precision' => null, 'fixed' => null],
        'address3' => ['type' => 'string', 'length' => 50, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '住所３', 'precision' => null, 'fixed' => null],
        'tel_no' => ['type' => 'string', 'length' => 15, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '電話番号', 'precision' => null, 'fixed' => null],
        'fax_no' => ['type' => 'string', 'length' => 15, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => 'FAX番号', 'precision' => null, 'fixed' => null],
        'tanto_name' => ['type' => 'string', 'length' => 50, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '担当者名', 'precision' => null, 'fixed' => null],
        'tanto_email' => ['type' => 'string', 'length' => 200, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '担当者メールアドレス', 'precision' => null, 'fixed' => null],
        'download_id' => ['type' => 'biginteger', 'length' => 20, 'unsigned' => false, 'null' => false, 'default' => '0', 'comment' => '取引ID(ダウンロードファイルの通し番号)', 'precision' => null, 'autoIncrement' => null],
        'download_state' => ['type' => 'integer', 'length' => 11, 'unsigned' => false, 'null' => false, 'default' => '0', 'comment' => 'ダウンロードの状態(0:未処理、9:ダウンロード中)', 'precision' => null, 'autoIncrement' => null],
        'close_time' => ['type' => 'string', 'length' => 4, 'null' => false, 'default' => '1700', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '締め時間(納品予定日を1営業日ずらす時刻（hhmm形式）)', 'precision' => null, 'fixed' => null],
        'sender_name' => ['type' => 'string', 'length' => 50, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => 'メール送信元名称', 'precision' => null, 'fixed' => null],
        'plan_code' => ['type' => 'string', 'length' => 5, 'null' => true, 'default' => '0', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => 'プランコード(プランが存在しない場合NULL)', 'precision' => null, 'fixed' => null],
        'is_maintenance' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => '0', 'comment' => 'データ保持フラグ(0:OFF、1:ON（リカバリ領域へ保持）)', 'precision' => null],
        'is_orderzan' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => '0', 'comment' => '発注残通報フラグ(0:OFF、1:ON（毎時間発注残があれば通報メール）)', 'precision' => null],
        'orderzan_emails' => ['type' => 'string', 'length' => 1024, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '発注残通報先メール(「;」で区切ることで複数アドレス対応)', 'precision' => null, 'fixed' => null],
        'is_order_code' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => '0', 'comment' => '(0:OFF、1:ON)', 'precision' => null],
        'self_name1' => ['type' => 'string', 'length' => 8, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '基幹特有コード名称１(基幹側で判定したいカラムの名称を設定する)', 'precision' => null, 'fixed' => null],
        'self_name2' => ['type' => 'string', 'length' => 8, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '基幹特有コード名称２(基幹側で判定したいカラムの名称を設定する)', 'precision' => null, 'fixed' => null],
        'self_name3' => ['type' => 'string', 'length' => 8, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '基幹特有コード名称３(基幹側で判定したいカラムの名称を設定する)', 'precision' => null, 'fixed' => null],
        'order_type_0' => ['type' => 'string', 'length' => 2, 'null' => false, 'default' => '通常', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '商品区分名称0(オーダーリストの商品区分に対応した名称)', 'precision' => null, 'fixed' => null],
        'order_type_1' => ['type' => 'string', 'length' => 2, 'null' => false, 'default' => '受発', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '商品区分名称1', 'precision' => null, 'fixed' => null],
        'order_type_2' => ['type' => 'string', 'length' => 2, 'null' => false, 'default' => '直送', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '商品区分名称2', 'precision' => null, 'fixed' => null],
        'order_type_3' => ['type' => 'string', 'length' => 2, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '商品区分名称3', 'precision' => null, 'fixed' => null],
        'order_type_4' => ['type' => 'string', 'length' => 2, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '商品区分名称4', 'precision' => null, 'fixed' => null],
        'order_type_5' => ['type' => 'string', 'length' => 2, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '商品区分名称5', 'precision' => null, 'fixed' => null],
        'order_type_6' => ['type' => 'string', 'length' => 2, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '商品区分名称6', 'precision' => null, 'fixed' => null],
        'order_type_7' => ['type' => 'string', 'length' => 2, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '商品区分名称7', 'precision' => null, 'fixed' => null],
        'order_type_8' => ['type' => 'string', 'length' => 2, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '商品区分名称8', 'precision' => null, 'fixed' => null],
        'order_type_9' => ['type' => 'string', 'length' => 2, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '商品区分名称9', 'precision' => null, 'fixed' => null],
        'customer_code_type' => ['type' => 'integer', 'length' => 1, 'unsigned' => false, 'null' => false, 'default' => '1', 'comment' => '取引先コード出力区分(1:取引先コード、2:取引先コード＋取引先枝番)', 'precision' => null, 'autoIncrement' => null],
        'terminal_order_type' => ['type' => 'integer', 'length' => 1, 'unsigned' => false, 'null' => false, 'default' => '1', 'comment' => '発注画面の初期表示順序(1:取込順、2:商品コード順)', 'precision' => null, 'autoIncrement' => null],
        'terminal_tab_type' => ['type' => 'integer', 'length' => 1, 'unsigned' => false, 'null' => false, 'default' => '1', 'comment' => '発注画面の初期表示タブ(1:分類タブ表示、2:検索タブ表示)', 'precision' => null, 'autoIncrement' => null],
        'use_businessman' => ['type' => 'boolean', 'length' => 1, 'unsigned' => false, 'null' => false, 'default' => '0', 'comment' => '営業マン機能利用フラグ(0:利用不可、1:利用可)', 'precision' => null, 'autoIncrement' => null],
        'terminal_price_type' => ['type' => 'integer', 'length' => 1, 'unsigned' => false, 'null' => false, 'default' => '0', 'comment' => '発注画面の金額表示(0:非表示、1:税抜、2:税込)', 'precision' => null, 'autoIncrement' => null],
        'terminal_picture_type' => ['type' => 'integer', 'length' => 1, 'unsigned' => false, 'null' => false, 'default' => '0', 'comment' => '発注画面の商品画像表示(0:非表示、1:表示)', 'precision' => null, 'autoIncrement' => null],
        'use_inquiry' => ['type' => 'boolean', 'length' => 1, 'unsigned' => false, 'null' => false, 'default' => '0', 'comment' => '問い合わせ入力機能(0:OFF、1:ON)', 'precision' => null, 'autoIncrement' => null],
        'use_fixed_phrase' => ['type' => 'boolean', 'length' => 1, 'unsigned' => false, 'null' => false, 'default' => '0', 'comment' => '定型文登録機能(0:OFF、1:ON)', 'precision' => null, 'autoIncrement' => null],
        'is_deleted' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => '0', 'comment' => '削除フラグ(1:論理削除)', 'precision' => null],
        'created' => ['type' => 'datetime', 'length' => null, 'null' => false, 'default' => null, 'comment' => '作成日', 'precision' => null],
        'created_user' => ['type' => 'string', 'length' => 20, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '作成者', 'precision' => null, 'fixed' => null],
        'updated' => ['type' => 'datetime', 'length' => null, 'null' => false, 'default' => null, 'comment' => '更新日', 'precision' => null],
        'updated_user' => ['type' => 'string', 'length' => 20, 'null' => false, 'default' => '', 'collate' => 'utf8mb4_ja_0900_as_cs', 'comment' => '更新者', 'precision' => null, 'fixed' => null],
        '_indexes' => [
            'idx_name' => ['type' => 'index', 'columns' => ['name'], 'length' => []],
        ],
        '_constraints' => [
            'primary' => ['type' => 'primary', 'columns' => ['code'], 'length' => []],
        ],
        '_options' => [
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_ja_0900_as_cs'
        ],
    ];
    // @codingStandardsIgnoreEnd

    /**
     * Init method
     *
     * @return void
     */
    public function init()
    {
        $this->records = [
            [
                'code' => 'c102ee8e-d65c-4503-bb49-d85675eec261',
                'name' => 'Lorem ipsum dolor sit amet',
                'postcode' => 'Lorem ',
                'address1' => 'Lorem ipsum dolor sit amet',
                'address2' => 'Lorem ipsum dolor sit amet',
                'address3' => 'Lorem ipsum dolor sit amet',
                'tel_no' => 'Lorem ipsum d',
                'fax_no' => 'Lorem ipsum d',
                'tanto_name' => 'Lorem ipsum dolor sit amet',
                'tanto_email' => 'Lorem ipsum dolor sit amet',
                'download_id' => 1,
                'download_state' => 1,
                'close_time' => 'Lo',
                'sender_name' => '',
                'plan_code' => 'Lor',
                'is_maintenance' => 1,
                'is_orderzan' => 1,
                'orderzan_emails' => '',
                'self_name1' => 'Lorem ',
                'self_name2' => 'Lorem ',
                'self_name3' => 'Lorem ',
                'order_type_0' => 'Lo',
                'order_type_1' => 'Lo',
                'order_type_2' => 'Lo',
                'order_type_3' => 'Lo',
                'order_type_4' => 'Lo',
                'order_type_5' => 'Lo',
                'order_type_6' => 'Lo',
                'order_type_7' => 'Lo',
                'order_type_8' => 'Lo',
                'order_type_9' => 'Lo',
                'customer_code_type' => 1,
                'terminal_order_type' => 1,
                'terminal_tab_type' => 1,
                'use_businessman' => 1,
                'terminal_price_type' => 1,
                'terminal_picture_type' => 1,
                'is_deleted' => 1,
                'created' => '2019-05-31 16:04:40',
                'created_user' => 'Lorem ipsum dolor ',
                'updated' => '2019-05-31 16:04:40',
                'updated_user' => 'Lorem ipsum dolor '
            ],
        ];
        parent::init();
    }
}
