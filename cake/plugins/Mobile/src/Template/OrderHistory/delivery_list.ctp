<?php

// リストが空の場合、メッセージを表示
if (count($dataList) === 0) {
    ?>
    <div class="message error">該当するデータが存在しません。</div>
    <?php
} else {
    ?>

    <?php
    // タブレット用ページネーション
    if (!$isMobile) {
        echo $this->element('pagination');
    }
    ?>

    <?php
    // 詳細ボタンの高さ：発注コードでまとめる場合は、3行で表示
    $buttonRowNum = '1';
    if ($useOrderCode || $isViewPrice) {
        $buttonRowNum = '2';
    }
    if ($useOrderCode && $isViewPrice) {
        $buttonRowNum = '3';
    }
    ?>
    <table class="history-list-area">
        <thead class="history-mobile__border-header">
        <tr>
            <th rowspan="<?= $buttonRowNum ?>">
                詳細
            </th>
            <th>納品日</th>
            <th>納品書番号</th>
            <th>明細件数</th>
        </tr>
        <?php if ($isViewPrice) { ?>
            <tr>
                <th>納品金額</th>
                <th>消費税</th>
                <th>合計金額</th>
            </tr>
        <?php } ?>
        <?php if ($useOrderCode) { ?>
            <tr>
                <th colspan="3">取引先名</th>
            </tr>
        <?php } ?>
        </thead>
        <tbody class="history-mobile__border-body">
        <?php foreach ($dataList as $data): ?>
            <tr class='history-mobile__border history-mobile__border--top'>
                <td rowspan="<?= $buttonRowNum ?>">
                    <?=
                    $this->Html->link("<i class=\"fas fa-file-alt\"></i>", [
                        'action' => 'delivery_detail',
                        $data['delivery_note_number'] ?? "", // パラメータ
                    ], ["class" => "history-list-area__detail", "escape" => false])
                    ?>
                </td>
                <td><?= isset($data['delivery_slip_date']) ? substr_replace(substr_replace($data['delivery_slip_date'], '/', 6, 0), '/', 4, 0) : "&ndash;"; ?></td>
                <td><?= isset($data['delivery_note_number']) ? $data['delivery_note_number'] : "&ndash;"; ?></td>
                <td><?= isset($data['detail_number']) && $data['detail_number'] != '0' ? $data['detail_number'] : "&ndash;"; ?></td>
            </tr>
            <?php if ($isViewPrice) { ?>
                <tr>
                    <td><?= isset($data['order_amount']) && $data['order_amount'] != '0' ? number_format($data['order_amount']) . " 円" : "&ndash;"; ?></td>
                    <td><?= isset($data['order_tax']) && $data['order_tax'] != '0' ? number_format($data['order_tax']) . " 円" : "&ndash;"; ?></td>
                    <td><?= isset($data['total_price']) && $data['total_price'] != '0' ? number_format($data['total_price']) . " 円" : "&ndash;"; ?></td>
                </tr>
            <?php } ?>
            <?php if ($useOrderCode) { ?>
                <tr>
                    <td colspan="3"><?= $data['customer_name'] ?? "" ?> (<?=
                        $data['customer_department_name'] != "" ? $data['customer_department_name'] : "－"
                        ?>)
                    </td>
                </tr>
            <?php } ?>
            <?php //} ?>
        <?php endforeach; ?>
        </tbody>
    </table>

    <?php
    // タブレット用ページネーション
    if (!$isMobile) {
        echo $this->element('pagination');
    }
    ?>
<?php } ?>