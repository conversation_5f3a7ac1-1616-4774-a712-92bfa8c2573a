<?php
/**
 * スマホ用レイアウト
 */
?>
<?php
// トップページのURLを作成
$topUrl = $this->Url->build([
    "controller" => "OrderList",
    "action" => "index",
]);
?>
<!DOCTYPE html>
<html>

<head>
    <?= $this->Html->charset() ?>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0 maximum-scale=1.0">
    <meta name="robots" content="noindex" />

    <title>
        <?php if ($title != ''): ?>
            <?= $pageName . " | " . $title ?>
        <?php endif; ?>
    </title>

    <?php if ($companyCode == "001") { ?>
    <?= $this->Html->meta('icon', $this->Url->image('/../img/' . $companyCode . '/favicon.ico')) ?>
    <?= $this->Html->meta([
        'link' => $this->Url->image('/../img/' . $companyCode . '/favicon.png'),
        'rel' => 'apple-touch-icon'
    ])
    ?>
    <?php } else { ?>
        <?php
            // 企業別ファビコン(ブラウザタブ用)の存在チェック
            $companyTabIconPath = WWW_ROOT . 'img/' . $companyCode . '/' . 'favicon.ico';
            $iconChange = false;
            if (file_exists($companyTabIconPath)) {
                $companyTabIconPath = $this->Url->image('/../../img/' . $companyCode . '/' . 'favicon.ico');
                $iconChange = true;
            }
        ?>
        <?php if ($iconChange) { ?>
            <?= $this->Html->meta('icon', $this->Url->image($companyTabIconPath)) ?>
        <?php } else { ?>
            <?= $this->Html->meta('icon', $this->Url->image('/../img/favicon.ico')) ?>
        <?php } ?>
        
        <?php
            // 企業別ファビコン(スマホホーム画面用)の存在チェック
            $companyHomeIconPath = WWW_ROOT . 'img/' . $companyCode . '/' . 'favicon.png';
            $iconChange = false;
            if (file_exists($companyHomeIconPath)) {
                $companyHomeIconPath = $this->Url->image('/../../img/' . $companyCode . '/' . 'favicon.png');
                $iconChange = true;
            }
        ?>
        <?php if ($iconChange) { ?>
            <?= $this->Html->meta([
                'link' => $this->Url->image($companyHomeIconPath),
                'rel' => 'apple-touch-icon'
            ])
            ?>
        <?php } else { ?>
            <?= $this->Html->meta([
                'link' => $this->Url->image('/../img/favicon.png'),
                'rel' => 'apple-touch-icon'
            ])
            ?>
        <?php } ?>

        
    <?php } ?>

    <!-- <?= $this->Html->css('app.css') ?> -->
    <?php
    if ($companyCode == "001") {
        echo $this->Html->css('style_mobile_act.css');
    } else {
        echo $this->Html->css('style_mobile.css');
    }
    ?>
    <?= $this->Html->css('font-awesome.min.css') ?>
    <?= LOGO_FONT_URL ?>

    <?= $this->fetch('meta') ?>
    <?= $this->fetch('css') ?>
    <link rel="stylesheet" href="https://use.typekit.net/coj7pbq.css">
</head>

<body>
<?php /* ログイン認証を行うページに設定 */ ?>
<?php if (isset($authUrl) && $authUrl !== "") echo $this->Form->hidden('authUrl', ['value' => $authUrl]); ?>
<?php if (isset($currentController) && $currentController !== "") echo $this->Form->hidden('currentController', ['value' => $currentController]); ?>
<?php if (isset($currentAction) && $currentAction !== "") echo $this->Form->hidden('currentAction', ['value' => $currentAction]); ?>
<?= $this->Form->hidden('prevController', ['value' => ""]); ?>
<?php if ($loginType == FLAG_BIZ_LOGIN || $loginType == FLAG_BIZ_SUB_LOGIN) { ?>
    <?= $this->Form->hidden('logoutUrl', ['value' => $bizLogoutUrl]) ?>
    <?= $this->Form->hidden('bizSelect', ['value' => $bizSelect]) ?>
<?php } else { ?>
    <?= $this->Form->hidden('logoutUrl', ['value' => $logoutUrl]) ?>
<?php } ?>
<?= $this->Form->hidden('listViewNum', ['value' => $defaultListNum ?? ""]) ?>
<?php /* カタログ機能の利用有無 */ ?>
<?php if ($enableCatalog && $loginType == FLAG_USER_LOGIN) echo $this->Form->hidden('useCatalog', ['value' => true]); ?>

<?php /* ログイン情報を保存 */ ?>
<?= $this->Form->hidden('loginId', ['value' => $loginId]) ?>
<?= $this->Form->hidden('loginName', ['value' => $loginName]) ?>
<?= $this->Form->hidden('loginName2', ['value' => $loginName2 ?? '']) ?>
<?= $this->Form->hidden('loginType', ['value' => $loginType]) ?>
<?= $this->Form->hidden('customerName', ['value' => $customerName]) ?>

<div id="header" class="header sticky-top">

    <?php /* 検索ワードを保存するセッションストレージのキー */ ?>
    <?= $this->Form->hidden('searchKey', ['value' => ""]) ?>
    <?php /* Ajax通信を行う場合のURL */ ?>
    <?php if (isset($ajaxUrl) && $ajaxUrl !== "") echo $this->Form->hidden('ajaxUrl', ['value' => $ajaxUrl]); ?>

    <!-- メニュー -->
    <nav class="header-menu navbar-light">
        <div class="header-menu__title-area">
            <h1 class="header-menu__title">
                <?php if ($companyCode == "001") { ?>
                    <div class="header-menu__title--logo">
                        <a href="<?= $topUrl ?>">
                            <?= $this->Html->image('001/logo.png', ["alt" => "Act Net Order", "style" => "width:130px;"]) ?>
                        </a>
                    </div>
                <?php } else if ($companyCode == "000") { ?>
                    <div class="header-menu__title--logo">
                        <a href="<?= $topUrl ?>">
                            <?= $this->Html->image($companyCode . '/logo_order.png', ["alt" => "Smart Order", "style" => "height:36px;"]) ?>
                        </a>
                    </div>
                <?php } else { ?>
                    <div class="header-menu__title--logo">
                        <a href="<?= $topUrl ?>">
                            <?php
                                // システムロゴ画像の存在チェック
                                $systemLogoPath = WWW_ROOT . 'img/' . $companyCode . '/' . 'app_logo';
                                $logoChange = false;
                                if (file_exists($systemLogoPath . '.png')) {
                                    $systemLogoPath = $this->Url->image($companyCode . '/' . 'app_logo.png');
                                    $logoChange = true;
                                } else if (file_exists($systemLogoPath . '.svg')) {
                                    $systemLogoPath = $this->Url->image($companyCode . '/' . 'app_logo.svg');
                                    $logoChange = true;
                                }
                            ?>
                            <?php if ($logoChange) { ?>
                                <img src="<?= $systemLogoPath ?>" alt="Smart Order" style="height:32px">
                            <?php } else { ?>
                                <?= $this->Html->image('app_logo.svg', ["alt" => "Smart Order", "style" => "height:32px;"]) ?>
                            <?php } ?>
                        </a>
                    </div>
                <?php } ?>
            </h1>

            <!-- カタログ -->
            <?php if ($enableCatalog && $enableCatalog2) { ?>
                <?php if ($companyCode == "001") { ?>
                    <span class="header-menu__button-area">
                        <button type="button" href="<?= CATALOG_URL ?>" class="header-menu__button--confirm header-menu__button-area__non-radius-button" id="catalog_link">
                            <?= $this->Html->image('catalog_icon_act.svg', ["alt" => "カタログへ", "style" => "width: 30px;"]) ?>
                        </button>
                    </span>
                <?php } else { ?>
                    <span class="header-menu__button-area">
                        <button type="button" href="<?= CATALOG_URL ?>" class="header-menu__button--confirm header-menu__button-area__radius-button" id="catalog_link">
                            <?= $this->Html->image('catalog_icon_smht.svg', ["alt" => "カタログへ", "style" => "width: 19px;"]) ?>
                        </button>
                    </span>
                <?php } ?>
            <?php } ?>

            <!-- お知らせ -->
            <span class="header-menu__button-area">
                <button type="button" href="#" data-toggle="modal" data-target="#news_dialog" class="header-menu__button-area__radius-button">
                    <i class="far fa-bell"></i>
                </button>
                <div class="header-menu__button-alert category__num--news" style="display: none;"></div>
            </span>

            <!-- 発注確認 -->
            <span class="header-menu__button-area">
                <button type="button" href="<?=
                $this->Url->build([
                    'controller' => 'order_confirm',
                    'action' => 'index',
                ])
                ?>" class="header-menu__button--confirm header-menu__button-area__radius-button">
                    <i class="fas fa-shopping-cart"></i>
                </button>

                <div class="header-menu__button-alert category__alert--now" style="display: none;"></div>
            </span>

            <button type="button" class="header-menu__menu-button navbar-toggler" data-toggle="collapse"
                    data-target="#Navber" aria-controls="Navber" aria-expanded="false" aria-label="ナビゲーションの切替">
                <span class="navbar-toggler-icon"></span>
            </button>
        </div>
        <div class="clearfix"></div>

        <div class="header-menu__items collapse navbar-collapse" id="Navber">
            <ul class="navbar-nav mr-auto">
                <li class="nav-item">
                    <span class="info-customer_name"></span> : <span class="info-login_name"></span>
                    <?php if ($loginType != FLAG_BIZ_SUB_LOGIN) { ?>
                        <span>様</span>
                    <?php } ?>
                </li>
                <li class="nav-item">
                    <?=
                    $this->Html->link("発注履歴", [
                        'controller' => 'order_history',
                        'action' => 'index',
                    ], ["class" => "nav-link"])
                    ?>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" data-toggle="modal" data-target="#news_dialog">
                        お知らせ
                    </a>
                </li>
                <?php // 営業マンでログインしている場合非表示 ?>
                <?php if ($loginType == FLAG_USER_LOGIN) { ?>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-toggle="modal" data-target="#inquiry_dialog">
                            お問合せ
                        </a>
                    </li>
                <?php } ?>
                <?php if ($enableCatalog && $enableCatalog2) { ?>
                    <li class="nav-item">
                        <?=
                        $this->Html->link("カタログ",
                            //CATALOG_URL,
                            "#",
                            ["class" => "nav-link" , "id" => "catalog_link_nav"])
                        ?>
                    </li>
                <?php } ?>
                <?php if ($enableSort && $loginType == FLAG_USER_LOGIN) { ?>
                    <li class="nav-item">
                        <?=
                        $this->Html->link("並べ替え", [
                            'controller' => 'order_sort',
                            'action' => 'index',
                        ], ["class" => "nav-link"])
                        ?>
                    </li>
                <?php } ?>
                <li class="nav-item">
                    <?php if ($loginType == FLAG_USER_LOGIN) { ?>
                        <?=
                        $this->Html->link(LOGOUT, [
                            'controller' => 'login',
                            'action' => 'logout',
                        ], ["class" => "nav-link logout"])
                        ?>
                        <?php // 営業マンでログインしている場合 ?>
                    <?php } else { ?>
                        <a class="nav-link logout" href="#" data-toggle="modal"
                           data-target="#biz_logout_dialog">ログアウト</a>
                    <?php } ?>
                </li>
            </ul>
        </div><!-- /.navbar-collapse -->
    </nav>

    <!-- タイトル欄 -->
    <div class="header-title">
        <?= $pageName ?>
    </div>
</div>

<div id="content" class="container">

    <?= $this->Flash->render() ?>
    <?= $this->fetch('content') ?>

</div>

<!-- お知らせダイアログ -->
<?= $this->element('news_dialog', [
    "dialogID" => "news_dialog",
]) ?>

<!-- 問い合わせダイアログ -->
<?= $this->element('inquiry_dialog', [
    "dialogID" => "inquiry_dialog",
]) ?>

<!-- ログアウト確認ダイアログ -->
<?= $this->element('biz_logout_dialog', [
    "dialogID" => "biz_logout_dialog",
]) ?>

<?= $this->Html->script('app.js') ?>
<?= $this->Html->script('common-mobile.js') ?>
<?= $this->fetch('script') ?>
</body>
</html>
