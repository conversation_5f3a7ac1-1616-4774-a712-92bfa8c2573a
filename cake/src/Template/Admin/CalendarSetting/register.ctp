<?php
/**
 * 管理画面：休業日登録/更新画面
 */

// 登録か更新を判定
$action = [];
if ($isInsert) {
    $action["action"] = 'insertData';
} else {
    $action["action"] = 'updateData';
    $action[] = $calendarData->id ?? "";
}
?>

<div class="row">
    <?=
    $this->Html->link(BACK, [
        'controller' => 'CalendarSetting',
        'action' => 'index',
    ], ["class" => "main__back-button"])
    ?>
</div>

<div class="row">
    <?= $this->Form->create(null, ['url' => $action, 'class' => 'input']) ?>
    <?= $this->Flash->render() ?>

    <div class="input__row">
        <div class="input__content input__content--two-column">
            <div class="input__content-title">
                対象日付
                <?php if ($isInsert) { ?>
                    <span class="required"/>
                <?php } ?>
            </div>
            <div class="col input__content-data">
                <?php
                if ($isInsert) {
                    $date = new DateTime();
                    $today = $date
                        ->format('Y-m-d');
                    ?>
                    <?= $this->Form->text('set_date', [
                        'id' => 'date',
                        'class' => 'input__content-data--code datetimepicker-input validate[required]',
                        'data-toggle' => 'datetimepicker',
                        'data-target' => '#date',
                        'autocomplete' => 'off',
                        'data-default' => $calendarData->set_date ?? $today,
                        'data-min-date' => $today,
                        'value' => '',
                        'readonly' => 'readonly'
                    ]) ?>
                <?php } else { ?>
                    <?= $this->Form->hidden('set_date', ['value' => $calendarData->set_date ?? ""]) ?>
                    <?= $this->Form->text('',
                        ['class' => 'input__content-data--code',
                            'value' => $calendarData->set_date ?? "",
                            'disabled' => 'disabled',
                        ]) ?>
                <?php } ?>
            </div>
        </div>
        <div class="input__content input__content--two-column">
            <div class="input__content-title">
                休業日区分
            </div>
            <div class="input__content-data">
                <?= $this->Form->radio('holiday_type',
                    [
                        ['value' => 1, 'text' => '定休日'],
                        ['value' => 2, 'text' => '祝祭日'],
                    ],
                    ['value' => $calendarData->holiday_type ?? 1, 'hiddenField' => false]) ?>
            </div>
        </div>
        <div class="input__content">
            <div class="input__content-title">
                コメント
            </div>
            <div class="input__content-data">
                <?= $this->Form->text('comment',
                    ['class' => 'input__content-data--text validate[maxSize[200]] enable-ime',
                        'maxlength' => '200',
                        'value' => $calendarData->comment ?? ""
                    ]) ?>
            </div>
        </div>
    </div>

    <?= $this->Flash->render() ?>

    <div class="input__row">
        <div class="input__content input__content--one-line input__button-area">
            <?php if ($isInsert) { ?>
                <button type="submit" class="input__button alert__button--insert">
                    <?= REGISTER ?>
                </button>
            <?php } else { ?>
                <button type="submit" class="input__button alert__button--update">
                    <?= UPDATE ?>
                </button>
            <?php } ?>
        </div>
    </div>

    <?= $this->Form->end() ?>
</div> <!-- row -->
