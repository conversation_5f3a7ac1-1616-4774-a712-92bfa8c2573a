<?php
/**
 * 管理画面：発注情報ダウンロード画面
 */

?>

<div id="list-dowoload"></div>

<div class="row">
    <?=
    $this->Html->link(BACK, [
        'controller' => 'menu',
        'action' => 'index',
    ], ["class" => "main__back-button"])
    ?>
</div>

<div class="row">
    <div class="content__center">
        <div id="default">
            新規の発注データをCSVファイルでダウンロードします。
        </div>
        <div id="failed" style="display:none;">
            <div id="error" class="message error">
            </div>
        </div>
        <div id="download">
            <button type="button" class="list__button list__button--orderdownload">
                ダウンロード
            </button>
        </div>
        <div id="complete" style="display:none;">
            <p>ダウンロードファイルの作成が完了しました。</p>
            <p>ダウンロードを行って下さい。</p>
            <p>有効データ件数：<span id="count"></span>件</p>
            <p>未処理データ件数：<span id="demoCount"></span>件</p>
        </div>
    </div>
</div>

<!-- 検索条件エリア -->
<div class="row">
    <?= $this->Form->create(null, ['url' => ['action' => 'file_list',], 'class' => 'search']) ?>
    <div class="content__center">過去の発注データをCSVファイルでダウンロードします。</div>
    <div class="search__title">検索条件</div>

    <div class="search__content search__content--one-line">
        <div class="search__content-title">
            ファイル作成日
        </div>
        <div class="search__content-data">
            <div class="col search__content-data--calendar-area">
                <?= $this->Form->text('date_from', [
                    'id' => 'date_from',
                    'class' => 'search__content-data--calendar-dual datetimepicker-input',
                    'data-toggle' => 'datetimepicker',
                    'data-target' => '#date_from',
                    'autocomplete' => 'off',
                    'data-default' => $date_from,
                    'value' => '',
                    'readonly' => 'readonly'
                ]) ?>
                <div class="search__content-data--bar">～</div>
                <?= $this->Form->text('date_to', [
                    'id' => 'date_to',
                    'class' => 'search__content-data--calendar-dual datetimepicker-input',
                    'data-toggle' => 'datetimepicker',
                    'data-target' => '#date_to',
                    'autocomplete' => 'off',
                    'data-default' => $date_to,
                    'value' => '',
                    'readonly' => 'readonly'
                ]) ?>
            </div>
        </div>
    </div>
    <div class="search__content search__content--one-line search__content--button-area">

        <button class="search__button--submit-files" type="submit">
            <?= SEARCH ?>
        </button>
        <?= $this->Form->button(CLEAR, ['type' => 'reset', 'class' => 'search__button--clear']) ?>
    </div>

</div> <!-- row -->

<div id="row">
    <?php if (isset($is_result)) { ?>

        <?php if ($is_result == 1) { ?>

            <div class="list">
                ページ番号　
                <?= $this->Form->select('page_no', $pageList) ?>
                <button type="button" id="next_page" class="list__button--min">
                    選　択
                </button>
            </div>

            <table class="list-area">
                <thead>
                <tr>
                    <th>ファイルの作成日時</th>
                    <th>ファイルの作成日時</th>
                </tr>
                </thead>
                <tbody>
                <?php
                $leftValue = true;
                foreach ($fileData

                         as $data) {
                    ?>
                    <?php if ($leftValue) echo "<tr>"; ?>
                    <td>
                        <?= $data['fileDisp'] ?>
                        <a class="list-area__button list-area__button--wide" href="<?= $data['filePath'] ?>">ダウンロード</a>
                    </td>
                    <?php if (!$leftValue) echo "</tr>"; ?>
                    <?php
                    $leftValue = !$leftValue;
                }
                if (!$leftValue) echo "<td></td>";
                ?>
                </tbody>
            </table>

        <?php } else { ?>
            <div class="content__center">
                ファイルがありません。検索条件を変更して再度検索してください。
            </div>
        <?php } ?>
    <?php } ?>
    <?= $this->Form->end() ?>
</div>

<?php /* CSVダウンロードを行う場合のURL */ ?>
<?php if (isset($downloadOrderUrl) && $downloadOrderUrl !== "") echo $this->Form->hidden('downloadOrderUrl', ['value' => $downloadOrderUrl]); ?>

