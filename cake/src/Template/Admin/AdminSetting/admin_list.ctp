<?php /* Ajax取得用リストフォーマット */ ?>

<!-- リストエリア -->
<div class="row">
    <div id="list">
        <?=
        $this->Html->link(ADD, [
            'action' => 'register',
        ], ["class" => "list__button"])
        ?>
    </div>

    <table class="list-area list-area__hover">
        <thead>
        <tr>
            <th style="width:1px;"><?php // 削除ボタン ?></th>
            <th style="width:1px;"><?php // 詳細ボタン ?></th>
            <th style="width:6rem;">No.</th>
            <th style="width:18rem;">ログインID</th>
            <th style="width:18rem;">ユーザー名称</th>
            <th style="width:9rem;">権限</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($listData as $data): ?>
            <tr>
                <td>
                    <?=
                    $this->Html->link(UPDATE, [
                        'action' => 'register',
                        $data->login_id,
                    ], ["class" => "list-area__button"]
                    ) ?>
                </td>
                <td>
                    <?=
                    $this->Html->link(DELETE, [
                        'action' => 'deleteData',
                        $data->login_id,
                    ], ["class" => "list-area__button alert__button--delete", "id" => $data->login_id]
                    ) ?>
                </td>
                <td><?= $numCount++ ?></td>
                <td><?= $data->login_id ?></td>
                <td><?= $data->name ?></td>
                <td>
                    <?= $data->authority_type == '9' ? "管理者" : "一般ユーザー" ?>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>

    <!-- ページネーション -->
    <nav aria-label="Page navigation example" class="paginator list-area__paginator">
        <ul class="pagination pagination-lg">
            <?php
            if ($this->Paginator->hasPrev()) {
                echo $this->Paginator->first('<<');
                echo $this->Paginator->prev('<');
            }

            echo $this->Paginator->numbers();

            if ($this->Paginator->hasNext()) {
                echo $this->Paginator->next('>');
                echo $this->Paginator->last('>>');
            }
            ?>
        </ul>
    </nav>
</div>