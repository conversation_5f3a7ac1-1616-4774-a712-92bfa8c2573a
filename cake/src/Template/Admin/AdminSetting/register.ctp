<?php
/**
 * 管理画面：ユーザー情報管理画面（追加・更新）
 */

// 登録か更新を判定
$action = [];
if ($isInsert) {
    $action["action"] = 'insertData';
} else {
    $action["action"] = 'updateData';
    $action[] = $data->login_id;
}
?>

<div class="row">
    <?=
    $this->Html->link(BACK, [
        'controller' => 'AdminSetting',
        'action' => 'index',
    ], ["class" => "main__back-button"])
    ?>
</div>

<div class="row">
    <?= $this->Form->create(null, ['url' => $action, 'class' => 'input']) ?>

    <div class="input__row">
        <div class="input__content">
            <div class="input__content-title">
                <?= LOGIN_ID ?>
                <?php if ($isInsert) {
                    echo '<span class="required"/>';
                } ?>
            </div>
            <div class="input__content-data">
                <?php if ($isInsert) { ?>
                    <?= $this->Form->tel('login_id', [
                        'class' => 'input__content-data--code validate[maxSize[15],custom[onlyAlphabetNumberSymbol],required] disable-ime',
                        'maxlength' => 15
                    ]) ?>
                <?php } else { ?>
                    <?= $this->Form->label('login_id', [
                        'value' => $data->login_id ?? ""]) ?>
                <?php } ?>
            </div>
        </div>

        <div class="input__content">
            <div class="input__content-title">
                <?= PASSWORD ?>
                <span class="required"/>
            </div>
            <div class="input__content-data">
                <?= $this->Form->text('password', [
                    'type' => 'password',
                    'class' => 'input__content-data--code validate[maxSize[15],custom[onlyAlphabetNumberSymbolAll],required] disable-ime',
                    'value' => $data->password ?? "",
                    'maxlength' => 15]) ?>
            </div>
        </div>

        <div class="input__content">
            <div class="input__content-title">
                <?php //TODO:定数化の検討 ?>
                ユーザー名称
                <span class="required"/>
            </div>
            <div class="input__content-data">
                <?= $this->Form->text('name', [
                    'class' => 'input__content-data--text validate[maxSize[50],required] enable-ime',
                    'value' => $data->name ?? "",
                    'maxlength' => 50]) ?>
            </div>
        </div>

        <div class="input__content">
            <div class="input__content-title">
                <?php //TODO:定数化の検討 ?>
                権限フラグ
            </div>
            <div class="input__content-data">
                <?php /* $isAuthorityChangeがtrueの場合はラジオボタンを操作できないようにする */ ?>
                <?= $this->Form->radio('authority_type', [
                    ['value' => GENERAL_ADMIN, 'text' => '一般ユーザー'],
                    ['value' => SUPER_ADMIN, 'text' => '管理者'],
                ], ['value' => $data->authority_type ?? GENERAL_ADMIN, 'hiddenField' => false,
                    'disabled' => ($isAuthorityChange ? "" : 'disabled')]) ?>
                    <?php
                    if(!$isAuthorityChange) {
                        echo '<input type="hidden" name="authority_type" value="' . $data->authority_type . '">';
                    }
                    ?>
            </div>
        </div>
    </div>

    <?= $this->Flash->render() ?>

    <div class="input__row">
        <div class="input__content input__content--one-line input__button-area">
            <?php if ($isInsert) { ?>
                <button type="submit" class="list__button alert__button--insert">
                    <?= REGISTER ?>
                </button>
            <?php } else { ?>
                <button type="submit" class="list__button alert__button--update">
                    <?= UPDATE ?>
                </button>
            <?php } ?>
        </div>
    </div>

    <?= $this->Form->end() ?>
</div>