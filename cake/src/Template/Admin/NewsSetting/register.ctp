<?php
/**
 * 管理画面：お知らせ登録画面（追加・更新）
 */

/* 専用のjsを読み込み */
$this->Html->script('admin-news.js', ['block' => 'script']);

// 登録か更新を判定
$action = [];
if ($isInsert) {
    $action["action"] = 'insertData';
} else {
    $action["action"] = 'updateData';
    $action[] = $newsData->id ?? "";
}
?>
<div class="row">
    <?=
    $this->Html->link(BACK, [
        'controller' => 'NewsSetting',
        'action' => 'index',
    ], ["class" => "main__back-button"])
    ?>
</div>

<div class="row">
    <?= $this->Form->create(null, ['url' => $action, 'class' => 'input']) ?>
    <?= $this->Flash->render() ?>

    <div class="input__row">
        <div class="input__content">
            <div class="input__content-title">
                記載日
                <?php if ($isInsert) { ?>
                    <span class="required"/>
                <?php } ?>
            </div>
            <?php if ($isInsert) { ?>
                <div class="input__content-data col">
                    <?= $this->Form->text('set_date', [
                        'id' => 'date',
                        'class' => 'input__content-data--code datetimepicker-input validate[required]',
                        'data-toggle' => 'datetimepicker',
                        'data-target' => '#date',
                        'autocomplete' => 'off',
                        'data-default' => $todayDate,
                        'readonly' => 'readonly'
                    ]) ?>
                </div>
            <?php } else { ?>
                <div class="input__content-data">
                    <?= $this->Form->label('', ['value' => $newsData->set_date ?? ""]) ?>
                </div>
                <?= $this->Form->text('set_date', [
                    'type' => 'hidden',
                    'value' => $newsData->set_date ?? ""]) ?>
            <?php } ?>
        </div>
        <div class="input__content">
            <div class="input__content-title">
                表示フラグ
            </div>
            <div class="input__content-data">
                <?= $this->Form->radio('is_visible', [
                    FLAG_ON => '表示', FLAG_OFF => '非表示'],
                    ['value' => $newsData->is_visible ?? 0, 'hiddenField' => true]) ?>
            </div>
        </div>
        <div class="input__content">
            <div class="input__content-title">
                タイトル
                <span class="required"/>
            </div>
            <div class="input__content-data">
                <?= $this->Form->text('title', [
                    'class' => 'input__content-data--text validate[maxSize[50],required] enable-ime',
                    'maxlength' => '50',
                    'value' => $newsData->title ?? ""]) ?>
            </div>
        </div>
        <div class="input__content">
            <div class="input__content-title">
                URL
            </div>
            <div class="input__content-data">
                <?= $this->Form->tel('url', [
                    'class' => 'input__content-data--text validate[maxSize[1024]] disable-ime',
                    'maxlength' => '1024',
                    'value' => $newsData->url ?? ""]) ?>
            </div>
        </div>
        <div class="input__content">
            <div class="input__content-title">
                メモ
            </div>
            <div class="input__content-data">
                <?= $this->Form->textarea('text', [
                    'class' => 'input__content-data--text validate[maxSize[1024]] enable-ime',
                    'maxlength' => '1024',
                    'value' => $newsData->text ?? ""]) ?>
                ※メモは1024文字以内
            </div>
        </div>
        <div class="input__content">
            <div class="input__content-title">
                カテゴリー

                <?php
                // ツールチップの内容
                $newsTabList = "";
                $maintenanceTabList = "";
                foreach ($newsCategoryLabel as $label) {
                    if ($label['type'] === '1') {
                        $newsTabList .= $label['name'] . ", ";
                    } else {
                        $maintenanceTabList .= $label['name'] . ", ";
                    }
                }

                $categoryToolTip = "指定したカテゴリーが、お知らせダイアログの対応したタブに表示されます。<br>"
                    . "現在の設定は下記です。<br>"
                    . "・お知らせタブ：{$newsTabList}<br>"
                    . "・メンテナンスタブ：{$maintenanceTabList}<br>";

                ?>
                <span class="content__tooltip"
                      data-toggle="tooltip" data-html="true"
                      data-placement="bottom"
                      title="<?= $categoryToolTip ?>"
                >
                <i class="fas fa-question-circle"></i>
                </span>
            </div>
            <div class="input__content-data">
                <?php
                $nowCategory = $newsData->category ?? 0;
                foreach ($newsCategoryLabel as $key => $category) {
                    $checked = '';
                    if ($key == $nowCategory) {
                        $checked = 'checked="checked"';
                    }
                    ?>
                    <div class="common__radio">
                        <label for="category-<?= $key ?>">
                            <input type="radio" name="category" value="<?= $key ?>"
                                   id="category-<?= $key ?>" <?= $checked ?>>
                            <?= $category['name'] ?>
                        </label>
                    </div>
                    <?php
                }
                ?>
            </div>
        </div>
        <div class="input__content">
            <div class="input__content-title">
                表示期間
            </div>
            <div class="input__content-data col">
                <?= $this->Form->text('date_from', [
                    'id' => 'date_from',
                    'class' => 'input__content-data--code datetimepicker-input',
                    'data-toggle' => 'datetimepicker',
                    'data-target' => '#date_from',
                    'autocomplete' => 'off',
                    'data-default' => $newsData->date_from ?? "",
                    'value' => '',
                    'readonly' => 'readonly'
                ]) ?>
                ～
                <?= $this->Form->text('date_to', [
                    'id' => 'date_to',
                    'class' => 'input__content-data--code datetimepicker-input',
                    'data-toggle' => 'datetimepicker',
                    'data-target' => '#date_to',
                    'autocomplete' => 'off',
                    'data-default' => $newsData->date_to ?? "",
                    'value' => '',
                    'readonly' => 'readonly'
                ]) ?>
            </div>
        </div>
        <div class="input__content">
            <div class="input__content-title">
                表示方法

                <?php
                // ツールチップの内容
                $categoryToolTip = "表示方法は下記を参照してください。<br>"
                    . "・発注画面に表示：発注画面のお知らせに表示されます。<br>"
                    . "　・ログイン画面に表示：ログイン画面に表示されます。<br>"
                    . "　・ポップアップ表示：発注画面にログイン後、自動でポップアップされます（1度のみ）<br>"
                    . "・管理画面に表示：管理画面のお知らせに表示されます。<br>";
                ?>
                <?php if ($useBusinessman) {
                    $categoryToolTip .= "・営業マン画面に表示：営業マンでログイン時、発注画面のお知らせに表示されます。";
                } ?>
                
                <span class="content__tooltip"
                      data-toggle="tooltip" data-html="true"
                      data-placement="bottom"
                      title="<?= $categoryToolTip ?>"
                >
                <i class="fas fa-question-circle"></i>
                </span>

            </div>
            <div class="input__content-data input__content-data--column">
                <div class="input__content-data--full">
                    <?= $this->Form->input('is_view_user', array(
                        'id' => 'checkboxViewUser',
                        'type' => 'checkbox',
                        'label' => '発注画面に表示',
                        'checked' => $newsData->is_view_user ?? true
                    )) ?>
                </div>
                <div class="input__content-data--full">
                    <div class="input__content-data--subline">
                        （
                        <?= $this->Form->input('is_login', array(
                            'id' => 'checkboxLogin',
                            'type' => 'checkbox',
                            'label' => 'ログイン画面に表示',
                            'checked' => $newsData->is_login ?? false
                        )) ?>
                        <?= $this->Form->input('is_popup', array(
                            'id' => 'checkboxPopup',
                            'type' => 'checkbox',
                            'label' => 'ポップアップ表示',
                            'checked' => $newsData->is_popup ?? false
                        )) ?>）
                    </div>
                </div>
                <div class="input__content-data--full">
                    <?= $this->Form->input('is_view_admin', array(
                        'type' => 'checkbox',
                        'label' => '管理画面に表示',
                        'checked' => $newsData->is_view_admin ?? false
                    )) ?>
                </div>
                <?php if ($useBusinessman) { ?>
                    <div class="input__content-data--full">
                        <?= $this->Form->input('is_biz', array(
                            'type' => 'checkbox',
                            'label' => '営業マン画面に表示',
                            'checked' => $newsData->is_biz ?? false
                        )) ?>
                    </div>
                <?php } ?>
            </div>
        </div>
        <div class="input__content">
            <div class="input__content-title">
                優先度
                <?php $tooltipText = "記載日が同じお知らせが複数あった場合、<br>優先度の数値が小さいものが一覧の上位に表示されます。"; ?>
                <span data-container="body" class="content__tooltip" data-toggle="tooltip" data-html="true"
                      data-placement="bottom" title=" <?= $tooltipText ?>
                ">
                <i class="fas fa-question-circle"></i>
                </span>
            </div>
            <div class="input__content-data">
                <?= $this->Form->select('priority',
                    [1 => '1', 2 => '2', 3 => '3', 4 => '4', 5 => '5', 6 => '6', 7 => '7', 8 => '8', 9 => '9', 10 => '10'],
                    ['default' => $newsData->priority ?? 10]) ?>
            </div>
        </div>
    </div>
    <div class="input__row">
        <div class="input__content input__content--one-line input__button-area">
            <?php if ($isInsert) { ?>
                <button type="submit" class="list__button alert__button--insert">
                    <?= REGISTER ?>
                </button>
            <?php } else { ?>
                <button type="submit" class="list__button alert__button--update">
                    <?= UPDATE ?>
                </button>
            <?php } ?>
        </div>
    </div>
    <?= $this->Form->end() ?>
</div>