<!-- 検索、カテゴリ欄、ページング -->
<?= $this->element('tablet_search_area') ?>

<!-- 商品リスト -->
<div class="row">
    <div id="orderlist-view">
    </div>
</div>
<div class="content__margin"></div>

<div class="fixed-bottom footer">
    <div class="footer__buttons">
        <?=
        $this->Html->link("発注する", [
            'controller' => 'OrderConfirm',
            'action' => 'index',
        ], ["class" => "btn btn-danger"])
        ?>
    </div>
</div>

<?php
// 納品日リストの初期値をjson形式で設定する

// データ更新用に、商品データをオブジェクト形式に設定する
$start = true;
$deliveryDate = "{";
foreach ($deliveryDateList as $key => $date) {
    if (!$start) $deliveryDate .= ',';
    $start = false;
    $deliveryDate .= '"' . $key . '":"' . $date . '"';
}
$deliveryDate .= "}";
?>

<?php if (isset($ajaxUrlCheckOrder) && $ajaxUrlCheckOrder !== "") echo $this->Form->hidden('ajaxUrlCheckOrder', ['value' => $ajaxUrlCheckOrder]); ?>

<script type="application/json" class="product-data"
        id="json-delivery-date"><?= $deliveryDate ?></script>

<?php // javascript読み込み ?>
<?= $this->Html->script('order-list.js', ['block' => 'script']) ?>
