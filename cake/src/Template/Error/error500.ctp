<?php

use App\Controller\Component\CommonComponent;

$this->layout = 'login';
$pageName = 'エラー';
$companyCode = CommonComponent::getCompanyCodeFromUrl();
$title = '';
$backController = USER_LOGIN_PAGE;
if (isset($controllerName) && $controllerName == BIZ_LOGIN_PAGE) $backController = BIZ_LOGIN_PAGE;
if(isset($loginType) && ($loginType == FLAG_BIZ_LOGIN
    || $loginType == FLAG_BIZ_SUB_LOGIN)) {
        $backController = BIZ_LOGIN_PAGE;
}
$this->set(compact('pageName', 'companyCode', 'title'));
?>
<!-- 発注画面のエラー表示 -->
<div class="row">
    <div class="error">
        <div class="message error">
            500 error : 対象のページが存在しません。
        </div>
        <?=
        $this->Html->link(BACK_LOGIN, [
            'controller' => $backController,
            'action' => 'logout',
        ], ["class" => "error__back-button"])
        ?>
    </div>
</div>
