<?php
/**
 * アカウント申請画面のテンプレート
 */
?>
<div class="row">
    <?php /* CSRF対策の為 Form helper を使用してテキストボックスを作成（以下ユーザーコントロールは同様に作成）*/ ?>
    <?= $this->Form->create(null, [
        'url' => ['action' => 'do_register'],
        'class' => 'input',
    ])
    ?>
    <div class="input__content">
        <div class="input__content--text">
            必要事項をご記入いただき、<BR>
            「お申し込み」ボタンを押してください
        </div>
        <?= $this->Flash->render() ?>
    </div>
    <div class="input__content">
        <div class="input__content--title input__content--register">取引先番号<?php if($companyCode == "001") echo "(6桁)"; ?>
            <div class="required">(必須)</div>
            ：
        </div>
        <div class="input__content--data">
            <?= $this->Form->text("customer_code", [
                "class" => "validate[required, custom[onlyAlphabetNumber]]",
                'maxlength' => '10',
                'placeholder' => '取引先番号(必須)',
            ]) ?>
        </div>
    </div>
    <div class="input__content">
        <div class="input__content--title input__content--register"><?= $secureCodeName ?><?php if($companyCode == "001") echo "(8桁)"; ?>
            <div class="required">(必須)</div>
            ：
        </div>
        <div class="input__content--data">
            <?= $this->Form->text("delivery_note_number", [
                "class" => "validate[required, custom[onlyAlphabetNumber]]",
                'maxlength' => '11',
                'placeholder' =>  $secureCodeName . '(必須)',
            ]) ?>
        </div>
    </div>
    <div class="input__content">
        <div class="input__content--title input__content--register">ご利用者様名
            <div class="required">(必須)</div>
            ：
        </div>
        <div class="input__content--data">
            <?= $this->Form->text("user_name", [
                "class" => "validate[required, maxSize[50]]",
                'maxlength' => '50',
                'placeholder' => 'ご利用者様名(必須)',
            ]) ?>
        </div>
    </div>
    <div class="input__content">
        <div class="input__content--title input__content--register">ご希望ログインID
            <div class="required">(必須)</div>
            ：
        </div>
        <div class="input__content--data">
            <?= $this->Form->text("login_id", [
                "class" => "validate[required, maxSize[20], custom[onlyAlphabetNumberSymbol]]",
                'maxlength' => '20',
                'placeholder' => 'ご希望ログインID(必須)',
            ]) ?>
        </div>
    </div>
    <div class="input__content">
        <div class="input__content--title input__content--register">メールアドレス
            <div class="required">(必須)</div>
            ：
        </div>
        <div class="input__content--data">
            <?= $this->Form->text("mail_address", [
                "class" => "validate[required, maxSize[200], custom[email]]",
                'maxlength' => '200',
                'placeholder' => 'メールアドレス(必須)',
            ]) ?>
        </div>
    </div>

    <div class="input__button input__button--area">
        <?= $this->Form->button('お申し込み', ['name' => 'register', 'type' => 'submit', 'class' => 'input__button--submit']) ?>
    </div>

    <div class="input__link">
        <a href="<?= $ajaxUrl = $this->Url->build(["controller" => "login", "action" => "index",]); ?>"
           class="login-link__regester">
            ログインページへ戻る
        </a>
    </div>

    <?= $this->Form->end() ?>
</div>
<?php // javascript読み込み ?>
<?= $this->Html->script('register.js', ['block' => 'script']) ?>
