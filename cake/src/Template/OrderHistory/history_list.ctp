<?php

// リストが空の場合、メッセージを表示
if (count($dataList) === 0) {
    ?>
    <div class="message error">該当するデータが存在しません。</div>
    <?php
} else {
    ?>

    <?php
    // タブレット用ページネーション
    if (!$isMobile) {
        echo $this->element('pagination');
    }
    ?>
    <table class="history-list-area">
        <thead>
        <tr>
            <th>
                <?php if ($isMobile) { ?>
                    詳細
                <?php } ?>
            </th>
            <th>発注日時</th>
            <th>伝票番号</th>
            <?php if ($useOrderCode || $loginType == FLAG_BIZ_LOGIN || $loginType == FLAG_BIZ_SUB_LOGIN) { ?>
                <th>取引先名</th>
            <?php } ?>
            <th>担当者</th>
            <?php // 営業マンでログインしている場合のみ表示 ?>
            <?php if ($loginType == FLAG_BIZ_LOGIN || $loginType == FLAG_BIZ_SUB_LOGIN) { ?>
                <th>営業担当者</th>
            <?php } ?>
            <th>件数</th>
            <th>状態</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($dataList as $data): ?>
            <tr class="<?php if ($data['other_user'] == 1) echo "history-list-area__background--other-user" ?>">
                <td>
                    <?php if ($isMobile) { ?>
                        <?=
                        $this->Html->link("<i class=\"fas fa-file-alt\"></i>", [
                            'action' => 'detail',
                            $data['slip_no'] ?? "", // パラメータ
                        ], ["class" => "history-list-area__detail", "escape" => false])
                        ?>
                    <?php } else { ?>
                        <?=
                        $this->Html->link(DETAIL, [
                            'action' => 'detail',
                            $data['slip_no'] ?? "", // パラメータ
                        ], ["class" => "history-list-area__button"])
                        ?>
                    <?php } ?>
                </td>
                <td><?= $data['date'] ?? "" ?></td>
                <td><?= $data['slip_no'] ?? "" ?></td>
                <?php if ($useOrderCode || $loginType == FLAG_BIZ_LOGIN || $loginType == FLAG_BIZ_SUB_LOGIN) { ?>
                    <td style="text-align: left;"><?= $data['customer_name'] ?? "" ?><?php
                        if($useOrderCode && $loginType == FLAG_USER_LOGIN) {
                            echo $data['department_name'] != "" ? '（' . $data['department_name'] . '）' : "";
                        }
                        ?><?php
                        if($data['is_catalog'] == FLAG_ON) {
                            echo ' (ｶﾀﾛｸﾞ)';
                        } ?>
                    </td>
                <?php } ?>
                <td><?= $data['user_name'] ?? "" ?></td>
                <?php // 営業マンでログインしている場合のみ表示 ?>
                <?php if ($loginType == FLAG_BIZ_LOGIN || $loginType == FLAG_BIZ_SUB_LOGIN) { ?>
                    <td><?= $data['biz_name'] ?? "" ?></td>
                <?php } ?>
                <td><?= $data['detail_num'] ?? "" ?></td>
                <td><?php if ($data['is_downloaded'] == "1") {
                        echo "受理";
                    } ?>
                </td>
            </tr>
            <?php //} ?>
        <?php endforeach; ?>
        </tbody>
    </table>

    <?php
    // タブレット用ページネーション
    if (!$isMobile) {
        echo $this->element('pagination');
    }
    ?>
<?php } ?>
