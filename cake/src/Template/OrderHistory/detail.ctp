<div class="row">
    <?=
    $this->Html->link("戻る", [
        'action' => 'index',
    ], ["class" => "content__back-button"])
    ?>
</div>

<?php /* csrfキー取得のため、空のフォームを作成 */ ?>
<?= $this->Form->create(null, ['url' => ['action' => 'index',]]) ?>
<?= $this->Form->end() ?>

<?php /* Ajax通信を行う場合のURL */ ?>
<?php if (isset($ajaxDetailUrl) && $ajaxDetailUrl !== "") echo $this->Form->hidden('ajaxDetailUrl', ['value' => $ajaxDetailUrl]); ?>
<?php if (isset($orderHistoryUrl) && $orderHistoryUrl !== "") echo $this->Form->hidden('orderHistoryUrl', ['value' => $orderHistoryUrl]); ?>
<?= $this->Form->hidden('slipNo', ['value' => $slipNo]) ?>

<!-- 発注履歴 -->
<div class="row">
    <div class="history">

        <div class="tab-content history-detail">
            <div class="history-detail-select" id="history-detail-select">

                <div class="history-detail-select__prev">
                    <?=
                    $this->Html->link('', [
                        'action' => 'detail',
                    ], [
                        "class" => "fas fa-arrow-alt-circle-left detail-prev",
                        "value" => "",
                        'style'=>'display:none;'
                    ])
                    ?>
                </div>

                <div class="history-detail-select__info">
                    <div class="history-detail-select__param">
                        <div class="history-detail-select__param--title">伝票番号：</div>
                        <div class="history-detail-select__param--value detail-slip-no"></div>
                    </div>
                    <div class="history-detail-select__param">
                        <div class="history-detail-select__param--title">発注日：</div>
                        <div class="history-detail-select__param--value detail-order-date"></div>
                    </div>
                    <div class="history-detail-select__param">
                        <div class="history-detail-select__param--title">取引先：</div>
                        <div class="history-detail-select__param--value detail-order-customer"></div>
                    </div>
                    <div class="history-detail-select__param">
                        <?php // 1: "担当者：", 2: "営業担当者" ?>
                        <div class="history-detail-select__param--title detail-order-user-type"></div>
                        <div class="history-detail-select__param--value detail-order-tantou"></div>
                    </div>
                    <?php
                    if ($isViewPrice) {
                    ?>
                        <div class="history-detail-select__param">
                            <div class="history-detail-select__param--title">合計金額：</div>
                            <div class="history-detail-select__param--value detail-order-total-price"></div>
                            <div class="history-detail-select__notice" style='display:none'>※参考価格</div>
                        </div>
                    <?php } ?>

                </div>

                <div class="history-detail-select__next">
                    <?=
                    $this->Html->link('', [
                        'action' => 'detail',
                    ], [
                        "class" => "fas fa-arrow-alt-circle-right detail-next",
                        "value" => "",
                        'style'=>'display:none;'
                    ])
                    ?>
                </div>
            </div>
        </div>

        <div class="history-detail__data">
            <div class="history-detail__data--separator">
                発注内容
            </div>
            <?php if ($isMobile) { ?>
                <?php //ページネーション ?>
                <div class="searchbar">
                    <?= $this->element('selector_pagination') ?>
                </div>
            <?php } ?>

            <div id="list-template">
            </div>

            <?php if ($isMobile) { ?>
                <?php //ページネーション ?>
                <div class="searchbar">
                    <?= $this->element('selector_pagination') ?>
                </div>
            <?php } ?>

        </div>

        <div class="history-detail__inquiry" style="display: none;">
            <div class="history-detail__inquiry--title">お問い合わせ内容</div>
            <div class="history-detail__inquiry--value detail-bikou"></div>
        </div>
    </div>
</div>

<div class="row">
    <?=
    $this->Html->link("戻る", [
        'action' => 'index',
    ], ["class" => "content__back-button"])
    ?>
</div>
<?php /* 専用のjsを読み込み */ ?>
<?= $this->Html->script('order-history-details.js', ['block' => 'script']) ?>
