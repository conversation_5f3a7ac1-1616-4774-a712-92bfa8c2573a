<?php
// タブレット/PC用ページネーション
if (!$isMobile) {
    echo $this->element('pagination');
}
?>

<div class="history-detail__data--table">
    <table>
        <thead>
        <tr>
            <?php if ($isViewPicture) { ?>
                <th></th>
            <?php } ?>
            <th width="300px">品名</th>
            <th>売上伝票日</th>
            <th>発注数量</th>
            <th>規格</th>
            <th>入数</th>
            <?php if ($isViewPrice) { ?>
                <th>単価</th>
                <th>消費税</th>
                <th>小計</th>
            <?php } ?>
            <th>備考</th>
        </tr>
        </thead>
        <tbody>
        <?php
        $i = 0;
        foreach ($dataList as $data):
            $i++;
            ?>
            <tr>
                <?php if ($isViewPicture) { ?>
                    <td class="history-detail__data--image" data-code="<?= $data["product_code"] ?>">
                        <?php if (isset($data['order_num']) && $data['order_num'] != '0') { ?>
                            <?php
                            // 画像が存在するか確認
                            $imagePath = WWW_ROOT . '/img/' . $companyCode . '/' . $data["product_code"] . '_1';
                            if (file_exists($imagePath . '.jpg')) {
                                $imagePath = $this->Url->image($companyCode . '/' . $data["product_code"] . '_1.jpg');
                            } else if (file_exists($imagePath . '.JPG')) {
                                $imagePath = $this->Url->image($companyCode . '/' . $data["product_code"] . '_1.JPG');
                            } else {
                                $imagePath = $this->Url->image('dummy.png');
                            }
                            ?>
                            <img src="<?= $imagePath ?>" alt="<?= $data["product_name"] ?>">
                        <?php } ?>
                    </td>
                <?php } ?>
                <td class="history-detail__data--name"
                    data-label="品名"><?= $this->Replace->trimSpace($data['product_name'] ?? "") ?></td>

                <td class="history-detail__data--sales-slip-date" data-label="売上伝票日">
                    <?php
                    if (isset($data['sales_slip_date'])) {
                        echo substr_replace(substr_replace($data['sales_slip_date'], '/', 6, 0), '/', 4, 0);
                        if ($data['slip_type'] == '1' || $data['slip_type'] == '2') {
                            echo " (確)";
                        }
                    } else {
                        echo "&ndash;";
                    }
                    ?>
                </td>
                <td class="history-detail__data--order-num" data-label="数量">
                    <?php
                    // 少数点以下が存在する場合のみ小数点表記とする
                    if (isset($data['order_num']) && $data['order_num'] != '0') {
                        $orderNum = $data['order_num'];
                        if ((int)$orderNum == (float)$orderNum) {
                            echo (int)$orderNum;
                        } else {
                            echo $orderNum;
                        }
                        echo $this->Replace->trimSpace($data['unit_name'] ?? "");
                    } else {
                        echo "&ndash;";
                    }
                    ?>
                </td>
                <td class="history-detail__data--kikaku" data-label="規格">
                    <?= isset($data['product_standard']) && $data['product_standard'] != '' ? $this->Replace->trimSpace($data['product_standard']) : "&ndash;"; ?>
                </td>
                <td class="history-detail__data--irisuu" data-label="入数">
                    <?php
                    // 少数点以下が存在する場合のみ小数点表記とする
                    if (isset($data['in_numbers']) && $data['in_numbers'] !== '') {
                        if ((int)$data['in_numbers'] == (float)$data['in_numbers']) {
                            echo (int)$data['in_numbers'];
                        } else {
                            echo $data['in_numbers'];
                        }
                    } else {
                        echo "&ndash;";
                    }
                    ?>
                </td>
                <?php if ($isViewPrice) { ?>
                    <td class="history-detail__data--price" data-label="単価">
                        <?php
                        // 少数点以下が存在する場合のみ小数点表記とする
                        if (isset($data['price']) && $data['price'] != '0') {
                            echo number_format($data['price']) . " 円";

                            // 税込税抜表示
                            $taxText = "";
                            switch ($data['tax_type']) {
                                case '1':
                                    $taxText = TEXT_NO_TAX;
                                    break;
                                case '2':
                                    $taxText = TEXT_IN_TAX;
                                    break;
                                case '3':
                                    $taxText = TEXT_TAX_FREE;
                                    break;
                            }
                            echo " " . $taxText;
                        } else {
                            echo "&ndash;";
                        }
                        ?>
                    </td>
                    <td class="history-detail__data--tax" data-label="消費税">
                        <?= isset($data['order_tax']) && $data['order_tax'] != '0' ? number_format($data['order_tax']) . "円" . "（" . $data['tax_rate'] . "％）" : "&ndash;"; ?>
                    </td>
                    <td>
                        <?= isset($data['sub_total']) && $data['sub_total'] != '0' ? number_format($data['sub_total']) . "円" : "&ndash;"; ?>
                    </td>
                <?php } ?>
                <td><?= $data['bikou'] ?? "" ?></td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>

    <?php
    // タブレット/PC用ページネーション
    if (!$isMobile) {
        echo $this->element('pagination');
    }
    ?>
</div>