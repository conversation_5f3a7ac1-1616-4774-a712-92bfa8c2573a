<?php
// タブレット/PC用ページネーション
if (!$isMobile) {
    echo $this->element('pagination');
}
?>

<div class="history-detail__data--table">
    <table>
        <thead>
        <tr>
            <?php if ($isViewPicture) { ?>
                <th></th>
            <?php } ?>
            <th width="300px">品名</th>
            <th>納品予定日</th>
            <th>発注数量</th>
            <th>規格</th>
            <th>入数</th>
            <?php
            if ($isViewPrice) {
                ?>
                <th>単価</th>
                <?php
            }
            ?>
            <th>備考</th>
        </tr>
        </thead>
        <tbody>
        <?php
        $i = 0;
        foreach ($dataList as $data):
            $i++;
            ?>
            <tr>
                <?php if ($isViewPicture) { ?>
                    <td class="history-detail__data--image">
                        <div class="history-detail__data--image-area">
                            <?php
                            // 画像が存在するか確認
                            $imagePath = WWW_ROOT . '/img/' . $companyCode . '/' . $data["management_product_code"] . '_1';
                            if (file_exists($imagePath . '.jpg')) {
                                $imagePath = $this->Url->image($companyCode . '/' . $data["management_product_code"] . '_1.jpg');
                            } else if (file_exists($imagePath . '.JPG')) {
                                $imagePath = $this->Url->image($companyCode . '/' . $data["management_product_code"] . '_1.JPG');
                            } else {
                                $imagePath = $this->Url->image('dummy.png');
                            }
                            ?>
                            <img src="<?= $imagePath ?>" alt="<?= $data["product_name"] ?>">
                        </div>
                    </td>
                <?php } ?>
                <td class="history-detail__data--name"
                    data-label="品名"><?= $this->Replace->trimSpace($data['product_name'] ?? "") ?></td>
                    <td class="history-detail__data--nouhinbi" data-label="納品予定日">
                    <?php
                    // 日付文字列に/を挿入する
                    if (isset($data['delivery_date'])) {
                        echo substr_replace(
                            substr_replace($data['delivery_date'], '/', 6, 0),
                            '/', 4, 0);
                    }
                    ?>
                </td>
                <td class="history-detail__data--order-num" data-label="数量">
                    <?php
                    if (isset($data['order_num'])) {
                        if ($data['order_num'] != 0) {
                            echo \App\Controller\Component\CommonComponent::setPriceFormat($data['order_num']);
                        } else {
                            echo "&ndash;";
                        }
                    }
                    ?>
                    <?= $this->Replace->trimSpace($data['unit_name'] ?? "") ?>
                </td>
                <td class="history-detail__data--code" data-label="商品CD"></td>
                <td class="history-detail__data--kikaku" data-label="規格"><?= $this->Replace->trimSpace($data['standard'] ?? "") ?></td>
                <td class="history-detail__data--irisuu" data-label="入数">
                    <?php
                    if (isset($data['in_numbers'])) {
                        if ($data['in_numbers'] != 0) {
                            echo \App\Controller\Component\CommonComponent::setPriceFormat($data['in_numbers']);
                        } else {
                            echo "&ndash;";
                        }
                    }
                    ?>
                </td>
                <?php
                if ($isViewPrice) {
                    ?>
                    <td class="history-detail__data--price" data-label="単価">
                        <?php
                        // 少数点以下が存在する場合のみ小数点表記とする
                        if (isset($data['price'])) {
                            if ($data['price'] != 0) {
                                echo \App\Controller\Component\CommonComponent::setPriceFormat($data['price']) . " 円";

                                // 税込税抜表示
                                $taxText = "";
                                switch ($data['tax_type']) {
                                    case '1':
                                        $taxText = TEXT_NO_TAX;
                                        break;
                                    case '2':
                                        $taxText = TEXT_IN_TAX;
                                        break;
                                    case '3':
                                        $taxText = TEXT_TAX_FREE;
                                        break;
                                }
                                echo " " . $taxText;
                            } else {
                                echo "&ndash;";
                            }
                        }
                        ?>
                    </td>
                    <?php
                }
                ?>
                <td class="history-detail__data--bikou" data-label="備考">
                    <?= $data['bikou'] ?? "" ?>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>

    <?php
    // タブレット/PC用ページネーション
    if (!$isMobile) {
        echo $this->element('pagination');
    }
    ?>
</div>
