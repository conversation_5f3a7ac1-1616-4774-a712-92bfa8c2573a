<?php

// リストが空の場合、メッセージを表示
if (count($dataList) === 0) {
    ?>
    <div class="message error">該当するデータが存在しません。</div>
    <?php
} else {
    ?>

    <?php
    // タブレット用ページネーション
    if (!$isMobile) {
        echo $this->element('pagination');
    }
    ?>
    <table class="history-list-area">
        <thead>
        <tr>
            <th>
            </th>
            <th>納品日</th>
            <th>納品書番号</th>
            <?php if ($useOrderCode) { ?>
                <th>取引先名</th>
            <?php } ?>
            <th>明細件数</th>
            <?php if ($isViewPrice) { ?>
                <th>納品金額</th>
                <th>消費税</th>
                <th>合計金額</th>
            <?php } ?>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($dataList as $data): ?>
            <tr>
                <td>
                    <?=
                    $this->Html->link(DETAIL, [
                        'action' => 'delivery_detail',
                        $data['delivery_note_number'] ?? "", // パラメータ
                    ], ["class" => "history-list-area__button"])
                    ?>
                </td>
                <td><?= isset($data['delivery_slip_date']) ? substr_replace(substr_replace($data['delivery_slip_date'], '/', 6, 0), '/', 4, 0) : "&ndash;"; ?></td>
                <td><?= isset($data['delivery_note_number']) ? $data['delivery_note_number'] : "&ndash;"; ?></td>
                <?php if ($useOrderCode) { ?>
                    <td style="text-align: left;"><?= $data['customer_name'] ?? "" ?> <?=
                        $data['customer_department_name'] != "" ? '(' . $data['customer_department_name'] . ')' : ""
                        ?>
                    </td>
                <?php } ?>
                <td><?= isset($data['detail_number']) && $data['detail_number'] != '0' ? $data['detail_number'] : "&ndash;"; ?></td>
                <?php if ($isViewPrice) { ?>
                    <td><?= isset($data['order_amount']) && $data['order_amount'] != '0' ? number_format($data['order_amount']) . " 円" : "&ndash;"; ?></td>
                    <td><?= isset($data['order_tax']) && $data['order_tax'] != '0' ? number_format($data['order_tax']) . " 円" : "&ndash;"; ?></td>
                    <td><?= isset($data['total_price']) && $data['total_price'] != '0' ? number_format($data['total_price']) . " 円" : "&ndash;"; ?></td>
                <?php } ?>
            </tr>
            <?php //} ?>
        <?php endforeach; ?>
        </tbody>
    </table>

    <?php
    // タブレット用ページネーション
    if (!$isMobile) {
        echo $this->element('pagination');
    }
    ?>
<?php } ?>