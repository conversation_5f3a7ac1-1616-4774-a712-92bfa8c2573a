<?php
/**
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @since         0.10.0
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 */
?>
<?= $customerName ?><?= "\n" ?>
<?= $userName ?>　様

平素は、格別のお引き立てを 賜りまして 誠にありがとうございます。
お客様よりお申込みいただいておりました、 <?= $systemName ?> のログイン情報につきまして、
下記の通り、ログイン情報を再送付させて頂きます。
（仮パスワード有効期限： <?= $expireDate ?>）

下記のログインID/仮パスワードでログイン後、本パスワードをご登録ください。

ログインID： <?= $loginId ?><?= "\n" ?>
仮パスワード： <?= $tmpPassword ?><?= "\n" ?>
URL: <?= $loginUrl ?><?= "\n" ?>

上記、URLをクリックすることで弊社Webサイトに接続されます。
接続後、お気に入り等に追加していただくと次回からのご利用の際便利です。
※お気に入り・ブックマーク・ショートカット等に追加される場合は、必ずログイン画面を登録して
いただきますようお願いいたします。

当メールの送信アドレスは送信専用となっております。
ご質問等がございましたら、弊社営業担当までお問合せいただきますようお願い申し上げます。

以上

*************************************
<?= $companyName ?><?= "\n" ?>
〒<?= $companyPostcode ?><?= "\n" ?>
<?php if ($companyAddress1 != '') : ?>
<?= $companyAddress1 ?><?= "\n" ?>
<?php endif; ?>
<?php if ($companyAddress2 != '') : ?>
<?= $companyAddress2 ?><?= "\n" ?>
<?php endif; ?>
<?php if ($companyAddress3 != '') : ?>
<?= $companyAddress3 ?><?= "\n" ?>
<?php endif; ?>
TEL:<?= $companyTelNo ?><?php if($companyFaxNo != "") { ?>　　FAX:<?= $companyFaxNo ?><?php } ?><?= "\n" ?>
*************************************