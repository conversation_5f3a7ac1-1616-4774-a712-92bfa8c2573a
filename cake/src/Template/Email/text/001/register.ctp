<?php
/**
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @since         0.10.0
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 */
?>
<?= $customerName ?><?= "\n" ?>
<?= $userName ?>　様	

平素は、格別のお引き立てを賜りまして誠にありがとうございます。
また、この度は弊社発注システム <?= $systemName ?> のお申込みを頂き、誠にありがとうございます。

下記のとおり、アカウントの登録申請を受け付けました。
3営業日以内にアカウント開通のお知らせが届かない場合は担当営業者までご連絡ください。

ご利用者様名: <?= $userName ?><?= "\n" ?>
ご希望ログインID：<?= $loginId ?><?= "\n" ?>
ご登録メールアドレス： <?= $mailAddress ?><?= "\n" ?>

当メールの送信アドレスは送信専用となっております。
ご質問等がございましたら、弊社営業担当までお問合せいただきますようお願い申し上げます。

以上

*************************************
<?= $companyName ?><?= "\n" ?>
〒<?= $companyPostcode ?><?= "\n" ?>
<?php if ($companyAddress1 != '') : ?>
<?= $companyAddress1 ?><?= "\n" ?>
<?php endif; ?>
<?php if ($companyAddress2 != '') : ?>
<?= $companyAddress2 ?><?= "\n" ?>
<?php endif; ?>
<?php if ($companyAddress3 != '') : ?>
<?= $companyAddress3 ?><?= "\n" ?>
<?php endif; ?>
TEL:<?= $companyTelNo ?><?php if($companyFaxNo != "") { ?>　　FAX:<?= $companyFaxNo ?><?php } ?><?= "\n" ?>
*************************************