<?php
/**
 * 発注画面ページングエリアテンプレート
 */
?>
<div class="selector">
    <div class="selector-limit">
        <div class="selector-limit__title">表示件数</div>
        <div class="selector-limit__block">
            <select name='selectListNum' class="selector-limit__select">
                <?php
                foreach ($viewNumList as $date) {
                    ?>
                    <option value="<?= $date ?>" class="selector-limit__select--item">
                        <?= $date ?>
                    </option>
                    <?php
                }
                ?>
            </select>
        </div>
    </div><!-- /.selector-limit -->

    <div class="selector-nouhinbi">
        <div class="selector-nouhinbi__title">納品予定日</div>
        <div class="selector-nouhinbi__block">
            <select class="selector-nouhinbi__select">
                <?php
                foreach ($deliveryDateList as $date) {
                    ?>
                    <option value="<?= $date ?>" class="selector-nouhinbi__select--item">
                        <?= $date ?>
                    </option>
                    <?php
                }
                ?>
            </select>
            <button type="button" class="selector-nouhinbi__button">
                一括変更
            </button>
        </div>
    </div><!-- /.selector-nouhinbi -->

    <?php //ページネーション ?>
    <?php
    if (!$isMobile) {
        // タブレット用
        echo $this->element('pagination');
    } else {
        // スマホ用
        echo $this->element('selector_pagination');
    }
    ?>
</div>
