<?php
/*
 * セレクトボックス式ページネーションのテンプレート
 */
?>
<div class="searchbar-pagination">
    <div class="searchbar-pagination__title">ページ切替</div>
    <div class="searchbar-pagination__block">
        <button class="searchbar-pagination__prev" disabled>
            前へ
        </button>
        <select class="searchbar-pagination__select">
            <?php
            for ($i = 1; $i <= 1; $i++) {
                ?>
                <option value="<?= $i ?>" class="searchbar-pagination__select--item">
                    <?= $i ?>
                </option>
                <?php
            }
            ?>
        </select>
        <div class="searchbar-pagination__separator">
            /
        </div>
        <div class="searchbar-pagination__total-page">
            10
        </div>
        <button class="searchbar-pagination__next">
            次へ
        </button>
    </div>
</div><!-- /.searchbar-pagination -->