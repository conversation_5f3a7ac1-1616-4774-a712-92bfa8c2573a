<?php
/**
 * 発注画面 表示形式選択ボタン
 */
?>
<?php if ($enablePicture == '1') { ?>
    <div class="searchbar-view">
        <?= $this->Form->hidden('viewMode', ['value' => $viewMode]) ?>
        <?= $this->Form->hidden('enablePicture', ['value' => $enablePicture]) ?>
        <div class="dropdown">
            <button type="submit" class="dropdown-toggle searchbar-view__button"
                    id="dropdownMenuButton"
                    data-toggle="dropdown"
                    aria-haspopup="true"
                    aria-expanded="false">
                <i class="fas "></i>
            </button>

            <div class="dropdown-menu dropdown-menu-right searchbar-view__dropdown"
                 aria-labelledby="dropdownMenuButton">
                <div class="dropdown-item searchbar-view__dropdown-item" data-view="<?= VIEW_MODE_TILE ?>">
                    <i class="fas fa-th"></i>タイル表示
                </div>
                <div class="dropdown-item searchbar-view__dropdown-item" data-view="<?= VIEW_MODE_LIST ?>">
                    <i class="fas fa-list"></i>リスト表示
                </div>
                <div class="dropdown-item searchbar-view__dropdown-item" data-view="<?= VIEW_MODE_NO_IMG ?>">
                    <i class="fas fa-bars"></i>画像無し
                </div>
            </div><!-- /.dropdown-menu -->
        </div>
    </div>
<?php } else { ?>
    <?= $this->Form->hidden('viewMode', ['value' => $viewMode]) ?>
    <?= $this->Form->hidden('enablePicture', ['value' => $enablePicture]) ?>
<?php } ?>
