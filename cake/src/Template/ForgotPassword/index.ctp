<?php
/**
 * パスワード再発行（紛失時）画面のテンプレート
 */
?>
<div class="row">
    <?php /* CSRF対策の為 Form helper を使用してテキストボックスを作成（以下ユーザーコントロールは同様に作成）*/ ?>
    <?= $this->Form->create(null, [
        'url' => ['action' => 'set_password'],
        'class' => 'input',
    ])
    ?>
    <div class="input__content">
        <div class="input__content--text">
            パスワードの再発行を行います。<br>
            <span class="input__content--br">ご登録のIDとメールアドレスを入力し、</span>送信ボタンを押してください。
        </div>
    </div>

    <?= $this->Flash->render() ?>

    <div class="input__content">
        <div class="input__content--title input__content--forgot">ログインID
            <div class="required">(必須)</div>
            ：
        </div>
        <div class="input__content--data">
            <?= $this->Form->text("login_id", ["class" => "validate[required]", 'value' => $loginId ?? "" , 'placeholder'=>'ログインID']) ?>
        </div>
    </div>
    <div class="input__content">
        <div class="input__content--title input__content--forgot">メールアドレス
            <div class="required">(必須)</div>
            ：
        </div>
        <div class="input__content--data">
            <?= $this->Form->text("mail_address", ["class" => "validate[required, custom[email]]", 'value' => $mailAddress ?? "", 'placeholder'=>'メールアドレス']) ?>
        </div>
    </div>

    <div class="input__button input__button--area">
        <?= $this->Form->button(SEND, ['type' => 'submit', 'class' => 'input__button--submit']) ?>
    </div>


    <div class="input__link">
        <a href="<?= $ajaxUrl = $this->Url->build(["controller" => "login", "action" => "index",]); ?>"
           class="login-link__regester">
            ログインページへ戻る
        </a>
    </div>

    <?= $this->Form->end() ?>
</div>
