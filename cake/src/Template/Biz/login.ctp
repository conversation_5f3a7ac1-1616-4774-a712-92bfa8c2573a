<?php
/**
 * 営業マン機能ログイン画面
 */
?>
<div class="row">
    <?php /* CSRF対策の為 Form helper を使用してテキストボックスを作成（以下ユーザーコントロールは同様に作成）*/ ?>
    <?= $this->Form->create('biz_login', [
        'url' => ['action' => 'do_login'],
        'class' => 'input',
    ])
    ?>

    <?php
    if (!$newsData->isEmpty()) {
        ?>
        <!-- お知らせエリア -->
        <div class="input__news">
            <div class="input__news--head">
                重要なお知らせ
            </div>
            <?php foreach ($newsData as $data) { ?>
                <div class="input__news--title">
                    <?= $data->set_date . " : " . $data->title ?>
                </div>
                <div class="input__news--contents">
                    <?= $data->text ?>
                    <br>
                    <?php if (strlen($data->url) > 0) { ?>
                        <div class="link"><a href='<?= $data->url ?>' target="_blank">リンクはこちら <i class="fas fa-external-link-alt"></i></a></div>
                    <?php } ?>
                </div>
                <hr/>
            <?php } ?>
        </div>
        <?php
    }
    ?>

    <div class="input__content">
        <div class="input__content--text">
            <div class="login-mode__auto content__disable">
                「ログイン」ボタンを押してください
            </div>
            <div class="login-mode__manual content__disable">
                <span class="input__content--br">ログイン情報をご入力の上</span>「ログイン」ボタンを押してください
            </div>
        </div>
    </div>

    <div class="login-mode__manual input__login-area">
        <!-- ID入力欄 -->
        <div class="input__login">
            <div class="input__content input__content--mobile">
                <div class="input__content--title input__content--login">担当者コード：</div>
                <div class="input__content--data">
                    <?= $this->Form->text("biz_login_id", ["class" => "validate[required, custom[onlyAlphabetNumberSymbol]]", 'placeholder' => "担当者コード"]) ?>
                </div>
            </div>
            <div class="input__content input__content--mobile">
                <div class="input__content--title input__content--login">パスワード：</div>
                <div class="input__content--data">
                    <?= $this->Form->password("biz_password", ["class" => "login__password validate[required, custom[onlyAlphabetNumberSymbolAll]]", 'placeholder' => "パスワード"]) ?>
                </div>
            </div>
        </div>
    </div>

    <?= $this->Flash->render() ?>

    <div class="login-mode__manual input__button--area">
        <div class="input__button">
            <?= $this->Form->button(LOGIN, ['type' => 'submit', 'name' => 'login_button', 'class' => 'input__button--submit']) ?>
        </div>
        <div class="input__button">
            <?= $this->Form->button(CLEAR, ['type' => 'reset', 'class' => 'input__button--clear']) ?>
        </div>
    </div>

    <?= $this->Form->end() ?>
</div>
<?= $this->Html->script('businessman.js', ['block' => 'script']) ?>

