<?php
/**
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @since         0.10.0
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 */
?>
<!DOCTYPE html>
<html>
<head>
    <?= $this->Html->charset() ?>
    <title>
        <?= $this->fetch('title') ?>
    </title>

    <?php if ($companyCode == "001") { ?>
    <?= $this->Html->meta('icon', $this->Url->image('/../img/' . $companyCode . '/favicon.ico')) ?>
    <?php } else { ?>
        <?php
            // 企業別ファビコン(ブラウザタブ用)の存在チェック
            $companyTabIconPath = WWW_ROOT . 'img/' . $companyCode . '/' . 'favicon.ico';
            $iconChange = false;
            if (file_exists($companyTabIconPath)) {
                $companyTabIconPath = $this->Url->image('/../../img/' . $companyCode . '/' . 'favicon.ico');
                $iconChange = true;
            }
        ?>
        <?php if ($iconChange) { ?>
            <?= $this->Html->meta('icon', $this->Url->image($companyTabIconPath)) ?>
        <?php } else { ?>
            <?= $this->Html->meta('icon', $this->Url->image('/../img/favicon.ico')) ?>
        <?php } ?>
    <?php } ?>

    <?= $this->Html->css('base.css') ?>
    <?= $this->Html->css('style.css') ?>

    <?= $this->fetch('meta') ?>
    <?= $this->fetch('css') ?>
    <?= $this->fetch('script') ?>
</head>
<body>
    <div id="container">
        <div id="header">
            <h1><?= __('Error') ?></h1>
        </div>
        <div id="content">
            <?= $this->Flash->render() ?>

            <?= $this->fetch('content') ?>
        </div>
        <div id="footer">
            <?= $this->Html->link(__('Back'), 'javascript:history.back()') ?>
        </div>
    </div>
    <?= $this->Form->hidden('logoutUrl', ['value' => $logoutUrl]) ?>
</body>
</html>
