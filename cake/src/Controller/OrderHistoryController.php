<?php

namespace App\Controller;

use App\Controller\AppController;
use App\Controller\Component\CommonComponent;
use App\Service\OrderDeliveryResultsService;
use App\Service\OrderHistoryService;
use Cake\Http\Exception\NotFoundException;
use Cake\Routing\Router;
use App\Service\UserService;
use App\Service\BizService;

/**
 * OrderHistory Controller
 * 発注履歴
 */
class OrderHistoryController extends AppController
{
    /**
     * 初期化関数
     * @throws \Exception
     */
    public function initialize()
    {
        parent::initialize();

        // 1ページ表示件数を設定する
        $this->paginate['limit'] = CommonComponent::getCodeMaster('SYS', 'ORDER_HISTORY_NUM')['number1'];
    }

    // ===============================================================
    //
    // 発注履歴
    //
    // ===============================================================

    /**
     * 発注履歴ページ
     */
    public function index()
    {
        CommonComponent::outputUserLog('発注履歴:発注履歴タブ 画面表示');
        $title = TITLE;
        $pageName = TXT_ORDER_HISTORY;

        /* ===========  Ajaxでリストを取得するページに記載 ここから  =========== */

        // Ajax通信を行う場合の取得用URL
        $ajaxUrl = Router::url([
            "controller" => "order_history",
            "action" => "history_list"
        ]);


        /* ===========  Ajaxでリストを取得するページに記載 ここまで  =========== */

        /* ===========  事前認証が必要なページに記載 ここから  =========== */

        $this->setLoginInfo();

        /* ===========  事前認証が必要なページに記載 ここまで  =========== */

        //企業コードをセッションから取得する
        $companyCode = $this->getCompanyCode();

        // ログインタイプをセッションから取得する
        $loginType = $this->getLoginType();

        $orderListService = new OrderDeliveryResultsService();
        $enableDeliveryResult = $orderListService->checkEnableDeliveryResult();

        // 価格表示フラグをセット(表示/非表示のみ設定)
        $isViewPrice = $this->getIsViewPrice();

        // 請求書タブ表示フラグをセット
        $isViewInvoice = $orderListService->checkSlipType();

        $this->set(compact('title', 'pageName', 'ajaxUrl', 'companyCode', 'enableDeliveryResult', 'isViewPrice', 'isViewInvoice', 'loginType'));

        CommonComponent::outputUserLog('発注履歴:発注履歴タブ 画面表示完了');
    }

    /**
     * 発注履歴一覧を取得する
     */
    public function history_list()
    {
        if ($this->request->is('ajax')) {
            CommonComponent::outputUserLog('発注履歴 リスト取得開始');

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                return $this->ajaxReturnLoginError();
            }

            // セッション呼び出し
            $session = $this->request->getSession();
            $loginType = $session->read('sessionUserInfo.loginType');

            $this->set(compact('loginType'));

            // POSTデータを取得
            $param = $this->getQueryParameter();

            $orderListService = new OrderHistoryService($param);

            try {
                $data = $orderListService->getOrderHistory();
                $dataList = $this->paginate($data);
            } catch (NotFoundException $ex) {
                // 範囲外のページが指定された場合のエラー処理
                CommonComponent::outputUserLog('範囲外のページ指定', $ex->getMessage());
                // 最後のページを取得する処理
                return $this->getLastPage();
            }

            // ページング情報
            $paginateData = array_values($this->Paginator->getPagingParams())[0];
            $currentPage = $paginateData['page'];
            $totalPage = $paginateData['pageCount'];

            // ページング用URL
            $paginateUrl = Router::url([
                "controller" => "order_history",
                "action" => "history_list"
            ]);

            $status = "";
            if (count($dataList) === 0) {
                $status = "nodata";
            }

            // 発注コード利用取引先か？
            $useOrderCode = $this->isUseOrderCode();

            // 変数をセットしビューに渡す
            $this->set(compact(
                'dataList',
                'useOrderCode'
            ));

            // テンプレートを無効化
            $this->viewBuilder()->setLayout('');

            // レンダリングされたviewを取得
            $body = $this->render()->body();

            CommonComponent::outputUserLog('発注履歴 リスト取得完了');

            // json形式で整形して返す
            return $this->ajaxReturnResponse($body, $status, $paginateUrl, $currentPage, $totalPage);
        }

    }

    /**
     * 発注履歴の明細を表示する
     *
     * @param [string] $orderId
     */
    public function detail($slipNo = null)
    {
        CommonComponent::outputUserLog('発注履歴明細 画面表示');

        $title = TITLE;
        $pageName = TXT_ORDER_HISTORY . " 明細";

        /* ===========  Ajaxでリストを取得するページに記載 ここから  =========== */

        // Ajax通信を行う場合の取得用URL
        $ajaxUrl = Router::url([
            "controller" => "order_history",
            "action" => "detail_header"
        ]);

        // Ajax通信を行う場合の取得用URL(明細リスト用)
        $ajaxDetailUrl = Router::url([
            "controller" => "order_history",
            "action" => "detail_list"
        ]);

        /* ===========  Ajaxでリストを取得するページに記載 ここまで  =========== */

        /* ===========  事前認証が必要なページに記載 ここから  =========== */

        $this->setLoginInfo();

        /* ===========  事前認証が必要なページに記載 ここまで  =========== */

        // 発注履歴 取得用URL
        $orderHistoryUrl = Router::url([
            "controller" => "order_history",
            "action" => "index"
        ]);

        //企業コードをセッションから取得する
        $companyCode = $this->getCompanyCode();

        // 価格表示フラグをセット(表示/非表示のみ設定)
        $isViewPrice = $this->getIsViewPrice();

        $this->set(compact('title', 'pageName', 'ajaxUrl', 'ajaxDetailUrl', 'companyCode', 'slipNo', 'orderHistoryUrl', 'isViewPrice'));

        CommonComponent::outputUserLog('発注履歴明細 画面表示完了');
    }

    /**
     * 発注履歴(明細)ページのヘッダ情報を取得する
     *
     */
    public function detail_header()
    {
        if ($this->request->is('ajax')) {

            CommonComponent::outputUserLog('発注履歴明細 ヘッダ情報取得');

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                return $this->ajaxReturnLoginError();
            }

            // POSTデータを取得
            $param = $this->getQueryParameter();

            $orderListService = new OrderHistoryService($param);

            $header = $orderListService->getOrderHistoryHeader();

            if (!$header) {
                CommonComponent::outputUserLog('発注履歴明細 ヘッダ情報取得失敗');
                $header = [
                    'status' => 'error',
                ];
            }

            $this->disableAutoRender();

            CommonComponent::outputUserLog('発注履歴明細 ヘッダ情報取得完了');

            return $this->response
                ->withType('application/json')
                ->withStringBody(json_encode($header));
        }
    }

    /**
     * 発注履歴(明細)ページのデータを表示する
     *
     */
    public function detail_list()
    {
        if ($this->request->is('ajax')) {

            CommonComponent::outputUserLog('発注履歴明細 明細情報取得');

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                return $this->ajaxReturnLoginError();
            }

            // POSTデータを取得
            $param = $this->getQueryParameter();

            $orderListService = new OrderHistoryService($param);

            try {
                $data = $orderListService->getOrderHistoryItems();
                $dataList = $this->paginate($data);
            } catch (NotFoundException $ex) {
                // 範囲外のページが指定された場合のエラー処理
                CommonComponent::outputUserLog('範囲外のページ指定', $ex->getMessage());
                // 最後のページを取得する処理
                return $this->getLastPage();
            }

            // ページング情報
            $paginateData = array_values($this->Paginator->getPagingParams())[0];
            $currentPage = $paginateData['page'];
            $totalPage = $paginateData['pageCount'];

            // ページング用URL
            $paginateUrl = Router::url([
                "controller" => "order_history",
                "action" => "detail_list"
            ]);

            $status = "";
            if (count($dataList) === 0) {
                $status = "nodata";
            }

            // 価格表示フラグをセット(表示/非表示のみ設定)
            $isViewPrice = $this->getIsViewPrice();

            // 画像表示フラグをセット
            $session = $this->request->getSession();
            $viewMode = $session->read('sessionOrderList.viewMode');
            $isViewPicture = ($viewMode === VIEW_MODE_NO_IMG) ? false : true;

            $companyCode = $this->getCompanyCode();

            // 変数をセットしビューに渡す
            $this->set(compact(
                'dataList',
                'isViewPrice',
                'isViewPicture',
                'companyCode'
            ));

            // テンプレートを無効化
            $this->viewBuilder()->setLayout('');

            // レンダリングされたviewを取得
            $body = $this->render()->body();

            CommonComponent::outputUserLog('発注履歴明細 明細情報取得完了');

            // json形式で整形して返す
            return $this->ajaxReturnResponse($body, $status, $paginateUrl, $currentPage, $totalPage);
        }
    }

    // ===============================================================
    //
    // 納品実績
    //
    // ===============================================================

    /**
     * 納品実績ページ
     */
    public function delivery()
    {
        CommonComponent::outputUserLog('発注履歴:納品実績タブ 画面表示');
        $title = TITLE;
        $pageName = TXT_ORDER_HISTORY;

        /* ===========  Ajaxでリストを取得するページに記載 ここから  =========== */

        // Ajax通信を行う場合の取得用URL
        $ajaxUrl = Router::url([
            "controller" => "order_history",
            "action" => "delivery_list"
        ]);

        //納品実績一覧DL用のURL
        $downloadUrl = Router::url([
            "controller" => "order_history",
            "action" => "delivery_results_list_export"
        ]);

        /* ===========  Ajaxでリストを取得するページに記載 ここまで  =========== */

        /* ===========  事前認証が必要なページに記載 ここから  =========== */

        $this->setLoginInfo();

        /* ===========  事前認証が必要なページに記載 ここまで  =========== */

        //企業コードをセッションから取得する
        $companyCode = $this->getCompanyCode();

        // 価格表示フラグをセット(表示/非表示のみ設定)
        $isViewPrice = $this->getIsViewPrice();

        $this->set(compact('title', 'pageName', 'ajaxUrl', 'downloadUrl', 'companyCode', 'isViewPrice'));

        CommonComponent::outputUserLog('発注履歴:納品実績タブ 画面表示完了');
    }

    /**
     * 納品実績のリストを取得する
     */
    public function delivery_list()
    {
        if ($this->request->is('ajax')) {

            CommonComponent::outputUserLog('納品実績 リスト取得開始');

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                return $this->ajaxReturnLoginError();
            }

            // セッション呼び出し
            $session = $this->request->getSession();
            $loginType = $session->read('sessionUserInfo.loginType');

            // 価格表示フラグをセット(表示/非表示のみ設定)
            $isViewPrice = $this->getIsViewPrice();

            $this->set(compact('loginType', 'isViewPrice'));

            // POSTデータを取得
            $param = $this->getQueryParameter();

            $deliveryResultService = new OrderDeliveryResultsService();

            try {
                $data = $deliveryResultService->getDeliveryResults($param);
                $dataList = $this->paginate($data);
            } catch (NotFoundException $ex) {
                // 範囲外のページが指定された場合のエラー処理
                CommonComponent::outputUserLog('範囲外のページ指定', $ex->getMessage());
                // 最後のページを取得する処理
                return $this->getLastPage();
            }

            // ページング情報
            $paginateData = array_values($this->Paginator->getPagingParams())[0];
            $currentPage = $paginateData['page'];
            $totalPage = $paginateData['pageCount'];

            // ページング用URL
            $paginateUrl = Router::url([
                "controller" => "order_history",
                "action" => "delivery_list"
            ]);

            $status = "";
            if (count($dataList) === 0) {
                $status = "nodata";
            }

            // 発注コード利用取引先か？
            $useOrderCode = $this->isUseOrderCode();

            //消費税額計算方法を取得する
            $taxAmountCalcType = $deliveryResultService->getTaxAmountCalcType();

            // 変数をセットしビューに渡す
            $this->set(compact(
                'dataList',
                'taxAmountCalcType',
                'useOrderCode'
            ));

            // テンプレートを無効化
            $this->viewBuilder()->setLayout('');

            // レンダリングされたviewを取得
            $body = $this->render()->body();

            CommonComponent::outputUserLog('納品実績 リスト取得完了');

            // json形式で整形して返す
            return $this->ajaxReturnResponse($body, $status, $paginateUrl, $currentPage, $totalPage);
        }
    }

    /**
     * 納品実績（明細）ページのデータを表示する
     * @param null $deliveryNoteNumber
     */
    public function delivery_detail($deliveryNoteNumber = null)
    {
        CommonComponent::outputUserLog('納品実績明細 画面表示');

        $title = TITLE;
        $pageName = TXT_DELIVERY_RESULTS . " 明細";

        /* ===========  Ajaxでリストを取得するページに記載 ここから  =========== */

        // Ajax通信を行う場合の取得用URL
        $ajaxUrl = Router::url([
            "controller" => "order_history",
            "action" => "delivery_detail_header"
        ]);

        // Ajax通信を行う場合の取得用URL(明細リスト用)
        $ajaxDetailUrl = Router::url([
            "controller" => "order_history",
            "action" => "delivery_detail_list"
        ]);

        /* ===========  Ajaxでリストを取得するページに記載 ここまで  =========== */

        /* ===========  事前認証が必要なページに記載 ここから  =========== */

        $this->setLoginInfo();

        /* ===========  事前認証が必要なページに記載 ここまで  =========== */

        // 発注履歴 取得用URL
        $orderHistoryUrl = Router::url([
            "controller" => "order_history",
            "action" => "index"
        ]);

        //企業コードをセッションから取得する
        $companyCode = $this->getCompanyCode();

        // 価格表示フラグをセット(表示/非表示のみ設定)
        $isViewPrice = $this->getIsViewPrice();

        $this->set(compact('title', 'pageName', 'ajaxUrl', 'ajaxDetailUrl', 'companyCode', 'deliveryNoteNumber', 'orderHistoryUrl', 'isViewPrice'));

        CommonComponent::outputUserLog('納品実績明細 画面表示完了');
    }

    /**
     * 納品実績データのヘッダを表示する
     * @return \Cake\Http\Response
     */
    public function delivery_detail_header()
    {
        if ($this->request->is('ajax')) {
            CommonComponent::outputUserLog('納品実績明細 ヘッダ情報取得');

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                return $this->ajaxReturnLoginError();
            }

            // POSTデータを取得
            $param = $this->getQueryParameter();

            $deliveryResultsService = new OrderDeliveryResultsService();

            $header = $deliveryResultsService->getDeliveryResultHeader($param);

            if (!$header) {
                CommonComponent::outputUserLog('納品実績明細 ヘッダ情報取得失敗');
                $header = [
                    'status' => 'error',
                ];
            }

            $this->disableAutoRender();

            CommonComponent::outputUserLog('納品実績明細 ヘッダ情報取得完了');

            return $this->response
                ->withType('application/json')
                ->withStringBody(json_encode($header));
        }
    }

    /**
     * 納品実績(明細)ページのデータを表示する
     *
     */
    public function delivery_detail_list()
    {
        if ($this->request->is('ajax')) {
            CommonComponent::outputUserLog('納品実績明細 明細情報取得');

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                return $this->ajaxReturnLoginError();
            }

            // POSTデータを取得
            $param = $this->getQueryParameter();

            $deliveryResultService = new OrderDeliveryResultsService();

            try {
                $data = $deliveryResultService->getDeliveryResultItems($param);
                $dataList = $this->paginate($data);
            } catch (NotFoundException $ex) {
                // 範囲外のページが指定された場合のエラー処理
                CommonComponent::outputUserLog('範囲外のページ指定', $ex->getMessage());
                // 最後のページを取得する処理
                return $this->getLastPage();
            }

            // ページング情報
            $paginateData = array_values($this->Paginator->getPagingParams())[0];
            $currentPage = $paginateData['page'];
            $totalPage = $paginateData['pageCount'];

            // ページング用URL
            $paginateUrl = Router::url([
                "controller" => "order_history",
                "action" => "delivery_detail_list"
            ]);

            $status = "";
            if (count($dataList) === 0) {
                $status = "nodata";
            }

            // 価格表示フラグをセット(表示/非表示のみ設定)
            $isViewPrice = $this->getIsViewPrice();

            // 画像表示フラグをセット
            $session = $this->request->getSession();
            $viewMode = $session->read('sessionOrderList.viewMode');
            $isViewPicture = ($viewMode === VIEW_MODE_NO_IMG) ? false : true;

            $companyCode = $this->getCompanyCode();

            //消費税額計算方法を取得する
            $taxAmountCalcType = $deliveryResultService->getTaxAmountCalcType();

            // 変数をセットしビューに渡す
            $this->set(compact(
                'dataList',
                'isViewPrice',
                'isViewPicture',
                'companyCode',
                'taxAmountCalcType'
            ));

            // テンプレートを無効化
            $this->viewBuilder()->setLayout('');

            // レンダリングされたviewを取得
            $body = $this->render()->body();

            CommonComponent::outputUserLog('納品実績明細 明細情報取得完了');

            // json形式で整形して返す
            return $this->ajaxReturnResponse($body, $status, $paginateUrl, $currentPage, $totalPage);
        }
    }


    // ===============================================================
    //
    // 請求書一覧
    //
    // ===============================================================

    /**
     * 請求書ページ
     */
    public function invoice()
    {
        CommonComponent::outputUserLog('発注履歴:請求書タブ 画面表示');
        $title = TITLE;
        $pageName = TXT_ORDER_HISTORY;

        /* ===========  Ajaxでリストを取得するページに記載 ここから  =========== */

        // Ajax通信を行う場合の取得用URL
        $ajaxUrl = Router::url([
            "controller" => "order_history",
            "action" => "invoice_list"
        ]);

        /* ===========  Ajaxでリストを取得するページに記載 ここまで  =========== */

        /* ===========  事前認証が必要なページに記載 ここから  =========== */

        $this->setLoginInfo();

        /* ===========  事前認証が必要なページに記載 ここまで  =========== */

        //企業コードをセッションから取得する
        $companyCode = $this->getCompanyCode();

        $this->set(compact('title', 'pageName', 'ajaxUrl', 'companyCode'));

        CommonComponent::outputUserLog('発注履歴:請求書タブ 画面表示完了');
    }

    /**
     * 請求書のリストを取得する
     */
    public function invoice_list()
    {
        if ($this->request->is('ajax')) {

            CommonComponent::outputUserLog('発注履歴 リスト取得開始');

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                return $this->ajaxReturnLoginError();
            }

            // セッション呼び出し
            $session = $this->request->getSession();
            $loginType = $session->read('sessionUserInfo.loginType');

            $this->set(compact('loginType'));

            // POSTデータを取得
            $param = $this->getQueryParameter();

            $deliveryResultsService = new OrderDeliveryResultsService($param);

            try {
                $data = $deliveryResultsService->getOrderInvoice();
                //PDF情報取得
                $pdfList = $deliveryResultsService->getpdfList($data);
                $dataList = $this->paginate($data);
            } catch (NotFoundException $ex) {
                // 範囲外のページが指定された場合のエラー処理
                CommonComponent::outputUserLog('範囲外のページ指定', $ex->getMessage());
                // 最後のページを取得する処理
                return $this->getLastPage();
            }

            // ページング情報
            $paginateData = array_values($this->Paginator->getPagingParams())[0];
            $currentPage = $paginateData['page'];
            $totalPage = $paginateData['pageCount'];

            // ページング用URL
            $paginateUrl = Router::url([
                "controller" => "order_history",
                "action" => "invoice_list"
            ]);

            $status = "";
            if (count($dataList) === 0) {
                $status = "nodata";
            }

            // 発注コード利用取引先か？
            $useOrderCode = $this->isUseOrderCode();

            // 変数をセットしビューに渡す
            $this->set(compact(
                'dataList',
                'pdfList',
                'useOrderCode'
            ));

            // テンプレートを無効化
            $this->viewBuilder()->setLayout('');

            // レンダリングされたviewを取得
            $body = $this->render()->body();

            CommonComponent::outputUserLog('発注履歴 リスト取得完了');

            // json形式で整形して返す
            return $this->ajaxReturnResponse($body, $status, $paginateUrl, $currentPage, $totalPage);
        }
    }

    /**
     * 納品実績データのダウンロード（CSV）
     */
    public function delivery_results_list_export()
    {
        // POSTデータを取得
        $param = $this->getQueryParameter();

        $deliveryResultsService = new OrderDeliveryResultsService($param);
        $result = $deliveryResultsService->exportCsv($param);

        if (count($result) > 0) {
            //NG
            // メッセージをセット
            $this->setDBMessageToFlashError($result);
        }
        return $this->redirect(['action' => 'delivery']);
    }

    /**
     * 請求書データのダウンロード（CSV）
     * @param null $invoiceCutoffDate
     */
    public function invoice_list_export($invoiceCutoffDate = null, $customerCode)
    {
        CommonComponent::outputUserLog('請求書データダウンロード 開始 No:' . $invoiceCutoffDate);

        // POSTデータを取得
        $param['invoice_cutoff_date'] = $invoiceCutoffDate;

        $deliveryResultsService = new OrderDeliveryResultsService($param);
        $result = $deliveryResultsService->exportCsvInvoice($customerCode);

        if (count($result) > 0) {
            //NG
            // メッセージをセット
            $this->setDBMessageToFlashError($result);
        }

        CommonComponent::outputUserLog('請求書データダウンロード 終了');

        return $this->redirect(['action' => 'invoice']);
    }

    /**
     * 金額表示フラグを取得する
     */
    public function getIsViewPrice() {
        $userService = new UserService();
        $bizService = new BizService();

        // ログインタイプをセッションから取得する
        $loginType = $this->getLoginType();

        // 金額の表示フラグ
        if ($loginType == FLAG_USER_LOGIN) {
            // 一般ユーザ
            $isViewPrice = $userService->getTerminalPriceType();
        } else {
            // 営業マン
            $isViewPrice = $bizService->getTerminalPriceType();
        }

        return $isViewPrice;
    }
}
