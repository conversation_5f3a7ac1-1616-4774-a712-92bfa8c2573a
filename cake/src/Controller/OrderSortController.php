<?php

namespace App\Controller;

use App\Controller\AppController;
use App\Controller\Component\CommonComponent;
use App\Service\OrderHeaderService;
use App\Service\OrderListService;
use App\Service\OrderSortService;
use App\Service\UserService;
use App\Service\BizService;
use Cake\Routing\Router;

/**
 * OrderSort Controller
 * 並び替えのテストのための画面
 */
class OrderSortController extends AppController
{
    /**
     * 初期化関数
     * @throws \Exception
     */
    public function initialize()
    {
        parent::initialize();
    }

    public function index()
    {
        CommonComponent::outputUserLog('並べ替え 画面表示');
        $title = TITLE;
        $pageName = '並べ替え';

        /* ===========  Ajaxでリストを取得するページに記載 ここから  =========== */

        // Ajax通信を行う場合の取得用URL
        $sortUrl = Router::url([
            "controller" => "order_sort",
            "action" => "sort_list"
        ]);

        /* ===========  Ajaxでリストを取得するページに記載 ここまで  =========== */

        /* ===========  事前認証が必要なページに記載 ここから  =========== */

        $this->setLoginInfo();

        /* ===========  事前認証が必要なページに記載 ここまで  =========== */

        //企業コードをセッションから取得する
        $companyCode = $this->getCompanyCode();

        // オーダリスト取得処理
        $orderService = new OrderListService();
        // カテゴリ
        $categoryList = $orderService->getCategoryData();
        // 商品数
        $orderListNum = $orderService->getOrderListNum();

        $userService = new UserService();
        $enablePicture = $userService->getTerminalPictureType();
        // 画像有りの場合：viewTile、無しの場合viewNoImg
        if ($enablePicture == '1') {
            $viewMode = "viewTile";
        } else {
            $viewMode = "viewNoImg";
        }

        // セッション呼び出し
        $session = $this->request->getSession();
        $loginType = $session->read('sessionUserInfo.loginType');

        $bizService = new BizService();

        // 金額の表示フラグ
        if ($loginType == FLAG_USER_LOGIN) {
            // 一般ユーザ
            $priceType = $userService->getTerminalPriceType();
        } else {
            // 営業マン
            $priceType = $bizService->getTerminalPriceType();
        }

        $orderHeaderService = new OrderHeaderService();
        // 前回の発注数
        $recentOrderNum = $orderHeaderService->getLastOrderListNum();

        $orderSortService = new OrderSortService();

        // お気に入り商品数を取得する
        $favoriteNum = -1;
        if ($userService->enableFavorite()) {
            $favoriteNum = $orderSortService->getFavoriteNum();
        }

        // ゴミ箱商品数を取得する
        $trashNum = -1;
        if ($userService->enableTrash()) {
            $trashNum = $orderSortService->getTrashNum();
        }

        // 並び順更新用URL
        $updateUrl = Router::url([
            "controller" => "order_sort",
            "action" => "update_sort"
        ]);

        // ゴミ箱用URL
        $ajaxUrl = Router::url([
            "controller" => "OrderList"
        ]);

        $this->set(compact(
            'title',
            'pageName',
            'ajaxUrl',
            'companyCode',
            'updateUrl',
            'sortUrl',
            'categoryList',
            'orderListNum',
            'recentOrderNum',
            'favoriteNum',
            'trashNum',
            'enablePicture',
            'viewMode',
            'priceType'
        ));

        CommonComponent::outputUserLog('並べ替え 画面表示完了');
    }

    /**
     * 並び替え用の商品一覧を表示する
     * ページングはしない
     * @throws \Exception
     */
    public function sort_list()
    {
        CommonComponent::outputUserLog('並べ替え リスト取得開始');

        if ($this->request->is('ajax')) {

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                return $this->ajaxReturnLoginError();
            }

            $orderListService = new OrderListService();
            $param = [
                'category_type' => 'all',
                'search_word' => '',
                'sort_type' => 'user',
            ];
            $dataList = $orderListService->getOrderList($param, false)
                ->all();

            //企業コードをセッションから取得する
            $companyCode = $this->getCompanyCode();

            $userService = new UserService();

            $enableFavorite = $userService->enableFavorite();
            $enableTrash = $userService->enableTrash();


            // 変数をセットしビューに渡す
            $this->set(compact(
                'dataList',
                'companyCode',
                'enableFavorite',
                'enableTrash'
            ));

            // テンプレートを無効化
            $this->viewBuilder()->setLayout('');

            // レンダリングされたviewを取得
            $body = $this->render()->body();

            CommonComponent::outputUserLog('並べ替え リスト取得完了');

            // json形式で整形して返す
            return $this->ajaxReturnResponse($body);
        }

    }

    /**
     * 並び順の更新
     *
     * @return \Cake\Http\Response|null
     * @throws \Exception
     */
    public function update_sort()
    {
        CommonComponent::outputUserLog('並べ替え 更新処理開始');

        if ($this->request->is('ajax')) {

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                return $this->ajaxReturnLoginError();
            }

            $param = $this->getQueryParameter();
            $data = json_decode($param['data']);

//            CommonComponent::outputUserLog('デシリアライズデータ', $data);

            if (count($data) !== 0) {
                $orderSortService = new OrderSortService();

                // 取得したデータを基にソート順を整形する
                $data = $orderSortService->formatSortNumber($data);

                // 取得したデータでDBのソート順カラムを更新
                $orderSortService->setSortNumber($data);

                // DBのソート順を1から連番に振り直す
                $orderSortService->updateSortNumber();

                // ソート順を通し番号に上書き

                CommonComponent::outputUserLog('並べ替え 更新処理完了');
            }

            // 更新した並び順で表示させる
            return $this->redirect(['action' => 'sort_list']);

        }
    }
}
