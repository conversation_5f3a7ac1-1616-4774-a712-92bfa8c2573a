<?php
declare(strict_types=1);

namespace App\Controller;

use App\Controller\Component\CommonComponent;
use App\Service\OrderHeaderService;
use App\Service\OrderListService;
use App\Service\OrderSortService;
use App\Service\UserService;
use App\Service\BizService;
use Cake\Http\Exception\NotFoundException;
use Cake\Routing\Router;
use App\Service\OrderManagementService;

/**
 * OrderListController
 * 発注一覧画面
 *
 * @property CommonComponent Common
 */
class OrderListController extends AppController
{
    private $screenType = VIEW_MODE_TILE;

    /**
     * 初期化関数
     * @throws \Exception
     */
    public function initialize()
    {
        parent::initialize();

        // 1ページ表示件数(デフォルト)を取得する
        $limit = CommonComponent::getCodeMaster('SYS', 'ORDER_VIEW_NUM')['number1'];
        $this->paginate['limit'] = (int)$limit;
    }

    /**
     * Index method
     * グリッド表示（画像有り）
     *
     */
    public function index()
    {
        CommonComponent::outputUserLog('発注一覧 画面表示');

        // サービスクラスのサンプル
        $title = TXT_ORDER_PAGE;
        $pageName = TXT_ORDER_LIST;

        /* ===========  Ajaxでリストを取得するページに記載 ここから  =========== */

        // Ajax通信を行う場合の取得用URL
        $ajaxUrl = Router::url([
            "controller" => "order_list"
        ]);

        $ajaxUrlCheckOrder = Router::url([
            "controller" => "order_list",
            "action" => "ajaxCheckOrderExist"
        ]);

        /* ===========  Ajaxでリストを取得するページに記載 ここまで  =========== */

        /* ===========  事前認証が必要なページに記載 ここから  =========== */

        $this->setLoginInfo();

        /* ===========  事前認証が必要なページに記載 ここまで  =========== */

        $companyCode = $this->getCompanyCode();

        $userService = new UserService();
        $bizService = new BizService();

        // 企業マスタから取得したterminal_picture_typeで発注一覧の初期表示を設定
        $enablePicture = $userService->getTerminalPictureType();
        // 画像有りの場合：viewTile、無しの場合viewNoImg
        if ($enablePicture == '1') {
            $viewMode = "viewTile";
        } else {
            $viewMode = "viewNoImg";
        }
        // セッション呼び出し
        $session = $this->request->getSession();
        $loginType = $session->read('sessionUserInfo.loginType');

        // 金額の表示フラグ
        if ($loginType == FLAG_USER_LOGIN) {
            // 一般ユーザ
            $priceType = $userService->getTerminalPriceType();
        } else {
            // 営業マン
            $priceType = $bizService->getTerminalPriceType();
        }

        // TODO 営業マンの場合処理を行わない
        // ログイン画面から遷移の場合、並び順テーブルを作成する
        $orderSortService = new OrderSortService();
        $referer = $this->referer(null, true); // 遷移元のURLを相対URLで取得
        if ($referer == "/login" || $referer == "/reset_password/success") {
            $result = $orderSortService->insertTOrderSort();

            if ($result) {
                // 連番を振り直す
                $orderSortService->updateSortNumber();
            }
        }

        // オーダリスト取得処理
        $orderService = new OrderListService();
        $categoryList = $orderService->getCategoryData();

        // すべての商品数
        $orderListNum = $orderService->getOrderListNum();

        // 基準となるカレンダーを作成、viewにセット
        $deliveryDateList = $orderService->calcNouhinbiList();

        // 起算日を取得
        $kisanDate = $orderService->getkisanDate();

        // 取引先の納品可能曜日設定を取得
        $deliveryDaysFlg = $orderService->getCustomerDeliveryDaysFlg();

        // 部門名リスト
        $departmentList = $orderService->getDepartmentList();

        // 表示件数リストを取得
        $viewNumList = $orderService->getViewNumList();
        $defaultListNum = $this->paginate['limit'];

        // 前回の発注数
        $orderHeaderService = new OrderHeaderService();
        $recentOrderNum = $orderHeaderService->getLastOrderListNum();

        // お気に入り商品数を取得する
        $favoriteNum = -1;
        if ($userService->enableFavorite()) {
            $favoriteNum = $orderSortService->getFavoriteNum();
        }

        // ゴミ箱商品数を取得する
        $trashNum = -1;
        if ($userService->enableTrash()) {
            $trashNum = $orderSortService->getTrashNum();
        }

        // 変数をセットしビューに渡す
        $this->set(compact(
            'title',
            'pageName',
            'ajaxUrl',
            'companyCode',
            'categoryList',
            'enablePicture',
            'priceType',
            'viewMode',
            'orderListNum',
            'recentOrderNum',
            'deliveryDateList',
            'deliveryDaysFlg',
            'kisanDate',
            'departmentList',
            'viewNumList',
            'defaultListNum',
            'favoriteNum',
            'trashNum',
            'ajaxUrlCheckOrder'
        ));

        CommonComponent::outputUserLog('発注一覧 画面表示完了');
    }

    /**
     * タイル表示のviewを返却
     *
     */
    public function viewTile()
    {
        if ($this->request->is('ajax')) {
            $this->screenType = VIEW_MODE_TILE;
            return $this->getView();
        }

        return $this->redirect(['action' => 'index']);
    }

    /**
     * リスト表示（画像あり）のviewを返却
     *
     */
    public function viewList()
    {
        if ($this->request->is('ajax')) {
            $this->screenType = VIEW_MODE_LIST;
            return $this->getView();
        }

        return $this->redirect(['action' => 'index']);
    }

    /**
     * リスト表示（画像なし）のviewを返却
     *
     */
    public function viewNoImg()
    {
        if ($this->request->is('ajax')) {
            $this->screenType = VIEW_MODE_NO_IMG;
            return $this->getView();
        }

        return $this->redirect(['action' => 'index']);
    }

    /**
     * Ajax取得用の共通関数
     * @return \Cake\Http\Response|null
     * @throws \Exception
     */
    public function getView()
    {
        CommonComponent::outputUserLog('viewの取得処理 開始');
        if ($this->request->is('ajax')) {

            $param = $this->getQueryParameter();

            // ログイン状態のチェック(発注画面)
            if (!$this->ajaxCheckLogin()) {
                return $this->ajaxReturnLoginError();
            }

            $orderListService = new OrderListService();

            // オーダーリストのバージョンNo更新判定
            // セッションからデータ取得
            $session = $this->request->getSession();
            $savedVersionNo = $session->read('sessionOrderList.versionNo');
            $nowVersionNo = $orderListService->getOrderlistMaxVersion();

            // オーダーリストの表示形式をセッションに保存
            $session->write('sessionOrderList.viewMode', $this->screenType);

            if ($nowVersionNo == 0 || $nowVersionNo > $savedVersionNo) {
                // 最終ログイン日時を取得し、値が空の場合を初回ログインと見なす
                if ($session->read('sessionOrderList.lastLoginDate') !== null) {
                    CommonComponent::outputUserLog('オーダーリストの更新あり', [$nowVersionNo, $savedVersionNo]);
                    return $this->ajaxReturnResponse('', 'updated');
                }
            }

            try {
                $data = $orderListService->getOrderList($param);

                // ページあたりの件数設定
                if (isset($param['list_num']))
                    $this->paginate['limit'] = $param['list_num'];
                $dataList = $this->paginate($data);
            } catch (NotFoundException $ex) {
                // 範囲外のページが指定された場合のエラー処理
                CommonComponent::outputUserLog('範囲外のページ指定', $ex->getMessage());
                // 最後のページを取得する処理
                return $this->getLastPage();
            }

            // ページング情報
            $paginateData = array_values($this->Paginator->getPagingParams())[0];
            $currentPage = $paginateData['page'];
            $totalPage = $paginateData['pageCount'];

            // ページング用URL
            $paginateUrl = Router::url([
                "controller" => "order_list",
                "action" => $this->screenType
            ]);

            $status = "";
            if (count($dataList) === 0) {
                $status = "nodata";
            }
            $orderType = $orderListService->getOrderType();

            // 納品日リスト
            $deliveryDateList = $orderListService->getNouhinbiList();

            // 起算日を取得
            $kisanDate = $orderListService->getkisanDate();

            // 取引先の納品可能曜日設定を取得
            $deliveryDaysFlg = $orderListService->getCustomerDeliveryDaysFlg();

            // 部門名リスト
            $departmentList = $orderListService->getDepartmentList();

            // 表示件数リストを取得
            $viewNumList = $orderListService->getViewNumList();

            $screenType = $this->screenType;
            $companyCode = $this->getCompanyCode();

            $userService = new UserService();
            $bizService = new BizService();

            // セッション呼び出し
            $session = $this->request->getSession();
            $loginType = $session->read('sessionUserInfo.loginType');

            // 金額の表示フラグ
            if ($loginType == FLAG_USER_LOGIN) {
                // 一般ユーザ
                $priceType = $userService->getTerminalPriceType();
            } else {
                // 営業マン
                $priceType = $bizService->getTerminalPriceType();
            }

            // カテゴリ：ゴミ箱モードの判定
            $isTrashMode = ($param['category_type'] === 'trash' ? true : false);

            $enableFavorite = $userService->enableFavorite();
            $enableTrash = $userService->enableTrash();

            // 変数をセットしビューに渡す
            $this->set(compact(
                'dataList',
                'orderType',
                'screenType',
                'companyCode',
                'deliveryDateList',
                'deliveryDaysFlg',
                'kisanDate',
                'departmentList',
                'viewNumList',
                'priceType',
                'isTrashMode',
                'enableFavorite',
                'enableTrash'
            ));

            // テンプレートを無効化
            $this->viewBuilder()->setLayout('');

            // リスト表示と画像なしのviewを共通化
            if ($this->screenType === VIEW_MODE_NO_IMG) $this->screenType = VIEW_MODE_LIST;

            // レンダリングされたviewを取得
            $body = $this->render($this->screenType)->body();

            CommonComponent::outputUserLog('viewの取得処理 完了');

            // json形式で整形して返す
            return $this->ajaxReturnResponse($body, $status, $paginateUrl, $currentPage, $totalPage);
        }
    }


    /**
     * 発注商品の最新データを取得する
     * @return \Cake\Http\Response|null
     */
    public function ajaxGetOrderItems()
    {
        if ($this->request->is('ajax')) {

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                CommonComponent::outputUserLog('商品データ取得 ログインエラー');
                return $this->ajaxReturnLoginError();
            }

            // POSTデータを取得
            $param = $this->getQueryParameter();

            if (!isset($param['order_data'])
                || !is_array($param['order_data'])
                || !count($param['order_data'])) {
                return $this->ajaxReturnResponse([], "nodata");
            }

            $orderListService = new OrderListService();
            $result = $orderListService->getOrderListFromProductCode($param['order_data']);
            $deliveryDateList = $orderListService->getNouhinbiList();
            // 価格変更の確認のため、納品日リストから最短納品日を取得する
            $nowDate = array_values($deliveryDateList)[0];

            $returnData = [];
            foreach ($result as $value) {
                // 保存データから該当要素を取得
                $orderData = [];
                foreach ($param['order_data'] as $orderDataValue) {
                    if ($orderDataValue['product_code'] === $value->management_product_code
                        && $orderDataValue['customer_code'] === $value->customer_code
                        && $orderDataValue['customer_edaban'] === $value->customer_edaban) {
                        $orderData = $orderDataValue;
                        break;
                    }
                }

                if (!count($orderData)) continue;

                // 現在価格の設定
                $price = $value->price_before;
                if (isset($value->price_switch_day)) {
                    $switchDate = $value->price_switch_day->format('Y/m/d');
                    if ($switchDate <= $nowDate) {
                        $price = $value->price_after;
                    }
                }

                CommonComponent::outputUserLog('商品データ取得');

                $returnData[] = [
                    'product_code' => $value->management_product_code,
                    'customer_code' => $value->customer_code,
                    'customer_edaban' => $value->customer_edaban,
                    'name' => $value->product_name,
                    'num' => $orderData['num'],
                    'nouhinbi' => $orderData['nouhinbi'],
                    'bikou' => $orderData['bikou'],
                    'kikaku' => $value->product_standard,
                    'irisuu' => $value->in_numbers,
                    'unit' => $value->unit_name,
                    'unit_code' => $orderData['unit_code'],
                    'price' => $price,
                    'tax_type' => $value->tax_type,
                    'leadtime' => $value->lead_time
                ];
            }

            // json形式で整形して返す
            return $this->ajaxReturnResponse($returnData);
        }
    }

    /**
     * カタログの発注商品の最新データを取得する
     * @return \Cake\Http\Response|null
     */
    public function ajaxGetCatalogOrderItems()
    {
        if ($this->request->is('ajax')) {

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                CommonComponent::outputUserLog('カタログ商品データ取得 ログインエラー');
                return $this->ajaxReturnLoginError();
            }

            // POSTデータを取得
            $param = $this->getQueryParameter();

            if (!isset($param['catalog_data'])
                || !is_array($param['catalog_data'])
                || !count($param['catalog_data'])) {
                return $this->ajaxReturnResponse([], "nodata");
            }

            // ランク情報存在チェック
            $userService = new UserService();
            $loginId = $_SESSION['sessionUserInfo']['loginId'];
            $rankInfo = $userService->checkRankInfo($loginId);
            if (!$rankInfo) {
                // ランク情報が存在しないため、空で返す
                return $this->ajaxReturnResponse([], "rankInfo_nodata");  // json形式で整形して返す
            }

            $orderListService = new OrderListService();
            $result = $orderListService->getCatalogOrderConfirmInfoFromProductCode($param['catalog_data']);
            $deliveryDateList = $orderListService->getNouhinbiList();

            $returnData = [];
            foreach ($result as $value) {
                // 保存データから該当要素を取得
                $orderData = [];
                foreach ($param['catalog_data'] as $orderDataValue) {
                    if ($orderDataValue['product_code'] === $value->product_code) {
                        $orderData = $orderDataValue;
                        break;
                    }
                }

                if (!count($orderData)) continue;

                $nouhinbi = $orderData['nouhinbi'];

                $leadtime = $value->lead_time;

                foreach ($deliveryDateList as $key => $val) {
                    //リードタイムを下回る納品日はスルー
                    //if ($key < $leadtime) continue;

                    //POST値の納品日が存在する場合、処理を抜ける
                    if ($val == $nouhinbi) {
                        break;
                    }

                    //POST値の納品日を超えた場合、その日付を納品日に設定し、処理を抜ける
                    if ($val > $nouhinbi) {
                        $nouhinbi = $val;
                        break;
                    }
                }

                CommonComponent::outputUserLog('カタログ商品データ取得');

                // カタログ商品データに単位が含まれていない場合は、空白を設定
                $unitCode = "";
                $unitName = "";
                if (isset($orderData['unit_code'])) $unitCode = $orderData['unit_code'];
                if (isset($orderData['unit_name'])) $unitName = $orderData['unit_name'];

                $returnData[] = [
                    'product_code' => $value->product_code,
                    'customer_code' => $orderData['customer_code'], //取引先コード
                    'customer_edaban' => $orderData['customer_edaban'], //枝番
                    'name' => $value->product_name,
                    'num' => $orderData['num'],
                    'nouhinbi' => $nouhinbi,
                    'bikou' => $orderData['bikou'],
                    'kikaku' => $value->product_standard,
                    'irisuu' => $value->in_numbers,
                    'unit' => $value->unit_name,
                    'unit_code' => $unitCode,
                    'unit_name' => $unitName,
                    //'price' => $price,
                    'tax_type' => $value->tax_type,
                    'leadtime' => $leadtime,
                ];
            }

            // json形式で整形して返す
            return $this->ajaxReturnResponse($returnData);
        }
    }

    /**
     * お気に入り商品の設定を行う
     */
    public function ajaxOrderFavorite()
    {
        if ($this->request->is('ajax')) {

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                return $this->ajaxReturnLoginError();
            }

            // POSTデータを取得
            $param = $this->getQueryParameter();

            if (!count($param))
                return $this->ajaxReturnResponse([], "nodata");

            $orderSortService = new OrderSortService();
            $setResult = $orderSortService->setOrderFavorite($param);
            if (!empty($setResult))
                return $this->ajaxReturnResponse([], "nodata");
            $getResult = $orderSortService->getFavoriteNum();

            // json形式で整形して返す
            $returnData = [];
            if (isset($getResult)) {
                $returnData[] = [
                    'favorite_num' => $getResult
                ];
            }

            return $this->ajaxReturnResponse($returnData, "success");
        }
    }

    /**
     * 商品のゴミ箱設定を行う
     */
    public function ajaxOrderTrash()
    {
        if ($this->request->is('ajax')) {

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                return $this->ajaxReturnLoginError();
            }

            // POSTデータを取得
            $param = $this->getQueryParameter();

            if (!count($param))
                return $this->ajaxReturnResponse([], "nodata");

            $orderSortService = new OrderSortService();
            $returnData = $orderSortService->setOrderTrash($param);
            if (empty($returnData))
                return $this->ajaxReturnResponse([], "nodata");

            return $this->ajaxReturnResponse($returnData, "success");
        }
    }

    /**
     * 発注情報管理テーブルにデータが存在するかを確認
     * @return \Cake\Http\Response|null
     */
    public function ajaxCheckOrderExist()
    {
        CommonComponent::outputUserLog('発注情報管理テーブル検索処理 開始');
        if ($this->request->is('ajax')) {

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                return $this->ajaxReturnLoginError();
            }

            // POSTデータを取得
            $param = $this->getQueryParameter();

            CommonComponent::outputUserLog('POSTデータ', $param);

            if (!count($param)) {
                CommonComponent::outputUserLog('POSTデータ取得失敗');
                return $this->ajaxReturnResponse([], "error");
            }

            $orderManagementService = new OrderManagementService();
            $result = $orderManagementService->TOrderManagementData($param);

            if ($result > 0) {
                CommonComponent::outputUserLog('前回の発注が完了しているため、ローカルストレージの発注商品情報を削除する');
                return $this->ajaxReturnResponse([], "Exist");
            
            } else {
                CommonComponent::outputUserLog('前回の発注が未完了のため、ローカルストレージの発注商品情報を残す');
                return $this->ajaxReturnResponse([], "notExist");
            }

        }
    }
}
