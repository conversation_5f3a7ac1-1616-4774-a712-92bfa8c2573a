<?php

namespace App\Controller;

use App\Controller\AppController;
use App\Controller\Component\CommonComponent;
use App\Service\UserService;
use Cake\Cache\Cache;

/**
 * CatalogAuthController
 * ログイン認証用API
 *
 * @property CommonComponent Common
 */
class CatalogAuthController extends AppController
{
    public function initialize()
    {
        parent::initialize();
        $this->loadComponent('RequestHandler');
    }

    /**
     * ログイン認証用API
     *
     * 使い方
     * - https://<domain>/<company_code>/catalog_auth
     *
     * Getパラメータ
     * - user_id
     * - api_key
     *
     * 戻り値
     * - 1:成功, 0:失敗 (int)
     */
    public function index()
    {
        $data = $this->request->getQueryParams();

        CommonComponent::outputUserLog('カタログログインAPI認証 開始');

        if ($data && isset($data['login_id']) && isset($data['api_key'])) {
            // ログイン確認
            $userService = new UserService(['company_code' => $this->Common->getCompanyCodeFromUrl()]);

            // ID/API_keyで認証
            $result = $userService->checkApiKey($data['login_id'], $data['api_key']);

            if ($result->status) {
                // 取得値をCookieに登録する
                return $this->returnData(true);
            }
        }

        return $this->returnData(false);
    }

    /**
     * 指定したメッセージをjson形式に変換して返す
     *
     * @param bool $isSuccess
     * @return \Cake\Http\Response
     */
    protected function returnData($isSuccess = false)
    {
        $data = 0;

        if ($isSuccess) {
            $data = 1;
            CommonComponent::outputUserLog('カタログ用API 商品情報取得 ログイン認証 成功');
        } else {
            CommonComponent::outputUserLog('カタログ用API 商品情報取得 ログイン認証 エラー');
        }

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($data));

    }

}
