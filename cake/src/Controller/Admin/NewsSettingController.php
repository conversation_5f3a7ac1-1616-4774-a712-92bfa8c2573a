<?php

namespace App\Controller\Admin;

use App\Controller\Component\CommonComponent;
use App\Service\AdminNewsService;
use Cake\Http\Exception\NotFoundException;
use Cake\Routing\Route\Route;
use Cake\Routing\Router;

/**
 * NewsSetting Controller
 * お知らせを設定する
 *
 */
class NewsSettingController extends AdminController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * Index method
     *
     * @return \Cake\Http\Response|void
     */
    public function index()
    {
        CommonComponent::outputAdminLog('お知らせ管理 画面表示');
        $title = TXT_ADMIN_PAGE;
        $pageName = TXT_NOTICE_MANAGE;

        /* ===========  Ajaxでリストを取得するページに記載 ここから  =========== */
        // Ajax通信の取得用URL
        $ajaxUrl = Router::url([
            "controller" => "news_setting",
            "action" => "news_list"
        ]);
        /* ===========  Ajaxでリストを取得するページに記載 ここまで  =========== */

        $this->set(compact('title', 'pageName', 'ajaxUrl'));

        CommonComponent::outputAdminLog('お知らせ管理 画面表示完了');
    }

    /**
     * Ajax通信でお知らせ登録画面のお知らせ一覧を返す
     * お知らせ登録画面用のリストデータ
     */
    public function news_list()
    {
        if ($this->request->is('ajax')) {
            CommonComponent::outputAdminLog('お知らせ管理 リスト取得開始');

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                return $this->ajaxReturnError();
            }

            /* ==============  返却データ設定処理 ここから  ============== */
            $adminService = new AdminNewsService($param = ['company_code' => $this->getCompanyCode()]);

            // データ取得処理
            try {
                //ソート順を指定
                $this->paginate['order'] = [
                    'MNews.set_date' => 'desc',
                    'MNews.id' => 'desc',
                ];

                // 開くページ番号を設定
                $this->setPaginationPageNo();

                // ページング化して取得
                $data = $adminService->getNews();
                $listData = $this->paginate($data);

            } catch (NotFoundException $ex) {
                // 範囲外のページが指定された場合のエラー処理
                CommonComponent::outputAdminLog('範囲外のページ指定', $ex->getMessage());
                // 最後のページを取得する処理
                return $this->getLastPage();
            }
            // テンプレートを無効化
            $this->viewBuilder()->setLayout('');

            // 通し番号を設定
            $numCount = $this->getStartColumnNumber();

            //カテゴリー表示用文字列を取得する
            $newsCategoryLabel = $adminService->getNewsCategory();

            // パラメータをセット
            $this->set(compact('listData', 'numCount', 'newsCategoryLabel'));
            /* ==============  返却データ設定処理 ここまで  ============== */

            CommonComponent::outputAdminLog('お知らせ管理 リスト取得完了');

            // レンダリングされたviewを返却
            return $this->ajaxReturnResponse($this->render()->body());
        }
    }

    /**
     * データの追加・更新ページを表示
     * @param null $registerId
     */
    public function register($registerId = null)
    {
        $title = TXT_ADMIN_PAGE;
        $pageName = TXT_NOTICE_MANAGE;

        $isInsert = false;
        $newsData = [];
        $todayDate = "";

        // IDをパラメータにセット
        $param['id'] = $registerId;
        $AdminService = new AdminNewsService($param);

        // パラメータによって、登録/更新を判定
        if ($registerId === null) {
            $pageName .= " 登録画面";
            $isInsert = true;
            $todayDate = date("Y/m/d");
            
            CommonComponent::outputAdminLog('お知らせ登録 画面表示');
        } else {
            //更新画面項目表示用処理
            $pageName .= " 更新画面";

            CommonComponent::outputAdminLog('お知らせ更新 画面表示');

            //更新画面の項目を取得
            $newsData = $AdminService->getNewsWithId();

            //データが取得できなかった場合、エラーメッセージを表示
            if($newsData === null){
                $errorMessage = [];
                $errorMessage["no_data"] = ["message" => E_A0004];
                $this->setDBMessageToFlashError($errorMessage);
                return $this->redirect(['action' => 'index']);
            }
        }

        //カテゴリー表示用文字列を取得する
        $newsCategoryLabel = $AdminService->getNewsCategory();

        $session = $this->request->getSession();

        // 営業マン利用フラグの取得
        $useBusinessman = $session->read('sessionAdminCompanyInfo.useBusinessman');

        // パラメータをセット
        $this->set(compact('title', 'pageName', "isInsert", 'newsData', 'todayDate', 'newsCategoryLabel', 'useBusinessman'));

        CommonComponent::outputAdminLog('お知らせ登録/更新 画面表示完了');
    }

    /**
     * データの追加処理
     */
    public function insertData()
    {
        $param = $this->request->getData();

        CommonComponent::outputAdminLog('お知らせ登録処理 開始');

        //DB処理
        $AdminService = new AdminNewsService($param);
        $result = $AdminService->insertMNews();

        if (empty($result)) {
            //OK
            CommonComponent::outputAdminLog('お知らせ登録処理 完了');
            // 表示するページ番号をパラメータに設定し、処理を返す
            return $this->redirect(['action' => 'index', 'page' => '1']);
        } else {
            //NG
            // メッセージをセット
            $this->setDBMessageToFlashError($result);

            CommonComponent::outputAdminLog('お知らせ登録処理 エラー', $result);

            $newsData = (object)$param;

            $title = TXT_ADMIN_PAGE;
            $pageName = TXT_NOTICE_MANAGE . " 登録画面";
            $isInsert = true;
            $todayDate = $param['set_date'];

            //カテゴリー表示用文字列を取得する
            $newsCategoryLabel = $AdminService->getNewsCategory();

            $this->set(compact('title', 'pageName', "isInsert", 'todayDate', 'newsData', 'newsCategoryLabel'));

            $this->render('register', null);
        }
    }

    /**
     * データの更新処理
     */
    public function updateData($updateId = null)
    {
        CommonComponent::outputAdminLog('お知らせ更新処理 開始');
        
        $param = $this->request->getData();

        // $paramが取得できない：リロードされた？場合はregisterへリダイレクト
        if (count($param) === 0) {
            CommonComponent::outputAdminLog('パラメータの取得失敗');
            return $this->redirect(['action' => 'register', $updateId]);
        }

        $param['id'] = $updateId;

        //DB処理、updateIdのデータを更新する
        $AdminService = new AdminNewsService($param);
        $result = $AdminService->updateMNews();

        if (empty($result)) {
            //OK
            CommonComponent::outputAdminLog('お知らせ更新処理 完了');

            // 表示するページ番号をパラメータに設定し、処理を返す
            return $this->redirect(['action' => 'index']);
        } else {
            //NG
            
            //更新対象のデータ取得に失敗した場合、一覧画面にリダイレクト
            if(array_key_exists("no_data", $result)){
                CommonComponent::outputAdminLog('更新対象のデータ取得失敗');
                $this->setDBMessageToFlashError($result);
                return $this->redirect(['action' => 'index']);
            }

            // メッセージをセット
            $this->setDBMessageToFlashError($result);

            CommonComponent::outputAdminLog('お知らせ更新処理 エラー', $result);

            $newsData = (object)$param;

            $title = TXT_ADMIN_PAGE;
            $pageName = TXT_NOTICE_MANAGE . " 更新画面";
            $isInsert = false;

            //カテゴリー表示用文字列を取得する
            $newsCategoryLabel = $AdminService->getNewsCategory();

            $this->set(compact('title', 'pageName', "isInsert", 'newsData', 'newsCategoryLabel'));

            $this->render('register', null);
        }
    }

    /**
     * 指定したデータの削除を行う
     */
    public function deleteData($deleteId = null)
    {
        // TODO IDが設定されていない場合の処理
        if (!isset($deleteId)) return null;

        CommonComponent::outputAdminLog('お知らせ削除処理 開始');

        $param['id'] = $deleteId;

        //deleteIdのデータを削除する
        $AdminService = new AdminNewsService($param);
        $result = $AdminService->deleteMNews();

        if (count($result) > 0) {
            //NG
            // メッセージをセット
            $this->setDBMessageToFlashError($result);

            CommonComponent::outputAdminLog('お知らせ削除処理 エラー', $result);
        }else {
            CommonComponent::outputAdminLog('お知らせ削除処理 完了');
        }

        return $this->redirect(['action' => 'index']);
    }
}
