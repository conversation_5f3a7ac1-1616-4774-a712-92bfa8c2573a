<?php

namespace App\Controller\Admin;

use App\Service\AdminBusinessmanService;
use App\Service\AdminCompanyService;
use Cake\Http\Exception\NotFoundException;
use Cake\Routing\Router;
use Cake\View\View;
use Cake\Datasource\ConnectionManager;
use App\Controller\Component\CommonComponent;

/**
 * BusinessmanSettingController
 * 取引先情報の管理を行う
 *
 * @property \App\Model\Table\TOrderHeadersTable $TOrderHeaders
 */
class BusinessmanSettingController extends AdminController
{
    /**
     * 初期化関数
     */
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * Index method
     *
     * @return \Cake\Http\Response|void
     */
    public function index()
    {
        $title = TXT_ADMIN_PAGE;
        $pageName = TXT_BIZ_PERSON_MANAGE;

        // Ajax取得用URL（取引先管理画面）
        $ajaxUrl = Router::url([
            "controller" => "businessman_setting",
            "action" => "businessman_list"
        ]);

        // カタログ機能の利用有無を取得
        $enableCatalog = (int)CommonComponent::getCodeMaster('SYS', 'ENABLE_CATALOG')['number1'];

        $this->set(compact('title', 'pageName', 'ajaxUrl', 'enableCatalog'));
    }

    /**
     * Ajax通信で取引先管理画面の取引先一覧を返す
     * 取引先管理画面用のリストデータ
     */
    public function businessman_list()
    {
        if ($this->request->is('ajax')) {

            // ログイン状態のチェック
            if (!$this->ajaxCheckLogin()) {
                return $this->ajaxReturnError();
            }

            /* ==============  返却データ設定処理 ここから  ============== */
            $param = $this->getQueryParameter();
            $businessmanService = new AdminBusinessmanService($param);

            // データ取得処理
            try {
                //ソート順を指定
                $this->paginate['order'] = [
                    'MBusinessmen.id' => 'asc'
                ];

                // 開くページ番号を設定
                $this->setPaginationPageNo();

                // ページング化して取得
                $data = $businessmanService->getBusinessmenWithParam();
                $listData = $this->paginate($data);

            } catch (NotFoundException $ex) {
                // 範囲外のページが指定された場合のエラー処理

                // 最後のページを取得する処理
                return $this->getLastPage($param);
            }

            // テンプレートを無効化
            $this->viewBuilder()->setLayout('');

            // 通し番号を設定
            $startNo = $this->getStartColumnNumber();

            // カタログ機能の利用有無を取得
            $enableCatalog = (int)CommonComponent::getCodeMaster('SYS', 'ENABLE_CATALOG')['number1'];

            // パラメータをセット
            $this->set(compact('listData', 'startNo', 'enableCatalog'));

            /* ==============  返却データ設定処理 ここまで  ============== */

            // レンダリングされたviewを返却
            return $this->ajaxReturnResponse($this->render()->body());
        }
    }

    /**
     * データの追加・更新ページを表示
     * @param null $registerId
     * @return \Cake\Http\Response|null
     */
    public function register($registerId = null)
    {
        $title = TXT_ADMIN_PAGE;
        $pageName = TXT_BIZ_PERSON_MANAGE;

        $data = [];
        $isInsert = false;

        $companyService = new AdminCompanyService();
        $companyData = $companyService->getCompanyData();
        //締め時間にコロンを挿入
        $companyCloseTime = substr($companyData->close_time, 0, 2) . ':' . substr($companyData->close_time, 2, 2);

        // カタログ機能の利用有無を取得
        $enableCatalog = (int)CommonComponent::getCodeMaster('SYS', 'ENABLE_CATALOG')['number1'];

        // パラメータによって、登録/更新を判定
        if (!isset($registerId)) {
            $pageName .= " 登録画面";
            $isInsert = true;
        } else {
            $pageName .= " 更新画面";
            // IDをパラメータにセット
            $param['businessman_id'] = $registerId;

            //更新画面項目表示用処理
            $businessmanService = new AdminBusinessmanService($param);
            $data = $businessmanService->getBusinessman($registerId);

            //データが取得できなかった場合、エラーメッセージを表示
            if ($data === null) {
                $errorMessage = [];
                $errorMessage["no_data"] = ["message" => E_A0004];
                $this->setDBMessageToFlashError($errorMessage);
                return $this->redirect(['action' => 'index']);
            }
        }

        // パラメータをセット
        $this->set(compact('title', 'pageName', "isInsert", 'data', 'companyCloseTime', 'enableCatalog'));
    }

    /**
     * データの追加処理
     */
    public function insertData()
    {
        $param = $this->request->getData();

        //DB処理
        $businessmanService = new AdminBusinessmanService($param);
        $result = $businessmanService->insertBusinessman();

        if (empty($result)) {
            //OK
            // 表示するページ番号をパラメータに設定し、処理を返す
            return $this->redirect(['action' => 'index', 'page' => '1']);
        } else {
            //NG

            // メッセージをセット
            $this->setDBMessageToFlashError($result);

            $data = (object)$param;

            $title = TXT_ADMIN_PAGE;
            $pageName = TXT_BIZ_PERSON_MANAGE . " 登録画面";
            $isInsert = true;

            $companyService = new AdminCompanyService();
            $companyData = $companyService->getCompanyData();
            //締め時間にコロンを挿入
            $companyCloseTime = substr($companyData->close_time, 0, 2) . ':' . substr($companyData->close_time, 2, 2);

            // カタログ機能の利用有無を取得
            $enableCatalog = (int)CommonComponent::getCodeMaster('SYS', 'ENABLE_CATALOG')['number1'];

            $this->set(compact('title', 'pageName', "isInsert", 'data', 'companyCloseTime', 'enableCatalog'));


            $this->render('register', null);
        }
    }

    /**
     * データの更新処理
     * @param $registerId
     * @return \Cake\Http\Response|null
     */
    public function updateData($registerId)
    {
        $param = $this->request->getData();
        // $paramが取得できない：リロードされた？場合はregisterへリダイレクト
        if (count($param) === 0) {
            return $this->redirect(['action' => 'register', $registerId]);
        }

        $param['id'] = $registerId;

        //DB処理、updateIdのデータを更新する
        $businessmanService = new AdminBusinessmanService($param);
        $result = $businessmanService->updateBusinessman();

        if (empty($result)) {
            //OK
            // 表示するページ番号をパラメータに設定し、処理を返す
            return $this->redirect(['action' => 'index']);
        } else {
            //NG

            //更新対象のデータ取得に失敗した場合、一覧画面にリダイレクト
            if (array_key_exists("no_data", $result)) {
                $this->setDBMessageToFlashError($result);
                return $this->redirect(['action' => 'index']);
            }

            // メッセージをセット
            $this->setDBMessageToFlashError($result);

            $data = (object)$param;

            $title = TXT_ADMIN_PAGE;
            $pageName = TXT_BIZ_PERSON_MANAGE . " 更新画面";
            $isInsert = false;

            $companyService = new AdminCompanyService();
            $companyData = $companyService->getCompanyData();
            //締め時間にコロンを挿入
            $companyCloseTime = substr($companyData->close_time, 0, 2) . ':' . substr($companyData->close_time, 2, 2);

            $this->set(compact('title', 'pageName', "isInsert", 'data','companyCloseTime'));

            $this->render('register', null);
        }
    }

    /**
     * 指定したデータの削除を行う
     * @param $registerId
     * @return \Cake\Http\Response|null
     */
    public function deleteData($registerId)
    {
        // TODO IDが設定されていない場合の処理
        if (!isset($registerId)) return null;

        $param['id'] = $registerId;

        //deleteIdのデータを削除する
        $businessmanService = new AdminBusinessmanService($param);
        $result = $businessmanService->deleteMbusinessmen();

        if (!empty($result)) {
            //NG

            // メッセージをセット
            $this->setDBMessageToFlashError($result);
        }
        return $this->redirect(['action' => 'index']);
    }
}
