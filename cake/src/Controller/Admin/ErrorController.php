<?php
/**
 * 管理画面用のエラー時の表示を行う画面
 *
 */

namespace App\Controller\Admin;

use App\Controller\Component\CommonComponent;
use <PERSON>ake\Controller\Controller;
use Cake\Core\Configure;
use Composer\Config;

/**
 * Admin Error page Controller
 *
 * Add your application-wide methods in the class below, your controllers
 * will inherit them.
 */
class ErrorController extends Controller
{

    public function initialize()
    {
        parent::initialize();

        $this->viewBuilder()->setLayout('login');
    }

    /**
     * Index method
     *
     * @param $errorCode
     * @return \Cake\Http\Response|void
     */
    public function index($errorCode)
    {
        CommonComponent::outputAdminLog('エラー 画面表示');
        $title = TXT_ADMIN_PAGE;
        $pageName = "エラーが発生しました";

        // URLから企業コードを取得
        $companyCode = CommonComponent::getCompanyCodeFromUrl();

        // エラーコードから、エラーメッセージを取得
        $message = constant($errorCode);

        $this->set(compact('title', 'pageName', 'errorCode', 'message', 'companyCode'));
    }
}
