<?php
namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * TStatistic Entity
 *
 * @property string $company_code
 * @property int $user_id
 * @property string $management_product_code
 * @property string $order_month
 * @property int $order_num
 * @property \Cake\I18n\FrozenTime $created
 * @property string $created_user
 * @property \Cake\I18n\FrozenTime $updated
 * @property string $updated_user
 *
 * @property \App\Model\Entity\MCustomer $m_customer
 * @property \App\Model\Entity\MUser $m_user
 */
class TStatistic extends Entity
{

    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array
     */
    protected $_accessible = [
        'company_code' => true,
        'customer_code' => true,
        'customer_edaban' => true,
        'user_id' => true,
        'management_product_code' => true,
        'order_month' => true,
        'order_num' => true,
        'created' => true,
        'created_user' => true,
        'updated' => true,
        'updated_user' => true,
    ];
}
