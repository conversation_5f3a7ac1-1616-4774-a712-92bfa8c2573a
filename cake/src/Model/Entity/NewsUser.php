<?php
namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * NewsUser Entity
 *
 * @property int $news_id
 * @property int $user_id
 *
 * @property \App\Model\Entity\MNews $m_news
 * @property \App\Model\Entity\MUser $m_user
 */
class NewsUser extends Entity
{

    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array
     */
    protected $_accessible = [
        'm_news' => true,
        'm_user' => true
    ];
}
