<?php
namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * TOrderItem Entity
 *
 * @property string $company_code
 * @property int $slip_no
 * @property int $slip_detail_no
 * @property int $customer_code
 * @property int $customer_edaban
 * @property int $customer_name
 * @property string $management_product_code
 * @property string $product_name
 * @property string $standard
 * @property float $in_numbers
 * @property string $unit_code
 * @property string $unit_name
 * @property string $category_code
 * @property string $category_name_1
 * @property string $category_name_2
 * @property string $category_name_3
 * @property float $price
 * @property float $separately_price
 * @property float $case_price
 * @property float $case_separately
 * @property float $order_num
 * @property string $order_amount
 * @property string $tax_type
 * @property string $tax_kbn
 * @property string $order_division
 * @property string $delivery_date
 * @property bool $is_catalog
 * @property bool $is_deleted
 * @property int $subtotal
 * @property string $bikou
 * @property \Cake\I18n\FrozenTime $created
 * @property string $created_user
 * @property \Cake\I18n\FrozenTime $updated
 * @property string $updated_user
 */
class TOrderItem extends Entity
{

    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array
     */
    protected $_accessible = [
        'company_code' => true,
        'slip_no' => true,
        'slip_detail_no' => true,
        'customer_code' => true,
        'customer_edaban' => true,
        'customer_name' => true,
        'management_product_code' => true,
        'product_name' => true,
        'standard' => true,
        'in_numbers' => true,
        'unit_code' => true,
        'unit_name' => true,
        'category_code' => true,
        'category_name_1' => true,
        'category_name_2' => true,
        'category_name_3' => true,
        'price' => true,
        'separately_price' => true,
        'case_price' => true,
        'case_separately' => true,
        'separately_unit_code' => true,
        'separately_unit_name' => true,
        'order_num' => true,
        'order_amount' => true,
        'tax_type' => true,
        'tax_kbn' => true,
        'order_division' => true,
        'delivery_date' => true,
        'is_catalog' => true,
        'is_deleted' => true,
        'subtotal' => true,
        'bikou' => true,
        'created' => true,
        'created_user' => true,
        'updated' => true,
        'updated_user' => true
    ];
}
