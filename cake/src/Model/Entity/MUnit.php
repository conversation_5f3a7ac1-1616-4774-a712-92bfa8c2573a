<?php
namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * MUnit Entity
 *
 * @property string $company_code
 * @property string $unit_code
 * @property string $unit_name
 * @property string $sort_number
 * @property string $delete_flg
 */
class MUnit extends Entity
{

    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array
     */
    protected $_accessible = [
        'company_code' => true,
        'unit_code' => true,
        'unit_name' => true,
        'sort_number' => true,
        'delete_flg' => true
    ];
}
