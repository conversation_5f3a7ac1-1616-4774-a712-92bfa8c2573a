<?php
namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * CProductList Entity
 *
 * @property string $company_code
 * @property string $product_code
 * @property string $product_name
 * @property string $product_kana
 * @property string $product_standard
 * @property float $in_numbers
 * @property string $product_packing
 * @property string $unit_name
 * @property string $category_code1
 * @property string $category_code2
 * @property string $category_code3
 * @property string $category_code4
 * @property string $category_code5
 * @property string|null $description1
 * @property string|null $description2
 * @property \Cake\I18n\FrozenDate $price_switch_day
 * @property int $rank_category_1
 * @property int $rank_category_2
 * @property float $rank1_separately_price_before
 * @property float $rank1_separately_price_after
 * @property float $rank1_case_price_before
 * @property float $rank1_case_price_after
 * @property float $rank2_separately_price_before
 * @property float $rank2_separately_price_after
 * @property float $rank2_case_price_before
 * @property float $rank2_case_price_after
 * @property float $rank3_separately_price_before
 * @property float $rank3_separately_price_after
 * @property float $rank3_case_price_before
 * @property float $rank3_case_price_after
 * @property float $rank4_separately_price_before
 * @property float $rank4_separately_price_after
 * @property float $rank4_case_price_before
 * @property float $rank4_case_price_after
 * @property float $rank5_separately_price_before
 * @property float $rank5_separately_price_after
 * @property float $rank5_case_price_before
 * @property float $rank5_case_price_after
 * @property float $rank6_separately_price_before
 * @property float $rank6_separately_price_after
 * @property float $rank6_case_price_before
 * @property float $rank6_case_price_after
 * @property float $rank7_separately_price_before
 * @property float $rank7_separately_price_after
 * @property float $rank7_case_price_before
 * @property float $rank7_case_price_after
 * @property float $rank8_separately_price_before
 * @property float $rank8_separately_price_after
 * @property float $rank8_case_price_before
 * @property float $rank8_case_price_after
 * @property float $rank9_separately_price_before
 * @property float $rank9_separately_price_after
 * @property float $rank9_case_price_before
 * @property float $rank9_case_price_after
 * @property int $case_separately
 * @property int $lead_time
 * @property bool $is_visible
 * @property int $measurement_division
 * @property int $decimal_point_permission_division
 * @property int $tax_type
 * @property int $tax_rate
 * @property \Cake\I18n\FrozenTime $created
 * @property string $created_user
 * @property \Cake\I18n\FrozenTime $updated
 * @property string $updated_user
 */
class CProductList extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array
     */
    protected $_accessible = [
        'product_name' => true,
        'product_kana' => true,
        'product_standard' => true,
        'in_numbers' => true,
        'product_packing' => true,
        'unit_name' => true,
        'category_code1' => true,
        'category_code2' => true,
        'category_code3' => true,
        'category_code4' => true,
        'category_code5' => true,
        'description1' => true,
        'description2' => true,
        'price_switch_day' => true,
        'price_before' => true,
        'price_after' => true,
        'rank_category_1' => true,
        'rank_category_2' => true,
        'rank1_separately_price_before' => true,
        'rank1_separately_price_after' => true,
        'rank1_case_price_before' => true,
        'rank1_case_price_after' => true,
        'rank2_separately_price_before' => true,
        'rank2_separately_price_after' => true,
        'rank2_case_price_before' => true,
        'rank2_case_price_after' => true,
        'rank3_separately_price_before' => true,
        'rank3_separately_price_after' => true,
        'rank3_case_price_before' => true,
        'rank3_case_price_after' => true,
        'rank4_separately_price_before' => true,
        'rank4_separately_price_after' => true,
        'rank4_case_price_before' => true,
        'rank4_case_price_after' => true,
        'rank5_separately_price_before' => true,
        'rank5_separately_price_after' => true,
        'rank5_case_price_before' => true,
        'rank5_case_price_after' => true,
        'rank6_separately_price_before' => true,
        'rank6_separately_price_after' => true,
        'rank6_case_price_before' => true,
        'rank6_case_price_after' => true,
        'rank7_separately_price_before' => true,
        'rank7_separately_price_after' => true,
        'rank7_case_price_before' => true,
        'rank7_case_price_after' => true,
        'rank8_separately_price_before' => true,
        'rank8_separately_price_after' => true,
        'rank8_case_price_before' => true,
        'rank8_case_price_after' => true,
        'rank9_separately_price_before' => true,
        'rank9_separately_price_after' => true,
        'rank9_case_price_before' => true,
        'rank9_case_price_after' => true,
        'case_separately' => true,
        'lead_time' => true,
        'is_visible' => true,
        'measurement_division' => true,
        'decimal_point_permission_division' => true,
        'tax_type' => true,
        'tax_rate' => true,
        'created' => true,
        'created_user' => true,
        'updated' => true,
        'updated_user' => true
    ];
}
