<?php
namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * DDeliveryResultItem Entity
 *
 * @property string company_code
 * @property string customer_code
 * @property string customer_edaban
 * @property string delivery_note_number
 * @property int detail_number
 * @property int is_deleted
 * @property string sales_slip_date
 * @property string delivery_slip_date
 * @property string invoice_cutoff_date
 * @property string slip_type
 * @property string sales_type
 * @property string product_code
 * @property string product_name
 * @property string product_standard
 * @property int in_numbers
 * @property string unit_name
 * @property float order_num
 * @property float order_price
 * @property float order_amount
 * @property float order_tax
 * @property int tax_type
 * @property int tax_rate
 * @property float paid_amount
 * @property float discount
 * @property string bikou
 * @property \Cake\I18n\FrozenTime $created
 * @property string created_user
 * @property \Cake\I18n\FrozenTime $updated
 * @property string updated_user
 */
class DDeliveryResultItem extends Entity
{

    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array
     */
    protected $_accessible = [
        'company_code' => true,
        'customer_code' => true,
        'customer_edaban' => true,
        'delivery_note_number' => true,
        'detail_number' => true,
        'is_deleted' => true,
        'sales_slip_date' => true,
        'delivery_slip_date' => true,
        'invoice_cutoff_date' => true,
        'slip_type' => true,
        'sales_type' => true,
        'product_code' => true,
        'product_name' => true,
        'product_standard' => true,
        'in_numbers' => true,
        'unit_name' => true,
        'order_num' => true,
        'order_price' => true,
        'order_amount' => true,
        'order_tax' => true,
        'tax_type' => true,
        'tax_rate' => true,
        'paid_amount' => true,
        'discount' => true,
        'bikou' => true,
        'created' => true,
        'created_user' => true,
        'updated' => true,
        'updated_user' => true,
    ];
}
