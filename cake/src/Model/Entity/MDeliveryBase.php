<?php
namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * MDeliveryBase Entity
 *
 * @property string $company_code
 * @property string $base_id
 * @property string $name
 * @property \Cake\I18n\FrozenTime $created
 * @property string $created_user
 * @property \Cake\I18n\FrozenTime $updated
 * @property string $updated_user
 *
 * @property \App\Model\Entity\Basis $basis
 */
class MDeliveryBase extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array
     */
    protected $_accessible = [
        'name' => true,
        'created' => true,
        'created_user' => true,
        'updated' => true,
        'updated_user' => true,
        'basis' => true
    ];
}
