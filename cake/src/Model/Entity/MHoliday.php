<?php
namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * MHoliday Entity
 *
 * @property string $company_code
 * @property \Cake\I18n\FrozenDate $set_date
 * @property int $holiday_type
 * @property string $comment
 * @property \Cake\I18n\FrozenTime $created
 * @property string $created_user
 * @property \Cake\I18n\FrozenTime $updated
 * @property string $updated_user
 */
class MHoliday extends Entity
{

    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array
     */
    protected $_accessible = [
        'company_code' => true,  //追記。bakeでは複合キーがここに記載されない。ここに記載がないカラムはDB処理実行時に値が消える
        'set_date' => true,  //追記。bakeでは複合キーがここに記載されない。ここに記載がないカラムはDB処理実行時に値が消える
        'weekday' => true,
        'holiday_type' => true,
        'comment' => true,
        'created' => true,
        'created_user' => true,
        'updated' => true,
        'updated_user' => true
    ];
}
