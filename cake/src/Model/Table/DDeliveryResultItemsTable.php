<?php

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * DDeliveryResultItems Model
 *
 * @method \App\Model\Entity\DDeliveryResultItem get($primaryKey, $options = [])
 * @method \App\Model\Entity\DDeliveryResultItem newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\DDeliveryResultItem[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\DDeliveryResultItem|bool save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\DDeliveryResultItem|bool saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\DDeliveryResultItem patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\DDeliveryResultItem[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\DDeliveryResultItem findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class DDeliveryResultItemsTable extends Table
{

    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('d_delivery_result_items');
        $this->setDisplayField('company_code');
        $this->setPrimaryKey(['company_code', 'delivery_note_number', 'detail_number']);

        $this->addBehavior('Timestamp');
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->scalar('company_code')
            ->maxLength('company_code', 3, '企業コード' . E_V0003)
            ->allowEmptyString('company_code', false, '企業コード' . E_V0001);

        $validator
            ->scalar('customer_code')
            ->maxLength('customer_code', 10, '取引先コード' . E_V0003)
            ->allowEmptyString('customer_code', false, '取引先コード' . E_V0001)
            ->add('customer_code', [
                'alphaNumeric' => [
                    'rule' => function ($value) {
                        return preg_match('/^[a-zA-Z0-9]+$/', $value) ? true : false;
                    },
                    'message' => '取引先コード' . E_V0007
                ]
            ]);

        $validator
            ->scalar('customer_edaban')
            ->maxLength('customer_edaban', 5, '取引先枝番' . E_V0003)
            ->allowEmptyString('customer_edaban', false, '取引先枝番' . E_V0001)
            ->add('customer_edaban', [
                'alphaNumeric' => [
                    'rule' => function ($value) {
                        return preg_match('/^[a-zA-Z0-9]+$/', $value) ? true : false;
                    },
                    'message' => '取引先枝番' . E_V0007
                ]
            ]);

        $validator
            ->scalar('delivery_note_number')
            ->maxLength('delivery_note_number', 11, '納品書番号' . E_V0003)
            ->allowEmptyString('delivery_note_number', false, '納品書番号' . E_V0001)
            ->numeric('delivery_note_number', '納品書番号' . E_V0006);

        $validator
            ->scalar('detail_number')
            ->maxLength('detail_number', 11, '明細行番号' . E_V0003)
            ->allowEmptyString('detail_number', false, '明細行番号' . E_V0001)
            ->numeric('detail_number', '明細行番号' . E_V0006);

        $validator
            ->scalar('is_deleted')
            ->maxLength('is_deleted', 1, '伝票削除区分' . E_V0003)
            ->allowEmptyString('is_deleted', false, '伝票削除区分' . E_V0001)
            ->numeric('is_deleted', '伝票削除区分' . E_V0006);

        $validator
            ->scalar('sales_slip_date')
            ->maxLength('sales_slip_date', 8, '売上伝票日付' . E_V0003)
            ->allowEmptyString('sales_slip_date', false, '売上伝票日付' . E_V0001)
            ->numeric('sales_slip_date', '売上伝票日付' . E_V0006);

        $validator
            ->scalar('delivery_slip_date')
            ->maxLength('delivery_slip_date', 8, '納品日付' . E_V0003)
            ->allowEmptyString('delivery_slip_date', false, '納品日付' . E_V0001)
            ->numeric('delivery_slip_date', '納品日付' . E_V0006);

        $validator
            ->scalar('invoice_cutoff_date')
            ->maxLength('invoice_cutoff_date', 8, '請求締日' . E_V0003)
            ->numeric('invoice_cutoff_date', '請求締日' . E_V0006);

        $validator
            ->scalar('slip_type')
            ->maxLength('slip_type', 1, '伝票区分' . E_V0003)
            ->allowEmptyString('slip_type', false, '伝票区分' . E_V0001)
            ->numeric('slip_type', '伝票区分' . E_V0006);

        $validator
            ->scalar('sales_type')
            ->maxLength('sales_type', 1, '売上伝票区分' . E_V0003)
            ->allowEmptyString('sales_type', false, '売上伝票区分' . E_V0001)
            ->numeric('sales_type', '売上伝票区分' . E_V0006);

        $validator
            ->scalar('product_code')
            ->maxLength('product_code', 15, '商品コード' . E_V0003)
            ->allowEmptyString('product_code', true)
            ->add('product_code', [
                'alphaNumeric' => [
                    'rule' => function ($value) {
                        return preg_match('/^[a-zA-Z0-9]+$/', $value) ? true : false;
                    },
                    'message' => '商品コード' . E_V0007
                ]
            ]);

        $validator
            ->scalar('product_name')
            ->maxLength('product_name', 50, '商品名' . E_V0003)
            ->allowEmptyString('product_name', true);

        $validator
            ->scalar('product_standard')
            ->maxLength('product_standard', 20, '規格' . E_V0003)
            ->allowEmptyString('product_standard', true);

        $validator
            ->scalar('in_numbers')
            ->allowEmptyString('in_numbers', true);

        $validator
            ->scalar('unit_name')
            ->maxLength('unit_name', 10, '単位名' . E_V0003)
            ->allowEmptyString('unit_name', true);

        $validator
            ->decimal('order_num')
            ->requirePresence('order_num', 'create')
            ->allowEmptyString('order_num', false, '売上数量' . E_V0001);

        $validator
            ->decimal('order_price')
            ->requirePresence('order_price', 'create')
            ->allowEmptyString('order_price', false, '売上単価' . E_V0001);

        $validator
            ->integer('order_amount')
            ->requirePresence('order_amount', 'create')
            ->maxLength('order_amount', 15, '売上金額' . E_V0003)
            ->allowEmptyString('order_amount', false, '売上金額' . E_V0001);

        $validator
            ->decimal('order_tax')
            ->requirePresence('order_tax', 'create')
            ->maxLength('order_tax', 15, '明細消費税額' . E_V0003)
            ->allowEmptyString('order_tax', false, '明細消費税額' . E_V0001);

        $validator
            ->integer('tax_type')
            ->allowEmptyString('tax_type', false, '税区分' . E_V0001)
            ->numeric('tax_type', '税区分' . E_V0006)
            ->add('tax_type', [
                'allowedNumber' => [
                    'rule' => function ($value) {
                        return ($value == 0 || $value == 1 || $value == 2 || $value == 3) ? true : false;
                    },
                    'message' => '税区分' . E_V0004
                ]
            ]);

        $validator
            ->integer('tax_rate')
            ->allowEmptyString('tax_rate', false, '税率' . E_V0001)
            ->numeric('tax_rate', '税率' . E_V0006)
            ->add('tax_rate', [
                'allowedNumber' => [
                    'rule' => function ($value) {
                        return ($value == 0 || $value == 8 || $value == 10) ? true : false;
                    },
                    'message' => '税率' . E_V0004
                ]
            ]);

        $validator
            ->integer('paid_amount')
            ->requirePresence('paid_amount', 'create')
            ->maxLength('paid_amount', 15, '入金額' . E_V0003)
            ->allowEmptyString('paid_amount', false, '入金額' . E_V0001);

        $validator
            ->integer('discount')
            ->requirePresence('discount', 'create')
            ->maxLength('discount', 15, '値引額' . E_V0003)
            ->allowEmptyString('discount', false, '値引額' . E_V0001);

        $validator
            ->scalar('bikou')
            ->maxLength('bikou', 25, '備考' . E_V0003)
            ->allowEmptyString('bikou', true);

        $validator
            ->scalar('created_user')
            ->maxLength('created_user', 20, '作成者' . E_V0003)
            ->allowEmptyString('created_user', false, '作成者' . E_V0001);

        $validator
            ->scalar('updated_user')
            ->maxLength('updated_user', 20, '更新者' . E_V0003)
            ->allowEmptyString('updated_user', false, '作成者' . E_V0001);

        return $validator;
    }
}
