<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * CProductInfoByBase Model
 *
 * @property \App\Model\Table\BasesTable|\Cake\ORM\Association\BelongsTo $Bases
 *
 * @method \App\Model\Entity\CProductInfoByBase get($primaryKey, $options = [])
 * @method \App\Model\Entity\CProductInfoByBase newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\CProductInfoByBase[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\CProductInfoByBase|bool save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\CProductInfoByBase saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\CProductInfoByBase patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\CProductInfoByBase[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\CProductInfoByBase findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class CProductInfoByBaseTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('c_product_info_by_base');
        $this->setDisplayField('company_code');
        $this->setPrimaryKey(['company_code', 'base_id', 'product_code']);

        $this->addBehavior('Timestamp');

        $this->belongsTo('Bases', [
            'foreignKey' => 'base_id',
            'joinType' => 'INNER'
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->scalar('company_code')
            ->maxLength('company_code', 3, '企業コード' . E_V0003)
            ->allowEmptyString('company_code', false, '企業コード' . E_V0001);

        $validator
            ->scalar('product_code')
            ->maxLength('product_code', 15)
            ->allowEmptyString('product_code', false, '商品コード' . E_V0001);

        $validator
            ->integer('order_division')
            ->requirePresence('order_division', 'create')
            ->allowEmptyString('order_division', false, '受発注区分' . E_V0001);

        $validator
            ->integer('lead_time')
            ->requirePresence('lead_time', 'create')
            ->allowEmptyString('lead_time', false, '納品リードタイム' . E_V0001);

        $validator
            ->date('price_switch_day')
            ->allowEmptyDate('price_switch_day');

        $validator
            ->decimal('price_before')
            ->requirePresence('price_before', 'create')
            ->allowEmptyString('price_before', true);

        $validator
            ->decimal('price_after')
            ->allowEmptyString('price_after');

        $validator
            ->boolean('is_deleted')
            ->allowEmptyString('is_deleted', false);

        $validator
            ->scalar('created_user')
            ->maxLength('created_user', 20)
            ->allowEmptyString('created_user', false);

        $validator
            ->scalar('updated_user')
            ->maxLength('updated_user', 20)
            ->allowEmptyString('updated_user', false);

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules)
    {
        $rules->add($rules->existsIn(['base_id'], 'Bases'));

        return $rules;
    }
}
