<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * MPlans Model
 *
 * @method \App\Model\Entity\MPlan get($primaryKey, $options = [])
 * @method \App\Model\Entity\MPlan newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\MPlan[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\MPlan|bool save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MPlan|bool saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MPlan patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\MPlan[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\MPlan findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class MPlansTable extends Table
{

    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('m_plans');
        $this->setDisplayField('name');
        $this->setPrimaryKey('code');

        $this->addBehavior('Timestamp');

        $this->hasMany('MCompanies')
            ->setForeignKey('plan_code')
            ->setBindingKey('code')
            ->setDependent(true);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->scalar('code')
            ->maxLength('code', 5)
            ->allowEmptyString('code', 'create');

        $validator
            ->scalar('name')
            ->maxLength('name', 100)
            ->requirePresence('name', 'create')
            ->allowEmptyString('name', false);

        $validator
            ->integer('max_customers')
            ->requirePresence('max_customers', 'create')
            ->allowEmptyString('max_customers', false);

        $validator
            ->scalar('created_user')
            ->maxLength('created_user', 20)
            ->requirePresence('created_user', 'create')
            ->allowEmptyString('created_user', false);

        $validator
            ->scalar('updated_user')
            ->maxLength('updated_user', 20)
            ->requirePresence('updated_user', 'create')
            ->allowEmptyString('updated_user', false);

        return $validator;
    }
}
