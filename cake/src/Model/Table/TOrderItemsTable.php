<?php

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * TOrderItems Model
 *
 * @method \App\Model\Entity\TOrderItem get($primaryKey, $options = [])
 * @method \App\Model\Entity\TOrderItem newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\TOrderItem[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\TOrderItem|bool save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\TOrderItem|bool saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\TOrderItem patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\TOrderItem[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\TOrderItem findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class TOrderItemsTable extends Table
{

    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('t_order_items');
        $this->setDisplayField('company_code');
        $this->setPrimaryKey(['company_code', 'slip_no', 'slip_detail_no']);

        $this->addBehavior('Timestamp');
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->scalar('company_code')
            ->maxLength('company_code', 3, '企業コード' . E_V0003)
            ->allowEmptyString('company_code', 'create');

        $validator
            ->nonNegativeInteger('slip_no')
            ->allowEmptyString('slip_no', 'create');

        $validator
            ->nonNegativeInteger('slip_detail_no')
            ->allowEmptyString('slip_detail_no', 'create');

        $validator
            ->scalar('customer_code')
            ->maxLength('code', 10)
            ->requirePresence('customer_code', 'create')
            ->allowEmptyString('customer_code', false, '取引先コード' . E_V0001);

        $validator
            ->scalar('customer_edaban')
            ->maxLength('edaban', 5)
            ->requirePresence('customer_edaban', 'create')
            ->allowEmptyString('customer_edaban', false, false, '取引先枝番' . E_V0001);

        $validator
            ->scalar('customer_name')
            ->maxLength('customer_name', 50, '取引先名' . E_V0003)
            ->allowEmptyString('customer_name', true);

        $validator
            ->scalar('management_product_code')
            ->maxLength('management_product_code', 32, '商品コード' . E_V0003)
            ->requirePresence('management_product_code', 'create')
            ->allowEmptyString('management_product_code', false, '商品コード' . E_V0001);

        $validator
            ->scalar('product_name')
            ->maxLength('product_name', 100, '商品名' . E_V0003)
            ->requirePresence('product_name', 'create')
            ->allowEmptyString('product_name', false, '商品名' . E_V0001);

        $validator
            ->scalar('standard')
            ->maxLength('standard', 20, '規格' . E_V0003)
            ->requirePresence('standard', 'create')
            ->allowEmptyString('standard', false, '規格' . E_V0001);

        $validator
            ->decimal('in_numbers')
            ->allowEmptyString('in_numbers', false, '入数' . E_V0001);

        $validator
            ->scalar('unit_code')
            ->maxLength('unit_code', 5, '単位コード' . E_V0003)
            ->requirePresence('unit_code', 'create')
            //->allowEmptyString('unit_code', false, '単位コード' . E_V0001);
            ->allowEmptyString('unit_code', true);

        $validator
            ->scalar('unit_name')
            ->maxLength('unit_name', 20, '単位名' . E_V0003)
            ->requirePresence('unit_name', 'create')
            ->allowEmptyString('unit_name', false, '単位名' . E_V0001);

        $validator
            ->scalar('category_code')
            ->maxLength('category_code1', 15, '分類コード' . E_V0003)
            ->requirePresence('category_code1', 'create')
            //->allowEmptyString('category_code1', false, '分類コード' . E_V0001);
            ->allowEmptyString('category_code1', true);

        $validator
            ->scalar('category_name_1')
            ->maxLength('category_name_1', 30, '大分類' . E_V0003)
            ->requirePresence('category_name_1', 'create')
            //->allowEmptyString('category_name_1', false, '大分類' . E_V0001);
            ->allowEmptyString('category_name_1', true);

        $validator
            ->scalar('category_name_2')
            ->maxLength('category_name_2', 30, '中分類' . E_V0003)
            ->requirePresence('category_name_2', 'create')
            ->allowEmptyString('category_name_2', true);

        $validator
            ->scalar('category_name_3')
            ->maxLength('category_name_3', 30, '小分類' . E_V0003)
            ->requirePresence('category_name_3', 'create')
            ->allowEmptyString('category_name_3', true);

        $validator
            ->decimal('price')
            ->requirePresence('price', 'create')
            ->allowEmptyString('price', false, '単価' . E_V0001);

        $validator
            ->decimal('separately_price')
            ->requirePresence('separately_price', 'create')
            ->allowEmptyString('separately_price', false, 'バラ単価' . E_V0001);

        $validator
            ->decimal('case_price')
            ->requirePresence('case_price', 'create')
            ->allowEmptyString('case_price', false, 'ケース単価' . E_V0001);

        $validator
            ->decimal('case_separately')
            ->requirePresence('case_separately', 'create')
            ->allowEmptyString('case_separately', false, 'ケースバラ区分' . E_V0001);

        $validator
            ->scalar('separately_unit_code')
            ->maxLength('separately_unit_code', 5, 'バラ単位コード' . E_V0003)
            ->requirePresence('separately_unit_code', 'create')
            ->allowEmptyString('separately_unit_code', true);

        $validator
            ->scalar('separately_unit_name')
            ->maxLength('separately_unit_name', 20, 'バラ単位名' . E_V0003)
            ->requirePresence('separately_unit_name', 'create')
            ->allowEmptyString('separately_unit_name', false, 'バラ単位名' . E_V0001);

        $validator
            ->decimal('order_num')
            ->requirePresence('order_num', 'create')
            ->allowEmptyString('order_num', false, '発注数量' . E_V0001)
            ->add('order_num', 'custom1', [
                'rule' => function ($value, $context) {
                    // 数量の桁数などのバリデーション
                    if (!preg_match('/^\d+(\.\d+)?$/', $value)) {
                        var_dump('数値ではない');
                        return false;
                    }
                    return true;
                },
                'message' => '発注数量' . 'に数値以外が含まれています'
            ])->add('order_num', 'custom2', [
                'rule' => function ($value, $context) {
                    // 数量の桁数などのバリデーション
                    if ($value > 9999.99) {
                        var_dump('最大数以上');
                        return false;
                    }
                    return true;
                },
                'message' => '発注数量' . 'が最大数を超えています'
            ])->add('order_num', 'custom3', [
                'rule' => function ($value, $context) {
                    // 数量の桁数などのバリデーション
                    if ($value != (int)($value * 100) / 100) {
                        var_dump('小数点が2桁以上');
                        return false;
                    }
                    return true;
                },
                'message' => '発注数量' . 'の小数部は2桁以内で入力してください'
            ]);

        $validator
            ->integer('order_amount')
            ->maxLength('order_amount', 15, '金額' . E_V0003)
            ->requirePresence('order_amount', 'create')
            ->allowEmptyString('order_amount', false, '金額' . E_V0001);

        $validator
            ->decimal('tax_type')
            ->requirePresence('tax_type', 'create')
            ->allowEmptyString('tax_type', false, '税区分' . E_V0001);

        $validator
            ->decimal('tax_kbn')
            ->requirePresence('tax_kbn', 'create')
            ->allowEmptyString('tax_kbn', false, '消費税算出区分' . E_V0001);

        $validator
            ->decimal('order_division')
            ->requirePresence('order_division', 'create')
            ->allowEmptyString('order_division', false, '受発注区分' . E_V0001);

        $validator
            ->scalar('delivery_date')
            ->maxLength('delivery_date', 8, '納品日' . E_V0003)
            ->requirePresence('delivery_date', 'create')
            ->allowEmptyString('delivery_date', false, '納品日' . E_V0001);

        $validator
            ->boolean('is_catalog')
            ->requirePresence('is_catalog', 'create')
            ->allowEmptyString('is_catalog', false, 'カタログフラグ' . E_V0001);

        $validator
            ->boolean('is_deleted')
            ->requirePresence('is_deleted', 'create')
            ->allowEmptyString('is_deleted', false, '削除フラグ' . E_V0001);

        $validator
            ->integer('subtotal')
            ->requirePresence('subtotal', 'create')
            ->allowEmptyString('subtotal', false, '小計' . E_V0001);

        $validator
            ->scalar('bikou')
            ->maxLength('bikou', 50, '伝票明細備考' . E_V0003)
            ->requirePresence('bikou', 'create')
            ->allowEmptyString('bikou', false, '伝票明細備考' . E_V0001);

        $validator
            ->scalar('created_user')
            ->maxLength('created_user', 20, '作成者' . E_V0003)
            ->requirePresence('created_user', 'create')
            ->allowEmptyString('created_user', false, '作成者' . E_V0001);

        $validator
            ->scalar('updated_user')
            ->maxLength('updated_user', 20, '更新者' . E_V0003)
            ->requirePresence('updated_user', 'create')
            ->allowEmptyString('updated_user', false, '更新者' . E_V0001);

        return $validator;
    }
}
