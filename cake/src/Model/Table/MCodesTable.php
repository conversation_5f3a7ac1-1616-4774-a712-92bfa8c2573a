<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * MCodes Model
 *
 * @method \App\Model\Entity\MCode get($primaryKey, $options = [])
 * @method \App\Model\Entity\MCode newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\MCode[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\MCode|bool save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MCode|bool saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MCode patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\MCode[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\MCode findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class MCodesTable extends Table
{

    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('m_codes');
        $this->setDisplayField('company_code');
        $this->setPrimaryKey(['company_code', 'key1', 'key2']);

        $this->addBehavior('Timestamp');
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->scalar('company_code')
            ->maxLength('company_code', 3)
            ->allowEmptyString('company_code', 'create');

        $validator
            ->scalar('key1')
            ->maxLength('key1', 3)
            ->allowEmptyString('key1', 'create');

        $validator
            ->scalar('key2')
            ->maxLength('key2', 25)
            ->allowEmptyString('key2', 'create');

        $validator
            ->scalar('name1')
            ->maxLength('name1', 200)
            ->requirePresence('name1', 'create')
            ->allowEmptyString('name1', false);

        $validator
            ->scalar('name2')
            ->maxLength('name2', 200)
            ->requirePresence('name2', 'create')
            ->allowEmptyString('name2', false);

        $validator
            ->scalar('name3')
            ->maxLength('name3', 200)
            ->requirePresence('name3', 'create')
            ->allowEmptyString('name3', false);

        $validator
            ->decimal('number1')
            ->requirePresence('number1', 'create')
            ->allowEmptyString('number1', false);

        $validator
            ->decimal('number2')
            ->requirePresence('number2', 'create')
            ->allowEmptyString('number2', false);

        $validator
            ->decimal('number3')
            ->requirePresence('number3', 'create')
            ->allowEmptyString('number3', false);

        $validator
            ->scalar('created_user')
            ->maxLength('created_user', 20)
            ->requirePresence('created_user', 'create')
            ->allowEmptyString('created_user', false);

        $validator
            ->scalar('updated_user')
            ->maxLength('updated_user', 20)
            ->requirePresence('updated_user', 'create')
            ->allowEmptyString('updated_user', false);

        return $validator;
    }
}
