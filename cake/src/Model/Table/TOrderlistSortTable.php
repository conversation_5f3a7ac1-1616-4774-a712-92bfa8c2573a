<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * TOrderlistSort Model
 *
 * @method \App\Model\Entity\TOrderlistSort get($primaryKey, $options = [])
 * @method \App\Model\Entity\TOrderlistSort newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\TOrderlistSort[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\TOrderlistSort|bool save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\TOrderlistSort|bool saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\TOrderlistSort patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\TOrderlistSort[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\TOrderlistSort findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class TOrderlistSortTable extends Table
{

    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('t_orderlist_sort');
        $this->setDisplayField('company_code');
        $this->setPrimaryKey(['company_code', 'customer_code', 'customer_edaban', 'management_product_code', 'user_id']);

        $this->addBehavior('Timestamp');

        // $this->belongsTo('Users', [
        //     'foreignKey' => 'user_id',
        //     'joinType' => 'INNER'
        // ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->scalar('company_code')
            ->maxLength('company_code', 3)
            ->allowEmptyString('company_code', 'create');

        $validator
            ->scalar('customer_code')
            ->maxLength('customer_code', 10)
            ->allowEmptyString('customer_code', 'create');

        $validator
            ->scalar('customer_edaban')
            ->maxLength('customer_edaban', 5)
            ->allowEmptyString('customer_edaban', 'create');

        $validator
            ->scalar('management_product_code')
            ->maxLength('management_product_code', 15)
            ->allowEmptyString('management_product_code', 'create');

        $validator
            ->nonNegativeInteger('sort_order_number')
            ->allowEmptyString('sort_order_number');

        $validator
            ->boolean('is_favorite')
            ->requirePresence('is_favorite', 'create')
            ->allowEmptyString('is_favorite', false);

        $validator
            ->boolean('is_trash')
            ->requirePresence('is_trash', 'create')
            ->allowEmptyString('is_trash', false);

        $validator
            ->scalar('created_user')
            ->maxLength('created_user', 20)
            ->requirePresence('created_user', 'create')
            ->allowEmptyString('created_user', false);

        $validator
            ->scalar('updated_user')
            ->maxLength('updated_user', 20)
            ->requirePresence('updated_user', 'create')
            ->allowEmptyString('updated_user', false);

        return $validator;
    }
}
