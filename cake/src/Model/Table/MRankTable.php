<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * MRank Model
 *
 * @method \App\Model\Entity\MRank get($primaryKey, $options = [])
 * @method \App\Model\Entity\MRank newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\MRank[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\MRank|bool save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MRank|bool saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MRank patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\MRank[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\MRank findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class MRankTable extends Table
{

    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('m_rank');
        $this->setDisplayField('company_code');
        $this->setPrimaryKey(['company_code', 'customer_code', 'customer_edaban']);

    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->scalar('company_code')
            ->maxLength('company_code', 3, '企業コード' . E_V0003)
            ->allowEmptyString('company_code', false, '企業コード' . E_V0001);

        $validator
            ->scalar('customer_code')
            ->maxLength('customer_code', 10, '取引先コード' . E_V0003)
            ->allowEmptyString('customer_code', false, '取引先コード' . E_V0001)
            ->add('customer_code', [
                'alphaNumeric' => [
                    'rule' => function ($value) {
                        return preg_match('/^[a-zA-Z0-9]+$/', $value) ? true : false;
                    },
                    'message' => '取引先コード' . E_V0007
                ]
            ]);

        $validator
            ->scalar('customer_edaban')
            ->maxLength('customer_edaban', 5, '取引先枝番' . E_V0003)
            ->allowEmptyString('customer_edaban', false, '取引先枝番' . E_V0001)
            ->add('customer_edaban', [
                'alphaNumeric' => [
                    'rule' => function ($value) {
                        return preg_match('/^[a-zA-Z0-9]+$/', $value) ? true : false;
                    },
                    'message' => '取引先枝番' . E_V0007
                ]
            ]);
        
        $validator
            ->integer('rank_1')
            ->maxLength('rank_1', 1, '販売ランク区分1' . E_V0003)
            ->allowEmptyString('rank_1', false, '販売ランク区分1' . E_V0001)
            ->numeric('rank_1', '販売ランク区分1' . E_V0006);

        $validator
            ->integer('rank_2')
            ->maxLength('rank_2', 1, '販売ランク区分2' . E_V0003)
            ->allowEmptyString('rank_2', false, '販売ランク区分2' . E_V0001)
            ->numeric('rank_2', '販売ランク区分2' . E_V0006);

        $validator
            ->integer('rank_3')
            ->maxLength('rank_3', 1, '販売ランク区分3' . E_V0003)
            ->allowEmptyString('rank_3', false, '販売ランク区分3' . E_V0001)
            ->numeric('rank_3', '販売ランク区分3' . E_V0006);

        $validator
            ->integer('rank_4')
            ->maxLength('rank_4', 1, '販売ランク区分4' . E_V0003)
            ->allowEmptyString('rank_4', false, '販売ランク区分4' . E_V0001)
            ->numeric('rank_4', '販売ランク区分4' . E_V0006);

        $validator
            ->integer('rank_5')
            ->maxLength('rank_5', 1, '販売ランク区分5' . E_V0003)
            ->allowEmptyString('rank_5', false, '販売ランク区分5' . E_V0001)
            ->numeric('rank_5', '販売ランク区分5' . E_V0006);

        $validator
            ->integer('rank_6')
            ->maxLength('rank_6', 1, '販売ランク区分6' . E_V0003)
            ->allowEmptyString('rank_6', false, '販売ランク区分6' . E_V0001)
            ->numeric('rank_6', '販売ランク区分6' . E_V0006);

        $validator
            ->integer('rank_7')
            ->maxLength('rank_7', 1, '販売ランク区分7' . E_V0003)
            ->allowEmptyString('rank_7', false, '販売ランク区分7' . E_V0001)
            ->numeric('rank_7', '販売ランク区分7' . E_V0006);

        $validator
            ->scalar('created_user')
            ->maxLength('created_user', 20)
            ->requirePresence('created_user', 'create')
            ->allowEmptyString('created_user', false);

        $validator
            ->scalar('updated_user')
            ->maxLength('updated_user', 20)
            ->requirePresence('updated_user', 'create')
            ->allowEmptyString('updated_user', false);

        return $validator;
    }
}
