<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * TOrderHeaders Model
 *
 * @property \App\Model\Table\CustomersTable|\Cake\ORM\Association\BelongsTo $Customers
 * @property \App\Model\Table\UsersTable|\Cake\ORM\Association\BelongsTo $Users
 * @property \App\Model\Table\BusinessmenTable|\Cake\ORM\Association\BelongsTo $Businessmen
 *
 * @method \App\Model\Entity\TOrderHeader get($primaryKey, $options = [])
 * @method \App\Model\Entity\TOrderHeader newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\TOrderHeader[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\TOrderHeader|bool save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\TOrderHeader|bool saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\TOrderHeader patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\TOrderHeader[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\TOrderHeader findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class TOrderHeadersTable extends Table
{

    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('t_order_headers');
        $this->setDisplayField('company_code');
        $this->setPrimaryKey(['company_code', 'slip_no']);

        $this->addBehavior('Timestamp');

        $this->belongsTo('m_users', [
            'foreignKey' => 'id'
        ]);
        $this->belongsTo('m_companies', [
            'foreignKey' => 'code'
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->scalar('company_code')
            ->maxLength('company_code', 3, '企業コード' . E_V0003)
            ->allowEmptyString('company_code', 'create');

        $validator
            ->nonNegativeInteger('slip_no')
            ->allowEmptyString('slip_no', 'create');

        $validator
            ->integer('customer_id')
            ->requirePresence('customer_id', 'create')
            ->allowEmptyString('customer_id', false, '取引先ID' . E_V0001);

        $validator
            ->scalar('customer_code')
            ->maxLength('code', 10)
            ->requirePresence('customer_code', 'create')
            ->allowEmptyString('customer_code', false, '取引先コード' . E_V0001);

        $validator
            ->scalar('customer_edaban')
            ->maxLength('edaban', 5)
            ->requirePresence('customer_edaban', 'create')
            ->allowEmptyString('customer_edaban', false, false, '取引先枝番' . E_V0001);

        $validator
            ->scalar('customer_name')
            ->maxLength('name', 50)
            ->requirePresence('customer_name', 'create')
            ->allowEmptyString('customer_name', false, '取引先名' . E_V0001);

        $validator
            ->scalar('slip_date')
            ->maxLength('slip_date', 8)
            ->requirePresence('slip_date', 'create')
            ->allowEmptyString('slip_date', false, '伝票日付' . E_V0001);

        $validator
            ->integer('user_id')
            ->requirePresence('user_id', 'create')
            ->allowEmptyString('user_id', true);

        $validator
            ->integer('businessman_id')
            ->requirePresence('businessman_id', 'create')
            ->allowEmptyString('businessman_id', true);

        $validator
            ->boolean('is_catalog')
            ->requirePresence('is_catalog', 'create')
            ->allowEmptyString('is_catalog', false, 'カタログフラグ' . E_V0001);

        $validator
            ->boolean('is_demo')
            ->requirePresence('is_demo', 'create')
            ->allowEmptyString('is_demo', false, 'デモ用フラグ' . E_V0001);

        $validator
            ->boolean('is_downloaded')
            ->requirePresence('is_downloaded', 'create')
            ->allowEmptyString('is_downloaded', false, 'ダウンロードフラグ' . E_V0001);

        $validator
            ->boolean('is_deleted')
            ->requirePresence('is_deleted', 'create')
            ->allowEmptyString('is_deleted', false, '削除フラグ' . E_V0001);

        $validator
            ->nonNegativeInteger('subtotal')
            ->requirePresence('subtotal', 'create')
            ->allowEmptyString('subtotal', false, '小計' . E_V0001);

        $validator
            ->nonNegativeInteger('tax_total')
            ->requirePresence('tax_total', 'create')
            ->allowEmptyString('tax_total', false, '消費税' . E_V0001);

        $validator
            ->scalar('bikou')
            ->maxLength('bikou', 512, '伝票備考' . E_V0003)
            ->requirePresence('bikou', 'create')
            ->allowEmptyString('bikou', 'create');

        $validator
            ->scalar('created_user')
            ->maxLength('created_user', 20, '作成者' . E_V0003)
            ->requirePresence('created_user', 'create')
            ->allowEmptyString('created_user', false, '作成者' . E_V0001);

        $validator
            ->scalar('updated_user')
            ->maxLength('updated_user', 20, '更新者' . E_V0003)
            ->requirePresence('updated_user', 'create')
            ->allowEmptyString('updated_user', false, '更新者' . E_V0001);

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules)
    {
        $rules->add($rules->existsIn(['id'], 'm_users'));
        $rules->add($rules->existsIn(['code'], 'm_companies'));

        return $rules;
    }
}
