<?php
namespace App\Model\Table;

use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * MBusinessmen Model
 *
 * @method \App\Model\Entity\MBusinessman get($primaryKey, $options = [])
 * @method \App\Model\Entity\MBusinessman newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\MBusinessman[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\MBusinessman|bool save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MBusinessman|bool saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MBusinessman patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\MBusinessman[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\MBusinessman findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class MBusinessmenTable extends Table
{

    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('m_businessmen');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->scalar('company_code')
            ->maxLength('company_code', 3)
            ->requirePresence('company_code', 'create')
            ->allowEmptyString('company_code', false);

        $validator
            ->scalar('login_id')
            ->maxLength('login_id', 15, '担当者コード' . E_V0003)
            ->requirePresence('login_id', 'create')
            ->allowEmptyString('login_id', false)
            ->add('login_id', [
                'login_id_unique' => [
                    'rule' => ['validateUnique', ['scope' => 'company_code']],
                    'provider' => 'table',
                    'message' => '担当者コード' . E_V0005
                ]
            ]);

        $validator
            ->scalar('password')
            ->maxLength('password', 15, 'パスワード' . E_V0003)
            ->requirePresence('password', 'create')
            ->allowEmptyString('password', true);

        $validator
            ->scalar('name')
            ->maxLength('name', 20, '営業担当者名' . E_V0003)
            ->requirePresence('name', 'create')
            ->allowEmptyString('name', false, '営業担当者名' . E_V0001);

        $validator
            ->scalar('bu_code')
            ->maxLength('bu_code', 3, '部コード' . E_V0003)
            ->allowEmptyString('bu_code', true);

        $validator
            ->scalar('ka_code')
            ->maxLength('ka_code', 3, '課コード' . E_V0003)
            ->allowEmptyString('ka_code', true);

        $validator
            ->scalar('kakari_code')
            ->maxLength('kakari_code', 3, '係コード' . E_V0003)
            ->allowEmptyString('kakari_code', true);

        $validator
            ->scalar('base_id')
            ->maxLength('base_id', 3, '拠点ID' . E_V0003)
            ->allowEmptyString('base_id', true);

        $validator
            ->boolean('is_orderstop')
            ->requirePresence('is_orderstop', 'create')
            ->allowEmptyString('is_orderstop', false);

        $validator
            ->dateTime('last_order_date')
            ->requirePresence('last_order_date', 'create')
            ->allowEmptyDateTime('last_order_date');

        $validator
            ->boolean('use_businessman_close_time')
            ->requirePresence('use_businessman_close_time', 'create')
            ->allowEmptyString('use_businessman_close_time', false);

        $validator
            ->scalar('businessman_close_time')
            ->maxLength('businessman_close_time', 4)
            ->requirePresence('businessman_close_time', 'create')
            ->allowEmptyString('businessman_close_time', true);

        $validator
            ->scalar('created_user')
            ->maxLength('created_user', 20)
            ->requirePresence('created_user', 'create')
            ->allowEmptyString('created_user', false);

        $validator
            ->scalar('updated_user')
            ->maxLength('updated_user', 20)
            ->requirePresence('updated_user', 'create')
            ->allowEmptyString('updated_user', false);

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules)
    {
        $rules->add($rules->isUnique(
            ['company_code', 'login_id'],
            '担当者コード' . E_V0005
        ));

        return $rules;
    }
}
