<?php

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * CProductList Model
 *
 * @method \App\Model\Entity\CProductList get($primaryKey, $options = [])
 * @method \App\Model\Entity\CProductList newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\CProductList[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\CProductList|bool save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\CProductList saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\CProductList patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\CProductList[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\CProductList findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class CProductListTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('c_product_list');
        $this->setDisplayField('company_code');
        $this->setPrimaryKey(['company_code', 'product_code']);

        $this->addBehavior('Timestamp');
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->scalar('company_code')
            ->maxLength('company_code', 3, '企業コード' . E_V0003)
            ->allowEmptyString('company_code', false, '企業コード' . E_V0001);

        $validator
            ->scalar('product_code')
            ->maxLength('product_code', 15, '自社管理商品コード' . E_V0003)
            ->numeric('product_code', '自社管理商品コード' . E_V0006)
            ->allowEmptyString('product_code', false, '自社管理商品コード' . E_V0001);

        $validator
            ->scalar('product_name')
            ->maxLength('product_name', 200, '商品名' . E_V0003)
            ->allowEmptyString('product_name', true);

        $validator
            ->scalar('product_kana')
            ->maxLength('product_kana', 200, '商品名カナ' . E_V0003)
            ->allowEmptyString('product_kana', true);

        $validator
            ->scalar('product_standard')
            ->maxLength('product_standard', 20, '規格' . E_V0003)
            ->allowEmptyString('product_standard', true);

        $validator
            ->decimal('in_numbers')
            ->numeric('in_numbers', '入数' . E_V0006)
            ->allowEmptyString('in_numbers', true);

        $validator
            ->scalar('product_packing')
            ->maxLength('product_packing', 20, '荷姿' . E_V0003)
            ->allowEmptyString('product_packing', true);

        $validator
            ->scalar('unit_name')
            ->maxLength('unit_name', 10, '単位名' . E_V0003)
            ->allowEmptyString('unit_name', true);

        $validator
            ->scalar('category_code1')
            ->maxLength('category_code1', 4, '分類１' . E_V0003)
            ->numeric('category_code1', '分類１' . E_V0006)
            ->allowEmptyString('category_code1', true);

        $validator
            ->scalar('category_code2')
            ->maxLength('category_code2', 4, '分類２' . E_V0003)
            ->numeric('category_code2', '分類２' . E_V0006)
            ->allowEmptyString('category_code2', true);

        $validator
            ->scalar('category_code3')
            ->maxLength('category_code3', 4, '分類３' . E_V0003)
            ->numeric('category_code3', '分類３' . E_V0006)
            ->allowEmptyString('category_code3', true);

        $validator
            ->scalar('category_code4')
            ->maxLength('category_code4', 4, '分類４' . E_V0003)
            ->numeric('category_code4', '分類４' . E_V0006)
            ->allowEmptyString('category_code4', true);

        $validator
            ->scalar('category_code5')
            ->maxLength('category_code5', 4, '分類５' . E_V0003)
            ->numeric('category_code5', '分類５' . E_V0006)
            ->allowEmptyString('category_code5', true);

        $validator
            ->scalar('description1')
            ->maxLength('description1', 2000, '商品説明１' . E_V0003)
            ->allowEmptyString('description1', true);

        $validator
            ->scalar('description2')
            ->maxLength('description1', 2000, '商品説明２' . E_V0003)
            ->allowEmptyString('description2', true);

        $validator
            ->date('price_switch_day', ['ymd'], '単価切り替え日' . E_V0004)
            ->allowEmptyDate('price_switch_day', true);
        
        $validator
            ->integer('rank_category_1', '大分類' . E_V0004)
            ->maxLength('rank_category_1', 1, '大分類' . E_V0003)
            ->allowEmptyString('rank_category_1', false, '大分類' . E_V0001);

        $validator
            ->integer('rank_category_2', '中分類' . E_V0004)
            ->maxLength('rank_category_2', 1, '中分類' . E_V0003)
            ->allowEmptyString('rank_category_2', false, '中分類' . E_V0001);
        
        $validator
            ->decimal('rank1_separately_price_before', null, 'ランク1バラ単価（前）' . E_V0004)
            ->allowEmptyString('rank1_separately_price_before', false, 'ランク1バラ単価（前）' . E_V0001);

        $validator
            ->decimal('rank1_separately_price_after', null, 'ランク1バラ単価（後）' . E_V0004)
            ->allowEmptyString('rank1_separately_price_after', true);
        
        $validator
            ->decimal('rank1_case_price_before', null, 'ランク1ケース単価（前）' . E_V0004)
            ->allowEmptyString('rank1_case_price_before', false, 'ランク1ケース単価（前）' . E_V0001);

        $validator
            ->decimal('rank1_case_price_after', null, 'ランク1ケース単価（後）' . E_V0004)
            ->allowEmptyString('rank1_case_price_after', true);

        $validator
            ->decimal('rank2_separately_price_before', null, 'ランク2バラ単価（前）' . E_V0004)
            ->allowEmptyString('rank2_separately_price_before', false, 'ランク2バラ単価（前）' . E_V0001);

        $validator
            ->decimal('rank2_separately_price_after', null, 'ランク2バラ単価（後）' . E_V0004)
            ->allowEmptyString('rank2_separately_price_after', true);
        
        $validator
            ->decimal('rank2_case_price_before', null, 'ランク2ケース単価（前）' . E_V0004)
            ->allowEmptyString('rank2_case_price_before', false, 'ランク2ケース単価（前）' . E_V0001);

        $validator
            ->decimal('rank2_case_price_after', null, 'ランク2ケース単価（後）' . E_V0004)
            ->allowEmptyString('rank2_case_price_after', true);
        
        $validator
            ->decimal('rank3_separately_price_before', null, 'ランク3バラ単価（前）' . E_V0004)
            ->allowEmptyString('rank3_separately_price_before', false, 'ランク3バラ単価（前）' . E_V0001);

        $validator
            ->decimal('rank3_separately_price_after', null, 'ランク3バラ単価（後）' . E_V0004)
            ->allowEmptyString('rank3_separately_price_after', true);
        
        $validator
            ->decimal('rank3_case_price_before', null, 'ランク3ケース単価（前）' . E_V0004)
            ->allowEmptyString('rank3_case_price_before', false, 'ランク3ケース単価（前）' . E_V0001);

        $validator
            ->decimal('rank3_case_price_after', null, 'ランク3ケース単価（後）' . E_V0004)
            ->allowEmptyString('rank3_case_price_after', true);

        $validator
            ->decimal('rank4_separately_price_before', null, 'ランク4バラ単価（前）' . E_V0004)
            ->allowEmptyString('rank4_separately_price_before', false, 'ランク4バラ単価（前）' . E_V0001);

        $validator
            ->decimal('rank4_separately_price_after', null, 'ランク4バラ単価（後）' . E_V0004)
            ->allowEmptyString('rank4_separately_price_after', true);
        
        $validator
            ->decimal('rank4_case_price_before', null, 'ランク4ケース単価（前）' . E_V0004)
            ->allowEmptyString('rank4_case_price_before', false, 'ランク4ケース単価（前）' . E_V0001);

        $validator
            ->decimal('rank4_case_price_after', null, 'ランク4ケース単価（後）' . E_V0004)
            ->allowEmptyString('rank4_case_price_after', true);
        
        $validator
            ->decimal('rank5_separately_price_before', null, 'ランク5バラ単価（前）' . E_V0004)
            ->allowEmptyString('rank5_separately_price_before', false, 'ランク5バラ単価（前）' . E_V0001);

        $validator
            ->decimal('rank5_separately_price_after', null, 'ランク5バラ単価（後）' . E_V0004)
            ->allowEmptyString('rank5_separately_price_after', true);
        
        $validator
            ->decimal('rank5_case_price_before', null, 'ランク5ケース単価（前）' . E_V0004)
            ->allowEmptyString('rank5_case_price_before', false, 'ランク5ケース単価（前）' . E_V0001);

        $validator
            ->decimal('rank5_case_price_after', null, 'ランク5ケース単価（後）' . E_V0004)
            ->allowEmptyString('rank5_case_price_after', true);

        $validator
            ->decimal('rank6_separately_price_before', null, 'ランク6バラ単価（前）' . E_V0004)
            ->allowEmptyString('rank6_separately_price_before', false, 'ランク6バラ単価（前）' . E_V0001);

        $validator
            ->decimal('rank6_separately_price_after', null, 'ランク6バラ単価（後）' . E_V0004)
            ->allowEmptyString('rank6_separately_price_after', true);
        
        $validator
            ->decimal('rank6_case_price_before', null, 'ランク6ケース単価（前）' . E_V0004)
            ->allowEmptyString('rank6_case_price_before', false, 'ランク6ケース単価（前）' . E_V0001);

        $validator
            ->decimal('rank6_case_price_after', null, 'ランク6ケース単価（後）' . E_V0004)
            ->allowEmptyString('rank6_case_price_after', true);
        
        $validator
            ->decimal('rank7_separately_price_before', null, 'ランク7バラ単価（前）' . E_V0004)
            ->allowEmptyString('rank7_separately_price_before', false, 'ランク7バラ単価（前）' . E_V0001);

        $validator
            ->decimal('rank7_separately_price_after', null, 'ランク7バラ単価（後）' . E_V0004)
            ->allowEmptyString('rank7_separately_price_after', true);
        
        $validator
            ->decimal('rank7_case_price_before', null, 'ランク7ケース単価（前）' . E_V0004)
            ->allowEmptyString('rank7_case_price_before', false, 'ランク7ケース単価（前）' . E_V0001);

        $validator
            ->decimal('rank7_case_price_after', null, 'ランク7ケース単価（後）' . E_V0004)
            ->allowEmptyString('rank7_case_price_after', true);

        $validator
            ->decimal('rank8_separately_price_before', null, 'ランク8バラ単価（前）' . E_V0004)
            ->allowEmptyString('rank8_separately_price_before', false, 'ランク8バラ単価（前）' . E_V0001);

        $validator
            ->decimal('rank8_separately_price_after', null, 'ランク8バラ単価（後）' . E_V0004)
            ->allowEmptyString('rank8_separately_price_after', true);
        
        $validator
            ->decimal('rank8_case_price_before', null, 'ランク8ケース単価（前）' . E_V0004)
            ->allowEmptyString('rank8_case_price_before', false, 'ランク8ケース単価（前）' . E_V0001);

        $validator
            ->decimal('rank8_case_price_after', null, 'ランク8ケース単価（後）' . E_V0004)
            ->allowEmptyString('rank8_case_price_after', true);

        $validator
            ->decimal('rank9_separately_price_before', null, 'ランク9バラ単価（前）' . E_V0004)
            ->allowEmptyString('rank9_separately_price_before', false, 'ランク9バラ単価（前）' . E_V0001);

        $validator
            ->decimal('rank9_separately_price_after', null, 'ランク9バラ単価（後）' . E_V0004)
            ->allowEmptyString('rank9_separately_price_after', true);
        
        $validator
            ->decimal('rank9_case_price_before', null, 'ランク9ケース単価（前）' . E_V0004)
            ->allowEmptyString('rank9_case_price_before', false, 'ランク9ケース単価（前）' . E_V0001);

        $validator
            ->decimal('rank9_case_price_after', null, 'ランク9ケース単価（後）' . E_V0004)
            ->allowEmptyString('rank9_case_price_after', true);

        $validator
            ->integer('case_separately')
            ->maxLength('case_separately', 1, 'ケースバラ区分' . E_V0003)
            ->allowEmptyString('case_separately', false, 'ケースバラ区分' . E_V0001)
            ->numeric('case_separately', 'ケースバラ区分' . E_V0006)
            ->add('case_separately', [
                'allowedNumber' => [
                    'rule' => function ($value) {
                        return ($value == 1 || $value == 2 || $value == 3) ? true : false;
                    },
                    'message' => 'ケースバラ区分' . E_V0004
                ]
            ]);

        $validator
            ->integer('lead_time', null, 'リードタイム' . E_V0004)
            ->requirePresence('lead_time', 'create')
            ->allowEmptyString('lead_time', false, 'リードタイム' . E_V0001);

        $validator
            ->boolean('is_visible')
            ->allowEmptyString('is_visible', true);

        $validator
            ->integer('measurement_division', '計量区分' . E_V0004)
            ->allowEmptyString('measurement_division', true);

        $validator
            ->integer('decimal_point_permission_division', '小数点許可区分' . E_V0004)
            ->allowEmptyString('decimal_point_permission_division', true);

        $validator
            ->integer('tax_type', '税区分' . E_V0004)
            ->allowEmptyString('tax_type', true);

        $validator
            ->integer('tax_rate', '税率' . E_V0004)
            ->allowEmptyString('tax_rate', true);

        $validator
            ->scalar('created_user')
            ->maxLength('created_user', 20)
            ->requirePresence('created_user', 'create')
            ->allowEmptyString('created_user', false);

        $validator
            ->scalar('updated_user')
            ->maxLength('updated_user', 20)
            ->requirePresence('updated_user', 'create')
            ->allowEmptyString('updated_user', false);

        return $validator;
    }
}
