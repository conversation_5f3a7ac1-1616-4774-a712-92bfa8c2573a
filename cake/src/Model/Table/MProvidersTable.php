<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * MProviders Model
 *
 * @method \App\Model\Entity\MProvider get($primaryKey, $options = [])
 * @method \App\Model\Entity\MProvider newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\MProvider[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\MProvider|bool save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MProvider|bool saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MProvider patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\MProvider[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\MProvider findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class MProvidersTable extends Table
{

    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('m_providers');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->nonNegativeInteger('id')
            ->allowEmptyString('id', 'create');

        $validator
            ->scalar('login_id')
            ->maxLength('login_id', 15)
            ->allowEmptyString('login_id', 'create');

        $validator
            ->scalar('password')
            ->maxLength('password', 255)
            ->requirePresence('password', 'create')
            ->allowEmptyString('password', false);

        $validator
            ->scalar('name')
            ->maxLength('name', 50)
            ->requirePresence('name', 'create')
            ->allowEmptyString('name', false);

        $validator
            ->scalar('created_user')
            ->maxLength('created_user', 20)
            ->requirePresence('created_user', 'create')
            ->allowEmptyString('created_user', false);

        $validator
            ->scalar('updated_user')
            ->maxLength('updated_user', 20)
            ->requirePresence('updated_user', 'create')
            ->allowEmptyString('updated_user', false);

        return $validator;
    }
}
