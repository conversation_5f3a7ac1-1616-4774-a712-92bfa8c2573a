<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * MHoliday Model
 *
 * @method \App\Model\Entity\MHoliday get($primaryKey, $options = [])
 * @method \App\Model\Entity\MHoliday newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\MHoliday[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\MHoliday|bool save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MHoliday|bool saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MHoliday patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\MHoliday[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\MHoliday findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class MHolidayTable extends Table
{

    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('m_holiday');
        $this->setDisplayField('company_code');
        //$this->setPrimaryKey(['company_code', 'set_date']);
        $this->setPrimaryKey('id'); //主キーをidに変更、更新と削除はidで行う為

        $this->addBehavior('Timestamp');
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->scalar('company_code')
            ->maxLength('company_code', 3)
            ->allowEmptyString('company_code', 'create');

        $validator
            ->date('set_date')
            ->allowEmptyDate('set_date', 'create')
            ->add('set_date', [
                'set_date_unique' => [
                    'rule' => ['validateUnique', ['scope' => 'company_code']],
                    'provider' => 'table',
                    'message' => '対象日付' . E_V0005
                ]
            ]);

        $validator
            ->integer('weekday')
            ->requirePresence('weekday', 'create')
            ->allowEmptyString('holiday_type', false);

        $validator
            ->integer('holiday_type')
            ->requirePresence('holiday_type', 'create')
            ->allowEmptyString('holiday_type', false);

        $validator
            ->scalar('comment')
            ->maxLength('comment', 200, 'コメント' . E_V0003)
            ->requirePresence('comment', 'create')
            ->allowEmptyString('comment', true);

        $validator
            ->scalar('created_user')
            ->maxLength('created_user', 20)
            ->requirePresence('created_user', 'create')
            ->allowEmptyString('created_user', false);

        $validator
            ->scalar('updated_user')
            ->maxLength('updated_user', 20)
            ->requirePresence('updated_user', 'create')
            ->allowEmptyString('updated_user', false);

        return $validator;
    }
}
