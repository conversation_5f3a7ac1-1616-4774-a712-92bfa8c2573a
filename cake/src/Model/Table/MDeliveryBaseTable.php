<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * MDeliveryBase Model
 *
 * @property \App\Model\Table\BasesTable|\Cake\ORM\Association\BelongsTo $Bases
 *
 * @method \App\Model\Entity\MDeliveryBase get($primaryKey, $options = [])
 * @method \App\Model\Entity\MDeliveryBase newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\MDeliveryBase[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\MDeliveryBase|bool save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MDeliveryBase saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MDeliveryBase patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\MDeliveryBase[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\MDeliveryBase findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class MDeliveryBaseTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('m_delivery_base');
        $this->setDisplayField('name');
        $this->setPrimaryKey(['company_code', 'base_id']);

        $this->addBehavior('Timestamp');

        $this->belongsTo('Bases', [
            'foreignKey' => 'base_id',
            'joinType' => 'INNER'
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->scalar('company_code')
            ->maxLength('company_code', 3)
            ->allowEmptyString('company_code', 'create');

        $validator
            ->scalar('name')
            ->maxLength('name', 200)
            ->allowEmptyString('name', false);

        $validator
            ->scalar('created_user')
            ->maxLength('created_user', 20)
            ->allowEmptyString('created_user', false);

        $validator
            ->scalar('updated_user')
            ->maxLength('updated_user', 20)
            ->allowEmptyString('updated_user', false);

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules)
    {
        $rules->add($rules->existsIn(['base_id'], 'Bases'));

        return $rules;
    }
}
