<?php

namespace App\Command;

use App\Service\CatalogBaseImportService;
use App\Service\CatalogProductImportService;
use Cake\Console\Arguments;
use Cake\Console\ConsoleIo;
use Cake\Console\ConsoleOptionParser;
use Cake\Mailer\Email;


/**
 * CatalogImportCommand
 * コンソールコマンドから処理を実行する
 *
 * カタログデータのインポート処理
 *
 * 実行コマンド：bin/cake CatalogImport <companyCode>
 */
class CatalogImportCommand extends BaseCommand
{
    /**
     * CatalogImportCommand constructor.
     */
    public function initialize()
    {
        parent::initialize();

        $this->importLog = new ImportLogClass();
    }

    /**
     * コマンドの引数判定
     * 引数が間違っている場合、処理を終了し、正しいコマンドを表示
     *
     * @param ConsoleOptionParser $parser
     * @return ConsoleOptionParser
     */
    protected function buildOptionParser(ConsoleOptionParser $parser)
    {
        $parser->addArgument('companyCode', [
            'help' => 'Company Code (ex. 001)'
        ]);
        return $parser;
    }

    /**
     * コマンドメイン処理
     *
     * インポート用csvファイルの存在確認
     * csvの内容確認
     * csvファイルの内容を結合し、インポート処理に渡す
     * 実行済みcsvファイルの移動
     *
     * ログは$this->commandLogに出力する
     *
     * @param Arguments $args
     * @param ConsoleIo $io
     * @return void
     */
    public function execute(Arguments $args, ConsoleIo $io)
    {
        // 初期化処理
        $this->init($io);

        // 開始ログ出力
        $currentDate = date("Y-m-d H:i:s");
        $this->commandLog($currentDate);
        $this->commandLog('カタログデータインポート処理(バッチ) 開始' . "\n");

        // パラメータから企業コードをセット
        $this->setCompanyCode($args->getArgument('companyCode'));

        $this->initUploadDirInfo(DIR_CATALOG_DATA_CSV . $this->companyCode);

        $csvPathList = $this->getCsvPathList($this->importDir, 'csv');

        $catalogImportService = new CatalogProductImportService($io);
        $catalogBaseImportService = new CatalogBaseImportService($io);

        $isFormatError = false;
        // 取込前にフォーマットに問題無いかをチェックする
        foreach ($csvPathList as $csvPath) {
            // ファイルパス情報を取得する
            $pathData = pathinfo($csvPath);

            $this->commandLog("「{$pathData['basename']}」フォーマット確認開始");

            if ($catalogImportService->isValidFileName($pathData['filename'])) {
                // カタログ商品取り込み処理
                $result = $catalogImportService->csvFileCheck($csvPath);
            } else if ($catalogBaseImportService->isValidFileName($pathData['filename'])) {
                // 拠点別商品データ取り込み処理
                $result = $catalogBaseImportService->csvFileCheck($csvPath);
            } else {
                // エラーメッセージを設定
                $result = 'ファイル名が正しくありません。';
            }

            if (!is_numeric($result)) {
                $isFormatError = true;
                // エラーメッセージを出力
                $this->commandLog("Error! ファイル名「{$pathData['basename']}」: {$result}");

                // エラーメッセージを追加
                $this->importLog->addErrorMessage($pathData['basename'] . ': ' . $result);

                // 取込エラーとなったCSVを「error」フォルダへ移動させる
                rename(($this->importDir . '/' . $pathData['basename']), ($this->errorDir . '/' . $pathData['basename']));
            }
        }

        // フォーマットに問題がなければ取り込み処理を開始する
        if(!$isFormatError) {
            foreach ($csvPathList as $csvPath) {
                // ファイルパス情報を取得する
                $pathData = pathinfo($csvPath);

                $this->commandLog("「{$pathData['basename']}」取り込み開始");

                if ($catalogImportService->isValidFileName($pathData['filename'])) {
                    // カタログ商品取り込み処理
                    $result = $catalogImportService->importCsvFile($csvPath);
                } else if ($catalogBaseImportService->isValidFileName($pathData['filename'])) {
                    // 拠点別商品データ取り込み処理
                    $result = $catalogBaseImportService->importCsvFile($csvPath);
                } else {
                    // エラーメッセージを設定
                    $result = 'ファイル名が正しくありません。';
                }

                // インポート処理の結果を設定
                if (is_numeric($result)) {
                    // 完了メッセージを出力
                    $nowDate = date("Y-m-d H:i:s");
                    $this->commandLog("       ファイル名「{$pathData['basename']}」の取り込み完了 処理件数:{$result}件" . " (" . $nowDate . ")");

                    // 完了メッセージを追加
                    $this->importLog->addDoneMessage("{$pathData['basename']} 処理件数:{$result}件");

                    // 取込完了したCSVを「done」フォルダへ移動させる
                    rename(($this->importDir . '/' . $pathData['basename']), ($this->doneDir . '/' . $pathData['basename']));
                } else {
                    $warnings = explode(':', $result);

                    if (count($warnings) === 1) {
                        // エラーメッセージを出力
                        $this->commandLog("Error! ファイル名「{$pathData['basename']}」: {$result}");

                        // エラーメッセージを追加
                        $this->importLog->addErrorMessage($pathData['basename'] . ': ' . $result);

                        // 取込エラーとなったCSVを「error」フォルダへ移動させる
                        rename(($this->importDir . '/' . $pathData['basename']), ($this->errorDir . '/' . $pathData['basename']));
                    } else {
                        // Warningの場合
                        // 完了メッセージを出力
                        $nowDate = date("Y-m-d H:i:s");
                        $this->commandLog("       ファイル名「{$pathData['basename']}」の取り込み完了 処理件数:{$warnings[1]}件" . " (" . $nowDate . ")");
                        $this->commandLog("\n 警告:\n{$warnings[2]}");

                        // 完了メッセージを追加
                        $this->importLog->addDoneMessage("{$pathData['basename']} 処理件数:{$warnings[1]}件\n 警告:\n{$warnings[2]}");

                        // 取込完了したCSVを「done」フォルダへ移動させる
                        rename(($this->importDir . '/' . $pathData['basename']), ($this->doneDir . '/' . $pathData['basename']));

                    }
                }
            }

            // カタログ商品データを整理する
            $this->removeCatalogDB();
        }

        $this->commandLog("\nカタログデータインポート処理(バッチ) 完了メール送付処理");

        // 完了報告のメールを送信

        $this->sendEmailWithTemplate('catalog', [
            'doneMessage' => $this->importLog->doneMessage,
            'errorMessage' => $this->importLog->errorMessage,
            'doneNum' => $this->importLog->doneNum,
            'errorNum' => $this->importLog->errorNum,
        ]);

        $this->commandLog("\n成功： " . $this->importLog->doneNum . "件, 失敗: " . $this->importLog->errorNum . "件");

        $this->commandLog("\n" . 'カタログデータインポート処理(バッチ) 終了');
    }

    /**
     * メールのタイトルを取得
     *
     * @return string
     */
    protected function getMailSubject()
    {
        return sprintf($this->getCodeMaster('MSG', 'MAIL_TITLE_CATALOG')['name1'], $this->getSystemName());
    }

    /**
     * カタログDBから不要商品を削除する
     * @return string
     */
    public function removeCatalogDB()
    {
        $command = 'php ' . CATALOG_DIR . 'bin/console eccube:delete:products';

        exec($command, $output);

        $outputLog = '';
        foreach ($output as $key => $log) {
            $outputLog .= $log . "\n";
        }

        $this->commandLog("\n" . $outputLog);
    }
}
