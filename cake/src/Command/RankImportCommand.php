<?php

namespace App\Command;

use App\Service\RankImportService;
use Cake\Console\Arguments;
use Cake\Console\ConsoleIo;
use Cake\Console\ConsoleOptionParser;


/**
 * RankImportCommand
 * コンソールコマンドから処理を実行する
 *
 * ランクデータのインポート処理
 *
 * 実行コマンド：bin/cake RankImport <companyCode>
 */
class RankImportCommand extends BaseCommand
{
    /**
     * RankImportCommand constructor.
     */
    public function initialize()
    {
        parent::initialize();

        $this->importLog = new ImportLogClass();
    }

    /**
     * コマンドの引数判定
     * 引数が間違っている場合、処理を終了し、正しいコマンドを表示
     *
     * @param ConsoleOptionParser $parser
     * @return ConsoleOptionParser
     */
    protected function buildOptionParser(ConsoleOptionParser $parser)
    {
        $parser->addArgument('companyCode', [
            'help' => 'Company Code (ex. 001)'
        ]);
        return $parser;
    }

    /**
     * コマンドメイン処理
     *
     * インポート用csvファイルの存在確認
     * csvの内容確認
     * csvファイルの内容を結合し、インポート処理に渡す
     * 実行済みcsvファイルの移動
     *
     * ログは$this->commandLogに出力する
     *
     * @param Arguments $args
     * @param ConsoleIo $io
     * @return void
     */
    public function execute(Arguments $args, ConsoleIo $io)
    {
        // 初期化処理
        $this->init($io);

        // 開始ログ出力
        $currentDate = date("Y-m-d H:i:s");
        $this->commandLog($currentDate);
        $this->commandLog('取引先販売ランクインポート処理(バッチ) 開始' . "\n");

        // パラメータから企業コードをセット
        $this->setCompanyCode($args->getArgument('companyCode'));

        $this->initUploadDirInfo(DIR_RANK_CSV . $this->companyCode);

        $csvPathList = $this->getCsvPathList($this->importDir, 'csv');

        $rankImportService = new RankImportService($io);

        foreach ($csvPathList as $csvPath) {
            // ファイルパス情報を取得する
            $pathData = pathinfo($csvPath);

            $this->commandLog("「{$pathData['basename']}」取り込み開始");

            if ($rankImportService->isValidFileName($pathData['filename'])) {
                $result = $rankImportService->importCsvFile($csvPath);
            } else {
                // エラーメッセージを設定
                $result = 'ファイル名が正しくありません。';
            }

            // インポート処理の結果を設定
            if (is_numeric($result)) {
                // 完了メッセージを出力
                $nowDate = date("Y-m-d H:i:s");
                $this->commandLog("       ファイル名「{$pathData['basename']}」の取り込み完了 処理件数:{$result}件" . " (" . $nowDate . ")");

                // 完了メッセージを追加
                $this->importLog->addDoneMessage("{$pathData['basename']} 処理件数:{$result}件");

                // 取込完了したCSVを「done」フォルダへ移動させる
                rename(($this->importDir . '/' . $pathData['basename']), ($this->doneDir . '/' . $pathData['basename']));
            } else {
                // エラーメッセージを出力
                $this->commandLog("Error! ファイル名「{$pathData['basename']}」: {$result}");

                // エラーメッセージを追加
                $this->importLog->addErrorMessage($pathData['basename'] . ': ' . $result);

                // 取込エラーとなったCSVを「error」フォルダへ移動させる
                rename(($this->importDir . '/' . $pathData['basename']), ($this->errorDir . '/' . $pathData['basename']));
            }
        }

        $this->commandLog("\n取引先販売ランクインポート処理(バッチ) 完了メール送付処理");

        // 完了報告のメールを送信

        $this->sendEmailWithTemplate('rank', [
            'doneMessage' => $this->importLog->doneMessage,
            'errorMessage' => $this->importLog->errorMessage,
            'doneNum' => $this->importLog->doneNum,
            'errorNum' => $this->importLog->errorNum,
        ]);

        $this->commandLog("\n成功： " . $this->importLog->doneNum . "件, 失敗: " . $this->importLog->errorNum . "件");

        $this->commandLog("\n" . '取引先販売ランクインポート処理(バッチ) 終了');
    }

    /**
     * メールのタイトルを取得
     *
     * @return string
     */
    protected function getMailSubject()
    {
        return sprintf($this->getCodeMaster('MSG', 'MAIL_TITLE_RANK')['name1'], $this->getSystemName());
    }
}
