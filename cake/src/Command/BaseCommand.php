<?php

namespace App\Command;

use Cake\Console\ConsoleIo;
use Cake\Controller\ComponentRegistry;
use App\Controller\Component\CommonComponent;
use Cake\Console\Command;
use Cake\Mailer\Email;
use Cake\ORM\TableRegistry;

/**
 * @property  CommonComponent Common
 */
abstract class BaseCommand extends Command
{
    /** @var ConsoleIo */
    private $io;

    /** @var string */
    protected $companyCode = '';

    /** @var string CSVファイルの保存ディレクトリ */
    protected $importDir = '';

    /** @var string 正常完了ファイルの移動先ディレクトリ */
    protected $doneDir = '';

    /** @var string 異常ファイルの移動先ディレクトリ */
    protected $errorDir = '';

    /** @var ImportLogClass ログ保存用クラス */
    protected $importLog = null;

    /**
     * 初期化処理
     */
    public function initialize()
    {
        parent::initialize();

        // モデルの利用
        $this->Common = new CommonComponent(new ComponentRegistry());

        // セッションの開始
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
    }

    /**
     * 初期化処理
     * execute関数で必ず実行すること
     *
     * @param ConsoleIo $io
     */
    protected function init(ConsoleIo $io)
    {
        $this->io = $io;
    }

    /**
     * ターミナルへのログ出力
     *
     * @param $message
     */
    protected function commandLog($message)
    {
        $this->io->error($message);
    }

    /**
     * 指定したディレクトリが存在しない場合は新規に作成する
     * ディレクトリが存在する or 作成に成功した場合はtrueを返す
     * 同名のファイルが存在した場合 or 作成に失敗した場合はfalseを返す
     *
     * @param $path string ディレクトリのフルパス
     * @return bool
     */
    protected function createDir($path)
    {
        if (!file_exists($path))
            return mkdir($path);

        if (is_dir($path))
            return true;

        return false;
    }

    /**
     * 企業コードをセットする
     * 内容が不正の場合はコマンド処理を終了させる
     *
     * @param $companyCode
     */
    protected function setCompanyCode($companyCode)
    {
        // パラメータのバリデーション
        if (!$companyCode || strlen($companyCode) !== 3) {
            $this->commandLog('Error! 企業コードが不正です: ' . $companyCode);
            $this->abort();
        }

        $this->companyCode = $companyCode;
    }

    /**
     * 指定したディレクトリに存在するファイル（フルパス）一覧を取得する
     * - 指定した拡張子のファイルのみ取得する
     * - 拡張子が未指定の場合はすべてのファイルを取得する
     * - ファイル名の昇順でソートして返す
     *
     * エラー処理
     * - ディレクトリが存在しない場合はコマンド処理を終了させる
     * - 対象のファイルが存在しない場合はコマンド処理を終了させる
     *
     * @param $dirPath string 取得対象のディレクトリ（フルパス）
     * @param $extension string 拡張子（.を除いた部分を指定）
     * @return array
     */
    protected function getCsvPathList($dirPath, $extension = '')
    {
        if (!file_exists($dirPath)) {
            $this->commandLog('Error! ディレクトリが存在しません: ' . $dirPath);
            $this->abort();
        }

        $filePath = $dirPath . '/*' . ($extension === '' ? '' : '.' . $extension);
        $csvPathList = glob($filePath);

        if (!$csvPathList || count($csvPathList) === 0) {
            $this->commandLog("Error! {$extension}ファイルが存在しません: {$dirPath}");
            $this->abort();

            return [];
        }

        sort($csvPathList);   // ファイル名の昇順で並べ替える

        return $csvPathList;
    }

    /**
     * CSV入出力ディレクトリの初期設定を行う
     * @param $csvDirPath
     */
    protected function initUploadDirInfo($csvDirPath)
    {
        // コードマスタのデータを取得
        $this->loadCodeMaster();

        // インポートCSVデータのリストアップ
        $this->importDir = $csvDirPath;

        // 処理後のCSV移動先フォルダ名を設定
        $moveDir = $this->importDir . '/' . date("Ymd"); // yyyymmdd
        $this->doneDir = $moveDir . '/' . 'done';
        $this->errorDir = $moveDir . '/' . 'error';

        // 処理後のCSV移動先フォルダを作成
        if (!$this->createDir($moveDir)) {
            $this->commandLog('Error! ディレクトリの作成に失敗しました: ' . $moveDir);
            $this->abort();
        }
        if (!$this->createDir($this->doneDir)) {
            $this->commandLog('Error! ディレクトリの作成に失敗しました: ' . $this->doneDir);
            $this->abort();
        }
        if (!$this->createDir($this->errorDir)) {
            $this->commandLog('Error! ディレクトリの作成に失敗しました: ' . $this->errorDir);
            $this->abort();
        }
    }

    /* ====================================
     * コードマスタ関連
     * ==================================== */

    /**
     * コードマスタの初期設定
     * DBから取得してsessionに保存する
     */
    protected function loadCodeMaster()
    {
        $this->Common->loadCodeMaster($this->companyCode);
    }

    /**
     * コードマスタより必要な情報を取得する
     * @param $key1 string 認証コード1
     * @param $key2 string 認証コード2
     * @return array
     */
    protected function getCodeMaster($key1, $key2)
    {
        return $this->Common->getCodeMaster($key1, $key2);
    }

    /* ====================================
     * メール送付関連
     * ==================================== */

    /**
     * システム名を取得
     *
     * @return string
     */
    protected function getSystemName()
    {
        return $this->getCodeMaster('SYS', 'SYSTEM_NAME')['name1'];
    }

    /**
     * 送信元メールアドレスを取得
     *
     * @return string
     */
    protected function getMailFrom()
    {
        return $this->getCodeMaster('SYS', 'SYSTEM_EMAIL')['name1'];
    }

    /**
     * 送信元名称を取得
     *
     * @return string
     */
    protected function getMailFromName()
    {
        return $this->getCodeMaster('SYS', 'SYSTEM_EMAIL')['name2'];
    }

    /**
     * メールのタイトルを取得
     * ※ 継承先で定義すること
     *
     * @return string
     */
    abstract protected function getMailSubject();

    /**
     * ドメインを取得
     *
     * @return string
     */
    protected function getDomain()
    {
        return $this->getCodeMaster('SYS', 'DOMAIN')['name1'];
    }

    /**
     * メール送付関数
     *
     * @param $template string メールテンプレート名
     * @param $values array テンプレートに渡す引数
     * @return void
     */
    protected function sendEmailWithTemplate($template, $values = [])
    {
        // メール送信処理
        $email = new Email();
        try {
            $email->setTemplate($template)
                ->setViewVars($values)
                ->setDomain($this->getDomain())
                ->setFrom([$this->getMailFrom() => $this->getMailFromName()])
                ->setTo($this->getTantoEmail())
                ->setSubject($this->getMailSubject());

            // BCCにBMCメンバーを設定する(本番環境のみ)
            if (IS_DEBUG === false) {
                $email->setBcc(CRON_CHECK_ADDRESS);
            }

            $email->send();
        } catch (\Exception $e) {
            $this->commandLog('Error! カタログデータインポート処理(バッチ) メール送信エラー');
        }
    }

    /**
     * メール送信先アドレスを取得する
     *
     * @return array メールアドレスの配列
     */
    protected function getTantoEmail()
    {
        $mCompanies = TableRegistry::getTableLocator()->get('m_companies');
        $companyData = $mCompanies
            ->find()
            ->where(['code' => $this->companyCode])
            ->first();
        $emailTo = $companyData->tanto_email;

        //セミコロン（;）の前後でメールアドレスを分割する
        $emailTo = explode(";", $emailTo);

        //空の要素があれば削除する
        $emailTo = array_filter($emailTo);

        return $emailTo;
    }
}
