<?php
declare(strict_types=1);

namespace App\Service;

use Cake\Controller\Controller;
use Cake\I18n\Time;
use Cake\ORM\Query;
use App\Controller\Component\CommonComponent;

/**
 * Class ProviderNewsService
 * お知らせ情報マスタの操作
 *
 * @property \App\Model\Table\MNewsTable MNews
 * @package App\Service
 *
 */
class ProviderNewsService extends AppService
{
    private $id = "";
    private $set_date = "";
    private $date_from = "";
    private $date_to = "";
    private $title = "";
    private $category = 0; 
    private $text = "";
    private $url = "";
    private $is_visible = 1;
    private $is_login = 0;
    private $is_popup = 0;
    private $is_view_user = 0;
    private $is_view_admin = 0;
    private $priority = 0;

    /**
     * コンストラクタ
     */
    public function __construct($param)
    {
        parent::__construct();

        if (count($param) == 0) return;

        $this->id = $param['id'] ?? "";
        $this->set_date = $param['set_date'] ?? "";
        $this->date_from = $param['date_from'] ?? "";
        $this->date_to = $param['date_to'] ?? "";
        $this->title = $param['title'] ?? "";
        $this->category = $param['category'] ?? "";
        $this->text = $param['text'] ?? "";
        $this->url = $param['url'] ?? "";
        $this->is_visible = $param['is_visible'] ?? "";
        $this->is_login = $param['is_login'] ?? "";
        $this->is_popup = $param['is_popup'] ?? "";
        $this->is_view_user = $param['is_view_user'] ?? "";
        $this->is_view_admin = $param['is_view_admin'] ?? "";
        $this->priority = $param['priority'] ?? "";
    }

    /**
     * 初期化関数
     */
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * お知らせ情報テーブルよりデータを取得する
     *
     * @return \Cake\ORM\Query $object
     */
    public function getNews()
    {
        $newsData = $this->MNews
            ->find()
            ->where(['is_provider' => 1]);

        return $newsData;
    }

    /**
     * お知らせ情報テーブルよりデータを取得する
     * IDを条件として取得する
     *
     * @return \Cake\ORM\Query $object
     */

    public function getNewsWithId(): ?object
    {
        $newsData = $this->MNews
            ->find()
            ->where(['id' => $this->id])
            ->first();

        return $newsData;
    }

    /**
     * お知らせ情報マスタへのデータの追加を行う
     */
    public function insertMNews()
    {
        $nowTime = Time::now(); //システムの現在時刻取得
        //DB登録用データ作成
        $saveData = [
            'company_code' => "ZZZ",   //企業コード
            'set_date' => $this->set_date,
            'date_from' => $this->date_from,
            'date_to' => $this->date_to,
            'title' => $this->title,
            'category' => 3,
            'text' => $this->text,
            'url' => $this->url,
            'is_visible' => $this->is_visible,
            'is_login' => $this->is_login,
            'is_popup' => $this->is_popup,
            'is_view_user' => $this->is_view_user,
            'is_view_admin' => $this->is_view_admin,
            'is_biz' => 0,
            'is_provider' => 1,
            'priority' => $this->priority,
            'created' => $nowTime,
            'created_user' => "Provider",
            'updated' => $nowTime,
            'updated_user' => "Provider"
        ];

        // DBへの登録処理
        $message = $this->insertData($this->MNews, $saveData);
        return $message;
    }

    /**
     * お知らせ情報マスタへのデータの更新を行う
     */
    public function updateMNews()
    {
        $nowTime = Time::now(); //システムの現在時刻取得
        //DB登録用データ作成
        $saveData = [
            'set_date' => $this->set_date,
            'date_from' => $this->date_from,
            'date_to' => $this->date_to,
            'title' => $this->title,
            'category' => 3,
            'text' => $this->text,
            'url' => $this->url,
            'is_visible' => $this->is_visible,
            'is_login' => $this->is_login,
            'is_popup' => $this->is_popup,
            'is_view_user' => $this->is_view_user,
            'is_view_admin' => $this->is_view_admin,
            'is_biz' => 0,
            'is_provider' => 1,
            'priority' => $this->priority,
            'updated' => $nowTime,
            'updated_user' => "Provider"
        ];

        // DBへの登録処理
        $message = $this->updateData($this->MNews, $saveData, $this->id);

        return $message;
    }

    /**
     * お知らせ情報マスタのデータの削除を行う
     */
    public function deleteMNews()
    {
        // DBからの削除処理
        $message = $this->deleteData($this->MNews, $this->id);

        return $message;
    }

    /**
     * コードマスタからカテゴリ表示用の文字列を取得する
     *
     * @return array
     */
    public function getNewsCategory()
    {
        return CommonComponent::getNewsCategory();
    }
}