<?php
declare(strict_types=1);

namespace App\Service;

use Cake\I18n\Time;
use Cake\ORM\Table;
use App\Controller\Component\CommonComponent;

//use Cake\ORM\TableRegistry;

/**
 * Class AdminCompanyService
 *企業情報更新画面
 *
 * @package App\Service
 *
 * @property \App\Model\Table\MCompaniesTable $MCompanies
 */
class AdminCompanyService extends AppService
{
    private $company_name = "";
    private $post_code = "";
    private $address1 = "";
    private $address2 = "";
    private $address3 = "";
    private $tel_no = "";
    private $fax_no = "";
    private $tanto_name = "";
    private $tanto_email = "";
    private $close_time = "";
    private $sender_name = "";
    private $is_maintenance = 0;
    private $is_orderzan = 0;
    private $orderzan_emails = "";
    private $is_order_code = 0;
    private $self_name1 = "";
    private $self_name2 = "";
    private $self_name3 = "";
    private $order_type = [];
    private $use_businessman = 0;
    private $customer_code_type = 0;
    private $terminal_order_type = "";
    private $terminal_tab_type = 1;
    private $terminal_price_type = "";
    private $terminal_picture_type = "";
    private $use_inquiry = "";
    private $use_fixed_phrase = "";


    /**
     * コンストラクタ
     * @param array $param
     */
    public function __construct($param = [])
    {
        parent::__construct();

        if (count($param) == 0) return;

        // 検索条件
        $this->company_name = $param['name'] ?? "";
        $this->post_code = $param['postcode'] ?? "";
        $this->address1 = $param['address1'] ?? "";
        $this->address2 = $param['address2'] ?? "";
        $this->address3 = $param['address3'] ?? "";
        $this->tel_no = $param['tel_no'] ?? "";
        $this->fax_no = $param['fax_no'] ?? "";
        $this->tanto_name = $param['tanto_name'] ?? "";
        $this->tanto_email = $param['tanto_email'] ?? "";
        // 締め時間はコロンを外す
        $this->close_time = str_replace(':', '', $param['close_time'] ?? "");

        $this->sender_name = $param['sender_name'] ?? "";
        //データ保持機能は使用しない
        $this->is_maintenance = 0;
        //発注残通報機能は使用しない
        $this->is_orderzan = 0;
        //発注残通報先メールアドレスは使用しない
        $this->orderzan_emails = "";
        $this->is_order_code = $param['is_order_code'] ?? 0;
        //基幹特有コード名称は使用しない
        $this->self_name1 = "";
        $this->self_name2 = "";
        $this->self_name3 = "";
        for ($i = 0; $i < 10; $i++) {
            $this->order_type[$i] = $param['order_type_' . $i] ?? "";
        }
        $this->use_businessman = $param['use_businessman'] ?? 0;
        $this->customer_code_type = $param['customer_code_type'] ?? 0;
        $this->terminal_order_type = $param['terminal_order_type'] ?? "";
        $this->terminal_tab_type = $param['terminal_tab_type'] ?? 1;
        $this->terminal_price_type = $param['terminal_price_type'] ?? "";
        $this->terminal_picture_type = $param['terminal_picture_type'] ?? "";
        $this->use_inquiry = $param['use_inquiry'] ?? "";
        $this->use_fixed_phrase = $param['use_fixed_phrase'] ?? "";
    }

    /**
     * 初期化関数
     */
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 企業マスタからデータを取得
     */
    public function getCompanyData(): object
    {
        $m_companies = $this->MCompanies
            ->find()
            ->contain('MPlans')
            ->where(['MCompanies.code' => $this->companyCode])
            ->first();

        return $m_companies;
    }

    /**
     * 企業マスタの情報をセッションに登録する
     */
    public function setCompanyDataToSession()
    {
        $companyInfo = $this->getCompanyData();

        if($companyInfo){
            $_SESSION['sessionAdminCompanyInfo']['useBusinessman'] = $companyInfo->use_businessman;
        }
    }

    /**
     * 企業マスタへデータを更新する
     * ※管理者の所属する企業しか更新できない
     */
    public function updateCompanyData()
    {
        $nowTime = Time::now(); //システムの現在時刻取得

        //DB登録用データ作成
        $saveData = [
            'name' => $this->company_name,
            'postcode' => $this->post_code,
            'address1' => $this->address1,
            'address2' => $this->address2,
            'address3' => $this->address3,
            'tel_no' => $this->tel_no,
            'fax_no' => $this->fax_no,
            'tanto_name' => $this->tanto_name,
            'tanto_email' => $this->tanto_email,
            'close_time' => $this->close_time,
            'sender_name' => $this->sender_name,
            'is_maintenance' => $this->is_maintenance,
            'is_orderzan' => $this->is_orderzan,
            'orderzan_emails' => $this->orderzan_emails,
            'is_order_code' => $this->is_order_code,
            'self_name1' => $this->self_name1,
            'self_name2' => $this->self_name2,
            'self_name3' => $this->self_name3,
            'customer_code_type' => $this->customer_code_type,
            'terminal_order_type' => $this->terminal_order_type,
            'terminal_tab_type' => $this->terminal_tab_type,
            'use_businessman' => $this->use_businessman,
            'terminal_price_type' => $this->terminal_price_type,
            'terminal_picture_type' => $this->terminal_picture_type,
            'use_inquiry' => $this->use_inquiry,
            'use_fixed_phrase' => $this->use_fixed_phrase,
            'updated' => $nowTime,
            'updated_user' => $this->userLoginId
        ];
        for ($i = 0; $i < 10; $i++) {
            $saveData['order_type_' . $i] = $this->order_type[$i];
        }

        /*TODO 当機能は使用しないが残しておく
        //発注残通報機能を利用しない場合、発注残通報先メールアドレスをNULLで登録
        if ($saveData['is_orderzan'] === '0') {
            $saveData['orderzan_emails'] = '0';
        }
        */

        //営業マン機能を利用しない場合、「取引先コード出力区分・商品表示順・端末初期表示タブ」をデフォルト値：1で登録
        if ($saveData['use_businessman'] === '0') {
            $saveData['customer_code_type'] = '1';
            $saveData['terminal_order_type'] = '1';
            //$saveData['terminal_tab_type'] = '1';
        }

        //問い合わせ入力機能を利用しない場合、定型文登録機能をデフォルト値：0で登録
        if ($saveData['use_inquiry'] === '0') {
            $saveData['use_fixed_phrase'] = '0';
        }

        // DBへの登録処理
        $message = $this->updateData($this->MCompanies, $saveData, [$this->companyCode]);

        CommonComponent::outputAdminLog('企業マスタ Update', $saveData);

        return $message;
    }

    /**
     * 商品区分をセッションに設定
     */
    public function setOrderType()
    {
        $company = $this->getCompanyData();

        if (isset($company)) {
            $orderType = $company->order_type_0 . ','
                . $company->order_type_1 . ','
                . $company->order_type_2 . ','
                . $company->order_type_3 . ','
                . $company->order_type_4 . ','
                . $company->order_type_5 . ','
                . $company->order_type_6 . ','
                . $company->order_type_7 . ','
                . $company->order_type_8 . ','
                . $company->order_type_9 . ',';

            $_SESSION['sessionAdminCompanyInfo']['orderType'] = $orderType;
        }
    }

    /**
     * 締め時間を取得する
     * 取引先締め時間が設定されている場合はそちらを取得する
     */
    public function getCloseTime(): string
    {
        $m_companies = $this->MCompanies
            ->find()
            ->where(['MCompanies.code' => $this->companyCode])
            ->first();

        return $m_companies['close_time'];
    }

    /**
     * @return mixed
     */
    public function isBusinessmanMode(){
        $companyData = $this->MCompanies
            ->find()
            ->select('use_businessman')
            ->where(['MCompanies.code' => $this->companyCode])
            ->first();

        return $companyData->use_businessman;
    }
}
