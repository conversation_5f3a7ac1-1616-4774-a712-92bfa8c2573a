<?php
declare(strict_types=1);

namespace App\Service;

use Cake\I18n\Time;
use Cake\ORM\Table;

//use Cake\ORM\TableRegistry;

/**
 * Class AdminPlanService
 * プランマスタ操作サービス
 *
 * @package App\Service
 *
 * @property \App\Model\Table\MCompaniesTable $MCompanies
 * @property \App\Model\Table\MPlansTable MPlans
 */
class AdminPlanService extends AppService
{

    /**
     * コンストラクタ
     * @param array $param
     */
    public function __construct($param = [])
    {
        parent::__construct();

        if (count($param) == 0) return;
    }

    /**
     * 初期化関数
     */
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * プランマスタからデータを取得
     */
    public function getPlanList(): object
    {
        $m_companies = $this->MPlans
            ->find()
            ->select([
                'code' => 'MPlans.code',
                'name' => 'MPlans.name',
                'max' => 'FORMAT(MPlans.max_customers, 0)',
                'company_code' => 'mc.code'
            ])
            ->join([
                'table' => 'm_companies',
                'alias' => 'mc',
                'type' => 'LEFT',
                'conditions' => [
                    'mc.code' => $this->companyCode,
                    'mc.plan_code = MPlans.code'
                ]
            ])
            ->all();

        return $m_companies;
    }

}