<?php
declare(strict_types=1);

namespace App\Service;

use App\Controller\Component\CommonComponent;
use App\Service\OrderListService;

/**
 * Class UserLoginData
 * ログイン情報を保存するクラス
 * @package App\Service
 */
class UserLoginData
{
    public $status;
    public $change_password = false;

    public $login_id = '';
    public $secure_code = '';
    public $close_time = '';
    public $delivery_days_flg = '';
    public $user_name = '';
    public $company_code = '';
    public $customer_code = '';
    public $order_code = '';
    public $customer_name = '';
    public $use_order_code = '';
    public $base_id = '';
    public $order_type = [];

    public $is_demo = false;
    public $is_businessman = false;

    /** @var bool 金額の表示・非表示(trueの場合表示) */
    public $terminal_price_type = false;

    /**
     * UserLoginData constructor.
     * @param bool $result
     * @param object $user ユーザマスタの1レコード
     */
    public function __construct(bool $result, $user = null)
    {
        $this->status = $result;
        $this->change_password = false;

        if ($user) {
            $this->login_id = $user->login_id;
            $this->secure_code = $user->secure_code;
            $this->close_time = $user->close_time;
            $this->user_name = $user->name;
            $this->company_code = $user->company_code;
            $this->customer_code = $user->customer_code;
            $this->order_code = $user->order_code;
            $this->customer_name = $user->customer_name;
            $this->use_order_code = $user->is_order_code && $user->order_code && $user->use_order_code;

            $this->order_type[0] = $user->order_type_0;
            $this->order_type[1] = $user->order_type_1;
            $this->order_type[2] = $user->order_type_2;
            $this->order_type[3] = $user->order_type_3;
            $this->order_type[4] = $user->order_type_4;
            $this->order_type[5] = $user->order_type_5;
            $this->order_type[6] = $user->order_type_6;
            $this->order_type[7] = $user->order_type_7;
            $this->order_type[8] = $user->order_type_8;
            $this->order_type[9] = $user->order_type_9;

            $this->terminal_price_type = $user->terminal_price_type;

            $this->is_demo = $user->is_demo;

            // 拠点IDを取得
            $this->base_id = $this->getBaseId();

            // 営業マンログインか
           $this->is_businessman = $user->is_businesman ?? false;

           $this->delivery_days_flg = $this->getDeliveryDaysFlg();
        }
    }

    private function getBaseId()
    {
        $catalog = new CatalogProductService([
            'customer_code' => $this->customer_code,
            'order_code' => $this->order_code,
            'use_order_code' => ''
        ]);

        return $catalog->getBaseID();
    }

    private function getDeliveryDaysFlg()
    {
        $orderListService = new OrderListService();
        $deliveryDaysFlg = $orderListService->getCustomerDeliveryDaysFlg();
        return $deliveryDaysFlg;
    }

}
