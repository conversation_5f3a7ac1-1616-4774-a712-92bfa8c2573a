<?php
declare(strict_types=1);

namespace App\Service;

use Cake\Controller\Controller;
use App\Controller\Component\CommonComponent;
use Cake\Routing\Router;
use DateTime;

/**
 * Class AdminUserService
 * ユーザーマスタの操作
 *
 * @package App\Service
 *
 * @property \App\Model\Table\MUsersTable $MUsers
 * @property \App\Model\Table\MCustomersTable MCustomers
 * @property \App\Model\Table\MCompaniesTable $MCompanies
 */
class AdminUserService extends AppService
{
    private $user_id = "";
    private $user_login_id = "";
    private $user_name = "";
    private $mail_address = "";
    private $tel_no = "";
    private $is_approved = "";
    private $is_ordered = "";
    private $is_logged_in = "";
    private $is_demo = "";
    private $customer_code_type = "";
    private $is_active = "";
    private $order_sort_type = "";
    private $tmp_password = "";
    private $customer_id = "";
    private $customer_code = "";
    private $customer_name = "";
    private $users_id = "";

    /**
     * コンストラクタ
     */
    public function __construct($param)
    {
        parent::__construct();

        if (count($param) == 0) return;

        // 検索条件
        $this->user_id = $param['user_id'] ?? "";
        $this->user_login_id = $param['user_login_id'] ?? "";
        $this->user_name = $param['user_name'] ?? "";
        $this->mail_address = $param['mail_address'] ?? "";
        $this->tel_no = $param['tel_no'] ?? "";
        $this->is_approved = $param['is_approved'] ?? "";
        $this->is_ordered = $param['is_ordered'] ?? "";
        $this->is_logged_in = $param['is_logged_in'] ?? "";
        $this->is_active = $param['is_active'] ?? "";
        $this->order_sort_type = $param['order_sort_type'] ?? "";
        $this->is_demo = $param['is_demo'] ?? "";
        $this->customer_code_type = $param['customer_code_type'] ?? "";
        $this->customer_code = $param['customer_code'] ?? "";
        $this->customer_name = $param['customer_name'] ?? "";

        //登録データ
        $this->tmp_password = $param['tmp_password'] ?? "";
        $this->tmp_password_time = $param['tmp_password_time'] ?? "";

        $this->customer_id = $param['customer_id'] ?? "";

        //削除するユーザーID（複数）
        $this->users_id = $param['users_id'] ?? "";
    }

    /**
     * 初期化関数
     */
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * ユーザマスタよりデータを取得する
     *
     * @return \Cake\ORM\Query $object
     */
    public function getUser(): object
    {
        $userData = $this->MUsers
            ->find()
            ->where(['id' => $this->user_id])
            ->first();

        return $userData;
    }

    /**
     * ユーザマスタよりデータを取得する
     *
     * @return \Cake\ORM\Query $object
     */
    public function getUsers(): object
    {
        // 検索条件の有無に応じてWhere句をセット
        $where = [];
        $where["MUsers.customer_id"] = $this->customer_id;
        $where["MUsers.is_deleted"] = 0;

        $userData = $this->MUsers
            ->find()
            ->where($where);

        return $userData;
    }

    /**
     * ユーザマスタよりデータを取得する
     *
     * @return \Cake\ORM\Query $object
     */
    public function getUsersWithParam(): object
    {
        $model = $this->MUsers;
        $users_where = [];
        $customerIdList = [];
        $queryExprCustomersIdWithCode = [];
        $queryExprCustomersIdWithName = [];
        $queryExpr = $this->MUsers->find()->newExpr();

        //削除フラグが0のデータを取得
        $users_where["MUsers.is_deleted"] = false;

        // 検索条件の有無に応じてWhere句をセット
        //検索条件に端末利用者名を設定する
        if ($this->user_name !== "") $users_where["MUsers.name LIKE"] = "%" . $this->user_name . "%";
        //検索条件にログインIDを設定する
        if ($this->user_login_id !== "") $users_where["MUsers.login_id LIKE"] = "%" . $this->user_login_id . "%";
        //検索条件に取引先コードを設定する
        if ($this->customer_code !== "") {
            //取引先マスタには企業コードが存在するが、ここでは企業コードにかかわらず全て検索したいのでFLAG_OFFで検索する
            $customerIdList = $this->getIdListWithParam('MCustomers', 'code', $this->customer_code, FLAG_OFF);
            $queryExprCustomersIdWithCode = $queryExpr
                ->in("MUsers.customer_id", $customerIdList);
        } else {
            $queryExprCustomersIdWithCode = [];
        }
        //検索条件に取引先名を設定する
        if ($this->customer_name !== "") {
            $customerIdList = $this->getIdListWithParam('MCustomers', 'name', $this->customer_name, FLAG_OFF);
            $queryExprCustomersIdWithName = $queryExpr
                ->in("MUsers.customer_id", $customerIdList);
        } else {
            $queryExprCustomersIdWithName = [];
        }
        //検索条件に承認/未承認（両方/0/1）を設定する
        if ($this->is_approved === "1") $users_where["MUsers.is_approved"] = false;
        if ($this->is_approved === "2") $users_where["MUsers.is_approved"] = true;
        //検索条件に発注済み（すべて/未発注/発注済み）を設定する
        if ($this->is_ordered === "1") $users_where["MUsers.last_order_date is"] = null;
        if ($this->is_ordered === "2") $users_where["MUsers.last_order_date is not"] = null;
        //検索条件に初回ログイン（すべて/未ログイン/ログイン済み）を設定する
        if ($this->is_logged_in === "1") $users_where["MUsers.last_login_date is"] = null;
        if ($this->is_logged_in === "2") $users_where["MUsers.last_login_date is not"] = null;
        //検索条件にデモ端末（すべて/デモ端末/デモ以外）を設定する
        if ($this->is_demo === "1") $users_where["MUsers.is_demo"] = true;
        if ($this->is_demo === "2") $users_where["MUsers.is_demo"] = false;

        $usersData = $model
            ->find()
            ->select([
                'id' => 'MUsers.id',
                'login_id' => 'MUsers.login_id',
                'name' => 'MUsers.name',
                'customer_id' => 'mc.id',
                'customer_code' => 'mc.code',
                'customer_name' => 'mc.name',
                'is_active' => 'MUsers.is_active',
                'order_sort_type' => 'MUsers.order_sort_type',
                'is_approved' => 'MUsers.is_approved',
                'is_demo' => 'MUsers.is_demo',
                'last_login_date' => 'MUsers.last_login_date',
                'last_order_date' => 'MUsers.last_order_date'
            ])
            ->join([
                'mc' => [
                    'table' => 'm_customers',
                    'type' => 'LEFT',
                    'conditions' => 'mc.id = MUsers.customer_id'
                ]
            ])
            ->where(['mc.company_code' => $this->companyCode])
            ->where($users_where)
            ->where($queryExprCustomersIdWithCode)
            ->where($queryExprCustomersIdWithName);

        return $usersData;
    }

    /**
     * ユーザマスタへ登録
     */
    public function insertUser()
    {
        $model = $this->MUsers;
        $currentDateTime = date("Y/m/d H:i:s"); //システムの現在日時取得

        //取引先マスタの存在チェック
        $customerData = $this->MCustomers
            ->find()
            ->where(['id' => $this->customer_id,
                'is_deleted' => false])
            ->first();

        if ($customerData === null) {
            $message = [];
            $message["no_data"] = ["message" => E_A0004];
            return $message;
        }

        //ログインID重複チェック
        $userData = $model
            ->find()
            ->join([
                'mc' => [
                    'table' => 'm_customers',
                    'type' => 'INNER',
                    'conditions' => [
                        'mc.id = MUsers.customer_id',
                        'mc.is_deleted = false'
                    ]
                ]
            ])
            ->where([
                'mc.company_code' => $this->companyCode,
                'MUsers.login_id' => $this->user_login_id,
                'MUsers.is_deleted' => false,
            ])
            ->count();
        if ($userData > 0) {
            $message = [];
            $message["duplicate_login_id"] = ["message" => 'ログインID' . E_V0005];
            return $message;
        }

        $insertData = [
            'login_id' => $this->user_login_id,
            'customer_id' => $this->customer_id,
            'mail_address' => $this->mail_address,
            'tel_no' => $this->tel_no,
            'password' => '',
            'name' => $this->user_name,
            'secure_code' => '',
            'tmp_password' => $this->tmp_password,
            'tmp_password_time' => $currentDateTime,
            'is_approved' => true,
            'is_active' => $this->is_active,
            'order_view_type' => 0,
            'order_category_type' => 0,
            'order_sort_type' => $this->order_sort_type,
            'is_demo' => $this->is_demo,
            'customer_code_type' => $this->customer_code_type,
            'is_deleted' => 0,
            'last_login_date' => null,
            'last_order_date' => null,
            'created' => $currentDateTime,
            'created_user' => $this->userLoginId,
            'updated' => $currentDateTime,
            'updated_user' => $this->userLoginId
        ];

        $model = $this->MUsers;
        $message = $this->insertData($model, $insertData);

        CommonComponent::outputAdminLog('ユーザマスタ Insert', $insertData);

        return $message;
    }

    /**
     * ユーザマスタの更新
     */
    public function updateUser()
    {
        //更新端末の存在チェック
        $userData = $this->MUsers
            ->find()
            ->where(['id' => $this->user_id,
                'customer_id' => $this->customer_id,
                'is_deleted' => false])
            ->first();

        if ($userData === null) {
            $message = [];
            $message["no_data"] = ["message" => E_A0004];
            return $message;
        }

        $currentDateTime = date("Y/m/d H:i:s"); //システムの現在日時取得

        $updateData = [
            'mail_address' => $this->mail_address,
            'tel_no' => $this->tel_no,
            'name' => $this->user_name,
            'is_active' => $this->is_active,
            'order_sort_type' => $this->order_sort_type,
            'is_demo' => $this->is_demo,
            'customer_code_type' => $this->customer_code_type,
            'updated' => $currentDateTime,
            'updated_user' => $this->userLoginId
        ];

        if ($this->tmp_password !== "") {
            $updateData += [
                'tmp_password' => $this->tmp_password,
                'tmp_password_time' => $currentDateTime
            ];
        }

        $model = $this->MUsers;
        $message = $this->updateData($model, $updateData, $this->user_id);

        CommonComponent::outputAdminLog('ユーザマスタ Update', $updateData);

        return $message;
    }

    /**
     * ユーザーマスタのis_approved列を1行更新する
     *
     * @return array
     * @throws \Exception
     */
    public function approveUser()
    {
        $currentDateTime = date("Y/m/d H:i:s"); //システムの現在日時取得
        $intFlagOn = (int)FLAG_ON;
        $message = [];
        $model = $this->MUsers;

        //企業マスタからデータを取得
        $m_companie = $this->MCompanies
            ->find()
            ->where(['MCompanies.code' => $this->companyCode])
            ->first();
        
        //取引先マスタの存在チェック
        $customerData = $this->MCustomers
            ->find()
            ->where(['id' => $this->customer_id,
                'is_deleted' => false])
            ->first();

        if ($customerData === null) {
            $message["error"]["customer"] = "取引先が存在しません。";
            return $message;
        }

        $companyName = $m_companie->name;
        $companyPostcode = $m_companie->postcode;
        $companyAddress1 = $m_companie->address1;
        $companyAddress2 = $m_companie->address2;
        $companyAddress3 = $m_companie->address3;
        $companyTelNo = $m_companie->tel_no;
        $companyFaxNo = $m_companie->fax_no;
        $customerName = $customerData->name;

        // コードマスタから仮パスワードの有効期限を取得する
        $expireTime = CommonComponent::getCodeMaster('SYS', 'TMP_PASSWORD_EXPIRE_TIME')['number1'];
        $date = new DateTime();
        $expireDate = $date
            ->modify('+' . $expireTime . ' hour')
            ->format('Y年m月d日 H時i分');

        //送信先メールアドレスを取得する
        $mailTo = $this->getUserColumnWithId($this->user_id, "mail_address");
        //送信先メールアドレスが取得できなかった時はエラーメッセージを返す
        if ($mailTo === "") {
            $message["no_mail_address"] = ["message" => "送信先メールアドレスを取得できませんでした。"];
            return $message;
        }

        //仮パスワードを生成する
        $tmpPassword = CommonComponent::createRandomPassword(8);

        //データ作成処理
        $approveData = [
            'id' => $this->user_id,
            'tmp_password' => $tmpPassword,
            'tmp_password_time' => $currentDateTime,
            'is_approved' => $intFlagOn,
            'is_active' => $intFlagOn,
            'updated' => $currentDateTime,
            'updated_user' => $this->userLoginId
        ];

        //DB処理
        $message = $this->updateData($model, $approveData, $this->user_id);

        CommonComponent::outputAdminLog('ユーザマスタ（個別承認） Update', $approveData);

        //エラーが発生した場合はエラーメッセージをreturnする
        if (!empty($message)) {
            return $message;
        }

        //ログインID取得処理
        $userData = $this->getUser();
        $loginId = $userData->login_id;
        $userName = $userData->name;

        //メール送信用データ作成処理
        //送信元アドレス
        $fromAddr = CommonComponent::getCodeMaster('SYS', 'SYSTEM_EMAIL')['name1'];
        //送信者名
        $fromName = $this->getSenderName();
        //タイトル
        $systemName = CommonComponent::getCodeMaster('SYS', 'SYSTEM_NAME')['name1'];
        $subject = sprintf(CommonComponent::getCodeMaster('MSG', 'MAIL_TITLE_APPROVED')['name1'], $systemName);

        // ログインURL
        $loginUrl = $this->createLoginUrl();    //送信用ログインURL

        // footer
        $text = CommonComponent::getCodeMaster('MSG', 'MAIL_FOOTER')['name1'];
        $footer = str_replace('<br>', "\r\n", $text);

        //メールを送信する
        $isSendEmail = $this->sendTemplateEmail($fromAddr, $fromName, $mailTo, $subject,
            'approve',
            compact([
                'expireDate',
                'userName',
                'loginId',
                'tmpPassword',
                'loginUrl',
                //'footer'
                'systemName',
                'customerName',
                'companyName',
                'companyPostcode',
                'companyAddress1',
                'companyAddress2',
                'companyAddress3',
                'companyTelNo',
                'companyFaxNo',
            ]));
        if (!$isSendEmail) {
            $message["not_send_email"] = ["message" => "メールを送信できませんでした。"];
        }

        return $message;
    }

    /**
     * ユーザーマスタのデータの論理削除を行う
     */
    public function deleteMUsers()
    {
        // DBからの削除処理
        $model = $this->MUsers;
        $message = $this->logicalDeleteData($model, $this->users_id);

        CommonComponent::outputAdminLog('ユーザマスタ Delete', $this->users_id);

        return $message;
    }

    /**
     *ユーザーマスタのis_approved列を1行以上更新する
     */
    public function bulkApproveMUsers()
    {
        $currentDateTime = date("Y/m/d H:i:s"); //システムの現在日時取得
        $model = $this->MUsers;
        $messages = [];
        $intFlagOn = (int)FLAG_ON;
        // ログインURL
        $loginUrl = $this->createLoginUrl();    //送信用ログインURL

        //企業マスタからデータを取得
        $m_companie = $this->MCompanies
            ->find()
            ->where(['MCompanies.code' => $this->companyCode])
            ->first();
        
        //一人目のユーザ情報から、取引先IDを取得
        $userIdFirst = $this->users_id[0];

        //ユーザマスタ情報を取得
        $userData = $this->MUsers
            ->find()
            ->where(['id' => $userIdFirst])
            ->first();

        //取引先マスタの存在チェック
        $customerData = $this->MCustomers
            ->find()
            ->where(['id' => $userData->customer_id,
                'is_deleted' => false])
            ->first();

        if ($customerData === null) {
            $message["error"]["customer"] = "取引先が存在しません。";
            return $message;
        }

        $companyName = $m_companie->name;
        $companyPostcode = $m_companie->postcode;
        $companyAddress1 = $m_companie->address1;
        $companyAddress2 = $m_companie->address2;
        $companyAddress3 = $m_companie->address3;
        $companyTelNo = $m_companie->tel_no;
        $companyFaxNo = $m_companie->fax_no;
        $customerName = $customerData->name;

        // コードマスタから仮パスワードの有効期限を取得する
        $expireTime = CommonComponent::getCodeMaster('SYS', 'TMP_PASSWORD_EXPIRE_TIME')['number1'];
        $date = new DateTime();
        $expireDate = $date
            ->modify('+' . $expireTime . ' hour')
            ->format('Y年m月d日 H時i分');

        //メール送信用データ作成処理
        //送信元アドレス
        $fromAddr = CommonComponent::getCodeMaster('SYS', 'SYSTEM_EMAIL')['name1'];
        //送信者名
        $fromName = $this->getSenderName();
        //タイトル
        $systemName = CommonComponent::getCodeMaster('SYS', 'SYSTEM_NAME')['name1'];
        $subject = sprintf(CommonComponent::getCodeMaster('MSG', 'MAIL_TITLE_APPROVED')['name1'], $systemName);
        // footer
        $text = CommonComponent::getCodeMaster('MSG', 'MAIL_FOOTER')['name1'];
        $footer = str_replace('<br>', "\r\n", $text);

        foreach ($this->users_id as $userId) {
            $message = [];

            //ログインID取得処理
            $this->user_id = $userId;
            $userData = $this->getUser();
            $loginId = $userData->login_id;

            $userName = $userData->name; //ユーザー名を取得する

            //送信先メールアドレス処理
            $mailTo = $userData->mail_address;
            if ($mailTo === "") {
                $message["no_mail_address_" . $this->user_id] = ["message" => $userName . "さんの送信先メールアドレスを取得できませんでした。"];
                array_push($messages, $message);
                continue;
            }

            //仮パスワードを生成する
            $tmpPassword = CommonComponent::createRandomPassword(8);

            $approveData = [
                'tmp_password' => $tmpPassword,
                'tmp_password_time' => $currentDateTime,
                'is_approved' => $intFlagOn,
                'is_active' => $intFlagOn,
                'updated' => $currentDateTime,
                'updated_user' => $this->userLoginId
            ];
            $message = $this->updateData($model, $approveData, $userId);

            CommonComponent::outputAdminLog('ユーザマスタ（一括承認） Update', $approveData);

            array_push($messages, $message);

            //メールを送信する
            $isSendEmail = $this->sendTemplateEmail($fromAddr, $fromName, $mailTo, $subject,
                'approve',
                compact([
                    'expireDate',
                    'userName',
                    'loginId',
                    'tmpPassword',
                    'loginUrl',
                    //'footer'
                    'systemName',
                    'customerName',
                    'companyName',
                    'companyPostcode',
                    'companyAddress1',
                    'companyAddress2',
                    'companyAddress3',
                    'companyTelNo',
                    'companyFaxNo',
                ]));
            if (!$isSendEmail) {
                $message["not_send_email_" . $this->user_id] = ["message" => $userName . "さんにメールを送信できませんでした。"];
                array_push($messages, $message);
            }
        }

        return $messages;
    }

    /**
     * 引数のテーブルと列から、引数の値と企業コードを条件に、
     * 引数の値であいまい検索し、検索結果のidを配列で戻り値として返す
     *
     * @param object $table
     * @param string $column
     * @param string $value
     * @param string $isCompanyCode
     * @return array
     */
    private function getIdListWithParam($table, $column, $value, $isCompanyCode)
    {
        $idList = [];
        $i = 0;

        //企業コードが存在するテーブルである
        if ($isCompanyCode == FLAG_ON) {
            $entity = $this->$table->find()
                ->where([
                    $table . '.' . $column . ' LIKE' => '%' . $value . '%',
                    'company_code' => $this->companyCode
                ]);
            //企業コードが存在しないテーブルである
        } else {
            $entity = $this->$table->find()
                ->where([
                    $table . '.' . $column . ' LIKE' => '%' . $value . '%'
                ]);
        }

        foreach ($entity as $result) {
            $idList[$i] = $result->id;
            $i++;
        }

        return $idList;
    }

    /**
     * ユーザのログイン情報をユーザに再送メール送信する
     * 仮パスワードを作成して送付する
     */
    public function loginInfoResendMUsers()
    {
        $currentDateTime = date("Y/m/d H:i:s"); //システムの現在日時取得
        $model = $this->MUsers;

        //企業マスタからデータを取得
        $m_companie = $this->MCompanies
            ->find()
            ->where(['MCompanies.code' => $this->companyCode])
            ->first();
        
        //取引先マスタの存在チェック
        $customerData = $this->MCustomers
            ->find()
            ->where(['id' => $this->customer_id,
                'is_deleted' => false])
            ->first();

        if ($customerData === null) {
            $message["error"]["customer"] = "取引先が存在しません。";
            return $message;
        }

        $companyName = $m_companie->name;
        $companyPostcode = $m_companie->postcode;
        $companyAddress1 = $m_companie->address1;
        $companyAddress2 = $m_companie->address2;
        $companyAddress3 = $m_companie->address3;
        $companyTelNo = $m_companie->tel_no;
        $companyFaxNo = $m_companie->fax_no;
        $customerName = $customerData->name;

        // コードマスタから仮パスワードの有効期限を取得する
        $expireTime = CommonComponent::getCodeMaster('SYS', 'TMP_PASSWORD_EXPIRE_TIME')['number1'];
        $date = new DateTime();
        $expireDate = $date
            ->modify('+' . $expireTime . ' hour')
            ->format('Y年m月d日 H時i分');

        //仮パスワードを生成する
        $tmpPassword = CommonComponent::createRandomPassword(8);
        //仮パスワード更新用のデータを作成する
        $data = [
            'id' => $this->user_id,
            'tmp_password' => $tmpPassword,
            'tmp_password_time' => $currentDateTime,
            'updated' => $currentDateTime,
            'updated_user' => $this->userLoginId
        ];
        //仮パスワードを更新する
        $message = $this->updateData($model, $data, $this->user_id);

        CommonComponent::outputAdminLog('ユーザマスタ（仮パスワード更新） Update', $data);

        //仮パスワードが更新できなかった時はエラーメッセージを返す
        if (!empty($message)) {
            return $message;
        }

        //ログインIDを取得する
        $loginId = $this->getUserColumnWithId($this->user_id, "login_id");

        //ログインID取得処理
        $userData = $this->getUser();
        $userName = $userData->name; //ユーザー名を取得する

        //送信先メールアドレスを取得する
        $mailTo = $this->getUserColumnWithId($this->user_id, "mail_address");
        //送信先メールアドレスが取得できなかった時はエラーメッセージを返す
        if ($mailTo === "") {
            $errorMessage["mail_address"] = ["message" => "送信先メールアドレスを取得できませんでした。"];
            return $errorMessage;
        }

        //送信用ログインURLを作成する
        $loginUrl = $this->createLoginUrl();

        //メール送信用データ作成処理
        //送信元アドレス
        $fromAddr = CommonComponent::getCodeMaster('SYS', 'SYSTEM_EMAIL')['name1'];
        //送信者名
        $fromName = $this->getSenderName();
        //タイトル
        $systemName = CommonComponent::getCodeMaster('SYS', 'SYSTEM_NAME')['name1'];
        $subject = sprintf(CommonComponent::getCodeMaster('MSG', 'MAIL_TITLE_LOGIN_INFO')['name1'], $systemName);

        // footer
        $text = CommonComponent::getCodeMaster('MSG', 'MAIL_FOOTER')['name1'];
        $footer = str_replace('<br>', "\r\n", $text);

        //メールを送信する
        $isSendEmail = $this->sendTemplateEmail($fromAddr, $fromName, $mailTo, $subject,
            'resend_login',
            compact([
                'expireDate',
                'userName',
                'loginId',
                'tmpPassword',
                'loginUrl',
                //'footer'
                'systemName',
                'customerName',
                'companyName',
                'companyPostcode',
                'companyAddress1',
                'companyAddress2',
                'companyAddress3',
                'companyTelNo',
                'companyFaxNo',
            ]));
        if ($isSendEmail) {
            return '';
        } else {
            $errorMessage["sendEmail"] = ["message" => "メールを送信できませんでした。"];
            return $errorMessage;
        }
    }

    /**
     * ユーザーマスタから引数のユーザーIDを基に引数で指定した列を取得する
     *
     * @param string $userId
     * @param string $column
     * @return string $userColumn
     */
    private function getUserColumnWithId($userId, $column)
    {
        $userData = $this->MUsers
            ->find()
            ->where(['id' => $userId])
            ->first();
        $userColumn = $userData->$column;
        return $userColumn;
    }

    private function createLoginUrl()
    {
        $domain = CommonComponent::getCodeMaster('SYS', 'SYSTEM_URL')['name1'];
        $loginUrl = $domain . "/" . $this->companyCode . "/login";
        return $loginUrl;
    }

    /**
     * 承認時及びログイン情報再送付時にメールの本文を作成する
     *
     * @param string $loginId
     * @param string $tmpPassword
     * @param string $loginUrl
     * @param boolean $isResend
     * @return $mailBody
     */
    private function createMailBody($loginId, $tmpPassword, $loginUrl, $isResend = false)
    {
        $header = "ログイン情報を送付致します。";
        if ($isResend) {
            $header = "ログイン情報を再送付致します。";
        }

        $mailBody = "";
        $mailBody = $header . "\r\n" .
            "\r\n" .
            "ログイン出来ない等不明な点がございましたら管理者までご連絡ください。" . "\r\n" .
            "\r\n" .
            "＜ログイン情報＞" . "\r\n" .
            "  ログインID : " . $loginId . "\r\n" .
            "  仮パスワード : " . $tmpPassword . "\r\n" .
            "  ログインURL : " . $loginUrl;

        return $mailBody;
    }
}
