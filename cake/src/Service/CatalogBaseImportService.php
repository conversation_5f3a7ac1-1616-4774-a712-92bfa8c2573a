<?php
declare(strict_types=1);

namespace App\Service;

use App\Controller\Component\CommonComponent;
use App\Model\Table\CProductInfoByBaseTable;
use Cake\Console\ConsoleIo;
use Cake\Datasource\ConnectionManager;
use SplFileObject;

/**
 * Class CatalogBaseImportService
 * カタログ表示用の拠点別商品情報をCSVファイルからインポートする
 *
 * @property CProductInfoByBaseTable CProductInfoByBase
 * @package App\Service
 *
 */
class CatalogBaseImportService extends AbstractCsvImportService
{
    /**
     * コンストラクタ
     * @param ConsoleIo|null $io
     */
    public function __construct(ConsoleIo $io = null)
    {
        parent::__construct($io);
    }

    /**
     * ファイル名が「拠点別商品データ」か判定する
     *
     * @inheritDoc
     */
    public function isValidFileName($fileName): bool
    {
        $fileNameSplitArray = explode("-", $fileName);

        // 日付部分は考慮しない
        if ($fileNameSplitArray[0] === CATALOG_BASE_FILE_NAME) {
            return true;
        }

        return false;
    }

    /**
     * @inheritDoc
     */
    protected function readCsvFileToDb($csvFilePath): int
    {
        $insertData = [];
        $lines = 0;

        try {
            // SplFileObjectクラスを使ったCSVファイル処理
            $file = new SplFileObject($csvFilePath, 'r');
            $file->setFlags(
                SplFileObject::READ_CSV |
                SplFileObject::READ_AHEAD |
                SplFileObject::SKIP_EMPTY |
                SplFileObject::DROP_NEW_LINE
            );

            $this->setConnection();

            // インポート前に全データ削除
            $this->CProductInfoByBase->deleteAll([
                'company_code' => '001'
            ]);

            foreach ($file as $key => $line) {
                // UTF-8に変換
                mb_language("Japanese");
                $line = mb_convert_encoding($line, "UTF-8", "SJIS-win");

                // 列数チェック
                if (count($this->csvHeaderFormat) !== count($line)) {
                    throw new \Exception(($key + 1) . "行目の列数" . E_V0004);
                }

                $dbFormat = $this->replaceDbFormat($line);

                // バリデーション
                $validate = $this->CProductInfoByBase
                    ->newEntity($dbFormat);

                if (count($validate->getErrors()) > 0) {
                    // エラーメッセージを例外として投げる
                    $errorMessage = '';
                    foreach ($validate->getErrors() as $err) {
                        foreach ($err as $message) {
                            if ($errorMessage !== '') $errorMessage .= '|';
                            $errorMessage .= $message;
                            break;
                        }
                    }
                    throw new \Exception(($key + 1) . "行目の" . $errorMessage);
                }

                $insertData[] = $dbFormat;

                // 1,000行ごとにバルクインサート
                if (($key + 1) % 1000 === 0) {
                    $this->upsertToDb($insertData, $this->CProductInfoByBase);

                    $nowDate = date("H:i:s");
                    $this->commandLog("       " . ($key + 1 - 1000) . "-" . ($key + 1) . "行目 DB取り込み完了 (" . $nowDate . ")");

                    $insertData = [];
                }

                $lines = $key + 1;
            }

            // DBへインポート/更新
            if (count($insertData) > 0) {
                $this->upsertToDb($insertData, $this->CProductInfoByBase);

                $nowDate = date("H:i:s");
                $this->commandLog("       DB取り込み完了 (" . $nowDate . ")");
            }

            $this->commit();
        } catch (\Exception $e) {
            $this->rollback();

            throw $e;
        }

        return $lines;
    }

    /**
     * @inheritDoc
     */
    protected function replaceDbFormat($csvLineData): array
    {
        // 記述量削減のためにデータ抜き出しのためのクロージャを利用
        $getData = function ($rowName) use ($csvLineData) {
            return $csvLineData[$this->csvHeaderFormat[$rowName]['row']];
        };

        // 単価切り替え日の取得
        $getSwitchDay = $getData('price_switch_day');
        // 単価切り替え日が未設定の場合、単価をNULLで登録する
        if ($getSwitchDay === '' || $getSwitchDay === ' ' || $getSwitchDay === '　') {
            $switchDate = null;
            $priceBefore = null;
            $priceAfter = null;
        } else {
            $switchDate = CommonComponent::convertIntToDate($getSwitchDay);
            $priceBefore = $getData('price_before');
            $priceAfter = $getData('price_after');
        }
        $isVisible = true;

        return [
            'company_code' => $getData('company_code'),
            'product_code' => $getData('product_code'),
            'base_id' => $getData('base_id'),
            'order_division' => $getData('order_division'),
            'lead_time' => $getData('lead_time'),
            'price_switch_day' => $switchDate,
            'price_before' => $priceBefore,
            'price_after' => $priceAfter,
            'is_visible' => $isVisible,
            'created' => $this->currentDateTime,
            'created_user' => $this->currentUser,
            'updated' => $this->currentDateTime,
            'updated_user' => $this->currentUser
        ];
    }

    /**
     * @inheritDoc
     */
    protected function getExcludeColumns(): array
    {
        return [
            'company_code',
            'product_code',
            'base_id',
            'created',
            'created_user',
        ];
    }

    /**
     * @inheritDoc
     */
    protected function getCsvHeader(): array
    {
        /*
         * 書式
         * [
         *   '<列名>' => [  // DBのカラムと同じ名称
         *     'row' => '<int>',  // CSVファイルの列番号（0以上の整数）
         *     'description' => '<string>',  // 列の説明
         *     'required' => <bool>,  // 必須項目の場合true
         *   ],
         *   ...
         * ]
         */

        return [
            'company_code' => [
                'row' => 0,
                'description' => '企業コード',
                'required' => true
            ],
            'product_code' => [
                'row' => 1,
                'description' => '自社管理商品コード',
                'required' => true
            ],
            'base_id' => [
                'row' => 2,
                'description' => '拠点ID',
                'required' => false
            ],
            'order_division' => [
                'row' => 3,
                'description' => '受発注区分',
                'required' => false
            ],
            'lead_time' => [
                'row' => 4,
                'description' => '納品リードタイム',
                'required' => false
            ],
            'price_switch_day' => [
                'row' => 5,
                'description' => '単価切り替え日',
                'required' => false
            ],
            'price_before' => [
                'row' => 6,
                'description' => '単価（前）',
                'required' => false
            ],
            'price_after' => [
                'row' => 7,
                'description' => '単価（後）',
                'required' => false
            ],
        ];
    }

    /**
     * @inheritDoc
     */
    public function csvFileCheck($csvFilePath)
    {
        $lineNum = 0;

        // SplFileObjectクラスを使ったCSVファイル処理
        $file = new SplFileObject($csvFilePath, 'r');
        $file->setFlags(
            SplFileObject::READ_CSV |
            SplFileObject::READ_AHEAD |
            SplFileObject::SKIP_EMPTY |
            SplFileObject::DROP_NEW_LINE
        );

        foreach ($file as $key => $line) {
            // UTF-8に変換
            mb_language("Japanese");
            $line = mb_convert_encoding($line, "UTF-8", "SJIS-win");

            // 列数チェック
            if (count($this->csvHeaderFormat) !== count($line)) {
                return ($key + 1) . "行目の列数" . E_V0004;
            }

            $dbFormat = $this->replaceDbFormat($line);

            // バリデーション
            $validate = $this->CProductInfoByBase
                ->newEntity($dbFormat);

            if (count($validate->getErrors()) > 0) {
                // エラーメッセージを例外として投げる
                $errorMessage = '';
                foreach ($validate->getErrors() as $err) {
                    foreach ($err as $message) {
                        if ($errorMessage !== '') $errorMessage .= '|';
                        $errorMessage .= $message;
                        break;
                    }
                }
                return ($key + 1) . "行目の" . $errorMessage;
            }

            $lineNum = $key + 1;
        }

        return $lineNum;
    }

}
