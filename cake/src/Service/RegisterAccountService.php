<?php
declare(strict_types=1);

namespace App\Service;

use Cake\I18n\Time;
use Cake\ORM\Table;
use App\Controller\Component\CommonComponent;

/**
 * Class RegisterAccountService
 * アカウント登録処理
 *
 * @package App\Service
 *
 * @property \App\Model\Table\DDeliveryResultsTable $DDeliveryResults
 * @property \App\Model\Table\MUsersTable $MUsers
 * @property \App\Model\Table\MCustomersTable MCustomers
 * @property \App\Model\Table\MCompaniesTable $MCompanies
 */
class RegisterAccountService extends AppService
{
    private $customerCode = "";
    private $customerEdaban = "";
    private $deliveryNoteNumber = "";
    private $userName = "";
    private $loginId = "";
    private $mailAddress = "";
    private $telNumber = "";

    /**
     * constructor.
     *
     * @param array $param
     */
    public function __construct(array $param)
    {
        parent::__construct();

        $this->customerCode = $param['customer_code'] ?? "";
        $this->customerName = "";
        $this->deliveryNoteNumber = $param['delivery_note_number'] ?? "";
        $this->userName = $param['user_name'] ?? "";
        $this->loginId = $param['login_id'] ?? "";
        $this->mailAddress = $param['mail_address'] ?? "";
        $this->telNumber = $param['tel_no'] ?? "";
    }

    /**
     * DDeliveryResultsテーブルから照合
     * @return bool
     */
    public function checkDeliveryResults()
    {
        // 納品書番号を使う場合
        if (in_array($this->companyCode, USE_DELIVERY_NOTE_NUMBER_COMPANY_CODE_LIST)) {
            $result = $this->DDeliveryResults
                ->find()
                ->where([
                    'company_code' => $this->companyCode,
                    'customer_code' => $this->customerCode,
                    'delivery_note_number' => $this->deliveryNoteNumber,
                ])
                ->first();

            //取引先コード、納品書番号が存在すればtrue、それ以外はfalseを返す
            if ($result) {
                $this->customerEdaban = $result->customer_edaban;
                return true;
            }

            return false;
        }

        // コードマスタのセキュリティ番号を使う場合
        $result = $this->MCodes
                ->find()
                ->where([
                    'OR' =>
                        [
                            ["company_code" => $this->companyCode],
                            ["company_code" => "CMN"],
                        ],
                    'key1' => 'SYS',
                    'key2' => 'SECURE_CODE',
                ])
                ->order([
                    "company_code = 'CMN'" => 'ASC',
                ])
                ->first();

        if($result){
            if ($this->deliveryNoteNumber === $result->name1)
                return true;
        }

        return false;
    }

    /**
     * 登録可能なログインIDか判定する
     * @return bool 重複:false, 登録可:true
     */
    public function checkValidUserId()
    {
        $result = $this->MUsers
            ->find()
            ->innerJoin(
                ['MCustomers' => 'm_customers'],
                ['MCustomers.id = MUsers.customer_id']
            )
            ->where([
                'MUsers.is_deleted' => false,
                'MCustomers.company_code' => $this->companyCode,
                'login_id' => $this->loginId
            ])
            ->first();

        // 存在しない場合は登録可
        if (!$result) return true;

        return false;
    }

    /**
     * 新規ユーザを申請する
     */
    public function registerNewUser()
    {
        $nowTime = Time::now(); //システムの現在時刻取得
        $saveData = [
            "login_id" => $this->loginId,
            "mail_address" => $this->mailAddress,
            "password" => "",
            "name" => $this->userName,
            "secure_code" => "",
            "tmp_password" => "",
            "is_approved" => 0,
            "is_active" => 0,
            "is_demo" => 0,
            "customer_code_type" => 1,
            "is_deleted" => 0,
            "order_view_type" => 0,
            "order_category_type" => 0,
            "order_sort_type" => 1,
            "last_login_date" => null,
            "last_order_date" => null,
            'created' => $nowTime,
            'created_user' => 'admin',
            'updated' => $nowTime,
            'updated_user' => 'admin',
        ];
        // 取引先IDを取得

        $customer = $this->MCustomers
            ->find()
            ->where([
                'company_code' => $this->companyCode,
                'code' => $this->customerCode,
            ])
            ->first();

        if ($customer) {
            $saveData["customer_id"] = $customer->id;

            $this->customerName = $customer->name;

            $message = $this->insertData($this->MUsers, $saveData);

            CommonComponent::outputUserLog('新規ユーザ（アカウント申請） Insert', $saveData);
        } else {
            $message["error"]["customer"] = "取引先が存在しません。";
        }

        return $message;
    }

    /**
     * メールを送信する
     *
     * @return boolean $result
     */
    public function sendEmailRegister()
    {
        //企業マスタからデータを取得
        $m_companie = $this->MCompanies
            ->find()
            ->where(['MCompanies.code' => $this->companyCode])
            ->first();

        // コードマスタからメール送信用設定値を取得する
        $fromAddr = CommonComponent::getCodeMaster('SYS', 'SYSTEM_EMAIL')['name1'];
        //送信者名
        $fromName = $this->getSenderName();

        $systemName = CommonComponent::getCodeMaster('SYS', 'SYSTEM_NAME')['name1'];
        $subject = sprintf(CommonComponent::getCodeMaster('MSG', 'MAIL_TITLE_REGISTER')['name1'], $systemName);
        $loginUrl = CommonComponent::getCodeMaster('SYS', 'SYSTEM_URL')['name1'] . "/" . $this->companyCode . "/login";

        // footer
        $text = CommonComponent::getCodeMaster('MSG', 'MAIL_FOOTER')['name1'];
        $footer = str_replace('<br>', "\r\n", $text);

        $result = $this->sendTemplateEmail($fromAddr, $fromName, $this->mailAddress, $subject,
            'register',
            array(
                'userName' => $this->userName,
                'loginId' => $this->loginId,
                'mailAddress' => $this->mailAddress,
                'loginUrl' => $loginUrl,
                //'footer' => $footer
                'systemName' => $systemName,
                'customerName' => $this->customerName,
                'companyName' => $m_companie->name,
                'companyPostcode' => $m_companie->postcode,
                'companyAddress1' => $m_companie->address1,
                'companyAddress2' => $m_companie->address2,
                'companyAddress3' => $m_companie->address3,
                'companyTelNo' => $m_companie->tel_no,
                'companyFaxNo' => $m_companie->fax_no,
            ));

        return $result;
    }
}
