<?php
declare(strict_types=1);

namespace App\Service;

use App\Controller\Component\CommonComponent;
use Cake\Console\ConsoleIo;
use Cake\Datasource\ConnectionInterface;
use Cake\Datasource\ConnectionManager;
use Cake\ORM\Table;

/**
 * Class AbstractCsvImportService
 * CSVファイルからインポートする処理の基底クラス
 *
 * @package App\Service
 *
 */
abstract class AbstractCsvImportService extends AppService
{
    /** @var string 取り込み用CSVファイル名（ランダムな名称を設定する） */
    protected $tmpCsvFileName;

    /** @var array 取り込み用CSVのヘッダ情報 */
    protected $csvHeaderFormat = [];

    /** @var string 現在時刻（yyyy/mm/dd hh:mm:ss） */
    protected $currentDateTime;
    /** @var string アップデートユーザ名（system固定） */
    protected $currentUser;

    /** @var string 現在日付（yyyy/mm/dd） */
    protected $nowDate;

    /** @var ConsoleIo 標準入出力先 */
    protected $io;

    /**
     * コンストラクタ
     * @param ConsoleIo|null $io
     */
    public function __construct(ConsoleIo $io = null)
    {
        parent::__construct();

        $this->tmpCsvFileName = '';

        $this->csvHeaderFormat = $this->getCsvHeader();

        $this->currentDateTime = date("Y/m/d H:i:s");
        $this->currentUser = 'system';

        $this->nowDate = date("Y/m/d");

        $this->io = $io;
    }

    /**
     * ファイル名が正しいか判定する
     *
     * @param string $fileName CSVファイルのファイル名（拡張子を除いたもの）
     * @return bool
     */
    abstract public function isValidFileName($fileName): bool;

    /**
     * CSVファイルの内容を読み込んでDBを更新する
     * CSVフォーマットは、getColumnHeader()で指定する
     *
     * @param $csvFilePath
     * @return int
     * @throws \Exception
     */
    abstract protected function readCsvFileToDb($csvFilePath): int;

    /**
     * CSVファイルから取得したデータをDB挿入用フォーマットに変換する
     * @param $csvLineData array
     * @return array
     */
    abstract protected function replaceDbFormat($csvLineData): array;

    /**
     * DBにUPSERTする処理を記述
     *
     * 同キーのデータが存在しない -> Insert
     * 存在する -> 除外カラムを除いてUpdate
     *
     * @param $insertData
     * @param $insertTable Table インサートするテーブルのモデルを設定する
     * @throws \Exception
     */
    protected function upsertToDb($insertData, $insertTable)
    {
        if (count($insertData) === 0) return;

        // INSERT用のカラム名を取得
        $columns = array_keys($insertData[0]);

        // UPDATE用の項目を作成
        $updateColumnsText = '';

        // 除外カラムの取得
        $excludeColumns = $this->getExcludeColumns();

        foreach ($columns as $column) {
            // 除外カラムは更新しない
            if (in_array($column, $excludeColumns))
                continue;

            if ($updateColumnsText !== '') $updateColumnsText .= ', ';
            $updateColumnsText .= "`{$column}`=VALUES(`{$column}`)";
        }

        // 実行sqlの作成
        $query = $insertTable
            ->query()
            ->insert($columns);

        $valuesExpression = $query
            ->clause('values')
            ->setValues($insertData);

        $query->values($valuesExpression)
            ->epilog('ON DUPLICATE KEY UPDATE ' . $updateColumnsText);

        try {
            $query->execute();
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /** @var ConnectionInterface */
    protected $connection = null;

    protected function setConnection()
    {
        $this->connection = ConnectionManager::get('default');

        // コネクション確立
        $this->connection->begin();
    }

    /**
     * コミット処理
     */
    protected function commit()
    {
        $this->connection->commit();
    }

    /**
     * ロールバック処理
     */
    protected function rollback()
    {
        $this->connection->rollback();
    }

    /**
     * UPSERTする際に更新しないカラム名を記述
     *
     * @return array
     */
    abstract protected function getExcludeColumns(): array;

    /**
     * CSVファイルのヘッダ定義を記述
     *
     * @return array
     */
    abstract protected function getCsvHeader(): array;

    /**
     * 拡張子がCSVファイルか判定する
     *
     * @param string $extension 拡張子(.を除いたもの)
     * @return bool
     */
    public function isCsvFile($extension)
    {
        if ($extension === 'csv') {
            return true;
        }

        return false;
    }

    /**
     * ファイルサイズが制限値以下かを確認する
     * 制限オーバーの場合false
     *
     * @param $filePath
     * @return bool
     */
    private function isValidFileSize($filePath)
    {
        $batchFileSize = filesize($filePath);
        if ($batchFileSize > MAX_FILE_SIZE)
            return false;

        return true;
    }

    /**
     * 指定したファイルが正しいか確認する
     * - ファイル名、拡張子、ファイルサイズ
     *
     * @param $filePath string フルパス
     * @return string 正常:'', 異常時:エラーメッセージ
     */
    private function checkValidFile($filePath)
    {
        $pathInfo = pathinfo($filePath);

        if (!file_exists($filePath)) {
            CommonComponent::outputAdminLog(E_P0001);
            return E_P0001;
        }

        if (!$this->isValidFileName($pathInfo['filename'])) {
            CommonComponent::outputAdminLog(E_P0001);
            return E_P0001;
        }

        if (!$this->isCsvFile($pathInfo['extension'])) {
            CommonComponent::outputAdminLog(E_P0002);
            return E_P0002;
        }

        if (!$this->isValidFileSize($filePath)) {
            CommonComponent::outputAdminLog(E_P0003);
            return E_P0003;
        }

        return '';
    }

    /**
     * CSVファイルからDB追加を行う
     *
     * @param string $csvFilePath csvファイルのフルパス
     * @return string|int エラーの場合：エラーメッセージ、正常終了：取り込み件数
     */
    public function importCsvFile($csvFilePath)
    {
        // CSVファイルのチェック
        $checkResult = $this->checkValidFile($csvFilePath);
        if ($checkResult !== '') {
            return $checkResult;
        }

        // CSVファイルからデータを取得してDBを更新
        try {
            $result = $this->readCsvFileToDb($csvFilePath);
        } catch (\Exception $e) {
            return $e->getMessage();
        }

        return $result;
    }

    /**
     * ターミナルへのログ出力
     *
     * @param $message
     */
    protected function commandLog($message)
    {
        if (!$this->io)
            return;

        $this->io->error($message);
    }
}
