<?php
declare(strict_types=1);

namespace App\Service;

use App\Controller\Component\CommonComponent;
use App\Model\Table\CProductInfoByBaseTable;
use App\Model\Table\CProductListTable;
use App\Model\Table\DOrderlistTable;
use App\Model\Table\MBusinessmenTable;
use App\Model\Table\MCustomersTable;
use App\Model\Table\MUsersTable;
use DateTime;

/**
 * Class CatalogProductService
 * カタログ連携用の商品情報を取得するクラス
 *
 * @property DOrderlistTable DOrderlist
 * @property MCustomersTable MCustomers
 * @property MBusinessmenTable MBusinessmen
 * @property CProductListTable CProductList
 * @property CProductInfoByBaseTable CProductInfoByBase
 * @property MUsersTable MUsers
 * @package App\Service
 *
 */
class CatalogProductService extends AppService
{
    private $customerCode;
    private $orderCode;
    private $useOrderCode;

    private $baseId;

    /**
     * コンストラクタ
     * @param $data
     */
    public function __construct($data)
    {
        parent::__construct();

        // Getパラメータから値を設定
        $this->companyCode = CommonComponent::getCompanyCodeFromUrl();
        $this->customerCode = $data['customer_code'] ?? '';
        $this->orderCode = $data['order_code'] ?? '';
        $this->useOrderCode = $data['use_order_code'] ?? '';
    }

    /**
     * 指定した商品コードのデータを、オーダーリスト、カタログテーブルから取得して
     * 表示すべき情報を設定する
     *
     * @param array $productCodeList
     * @return array
     */
    public function getProductInfo(array $productCodeList)
    {
        CommonComponent::outputUserLog('商品データ取得', $productCodeList);

        // 納品予定日リストを作成する
        $nouhinbiList = [];
        $orderService = new OrderListService();
        try {
            $list = $orderService->calcNouhinbiList();
            foreach ($list as $key => $value) {
                $nouhinbiList[] = $value;
            }
        } catch (\Exception $e) {
            $nouhinbiList = [date('Y/m/d')];
        }

        // ログインユーザの拠点IDを取得する
        $this->baseId = $this->getBaseID();

        // 商品情報の取得
        $productData = $this->getProductList($productCodeList);

        // 商品情報を返却用クラスに設定
        $productInfoList = [];

        // 納品可能日の最終日
        $lastDate = $nouhinbiList[count($nouhinbiList) - 1];
        foreach ($productData as $data) {
            // スマ発にデータが存在するかで取得先を変更する
            if ($data->dol_product_name) {
                // スマ発に商品があれば、スマ発から取得
                $name = $data->dol_product_name;
                $standard = $data->dol_standard;
                $in_number = $data->dol_in_number;
                $unit_name = $data->dol_unit_name;
                $price_switch_day = $data->dol_price_switch_day;
                $price_before = $data->dol_price_before;
                $price_after = $data->dol_price_after;
                $lead_time = $data->dol_lead_time;

                $orderDivisionCode = $data->dol_order_division ?? '0';
            } else {
                // カタログ専用商品はカタログから取得
                $name = $data->cpl_product_name;
                $standard = $data->cpl_standard;
                $in_number = $data->cpl_in_number;
                $unit_name = $data->cpl_unit_name;

                $lead_time = $data->cpb_lead_time ?? $data->cpl_lead_time;;
                $orderDivisionCode = $data->cpb_order_division ?? '0';

                // 拠点マスタに存在しない場合はカタログマスタのものを利用する
                if (!$data->cpb_price_switch_day) {
                    $price_switch_day = $data->cpl_price_switch_day;
                    $price_before = $data->cpl_price_before;
                    $price_after = $data->cpl_price_after;
                } else {
                    $price_switch_day = $data->cpb_price_switch_day;
                    $price_before = $data->cpb_price_before;
                    $price_after = $data->cpb_price_after;
                }
            }

            // 受発注区分(名称)の設定
            $orderDivision = $data['order_type_' . $orderDivisionCode];
            // 納品予定日の設定
            $startDate = $nouhinbiList[$lead_time - 1] ?? $nouhinbiList[0];

            $productInfoList[$data->product_code] = new CatalogProductInfo(
                $data->product_code,
                $data->dol_customer_code,
                $data->dol_customer_edaban,
                $name,
                $standard,
                $in_number,
                $data->packing,
                $unit_name,
                $price_switch_day,
                $price_before,
                $price_after,
                $lead_time,
                $orderDivision,
                $orderDivisionCode,
                $data->measurement_division,
                $data->decimal_point_permission_division,
                $data->tax_type,
                $data->tax_rate,
                $startDate,
                $lastDate
            );
        }

//        CommonComponent::outputUserLog('商品データ取得 商品詳細取得完了', $productInfoList);

        return $productInfoList;
    }

    /**
     * ログインユーザの拠点IDを取得する
     *
     * @return string
     */
    public
    function getBaseID(): string
    {
        $subQuery = $this->DOrderlist
            ->find()
            ->select([
                'businessman_code' => 'businessman_code'
            ])
            ->where([
                "company_code" => $this->companyCode,
                "customer_code" => $this->customerCode,
            ]);

        $data = $this->MBusinessmen
            ->find()
            ->select([
                'base_id' => 'MBusinessmen.base_id',
            ])
            ->join([
                'ol' => [
                    'table' => $subQuery,
                    'type' => 'INNER',
                    'conditions' => [
                        'ol.businessman_code = MBusinessmen.login_id',
                    ]
                ]
            ])
            ->where([
                'MBusinessmen.company_code' => $this->companyCode
            ])
            ->order([
                'MBusinessmen.base_id' => 'ASC'
            ])
            ->first();

        return $data->base_id ?? DEFAULT_BASE_ID;
    }

    /**
     * CProductListテーブルから条件に合致するデータを取得する
     * @param array $productCodeList
     * @return \Cake\Datasource\ResultSetInterface
     */
    private
    function getProductList(array $productCodeList)
    {
        // 選択用の商品リストを整理
        $list = "";
        if (count($productCodeList) > 0) {
            foreach ($productCodeList as $value) {
                if ($list !== "") $list .= ",";
                $list .= "'" . $value . "'";
            }
        }

        // 条件の設定
        $where = [
            'CProductList.company_code' => $this->companyCode,
            'CProductList.is_visible' => '1',
        ];
        if ($list !== '') {
            $where[] = 'CProductList.product_code IN (' . $list . ')';
        }

        // Select句の設定
        $select = [
            'company_code' => 'CProductList.product_code',
            'product_code' => 'CProductList.product_code',
            'packing' => 'CProductList.product_packing',
            'measurement_division' => 'CProductList.measurement_division',
            'decimal_point_permission_division' => 'CProductList.decimal_point_permission_division',
            'tax_type' => 'CProductList.tax_type',
            'tax_rate' => 'CProductList.tax_rate',

            // カタログ商品情報
            'cpl_product_name' => 'CProductList.product_name',
            'cpl_standard' => 'CProductList.product_standard',
            'cpl_in_number' => 'CProductList.in_numbers',
            'cpl_unit_name' => 'CProductList.unit_name',
            'cpl_price_switch_day' => 'REPLACE(CProductList.price_switch_day, \'-\', \'/\')',
            'cpl_price_before' => 'CProductList.price_before',
            'cpl_price_after' => 'CProductList.price_after',
            'cpl_lead_time' => 'CProductList.lead_time',

            // 拠点マスタには金額情報が存在しない前提
            'cpb_order_division' => 'cpb.order_division',
            'cpb_lead_time' => 'cpb.lead_time',
            'cpb_price_switch_day' => 'REPLACE(cpb.price_switch_day, \'-\', \'/\')',
            'cpb_price_before' => 'cpb.price_before',
            'cpb_price_after' => 'cpb.price_after',

            // オーダーリストの商品情報
            'dol_product_name' => 'dol.product_name',
            'dol_customer_code' => 'dol.customer_code',
            'dol_customer_edaban' => 'dol.customer_edaban',
            'dol_standard' => 'dol.standard',
            'dol_in_number' => 'dol.in_numbers',
            'dol_unit_name' => 'dol.unit_name',
            'dol_price_switch_day' => 'REPLACE(dol.price_switch_day, \'-\', \'/\')',
            'dol_price_before' => 'dol.price_before',
            'dol_price_after' => 'dol.price_after',
            'dol_order_division' => 'dol.order_division',
            'dol_lead_time' => 'dol.lead_time',

            // 企業マスタから受発注区分を取得
            'order_type_0' => 'mc.order_type_0',
            'order_type_1' => 'mc.order_type_1',
            'order_type_2' => 'mc.order_type_2',
            'order_type_3' => 'mc.order_type_3',
            'order_type_4' => 'mc.order_type_4',
            'order_type_5' => 'mc.order_type_5',
            'order_type_6' => 'mc.order_type_6',
            'order_type_7' => 'mc.order_type_7',
            'order_type_8' => 'mc.order_type_8',
            'order_type_9' => 'mc.order_type_9',
        ];

        // サブクエリの作成
        $subDOrderList = $this->createSubQueryDOrderlist();
        $subCProductBase = $this->createSubQueryCProductInfoByBase();

        $data = $this->CProductList
            ->find()
            ->select($select)
            ->join([
                'dol' => [
                    'table' => $subDOrderList,
                    'type' => 'LEFT',
                    'conditions' => [
                        'dol.company_code = CProductList.company_code',
                        'dol.product_code = CProductList.product_code',
                    ]
                ],
                'cpb' => [
                    'table' => $subCProductBase,
                    'type' => 'LEFT',
                    'conditions' => [
                        'cpb.company_code = CProductList.company_code',
                        'cpb.product_code = CProductList.product_code',
                    ]
                ],
                'mc' => [
                    'table' => 'm_companies',
                    'type' => 'LEFT',
                    'conditions' => [
                        'mc.code = CProductList.company_code',
                    ]
                ]
            ])
            ->where($where);

        // TODO DEBUG後削除
//        CommonComponent::outputUserLog('商品データ取得 SQL', $data->sql());

        $data
            ->all();

        return $data;
    }

    /**
     * DOrderlistテーブルからサブクエリを作成する
     * @return \Cake\ORM\Query
     */
    private
    function createSubQueryDOrderlist()
    {
        // 絞り込み用サブクエリ
        $subQueryWhere = [];
        $subQueryGroup = [
            'company_code',
            'management_product_code',
        ];

        if ($this->useOrderCode == 0) {
            $subQueryWhere['customer_code'] = $this->customerCode;
            $subQueryGroup[] = 'customer_code';
        } else {
            $subQueryWhere['order_code'] = $this->orderCode;
            $subQueryGroup[] = 'order_code';
        }

        $subQuery = $this->DOrderlist
            ->find()
            ->select([
                'company_code' => 'company_code',
                'management_product_code' => 'management_product_code',
            ])
            ->where($subQueryWhere)
            ->group($subQueryGroup);

        $select = [
            'company_code' => 'company_code',
            'product_code' => 'management_product_code',
//            'customer_code' => 'customer_code',
            'customer_edaban' => 'MIN(customer_edaban)',
            'product_name' => 'MIN(product_name)',
            'standard' => 'MIN(product_standard)',
            'in_numbers' => 'MIN(in_numbers)',
            'unit_name' => 'MIN(unit_name)',
            'price_switch_day' => 'MIN(price_switch_day)',
            'price_before' => 'MIN(price_before)',
            'price_after' => 'MIN(price_after)',
            'lead_time' => 'MIN(lead_time)',
            'measurement_division' => 'MIN(measurement_division)',
            'decimal_point_permission_division' => 'MIN(decimal_point_permission_division)',
            'tax_type' => 'MIN(tax_type)',
            'tax_rate' => 'MIN(tax_rate)',
            'order_division' => 'MIN(order_division)',
        ];

        $where = [
            'company_code' => $this->companyCode,
            'is_visible' => '1',
        ];

        $group = [
            'company_code',
            'management_product_code',
            'is_visible'
        ];

        // 取引先コードで絞り込むか、発注コードで絞り込むか選択する
        if ($this->useOrderCode == 0) {
            $select['customer_code'] = 'customer_code';
            $where['customer_code'] = $this->customerCode;
            $group[] = 'customer_code';
        } else {
            $select['customer_code'] = 'MIN(customer_code)';
            $where['order_code'] = $this->orderCode;
            $group[] = 'order_code';
        }

        return $this->DOrderlist
            ->find()
            ->select($select)
            ->where($where)
            ->group($group);
    }

    /**
     * CProductInfoByBaseテーブルからサブクエリを作成する
     * @return \Cake\ORM\Query
     */
    private
    function createSubQueryCProductInfoByBase()
    {
        return $this->CProductInfoByBase
            ->find()
            ->select([
                'company_code' => 'company_code',
                'product_code' => 'product_code',
                'order_division' => 'order_division',
                'price_switch_day' => 'price_switch_day',
                'price_before' => 'price_before',
                'price_after' => 'price_after',
                'lead_time' => 'lead_time',
            ])
            ->where([
                'company_code' => $this->companyCode,
                'base_id' => $this->baseId,
                'is_visible' => '1',
            ]);
    }

    private
    function getDeliveryDateList()
    {

    }
}

/**
 * Class CatalogProductInfo
 *
 * 商品情報の返却用のオブジェクト
 * @package App\Controller
 */
class CatalogProductInfo
{
    public $product_code;
    public $customer_code;
    public $customer_edaban;
    public $name;
    public $standard;
    public $in_number;
    public $packing;
    public $unit_name;
    public $lead_time;
    /** @var string 受発注区分（名称） */
    public $order_division;
    public $order_division_code;
    public $measurement_division;
    public $decimal_point_permission_division;
    public $tax_type;
    public $tax_rate;

    public $price;
    /** @var string 価格変更日の表示内容(yyyy/mm/dd) */
    public $price_switch_day;
    /** @var string 計算用の価格変更日 */
    private $price_switch_day_calc;
    private $price_before;
    private $price_after;

    /** @var string 最短納品日（yyyy/mm/dd） */
    private $startDate;
    /** @var string 価格変更日を表示する上限日付（yyyy/mm/dd） */
    private $lastDate;

    /**
     * CatalogProductInfo constructor.
     * @param $product_code
     * @param $customer_code
     * @param $customer_edaban
     * @param $name
     * @param $standard
     * @param $in_number
     * @param $packing
     * @param $unit_name
     * @param $price_switch_day
     * @param $price_before
     * @param $price_after
     * @param $lead_time
     * @param $order_division
     * @param $order_division_code
     * @param $measurement_division
     * @param $decimal_point_permission_division
     * @param $tax_type
     * @param $tax_rate
     * @param $startDate
     * @param $lastDate
     */
    public function __construct(
        $product_code,
        $customer_code,
        $customer_edaban,
        $name,
        $standard,
        $in_number,
        $packing,
        $unit_name,
        $price_switch_day,
        $price_before,
        $price_after,
        $lead_time,
        $order_division,
        $order_division_code,
        $measurement_division,
        $decimal_point_permission_division,
        $tax_type,
        $tax_rate,
        $startDate,
        $lastDate
    )
    {
        $this->product_code = $product_code;
        $this->customer_code = $customer_code;
        $this->customer_edaban = $customer_edaban;
        $this->name = $name;
        $this->standard = $standard;
        $this->in_number = $in_number;
        $this->packing = $packing;
        $this->unit_name = $unit_name;
        $this->lead_time = $lead_time;
        $this->order_division = $order_division;
        $this->order_division_code = $order_division_code;
        $this->measurement_division = $measurement_division;
        $this->decimal_point_permission_division = $decimal_point_permission_division;
        $this->tax_type = $tax_type;
        $this->tax_rate = $tax_rate;

        $this->price_switch_day_calc = $price_switch_day;
        $this->price_before = $price_before;
        $this->price_after = $price_after;

        $this->startDate = $startDate;
        $this->lastDate = $lastDate;

        $this->price_switch_day = '';
        $this->setPrice();
        $this->setPriceSwitchDay();
    }

    /**
     * 最短納品日 < 単価変更日の場合は、単価を変更する
     */
    private function setPrice()
    {
        $this->price = ($this->price_switch_day_calc <= $this->startDate ? $this->price_after : $this->price_before);
    }

    /**
     * 最短納品日 < 単価変更日 < 本日から1か月の場合は、単価変更日表示させる
     */
    private function setPriceSwitchDay()
    {
        if ($this->startDate < $this->price_switch_day_calc
            && $this->price_switch_day_calc <= $this->lastDate) {
            $this->price_switch_day = $this->price_switch_day_calc;
        }
    }
}
