<?php
declare(strict_types=1);

namespace App\Service;

use App\Controller\Component\CommonComponent;
use Cake\Auth\DefaultPasswordHasher;
use Cake\Datasource\ConnectionManager;
use Cake\ORM\Table;
use Cake\Utility\Security;

/**
 * Class UserService
 * 発注画面のユーザ認証用サービス
 *
 * @package App\Service
 *
 * @property \App\Model\Table\MUsersTable $MUsers
 */
class UserService extends AppService
{
    private $password = "";
    private $tmp_password = "";

    /**
     * UserService constructor.
     *
     * @param string $_companyCode
     * @param array $param
     */
    public function __construct($param = [])
    {
        parent::__construct();

        if (count($param) == 0) return;

        $this->companyCode = $param['company_code'] ?? "";

    }

    /**
     * MUser, MCustomerテーブルから、指定ユーザの情報を取得する
     * @param string $loginId ログインID
     * @param string $apiKey APIキー
     * @return \Cake\Datasource\EntityInterface|null DBエンティティ
     */
    private function getMUserObj($loginId, $apiKey = "")
    {
        $where = [
            'MUsers.is_active' => true,
            'MUsers.is_deleted' => false,
            'MUsers.is_approved' => true,
            'MCustomers.company_code' => $this->companyCode,
            'login_id' => $loginId,
        ];
        if ($apiKey !== "") $where['secure_code'] = $apiKey;

        $user = $this->MUsers
            ->find()
            ->select([
                // MUsers
                'id' => 'MUsers.id',
                'login_id' => 'MUsers.login_id',
                'mail_address' => 'MUsers.mail_address',
                'secure_code' => 'MUsers.secure_code',
                'name' => 'MUsers.name',
                'password' => 'MUsers.password',
                'tmp_password' => 'MUsers.tmp_password',
                'tmp_password_time' => 'MUsers.tmp_password_time',
                'last_login_date' => 'MUsers.last_login_date',
                'order_sort_type' => 'MUsers.order_sort_type',
                'is_demo' => 'MUsers.is_demo',
                // MCustomers
                'company_code' => 'MCustomers.company_code',
                'customer_id' => 'MCustomers.id',
                'customer_code' => 'MCustomers.code',
                'customer_edaban' => 'MCustomers.edaban',
                'order_code' => 'MCustomers.order_code',
                'use_order_code' => 'MCustomers.use_order_code',
                'customer_name' => 'MCustomers.name',
                'customer_code_type' => 'MCustomers.customer_code_type',
                'use_customer_close_time' => 'MCustomers.use_customer_close_time',
                'customer_close_time' => 'MCustomers.customer_close_time',
                'use_customer_delivery_days' => 'MCustomers.use_customer_delivery_days',
                'customer_delivery_days' => 'MCustomers.customer_delivery_days',
                'holiday_delivery_type' => 'MCustomers.holiday_delivery_type',
                'tax_amount_calc_type' => 'MCustomers.tax_amount_calc_type',
                // MCompanies
                'company_customer_code_type' => 'MCompanies.customer_code_type',
                'company_name' => 'MCompanies.name',
                'terminal_picture_type' => 'MCompanies.terminal_picture_type',
                'use_inquiry' => 'MCompanies.use_inquiry',
                'use_fixed_phrase' => 'MCompanies.use_fixed_phrase',
                'is_order_code' => 'MCompanies.is_order_code',
//                'close_time' => 'MCompanies.close_time',
                'close_time' => "CASE WHEN MCustomers.use_customer_close_time = 1 THEN MCustomers.customer_close_time ELSE MCompanies.close_time END",
                'order_type_0' => 'MCompanies.order_type_0',
                'order_type_1' => 'MCompanies.order_type_1',
                'order_type_2' => 'MCompanies.order_type_2',
                'order_type_3' => 'MCompanies.order_type_3',
                'order_type_4' => 'MCompanies.order_type_4',
                'order_type_5' => 'MCompanies.order_type_5',
                'order_type_6' => 'MCompanies.order_type_6',
                'order_type_7' => 'MCompanies.order_type_7',
                'order_type_8' => 'MCompanies.order_type_8',
                'order_type_9' => 'MCompanies.order_type_9',
                // MCompanies.terminal_price_type or MCustomers.terminal_price_type
                'terminal_price_type' => "CASE WHEN MCompanies.terminal_price_type = 0 THEN 0 ELSE CASE WHEN MCustomers.terminal_price_type = 1 THEN 1 ELSE 0 END END",
//                'same_order_code_num' => "(SELECT count(*) AS cnt FROM m_customers WHERE order_code = MCustomers.order_code AND company_code = MCustomers.company_code AND is_deleted = 0)",
                'same_order_code_num' => "(SELECT count(*) AS cnt FROM ( SELECT do.customer_code FROM d_orderlist as do WHERE do.order_code = MCustomers.order_code AND do.company_code = MCustomers.company_code AND do.is_visible = 1 GROUP BY do.customer_code ) AS d)",
            ])
            ->innerJoin(
                ['MCustomers' => 'm_customers'],
                ['MCustomers.id = MUsers.customer_id']
            )
            ->innerJoin(
                ['MCompanies' => 'm_companies'],
                ['MCustomers.company_code = MCompanies.code']
            )
            ->where($where)
            ->first();

        return $user;
    }

    /**
     * 対象取引先のランク情報の有無をチェック
     * @return チェック結果
     */
    public function checkRankInfo($loginId) {
        $user = $this->getMUserObj($loginId);
        $where = [
            'MRank.customer_code' => $user->customer_code,
        ];
        $rankInfoCnt = $this->MRank
            ->find()
            ->where($where)
            ->count();
        
        if ($rankInfoCnt > 0) {
            return 1;
        }
        return 0;
    }

    /**
     * MUserテーブルからログイン判定を行う
     *
     * @param string $loginId
     * @param string $password
     * @return UserLoginData
     */
    public function checkPassword(string $loginId, string $password)
    {
        $user = $this->getMUserObj($loginId);

        // Hash化されたパスワードの比較用
        $hasher = new DefaultPasswordHasher;

        if ($user) {
            $result = new UserLoginData(false, $user);

            if ($hasher->check($password, $user->password)) {
                // パスワードと一致した場合
                CommonComponent::outputUserLog('ログイン処理 パスワード一致');

                // 初回ログインの判定
                if (!$user->last_login_date)
                    $result->change_password = true;

            } else if (CommonComponent::checkExpireTime($user->tmp_password_time) &&
                $password === $user->tmp_password) {
                // 仮パスワードが有効期限内で、仮パスワードと一致した場合

                CommonComponent::outputUserLog('ログイン処理 仮パスワード認証');

                // 初回ログインに設定
                $result->change_password = true;
            } else {
                CommonComponent::outputUserLog('ログイン処理 パスワード不一致');

                // 認証失敗
                return $result;
            }

            // APIトークンを生成
            $api_key_plain = Security::hash(Security::randomBytes(32), 'sha256', false);

            CommonComponent::outputUserLog('ログイン処理 トークン作成：' . $api_key_plain);

            $updUser = $this->MUsers->get($user->id);
            $updUser->secure_code = $api_key_plain;
            //$updUser->last_login_date = date('Y/m/d H:i:s');
            $this->MUsers->save($updUser);

            // ログイン情報をセッションへ保存
            $this->setUserInfoToSession($user);
            // 最終ログイン日時を追加
            $this->setLastLoginTime($user);

            $result->secure_code = $api_key_plain;
            $result->status = true;
            return $result;
        }

        return new UserLoginData(false);
    }

    /**
     * MUserテーブルからAPIキーでのログイン判定を行う
     *
     * @param string $loginId
     * @param string $apiKey
     * @return UserLoginData
     */
    public function checkApiKey(string $loginId, string $apiKey)
    {
        //MUsersテーブルから、ログインIDとAPIキーを条件に1件目のデータをSELECTする
        $user = $this->getMUserObj($loginId, $apiKey);

        if ($user) {
            // ログイン情報をセッションへ保存
            $this->setUserInfoToSession($user);
            // 最終ログイン日時を追加
            $this->setLastLoginTime($user);

            return new UserLoginData(true, $user);
        }

        return new UserLoginData(false);
    }

    /**
     * ユーザの最終ログイン日時を更新する
     * @param $user
     */
    private function setLastLoginTime($user)
    {
        $nowDate = date('Y-m-d H:i:s');
        $updateData = [
            'last_login_date' => $nowDate,
            'updated' => $nowDate,
        ];

        $message = $this->updateData($this->MUsers, $updateData, $user->id);
    }

    /**
     * ユーザマスタのデータからユーザ情報をセッションに保存する
     * @param $user object ユーザマスタの1レコード
     * @param int $loginType 一般ユーザ：1, 営業マン:2
     */
    private function setUserInfoToSession($user, $loginType = FLAG_USER_LOGIN)
    {
        /////////////////////////////////////////////
        // 企業マスタのデータをセッションに保存する
        $_SESSION['sessionCompanyInfo']['code'] = $this->companyCode;
        $_SESSION['sessionCompanyInfo']['name'] = $user->company_name;
        $_SESSION['sessionCompanyInfo']['terminalPictureType'] = $user->terminal_picture_type;
        // 企業マスタのorder_typeを配列で保存
        $_SESSION['sessionCompanyInfo']['orderType']
            = $user->order_type_0 . ','
            . $user->order_type_1 . ','
            . $user->order_type_2 . ','
            . $user->order_type_3 . ','
            . $user->order_type_4 . ','
            . $user->order_type_5 . ','
            . $user->order_type_6 . ','
            . $user->order_type_7 . ','
            . $user->order_type_8 . ','
            . $user->order_type_9 . ',';
        $_SESSION['sessionCompanyInfo']['useInquiry'] = $user->use_inquiry;
        $_SESSION['sessionCompanyInfo']['useFixedPhrase'] = $user->use_fixed_phrase;

        /////////////////////////////////////////////
        // 取引先マスタのデータをセッションに保存する
        $_SESSION['sessionCustomerInfo']['id'] = $user->customer_id;
        $_SESSION['sessionCustomerInfo']['code'] = $user->customer_code;
        $_SESSION['sessionCustomerInfo']['edaban'] = $user->customer_edaban;
        // 企業マスタに発注コードの利用フラグが立っていたら取引先のフラグを確認
        $_SESSION['sessionCustomerInfo']['orderCode'] = $user->is_order_code && $user->use_order_code ? $user->order_code : "";

        $useOrderCode = true;
        // order_code利用フラグがfalse、または、取引先マスタに発注コードが設定されていない
        if (!$user->is_order_code || $user->order_code == '' || !$user->use_order_code) $useOrderCode = false;
        // 同じ発注コードを持つ取引先が0件か1件
        $_SESSION['sessionCustomerInfo']['useOrderCode'] = $useOrderCode;

        // 部門名を表示するフラグ(発注コード利用可＆複数取引先に紐づく場合 or 自分以外の取引先コードが発注コードに入っている場合)
        $viewDepartmentName = false;
        if ($useOrderCode
            && ($user->same_order_code_num > 1 || $user->order_code != $user->customer_code)) {
            $viewDepartmentName = true;
        }
        $_SESSION['sessionCustomerInfo']['viewDepartmentName'] = $viewDepartmentName;

        $_SESSION['sessionCustomerInfo']['name'] = $user->customer_name;
        $_SESSION['sessionCustomerInfo']['departmentName'] = $user->department_name;
        // 取引先マスタに設定されていなかったら企業マスタの値を使用する
        $_SESSION['sessionCustomerInfo']['customerCodeType'] = $user->customer_code_type == 0 ? $user->company_customer_code_type : $user->customer_code_type;
        // 取引先個別の締め時間
        $closeTime = $user->use_customer_close_time == 1 ? $user->customer_close_time : $user->close_time;
        // 締め時間に「:」が設定されていたら外す
        str_replace(':', '', $closeTime);
        $_SESSION['sessionCustomerInfo']['closeTime'] = $closeTime;
        // 納品可能曜日設定
        $_SESSION['sessionCustomerInfo']['deliveryDaysFlg'] = $user->use_customer_delivery_days;
        // 取引先個別の納品日(使用しない場合は全曜日を設定する)
        $_SESSION['sessionCustomerInfo']['deliveryDays'] = $user->use_customer_delivery_days == 1 ? $user->customer_delivery_days : "0,1,2,3,4,5,6";
        // 休業日の配達
        $_SESSION['sessionCustomerInfo']['holidayDeliveryType'] = $user->holiday_delivery_type;

        // 部門名を表示する場合、該当する取引先リストを取得する
        if ($viewDepartmentName) {
            $_SESSION['sessionCustomerInfo']['departmentList'] = $this->saveDepartmentList($user->order_code);
        }
        // 納品実績の消費税計算方式
        $_SESSION['sessionCustomerInfo']['taxAmountCalcType'] = $user->tax_amount_calc_type;
        // 金額表示
        $_SESSION['sessionCustomerInfo']['terminalPriceType'] = $user->terminal_price_type;
        /////////////////////////////////////////////
        // ユーザマスタのデータをセッションに保存する
        $_SESSION['sessionUserInfo']['id'] = $user->id;
        $_SESSION['sessionUserInfo']['loginId'] = $user->login_id;
        $_SESSION['sessionUserInfo']['name'] = $user->name;
        $_SESSION['sessionUserInfo']['mail'] = $user->mail_address;
        $_SESSION['sessionUserInfo']['loginType'] = $loginType;
        $_SESSION['sessionUserInfo']['orderSortType'] = $user->order_sort_type;
        $_SESSION['sessionUserInfo']['isDemo'] = $user->is_demo;
        $_SESSION['sessionUserInfo']['lastLoginDate'] = $user->last_login_date;

        /////////////////////////////////////////////
        // バージョンNoをセッションに保存する
        $_SESSION['sessionOrderList']['versionNo'] = $this->getOrderListMaxVersion();

        // 納品日リスト
        $orderService = new OrderListService();
        $orderService->calcNouhinbiList();
    }

    /**
     * 発注コードが同じ取引先の部門名リストをJSON形式で取得
     *
     * オーダーコードベースで発注コードが同じ商品に対応する取引先の部門名リストを取得
     */
    private function saveDepartmentList($orderCode)
    {
        $sql =
            "SELECT c.code, c.name, c.department_name "
            . "FROM m_customers as c "
            . "INNER JOIN ( "
            . "    SELECT DISTINCT customer_code "
            . "    FROM d_orderlist "
            . "    WHERE order_code = :order_code "
            . ") AS o "
            . "ON c.code = o.customer_code "
            . "  AND c.company_code = :company_code ";

        $param = array(
            'order_code' => $orderCode,
            'company_code' => $this->companyCode,
        );

        // SQL実行
        $connection = ConnectionManager::get('default');

        $customerData = $connection->execute($sql, $param)
            ->fetchAll('assoc');

        $result = [];
        foreach ($customerData as $data) {
            if (!isset($data['department_name']) || $data['department_name'] === "") {
                $data['department_name'] = '';
            }
            $result[$data['code']] = $data['department_name'];
        }

        return json_encode($result);
    }

    /**
     * オーダーリストからバージョンNoの最大値を取得
     */
    private function getOrderListMaxVersion()
    {
        $orderListService = new OrderListService();
        return $orderListService->getOrderlistMaxVersion();
    }

    /**
     * 指定ユーザのパスワードを本登録する（仮パスワードは削除）
     *
     * @param $userId
     * @param $password
     * @return array
     */
    public function setNewPassword($userId, $password)
    {
        $errorMessage = [];

        try {
            //更新対象のentity取得
            $updateEntity = $this->MUsers->get($userId);
        } catch (\RuntimeException $e) {
            // 更新対象が存在しない場合はエラーメッセージを返す
            $errorMessage["no_data"] = ["message" => E_A0004];
            return $errorMessage;
        }

        // パスワード有効期限のログを保存する
        $expire = (int)CommonComponent::getCodeMaster('SYS', 'TMP_PASSWORD_EXPIRE_TIME')["number1"];
        $expireDate = $expire / 24;

        $logArray = [
            'ログインID'=>$updateEntity->login_id,
            // '仮パスワード'=> $updateEntity->tmp_password,
            '仮パスワード設定日'=> $updateEntity->tmp_password_time,
            '仮パスワード有効期間(日)'=> $expireDate,
        ];
        CommonComponent::outputUserLog('ユーザマスタ 本パスワード登録', $logArray);

        // パスワードはオブジェクトとして指定
        $updateEntity->password = $password;
        $updateEntity->tmp_password = '';
        $updateEntity->tmp_password_time = null;

        CommonComponent::outputUserLog('ユーザマスタ 本パスワード登録 トランザクション開始');
        //トランザクション処理
        $connection = ConnectionManager::get('default');
        $connection->begin();
        try {
            //DB処理
            $this->MUsers->save($updateEntity);
            $connection->commit();  //コミット
            CommonComponent::outputUserLog('ユーザマスタ 本パスワード登録 コミット完了');

            return $errorMessage;
        } catch (\Cake\ORM\Exception\PersistenceFailedException $e) {
            //ロールバック
            $connection->rollback();
            CommonComponent::outputUserLog('ユーザマスタ 本パスワード登録 ロールバック');

            //エラーメッセージの取得
            $errorMessage['db_error']['message'] = $e->getMessage();
            return $errorMessage;
        }
    }


    /**
     * ログインID、APIkeyで行う簡易認証
     *
     * @param string $loginId
     * @param string $apiKey
     * @return bool
     */
    public function checkLoginWithApiKey(string $loginId, string $apiKey)
    {
        //MUsersテーブルから、ログインIDとAPIキーを条件に1件目のデータをSELECTする
        $user = $this->MUsers
            ->find()
            ->where([
                'login_id' => $loginId,
                'secure_code' => $apiKey,
                'is_approved' => true,
                'is_active' => true,
                'is_deleted' => false
            ])
            ->first();

        if ($user) {
            return true;
        }

        return false;
    }

    /**
     * 初回ログインかどうかを判定する
     * 判定方法：パスワードが空欄 → 初回ログインと判定
     *
     * @param string $loginId
     * @return void
     */
    public function checkFirstLogin(string $loginId)
    {
        //MUsersテーブルから、指定したログインIDのデータを取得
        $user = $this->MUsers
            ->find()
            ->join([
                'c' => [
                    'table' => 'm_customers',
                    'type' => 'INNER',
                    'conditions' => [
                        'c.company_code' => $this->companyCode,
                        'c.id = MUsers.customer_id'
                    ]
                ]
            ])
            ->where([
                'MUsers.login_id' => $loginId,
                'MUsers.is_approved' => true,
                'MUsers.is_active' => true,
                'MUsers.is_deleted' => false,
                'MUsers.password' => ''
            ]);
            $result = $user->first();

        if ($result) {
            return true;
        }

        return false;
    }


    //==================================================================
    // getter
    //==================================================================

    /**
     * ログインユーザの企業マスタパラメータ：terminalPictureType
     */
    public function getTerminalPictureType()
    {
        return $_SESSION['sessionCompanyInfo']['terminalPictureType'];
    }

    /**
     * ログインユーザの企業マスタパラメータ：terminalPriceType
     */
    public function getTerminalPriceType()
    {
        return $_SESSION['sessionCustomerInfo']['terminalPriceType'];
    }

    /**
     * コードマスタからパラメータを取得：ENABLE_FAVORITE
     * @return bool
     */
    public function enableFavorite()
    {
        if ((int)CommonComponent::getCodeMaster('SYS', 'ENABLE_FAVORITE')['number1'] === 1)
            return true;

        return false;
    }

    /**
     * コードマスタからパラメータを取得：ENABLE_TRASH
     * @return bool
     */
    public function enableTrash()
    {
        if ((int)CommonComponent::getCodeMaster('SYS', 'ENABLE_TRASH')['number1'] === 1)
            return true;

        return false;
    }

    /**
     * コードマスタからパラメータを取得：ENABLE_SORT
     * @return bool
     */
    public function enableSort()
    {
        if ((int)CommonComponent::getCodeMaster('SYS', 'ENABLE_SORT')['number1'] === 1)
            return true;

        return false;
    }

}
