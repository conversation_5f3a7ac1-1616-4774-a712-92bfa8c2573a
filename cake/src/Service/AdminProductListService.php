<?php
declare(strict_types=1);

namespace App\Service;

use App\Controller\Component\CommonComponent;
use Cake\Controller\Controller;
use Cake\Datasource\ConnectionManager;
use Cake\I18n\Time;
use \SplFileObject;
use \SplFileInfo;
use \Exception;
use DateTime;

/**
 * AdminProductList Service
 * CSVファイルをDBに登録する
 * オーダーリストをメンテナンスする
 *
 * @property \App\Model\Table\MCustomersTable MCustomers
 * @property \App\Model\Table\DOrderlistTable DOrderlist
 */
class AdminProductListService extends AppService
{
    //オーダーリストアップロード用
    private $record;
    private $isBatch;
    private $batchFileName;
    private $registColmunsName;
    private $csvCompanyCode;
    private $rowCountDone;  //バルクインサートが終わった行数

    //オーダーリストメンテナンス用
    private $managementProductCode2;    //範囲指定用
    private $customerCode;
    private $customerEdaban;
    private $customerName;
    private $orderCode;
    private $versionNo;
    private $managementProductCode;
    private $productName;
    private $leadTime;
    private $bikou;
    private $isVisible;
    private $taxType;
    private $taxRate;
    private $leadTimeOriginal;
    private $bikouOriginal;
    private $isVisibleOriginal;

    /**
     * アップロード時はバッチ処理か画面処理か及び
     * ファイル名またはファイルデータ並びに
     * メンテナンス時は検索か更新か選択削除か一括削除かを
     * パラメーターで受け取る
     *
     * @param array $param
     */
    public function __construct($param = null)
    {
        parent::__construct();

        if (isset($param['is_batch'])) {
            //画面からの処理かバッチからの処理かを設定。trueならバッチ、falseなら画面
            $this->isBatch = $param['is_batch'];

            //バッチからか画面からか判定
            if ($param['is_batch']) {
                //バッチ
                $this->batchFileName = $param['csv_file_info']; //CSVファイルのファイル名を設定
                $this->csvCompanyCode = $param['folder_name']; //CSVファイルの親ディレクトリ名（企業コード）を設定
            } else {
                //画面
                $this->record = $param['csv_file_info'];    //CSVファイルのデータを設定
            }
        }

        //DBの列順と列名定義
        $this->registColmunsName = [
            'company_code',
            'customer_code',
            'customer_edaban',
            'customer_name',
            'businessman_code',
            'order_code',
            'version_no',
            'management_product_code',
            'product_name',
            'product_standard',
            'in_numbers',
            'unit_code',
            'unit_name',
            'category_code1',
            'category_name_1',
            'category_name_2',
            'category_name_3',
            'price_switch_day',
            'price_before',
            'price_after',
            'case_price_before',
            'case_price_after',
            'case_separately',
            'order_division',
            'lead_time',
            'subject',
            'delivery_name',
            'delivery_address',
            'bikou',
            'is_visible',
            'import_order',
            'measurement_division',
            'decimal_point_permission_division',
            'tax_type',
            'tax_rate',
            'created',
            'created_user',
            'updated',
            'updated_user',
        ];

        if (isset($param['mode'])) {
            //modeに対応する値を変数に設定　TODO:定数化検討
            $search = '0';  //検索
            $update = '1';  //更新
            $delete = '2';  //選択削除（物理）
            $deleteBulk = '3';  //一括削除（物理）
            //検索
            if ($param['mode'] == $search) {
                $this->customerCode = $param['customer_code'] ?? "";
                $this->customerEdaban = $param['customer_edaban'] ?? "";
                $this->managementProductCode = $param['management_product_code'] ?? "";
                $this->managementProductCode2 = $param['management_product_code2'] ?? "";
                $this->productName = $param['product_name'] ?? "";
                //更新
            } else if ($param['mode'] == $update) {
                $i = 0;
                foreach ($param['customer_code'] as $value) {
                    $this->customerCode[$i] = $param['customer_code'][$i];
                    $this->customerEdaban[$i] = $param['customer_edaban'][$i];
                    $this->managementProductCode[$i] = $param['management_product_code'][$i];
                    $this->leadTime[$i] = $param['lead_time'][$i];
                    $this->bikou[$i] = $param['bikou'][$i];
                    $this->isVisible[$i] = $param['is_visible'][$i] ?? '9';
                    $this->leadTimeOriginal[$i] = $param['lead_time_original'][$i];
                    $this->bikouOriginal[$i] = $param['bikou_original'][$i];
                    $this->isVisibleOriginal[$i] = $param['is_visible_original'][$i] ?? '9';
                    $this->versionNo[$i] = $param['version_no'][$i];
                    $i++;
                }
                //選択削除
            } else if ($param['mode'] == $delete) {
                $i = 0;
                $j = 0;
                foreach ($param['customer_code'] as $value) {
                    if ($param['is_check_delete'][$i] == '1') {
                        $this->customerCode[$j] = $param['customer_code'][$i];
                        $this->customerEdaban[$j] = $param['customer_edaban'][$i];
                        $this->managementProductCode[$j] = $param['management_product_code'][$i];
                        $j++;
                    }
                    $i++;
                }
                //一括削除
            } else if ($param['mode'] == $deleteBulk) {
                $this->customerCode = $param['customer_code'] ?? "";
                $this->customerEdaban = $param['customer_edaban'] ?? "";
                $this->managementProductCode = $param['management_product_code'] ?? "";
                $this->managementProductCode2 = $param['management_product_code2'] ?? "";
            }
        }
    }

    /**
     * 初期化関数
     */
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * オーダーリストテーブルよりデータを取得する
     *
     * @return \Cake\ORM\Query $object
     */
    public function getHeaders(): object
    {
        // TODO 例外処理
        $orderList = $this->DOrderlist->find();

        return $orderList;
    }

    /**
     * オーダーリストテーブルよりデータを取得する
     *
     * @return \Cake\ORM\Query $object
     */
    public function getProductList(): object
    {
        // 検索条件の有無に応じてWhere句をセット
        $where = [];
        if ($this->productName !== "") $where["DOrderlist.product_name LIKE"] = "%" . $this->productName . "%";
        if ($this->customerCode !== "") $where["DOrderlist.customer_code LIKE"] = $this->customerCode . "%";
        if ($this->customerEdaban !== "") $where["DOrderlist.customer_edaban LIKE"] = $this->customerEdaban . "%";

        // TODO 例外処理
        $productData = $this->DOrderlist
            ->find()
            ->where($where)
            ->order(['management_product_code' => 'ASC']);

        return $productData;
    }

    /**
     * オーダーリストテーブルよりデータを取得する
     * 設定済みの検索条件をキーとして取得する
     *
     * @return \Cake\ORM\Query $object
     */
    public function getProductListWithParam(): object
    {
        // 検索条件の有無に応じてWhere句をセット
        $where = [];
        $where["DOrderlist.company_code "] = $this->companyCode;
        if ($this->customerCode !== "") $where["DOrderlist.customer_code LIKE"] = "%" . $this->customerCode . "%";
        if ($this->customerEdaban !== "") $where["DOrderlist.customer_edaban LIKE"] = "%" . $this->customerEdaban . "%";

        //検索条件に応じてQueryExpressionをセット
        $query = $this->DOrderlist->find()->newExpr();
        if ($this->managementProductCode !== "" and $this->managementProductCode2 !== "") {
            $productCodeRange = $query
                ->between("DOrderlist.management_product_code", $this->managementProductCode, $this->managementProductCode2);
        } else if ($this->managementProductCode !== "" and $this->managementProductCode2 === "") {
            $productCodeRange = $query
                ->gte("DOrderlist.management_product_code", $this->managementProductCode);
        } else if ($this->managementProductCode === "" and $this->managementProductCode2 !== "") {
            $productCodeRange = $query
                ->lte("DOrderlist.management_product_code", $this->managementProductCode2);
        } else {
            $data = $this->DOrderlist
                ->find()
                ->where($where);
            return $data;
        }

        // 結合したデータ取得
        $data = $this->DOrderlist
            ->find()
            ->where($where)
            ->where($productCodeRange);

        return $data;
    }

    /**
     * CSVデータ登録処理
     */
    public function insertCSV()
    {
        //変数の宣言と初期化
        $batchFolderPath = DIR_PRODUCT_CSV;    //バッチ実行時のCSVの配置フォルダパス
        $maxFileSize = MAX_FILE_SIZE;    //最大ファイルサイズ（バイト単位）
        $checkExtensionName = 'csv';    //チェックする拡張子名TODO:定数化検討
        $companyCode = '';  //企業コード
        $customerCode = ''; //取引先コード
        $loginId = '';  //ログインID
        $fileName = ''; //バッチ実行時の拡張子を除いたファイル名
        $customerList = []; //企業コード・取引先コード」のペアを保存する配列
        $insMCustomer = []; //取引先マスタINSERT用配列

        if ($this->isBatch) {
            $companyCode = $this->csvCompanyCode;

            //取引先コード取得処理
            $pathData = pathinfo($this->batchFileName);
            $fileName = $pathData["filename"];

            //バッチフォルダパス処理
            $batchFolderPath = $batchFolderPath . $companyCode . '/';

            //バッチフルパス処理
            $batchFullPath = $batchFolderPath . $this->batchFileName;

            //ファイル存在チェック処理
            if (!file_exists($batchFullPath)) {
                CommonComponent::outputAdminLog('ファイルが存在しないまたはファイル名が不正です');
                return E_P0001;
            }

            //バッチはログインしてないのでログインIDに固定値を設定
            //TODO:定数化検討
            $loginId = 'system';

            //拡張子チェック
            $path_parts = pathinfo($this->batchFileName);
            $batchFileExtension = $path_parts['extension'];
            if ($batchFileExtension != $checkExtensionName) {
                CommonComponent::outputAdminLog('ファイル拡張子が不正です');
                return E_P0002;
            }

            //サイズチェック
            $batchFileSize = filesize($batchFullPath);
            if ($batchFileSize > $maxFileSize) {
                CommonComponent::outputAdminLog('ファイルサイズ超過');
                return E_P0003;
            }
        } else {
            //企業コード取得処理
            $companyCode = $this->companyCode;  //セッションの企業コード

            //ログインID取得処理
            $loginId = $this->userLoginId;  //セッションのログインID

            //拡張子取得
            $csvFileName = $this->record['name'];
            $info = new SplFileInfo($csvFileName);
            $csvFileExtension = $info->getExtension();

            //拡張子チェック
            if ($csvFileExtension != $checkExtensionName) {
                CommonComponent::outputAdminLog('ファイル拡張子が不正です');
                return E_P0002;
            }

            //ファイルサイズ取得
            $csvFileSize = $this->record['size'];
            //ファイルサイズチェック
            if ($csvFileSize > $maxFileSize) {
                CommonComponent::outputAdminLog('ファイルサイズ超過');
                return E_P0003;
            }

            $batchFullPath = $this->record['tmp_name'];
        }

        //SplFileObjectクラスを使ったCSVファイル処理
        $i = 0; //行数カウント用の添え字

        $maxColumn = 35;    //最大列数  TODO:定数化検討
        $lowerColumn = 31;  //CSVに最低必要な列数
        
        $maxRow = 100000;    //最大行数 TODO:定数化検討
        $resultRowCount = null; //処理行数

        $file = new SplFileObject($batchFullPath, 'r');
        $file->setFlags(
            SplFileObject::READ_CSV |
            SplFileObject::READ_AHEAD |
            SplFileObject::SKIP_EMPTY |
            SplFileObject::DROP_NEW_LINE
        );

        //DB列順列名取得
        $columnsName = $this->registColmunsName;

        //DBのどのカラムにCSVのどの列を入れるかを取得
        $csvColumnsOrder = $this->registColmunsOrder();

        // アップデート用時刻取得
        $nowTime = Time::now(); //システムの現在時刻取得

        $customerNameList = [];
        $modelCustomers = $this->MCustomers;
        foreach ($file as $line) {
            //列数チェック
            $csvColumnCount = count($line);

            if ($csvColumnCount > $maxColumn
                || $csvColumnCount < $lowerColumn) {
                CommonComponent::outputAdminLog('列数が不正です');
                return ($i + 1) . "行目の" . "列数" . E_V0004;
            }

            //UTF-8に変換
            mb_language("Japanese");
            $line = mb_convert_encoding($line, "UTF-8", "SJIS-win");

            //企業コードチェック処理
            if ($line[1] != $companyCode) {
                CommonComponent::outputAdminLog('企業コードが不正です');
                return ($i + 1) . "行目の" . "企業コード" . E_V0004;
            }

            /*
                        //取引先コード-取引先枝番チェック処理（バッチ処理時のみ）
                        if ($this->isBatch) {
                            if ($line[2] . "-" . $line[3] != $fileName) {
                                CommonComponent::outputAdminLog('取引先コード-取引先枝番が不正です');
                                return ($i + 1) . "行目の" . "取引先コード-取引先枝番" . E_V0004;
                            }
                        }
            */

            //企業コード-取引先コードの組み合わせを取得
            $code = $line[1] . $line[2];
            if (!array_key_exists($code, $customerList)) {
                $customerList[$code] = array();
                //企業コード-取引先コードの組み合わせがDBの取引先マスタに存在するかチェックする
                $customerData = $modelCustomers
                    ->find()
                    ->where([
                        'company_code' => $line[1],
                        'code' => $line[2],
                        'is_deleted' => false
                    ])
                    ->first();

                $orderCode = $line[$csvColumnsOrder['order_code']] ?? '';

                //存在しない場合は、INSERT用配列を作成する
                if ($customerData == null) {
                    $nowTime = Time::now(); //システム現在時刻
                    $customerList[$code] = ([
                        'company_code' => $line[1],
                        'code' => $line[2],
                        'edaban' => '00', //固定値00
                        'customer_code_type' => '1',
                        'name' => ($line[4] == '') ? '取引先名未設定' : $line[4],
                        'department_name' => '',
                        'postcode' => '',
                        'address1' => '',
                        'address2' => '',
                        'address3' => '',
                        'tel_no' => '',
                        'fax_no' => '',
                        'terminal_price_type' => 0,
                        'order_code' => $orderCode,
                        'use_order_code' => ($orderCode == '') ? 0 : 1, //発注コードが設定されている場合、「１：発注コード単位」を設定
                        'self_code1' => '0',
                        'self_code2' => '0',
                        'self_code3' => '0',
                        'use_customer_close_time' => '0',
                        'customer_close_time' => '',
                        'use_customer_delivery_days' => '0',
                        'customer_delivery_days' => '',
                        'holiday_delivery_type' => 0,
                        'orderlist_version' => 0,
                        'is_deleted' => 0
                    ]);
                } else {
                    // 取引先名、発注コードが異なる場合は更新する
                    $updateName = $line[4] === '' ? $customerData->name : $line[4];
                    $updateOrderCode = $orderCode;
                    if ($customerData->name !== $updateName
                        || $customerData->order_code !== $updateOrderCode) {

                        $updateEntity = $modelCustomers->get($customerData->id);

                        $entity = $modelCustomers->patchEntity($updateEntity, [
                            'name' => $updateName,
                            'order_code' => $updateOrderCode,
                            'use_order_code' => $updateOrderCode == '' ? 0 : (int)$customerData->use_order_code,
                            'updated' => $nowTime,
                            'updated_user' => 'system'
                        ]);

                        try {
                            $modelCustomers->save($entity);
                        } catch (\Cake\ORM\Exception\PersistenceFailedException $e) {
                            CommonComponent::outputAdminLog('取引先名/発注コード アップデートエラー : ' . 'to customer_name:' . $updateName . ', to order_code:' . $updateOrderCode, $customerData);
                        }
                    }
                }
            }

            //連想配列に取り込み
            $records[$i] = $line;
            $i++;
        }

        //行数チェック
        if ($i > $maxRow) {
            CommonComponent::outputAdminLog("最大取り込み件数：{$maxRow}件を超過");
            return sprintf(E_P0004, $maxRow);
        }

        $resultRowCount = $i;   //CSVの行数

        $nowTime = Time::now(); //システム現在時刻
        $i = 0; //行数カウント用の添え字（0～$bulkInsertUnitの間でカウントし、バルクインサートしたら0に戻す）
        $this->rowCountDone = 0;  //バルクインサートした行数
        $bulkInsertUnit = 500;  //バルクインサートする行数 TODO:定数化検討
        $isBulkInsert = false;  //バルクインサート実行フラグ
        $deleted = array(); //DBから削除済みの（企業コード＋取引先コード＋取引先枝番）
        $varsionNumberList = [];    //（企業コード＋取引先コード＋取引先枝番）別バージョン番号
        $excludedCount = 0; //除外レコード数（バルクインサートする行数の中にいくつあったか）
        $excludedCountAll = 0; //除外レコード数（全ての行数の中にいくつあったか）
        $addVersionNo = (integer)1000; //TODO:定数化検討　バージョン番号インクリメント単位
        $rowCount = 1;  //エラー出力用の通算処理行数

        //DBの列順列名を設定
        $query = $this->DOrderlist->query()
            ->insert($columnsName);

        CommonComponent::outputAdminLog('オーダーリストアップロード トランザクション開始');

        //TODO:関数化検討
        //トランザクション処理
        $connection = ConnectionManager::get('default');
        $connection->begin();   //トランザクション開始
        try {
            foreach ($records as $line) {
                $j = 0; //DB列順用の添え字
                $setVersionNo = (integer)1000;  //TODO:定数化検討　DB登録用バージョン番号（初期値は新規登録）
                $fileCreateDate = $line[0]; //CSVのファイル作成日
                $customerCode = $line[$csvColumnsOrder['customer_code']]; //CSVの取引先コード
                $customerEdaban = $line[$csvColumnsOrder['customer_edaban']]; //CSVの取引先コード
                $customerName = $line[$csvColumnsOrder['customer_name']]; //CSVの取引先名
                $companyAndCustomer = $companyCode . '_' . $customerCode;   //企業コード_取引先コード
                $productListRow = null; //DB検索結果（一行）
                $versionNo = null;  //DB検索結果のバージョン番号列
                $orderCode = $line[$csvColumnsOrder['customer_code']];  //TODO:定数化検討　発注コードデフォルト値
                $price_switch_day = '2050/12/31';    //TODO:定数化検討　単価切替日デフォルト値
                $priceAfter = 0.0;  //TODO:定数化検討　単価（後）デフォルト値
                $casePriceAfter = 0.0; // ケース単価（後）デフォルト値
                $importOrder = $line[$csvColumnsOrder['import_order']];   //CSVの、取込順という項目名の値

                //ファイル作成日チェック処理
                $isFileCreateDate = true;
                if ($fileCreateDate == "") {
                    $isFileCreateDate = false;
                }
                if ($isFileCreateDate) {
                    if (!preg_match('/^([0-9]{14})$/', $fileCreateDate)) {
                        $isFileCreateDate = false;
                    }
                }
                if ($isFileCreateDate) {
                    if (!$this->isValidateDate($fileCreateDate, 'YmdHis')) {
                        $isFileCreateDate = false;
                    }
                }
                if ($isFileCreateDate === false) {
                    $connection->rollback();
                    CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック: ' . $this->batchFileName);
                    return $rowCount . "行目の" . "ファイル作成日" . E_V0004;
                }

                //CSVデータの企業コードと取引先コードと取引先枝番の組み合わせが初登場か
                if (in_array($companyAndCustomer, $deleted) === false) {
                    //企業コードと取引先コードを連想配列に設定（一括処理の為商品コードは不要）
                    $productListPrimaryKey = [
                        'company_code' => $companyCode,
                        'customer_code' => $customerCode,
//                        'customer_edaban' => $customerEdaban
                    ];

                    //CSVデータがDBに存在するかチェック
                    $productListRow = $this->DOrderlist
                        ->find()
                        ->where($productListPrimaryKey)
                        ->order(['version_no' => 'DESC'])
                        ->first();

                    //CSVのデータがDBに存在する場合の削除処理
                    $this->DOrderlist->deleteAll($productListPrimaryKey);

                    //バージョン番号カウントアップ処理
                    $varsionNumberList[$companyAndCustomer] = (int)(floor(($productListRow['version_no'] + $addVersionNo) / $addVersionNo) * $addVersionNo);

                    //削除した企業コード＋取引先コード＋取引先枝番の組み合わせを配列に設定
                    $deleted[] = $companyAndCustomer;
                }

                //取引先名チェック処理
                if (mb_strlen($customerName) > 50) {
                    $connection->rollback();
                    CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック: ' . $this->batchFileName);
                    return $rowCount . "行目の" . "取引先名" . E_V0004;
                }

                // 枝番チェック処理
                if ($customerEdaban == '') {
                    $customerEdaban = '00';
                }

                //バージョン番号取得処理
                if ($productListRow != null) {
                    $results = $productListRow->toArray();
                    $versionNo = $results['version_no'];
                }

                //発注コード処理
                if (isset($line[$csvColumnsOrder['order_code']])
                    && $line[$csvColumnsOrder['order_code']] != '') {
                    $orderCode = $line[$csvColumnsOrder['order_code']];
                }

                //バージョン番号処理
                //新規か更新か判定して更新ならカウントアップしたバージョン番号
                if (isset($varsionNumberList[$companyAndCustomer])) {
                    $setVersionNo = $varsionNumberList[$companyAndCustomer];
                }

                //入数チェック処理
                $inNumbers = $line[$csvColumnsOrder['in_numbers']];
                $isInNumbers = true;
                if ($inNumbers != '') {
                    $inNumbers = str_replace(',', '', (string)$inNumbers);
                    $isInNumbers = $this->isNumericFormat($inNumbers);
                    if ($isInNumbers === false) {
                        $connection->rollback();
                        CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック: ' . $this->batchFileName);
                        return $rowCount . "行目の" . "入数" . E_V0004;
                    }
                }

                //001以外の企業コードの場合、以下の項目の必須チェックを行う
                if ($companyCode != '001') {

                    //単位コード
                    $unitCode = $line[$csvColumnsOrder['unit_code']];
                    if ($unitCode == '') {
                        $connection->rollback();
                        CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック: ' . $this->batchFileName);
                        return "単位コード" . E_V0001 . "（" . $rowCount . "行目）";
                    }

                    //単位名
                    $unitName = $line[$csvColumnsOrder['unit_name']];
                    if ($unitName == '') {
                        $connection->rollback();
                        CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック: ' . $this->batchFileName);
                        return "単位名" . E_V0001 . "（" . $rowCount . "行目）";
                    }

                    //大分類
                    $categoryName1 = $line[$csvColumnsOrder['category_name_1']];
                    if ($categoryName1 == '') {
                        $connection->rollback();
                        CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック: ' . $this->batchFileName);
                        return "大分類" . E_V0001 . "（" . $rowCount . "行目）";
                    }
                }

                //単価切替日チェック処理
                $isPriceSwitchDay = true;
                if ($line[$csvColumnsOrder['price_switch_day']] != '') {
                    $priceSwitchDay = $line[$csvColumnsOrder['price_switch_day']];
                    //半角数値のみで構成された8桁かどうかチェックする
                    if (!preg_match('/^([0-9]{8})$/', $priceSwitchDay)) {
                        $isPriceSwitchDay = false;
                    }

                    //日付範囲チェック処理
                    if ($isPriceSwitchDay) {
                        if (($priceSwitchDay < '20000101') || ($priceSwitchDay > '20501231')) {
                            $isPriceSwitchDay = false;
                        }
                    }

                    //日付妥当性チェック処理
                    if ($isPriceSwitchDay) {
                        if (!$this->isValidateDate($priceSwitchDay, 'Ymd')) {
                            $isPriceSwitchDay = false;
                        }
                    }

                    if ($isPriceSwitchDay === false) {
                        $connection->rollback();
                        CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック: ' . $this->batchFileName);
                        return $rowCount . "行目の" . "単価切替日" . E_V0004;
                    } else {
                        $price_switch_day = date('Y-m-d', strtotime($priceSwitchDay));
                    }
                }

                //単価（前）処理
                $priceBefore = $line[$csvColumnsOrder['price_before']];
                $isPriceBefore = true;
                if ($priceBefore != '') {
                    $priceBefore = str_replace(',', '', (string)$priceBefore);
                    $isPriceBefore = $this->isNumericFormat($priceBefore);
                    if ($isPriceBefore === false) {
                        $connection->rollback();
                        CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック: ' . $this->batchFileName);
                        return $rowCount . "行目の" . "単価（前）" . E_V0004;
                    }
                }

                //単価（後）処理
                $isPriceAfter = true;
                if ($line[$csvColumnsOrder['price_after']] != '') {
                    $priceAfter = str_replace(',', '', (string)$line[$csvColumnsOrder['price_after']]);
                    $isPriceAfter = $this->isNumericFormat($priceAfter);
                    if ($isPriceAfter === false) {
                        $connection->rollback();
                        CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック: ' . $this->batchFileName);
                        return $rowCount . "行目の" . "単価（後）" . E_V0004;
                    }
                }

                //企業コード001の場合、以下の項目のチェックを行う
                if ($companyCode == '001') {
                    // ケース単価（前）
                    $casePriceBefore = $line[$csvColumnsOrder['case_price_before']];
                    $isCasePriceBefore = true;
                    if ($casePriceBefore != '') {
                        $casePriceBefore = str_replace(',', '', (string)$casePriceBefore);
                        $isCasePriceBefore = $this->isNumericFormat($casePriceBefore);
                        if ($isCasePriceBefore === false) {
                            $connection->rollback();
                            CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック: ' . $this->batchFileName);
                            return $rowCount . "行目の" . "ケース単価（前）" . E_V0004;
                        }
                    }

                    // ケース単価（後）
                    $isCasePriceAfter = true;
                    if ($line[$csvColumnsOrder['case_price_after']] != '') {
                        $casePriceAfter = str_replace(',', '', (string)$line[$csvColumnsOrder['case_price_after']]);
                        $isCasePriceAfter = $this->isNumericFormat($casePriceAfter);
                        if ($isCasePriceAfter === false) {
                            $connection->rollback();
                            CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック: ' . $this->batchFileName);
                            return $rowCount . "行目の" . "ケース単価（後）" . E_V0004;
                        }
                    }

                    // ケースバラ区分
                    $caseSeparately = $line[$csvColumnsOrder['case_separately']];
                    if ($caseSeparately == '') {
                        $connection->rollback();
                        CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック: ' . $this->batchFileName);
                        return "ケースバラ区分" . E_V0001 . "（" . $rowCount . "行目）";
                    }

                
                } else {
                    //企業コード001以外の場合はデフォルト値をセット
                    $casePriceBefore = 0.0;
                    $casePriceAfter = 0.0;
                    $caseSeparately = 0;
                }

                //取込順処理
                //取込順 = ファイル作成日 . '-' . 取込順
                if ($importOrder != '') {
                    $importOrder = sprintf('%06d', $importOrder);
                    $importOrder = substr($line[0], 0, 11) . '-' . $importOrder;
                }

                //営業マンコード
                $businessmanCode = $line[$csvColumnsOrder['businessman_code']] ?? "";

                //税区分チェック処理
                $taxType = $line[$csvColumnsOrder['tax_type']] ?? "";
                if ($taxType == "") {
                    $taxType = 0;
                }

                //税率チェック処理
                $taxRate = $line[$csvColumnsOrder['tax_rate']] ?? "";
                if ($taxRate == "") {
                    $taxRate = 0;
                }

                //INSERT用データ作成
                //左辺のDB列順に合わせて右辺を$line[$csvColumnsOrder['DB列名']]にすれば自動で対応するCSV列が格納される
                $articles[$i] = [
                    $columnsName[$j++] => $companyCode,
                    $columnsName[$j++] => $line[$csvColumnsOrder['customer_code']],
                    $columnsName[$j++] => $customerEdaban,
                    $columnsName[$j++] => $customerName,
                    $columnsName[$j++] => $businessmanCode,
                    $columnsName[$j++] => $orderCode,
                    $columnsName[$j++] => $setVersionNo,
                    $columnsName[$j++] => $line[$csvColumnsOrder['management_product_code']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['product_name']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['product_standard']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['in_numbers']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['unit_code']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['unit_name']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['category_code1']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['category_name_1']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['category_name_2']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['category_name_3']],
                    $columnsName[$j++] => $price_switch_day,
                    $columnsName[$j++] => $priceBefore,
                    $columnsName[$j++] => $priceAfter,
                    $columnsName[$j++] => $casePriceBefore,
                    $columnsName[$j++] => $casePriceAfter,
                    $columnsName[$j++] => $caseSeparately,
                    $columnsName[$j++] => $line[$csvColumnsOrder['order_division']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['lead_time']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['subject']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['delivery_name']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['delivery_address']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['bikou']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['is_visible']],
                    $columnsName[$j++] => $importOrder,
                    $columnsName[$j++] => $line[$csvColumnsOrder['measurement_division']],
                    $columnsName[$j++] => $line[$csvColumnsOrder['decimal_point_permission_division']],
                    $columnsName[$j++] => $taxType,
                    $columnsName[$j++] => $taxRate,
                    $columnsName[$j++] => $nowTime,
                    $columnsName[$j++] => $loginId,
                    $columnsName[$j++] => $nowTime,
                    $columnsName[$j++] => $loginId,
                ];

                //INSERT用データ行数が、バルクインサートする行数以上になるかCSVの最終行数以上になったら、クエリビルダーにセットする
                if ((count($articles) + $excludedCount) >= $bulkInsertUnit or ($this->rowCountDone + count($articles) + $excludedCount + $excludedCountAll) >= $resultRowCount) {
                    foreach ($articles as $article) {
                        $query->values($article);
                    }
                    $isBulkInsert = true;
                }

                //バルクインサート処理、TODO:関数化検討
                if ($isBulkInsert) {
                    //バリデーション処理
                    $errorRowCount = 1; //エラー発生行カウント用添え字（人間用なので1が初期値）
                    $entities = $this->DOrderlist->newEntities($articles);
                    foreach ($entities as $entity) {
                        if ($entity->errors()) {
                            foreach ($entity->errors() as $updateKey => $value) {
                                foreach ($value as $type => $message) {
                                    $errorMessage[$updateKey] = [$type => $message . "（" . ($this->rowCountDone + $errorRowCount) . "行目）"];
                                }
                            }
                            $connection->rollback();
                            CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック: ' . $this->batchFileName);
                            return $errorMessage;
                        }
                        $errorRowCount++;
                    }
                    try {
                        //DB処理本体
                        $query->execute();
                    } catch (\PDOException $e) {
                        //NG（PDOエラー）
                        $connection->rollback();
                        CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック: ' . $this->batchFileName);
                        $errerStatus = $e->errorInfo[2];    //PDOエラーの戻り値からエラーメッセージを取り出す
                        $errorMessage = $this->getErrorMessagePdo($errerStatus);
                        return $errorMessage;
                    }

                    //ループ用処理
                    $this->rowCountDone += count($articles);
                    $articles = [];
                    $excludedCountAll += $excludedCount;
                    $excludedCount = 0;
                    $i = 0;
                    $isBulkInsert = false;
                    $query = $this->DOrderlist->query()
                        ->insert($columnsName);
                }
                //ループ用処理
                if (!$isBulkInsert) {
                    $i++;
                }
                $rowCount++;
            }

            //取引先マスタへのINSERT用データを作成

            $query = $this->MCustomers->query()
                ->insert([
                    'company_code',
                    'code',
                    'edaban',
                    'customer_code_type',
                    'name',
                    'department_name',
                    'postcode',
                    'address1',
                    'address2',
                    'address3',
                    'tel_no',
                    'fax_no',
                    'order_code',
                    'use_order_code',
                    'self_code1',
                    'self_code2',
                    'self_code3',
                    'use_customer_close_time',
                    'customer_close_time',
                    'use_customer_delivery_days',
                    'customer_delivery_days',
                    'holiday_delivery_type',
                    'orderlist_version',
                    'is_deleted',
                    'created',
                    'created_user',
                    'updated',
                    'updated_user',
                ]);

            //ログインIDを取得
            $loginId = ($this->isBatch) ? 'system' : $this->userLoginId;

            foreach ($customerList as $val) {
                if (!empty($val)) {

                    //作成日、作成者、更新日、更新者を設定
                    $val['created'] = $nowTime;
                    $val['created_user'] = $loginId;
                    $val['updated'] = $nowTime;
                    $val['updated_user'] = $loginId;

                    //クエリを設定
                    $query->values($val);

                    //INSERT用配列に格納
                    $insMCustomer[] = $val;
                }
            }

            //取引先マスタのINSERT処理
            if (!empty($insMCustomer)) {

                CommonComponent::outputAdminLog('取引先マスタのINSERT 処理開始');

                //エンティティ処理
                $entities = $this->MCustomers->newEntities($insMCustomer);
                foreach ($entities as $entity) {
                    if ($entity->errors()) {
                        foreach ($entity->errors() as $updateKey => $value) {
                            foreach ($value as $type => $message) {
                                $errorMessage[$updateKey] = [$type => $message];
                            }
                        }
                        $connection->rollback();
                        CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック(取引先マスタの登録値が不正): ' . $this->batchFileName);
                        return $errorMessage;
                    }
                }

                try {
                    //DB処理本体
                    $query->execute();
                } catch (\PDOException $e) {
                    //NG（PDOエラー）
                    $connection->rollback();
                    $errerStatus = $e->errorInfo[2];    //PDOエラーの戻り値からエラーメッセージを取り出す
                    $errorMessage = $this->getErrorMessagePdoMCustomer($errerStatus);
                    CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック(取引先マスタのINSERT処理でエラー): ' . $this->batchFileName, $errorMessage);
                    return $errorMessage;
                }
                CommonComponent::outputAdminLog('取引先マスタのINSERT 処理完了');
            }
            $connection->commit();
            CommonComponent::outputAdminLog('オーダーリストアップロード コミット完了: ' . $this->batchFileName);
        } catch (\Exception $e) {
            $connection->rollback();
            CommonComponent::outputAdminLog('オーダーリストアップロード ロールバック: ' . $this->batchFileName);
        }
        return ($resultRowCount - $excludedCountAll);
    }

    /**
     * DBテーブルの列順とCSVデータの列順の組み合わせを連想配列で定義する
     * key（DBの列順） => value（CSVの列順）
     * DB・CSVともに列のカウントが0始まりなので1列目=0
     *
     */
    private function registColmunsOrder()
    {

        $csvColumnOrderToTableColumnOrder = [
            'company_code' => 1,//企業コード
            'customer_code' => 2,//取引先コード
            'customer_edaban' => 3,//取引先枝番
            'customer_name' => 4,//取引先名
            'businessman_code' => 28, //担当者コード（営業担当者コード）
            'order_code' => 29, //発注コード
            'version_no' => '',//バージョン番号（システムで設定するのでここでは空）
            'management_product_code' => 5, //自社管理商品コード
            'product_name' => 6, //商品名
            'product_standard' => 7, //規格
            'in_numbers' => 8,//入数
            'unit_code' => 9,//単位コード
            'unit_name' => 10,//単位名
            'category_code1' => 11,//分類コード
            'category_name_1' => 12,//大分類
            'category_name_2' => 13,//中分類
            'category_name_3' => 14,//小分類
            'price_switch_day' => 15,//単価切替日
            'price_before' => 16,//単価（前）
            'price_after' => 17,//単価（後）
            'order_division' => 18,//受発注区分
            'lead_time' => 19,//納品リードタイム
            'subject' => 20,//件名
            'delivery_name' => 21,//納品場所
            'delivery_address' => 22,//納品場所住所
            'bikou' => 23,//備考
            'is_visible' => 24,//非表示フラグ
            'import_order' => 25,//取込順
            'measurement_division' => 26,//計量区分
            'decimal_point_permission_division' => 27,//小数点許可区分
            'tax_type' => 30,//税区分
            'tax_rate' => 31,//税率
            'case_price_before' => 32,//ケース単価（前）
            'case_price_after' => 33,//ケース単価（後）
            'case_separately' => 34,//ケースバラ区分
            'created' => '',//作成日（システムで設定するのでここでは空）
            'created_user' => '',//作成者（システムで設定するのでここでは空）
            'updated' => '',//更新日（システムで設定するのでここでは空）
            'updated_user' => '',//更新日（システムで設定するのでここでは空）
        ];

        return $csvColumnOrderToTableColumnOrder;
    }

    /**
     * 画面で編集された値でオーダーリストテーブルを一括更新する
     *
     */
    public function updateDOrderlist()
    {
        $currentDateTime = date("Y/m/d H:i:s");; //システムの現在日時取得

        //更新条件設定
        $query = $this->DOrderlist->find()->where(function ($exp, $query) {
            $i = 0;
            $authors = [];
            foreach ($this->customerCode as $value) {
                $author[$i] = $exp->and_([
                    'company_code' => $this->companyCode,
                    'customer_code' => $this->customerCode[$i],
                    'customer_edaban' => $this->customerEdaban[$i],
                    'management_product_code' => $this->managementProductCode[$i]
                ]);
                $authors[$i] = $author[$i];
                $i++;
            }
            return $exp->or_($authors);
        });

        // データ取得
        $tableArray = $query->toArray();

        // 更新対象レコード存在チェック処理
        $recordCountPage = count($this->customerCode);
        $recordCountTable = count($tableArray);
        if ($recordCountPage !== $recordCountTable) {
            // 更新対象が存在しない場合はエラーメッセージを返す
            $errorMessage["no_data"] = ["message" => E_A0004];
            return $errorMessage;
        }

        // 更新データ作成
        $updateDataArray = [];
        $i = 0;
        $isUpdate = false;
        foreach ($tableArray as $line) {
            $updateData = [];

            //バージョン番号作成処理
            if ($this->leadTime[$i] != $this->leadTimeOriginal[$i] || $this->bikou[$i] != $this->bikouOriginal[$i] || $this->isVisible[$i] != $this->isVisibleOriginal[$i]) {
                $versionNo = (int)$this->versionNo[$i] + 1;
                $isUpdate = true;
            } else {
                $versionNo = $this->versionNo[$i];
            }

            // 主キー
            $updateData['company_code'] = $this->companyCode;
            $updateData['customer_code'] = $this->customerCode[$i];
            $updateData['customer_edaban'] = $this->customerEdaban[$i];
            $updateData['management_product_code'] = $this->managementProductCode[$i];

            // 更新値
            $updateData['version_no'] = $versionNo;
            $updateData['lead_time'] = $this->leadTime[$i];
            $updateData['bikou'] = $this->bikou[$i];
            $updateData['is_visible'] = $this->isVisible[$i];
            $updateData['updated'] = $currentDateTime;
            $updateData['updated_user'] = $this->userLoginId;
            array_push($updateDataArray, $updateData);

            $i++;
        }

        //更新された値が存在しない場合ここで戻る
        if ($isUpdate === false) {
            return $message = [];
        }

        // エンティティ作成
        $entities = $this->DOrderlist->patchEntities($tableArray, $updateDataArray);

        // バリデーション処理
        $i = 0;
        foreach ($entities as $entity) {
            $errors[$i] = $entity->errors();
            $i++;
        }
        $errors = array_filter($errors);
        if (!empty($errors)) {
            $errorMessage = $this->validationCheck($errors);
            return $errorMessage;
        }

        CommonComponent::outputAdminLog('オーダーリスト更新 トランザクション開始');

        //トランザクション処理
        $connection = ConnectionManager::get('default');    //処理するDBの設定
        $connection->begin();   //トランザクション開始
        try {
            //DB処理、一括更新
            $result = $this->DOrderlist->saveMany($entities);   //DB処理本体
            $connection->commit();  //コミット
            CommonComponent::outputAdminLog('オーダーリスト更新 コミット完了: ' . $this->batchFileName);

            return [];
        } catch (\Cake\ORM\Exception\PersistenceFailedException $e) {
            //ロールバック
            $connection->rollback();
            CommonComponent::outputAdminLog('オーダーリスト更新 ロールバック: ' . $this->batchFileName);

            //DB操作エラー
            $errorMessage['db_error']['message'] = $e->getMessage();
            return $errorMessage;
        }
    }

    /**
     * 画面でチェックされたレコードをオーダーリストから削除する
     *
     */
    public function deleteDOrderlist()
    {
        //削除条件設定
        $where = (function ($exp, $query) {
            $i = 0;
            $authors = [];
            foreach ($this->customerCode as $value) {
                $author[$i] = $exp->and_([
                    'company_code' => $this->companyCode,
                    'customer_code' => $this->customerCode[$i],
                    'customer_edaban' => $this->customerEdaban[$i],
                    'management_product_code' => $this->managementProductCode[$i]
                ]);
                $authors[$i] = $author[$i];
                $i++;
            }
            return $exp->or_($authors);
        });

        $result = $this->existDeleteRecords($where);
        if (!empty($result)) {
            return $result;
        }

        //データ削除
        $result = $this->transaction($where);
        return $result;
    }

    /**
     * 画面の検索条件に入力された条件で、
     * オーダーリストからレコードを一括削除する
     *
     */
    public function deleteBulkDOrderlist()
    {
        //削除条件設定
        $where = (function ($exp, $query) {
            //商品コード
            $author = '';
            if ($this->managementProductCode !== "" and $this->managementProductCode2 !== "") {
                $author = $exp->between("management_product_code", $this->managementProductCode, $this->managementProductCode2);
            } else if ($this->managementProductCode !== "" and $this->managementProductCode2 === "") {
                $author = $exp->gte("management_product_code", $this->managementProductCode);
            } else if ($this->managementProductCode === "" and $this->managementProductCode2 !== "") {
                $author = $exp->lte("management_product_code", $this->managementProductCode2);
            }
            //企業コードと取引先コードと取引先枝番
            $authors = $exp->and_([
                'company_code' => $this->companyCode,
                'customer_code' => $this->customerCode,
                $author
            ]);
            return $authors;
        });

        //データ削除
        $result = $this->transaction($where);
        return $result;
    }

    /**
     * バリデーション処理
     *
     * @param array $errors
     * @return array $errorMessage
     */
    private function validationCheck($errors)
    {
        $errorMessage = [];

        // バリデーションエラーの場合は、エラーを整形してメッセージを返す
        foreach ($errors as $error) {
            foreach ($error as $updateKey => $value) {
                foreach ($value as $type => $message) {
                    $errorMessage[$updateKey] = [$type => $message];
                }
            }
        }
        return $errorMessage;
    }

    /**
     * 削除対象のレコードがDBに存在するか確認する
     *
     * @param array $where
     * @return array $errorMessage
     */
    private function existDeleteRecords($where)
    {
        $errorMessage = null;

        // 削除対象レコード存在チェック処理
        $recordCountPage = count($this->customerCode);
        $recordCountTable = $this->DOrderlist->find()->where($where)->count();
        if ($recordCountPage !== $recordCountTable) {
            // 更新対象が存在しない場合はエラーメッセージを返す
            $errorMessage["no_data"] = ["message" => "削除対象のデータが存在しません。"];
            return $errorMessage;
        }
        return $errorMessage;
    }

    /**
     * deleteAllメソッドのトランザクション処理を行う
     *
     * @param array $where
     * @return array $errorMessage
     */
    private function transaction($where)
    {
        $errorMessage = [];

        CommonComponent::outputAdminLog('オーダーリスト削除 トランザクション開始');

        //トランザクション処理
        $connection = ConnectionManager::get('default');    //処理するDBの設定
        $connection->begin();   //トランザクション開始
        try {
            //物理削除処理
            $this->DOrderlist->deleteAll($where);

            $connection->commit();  //コミット
            CommonComponent::outputAdminLog('オーダーリスト削除 コミット完了: ' . $this->batchFileName);

            return $errorMessage;
        } catch (\Cake\ORM\Exception\PersistenceFailedException $e) {
            //ロールバック
            $connection->rollback();
            CommonComponent::outputAdminLog('オーダーリスト削除 ロールバック: ' . $this->batchFileName);

            //DB操作エラー
            $errorMessage['db_error']['message'] = $e->getMessage();
            return $errorMessage;
        }
    }

    /**
     * PDOエラーが主キーエラーか若しくはどの列で起きたか判定する
     *
     * @param string $status
     * @return string $message
     */
    private function getErrorMessagePdo($status)
    {
        $message = "";
        switch (true) {
            case strpos($status, 'PRIMARY') !== false:
                $message = $this->getErrorMessagePrimary($status, "企業コード-取引先コード-取引先枝番-商品コード");
                break;
            case strpos($status, 'company_code') !== false:
                $message = $this->getErrorRowNumber($status, "企業コード");
                break;
            case strpos($status, 'customer_code') !== false:
                $message = $this->getErrorRowNumber($status, "取引先コード");
                break;
            case strpos($status, 'customer_edaban') !== false:
                $message = $this->getErrorRowNumber($status, "取引先枝番");
                break;
            case strpos($status, 'customer_name') !== false:
                $message = $this->getErrorRowNumber($status, "取引先名");
                break;
            case strpos($status, 'businessman_code') !== false:
                $message = $this->getErrorRowNumber($status, "担当者コード");
                break;
            case strpos($status, 'order_code') !== false:
                $message = $this->getErrorRowNumber($status, "発注コード");
                break;
            case strpos($status, 'version_no') !== false:
                $message = $this->getErrorRowNumber($status, "バージョン情報");
                break;
            case strpos($status, 'management_product_code') !== false:
                $message = $this->getErrorRowNumber($status, "商品コード");
                break;
            case strpos($status, 'product_name') !== false:
                $message = $this->getErrorRowNumber($status, "商品名");
                break;
            case strpos($status, 'product_standard') !== false:
                $message = $this->getErrorRowNumber($status, "規格");
                break;
            case strpos($status, 'in_numbers') !== false:
                $message = $this->getErrorRowNumber($status, "入数");
                break;
            case strpos($status, 'unit_code') !== false:
                $message = $this->getErrorRowNumber($status, "単位コード");
                break;
            case strpos($status, 'unit_name') !== false:
                $message = $this->getErrorRowNumber($status, "単位名");
                break;
            case strpos($status, 'category_code1') !== false:
                $message = $this->getErrorRowNumber($status, "カテゴリコード");
                break;
            case strpos($status, 'category_name_1') !== false:
                $message = $this->getErrorRowNumber($status, "カテゴリ名１");
                break;
            case strpos($status, 'category_name_2') !== false:
                $message = $this->getErrorRowNumber($status, "カテゴリ名２");
                break;
            case strpos($status, 'category_name_3') !== false:
                $message = $this->getErrorRowNumber($status, "カテゴリ名３");
                break;
            case strpos($status, 'price_switch_day') !== false:
                $message = $this->getErrorRowNumber($status, "単価切替日");
                break;
            case strpos($status, 'price_before') !== false:
                $message = $this->getErrorRowNumber($status, "単価（前）");
                break;
            case strpos($status, 'price_after') !== false:
                $message = $this->getErrorRowNumber($status, "単価（後）");
                break;
            case strpos($status, 'order_division') !== false:
                $message = $this->getErrorRowNumber($status, "受発注区分");
                break;
            case strpos($status, 'lead_time') !== false:
                $message = $this->getErrorRowNumber($status, "納品リードタイム");
                break;
            case strpos($status, 'subject') !== false:
                $message = $this->getErrorRowNumber($status, "件名");
                break;
            case strpos($status, 'delivery_name') !== false:
                $message = $this->getErrorRowNumber($status, "納品場所");
                break;
            case strpos($status, 'delivery_address') !== false:
                $message = $this->getErrorRowNumber($status, "納品場所　住所");
                break;
            case strpos($status, 'bikou') !== false:
                $message = $this->getErrorRowNumber($status, "商品備考");
                break;
            case strpos($status, 'is_visible') !== false:
                $message = $this->getErrorRowNumber($status, "非表示フラグ");
                break;
            case strpos($status, 'import_order') !== false:
                $message = $this->getErrorRowNumber($status, "取込順");
                break;
            case strpos($status, 'measurement_division') !== false:
                $message = $this->getErrorRowNumber($status, "計量区分");
                break;
            case strpos($status, 'decimal_point_permission_division') !== false:
                $message = $this->getErrorRowNumber($status, "小数点許可区分");
                break;
            case strpos($status, 'tax_type') !== false:
                $message = $this->getErrorRowNumber($status, "税区分");
                break;
            case strpos($status, 'tax_rate') !== false:
                $message = $this->getErrorRowNumber($status, "税率");
                break;
        }

        return $message;
    }

    /**
     * PDOエラーが主キーエラーか若しくはどの列で起きたか判定する(取引先マスタINSERT時)
     *
     * @param string $status
     * @return string $message
     */
    private function getErrorMessagePdoMCustomer($status)
    {
        $message = "";
        switch (true) {
            case strpos($status, 'Duplicate') !== false:
                $message = $this->getErrorMessagePrimary($status, "企業コード-取引先コード-取引先枝番");
                break;
            case strpos($status, 'company_code') !== false:
                $message = $this->getErrorRowNumber($status, "企業コード");
                break;
            case strpos($status, 'code') !== false:
                $message = $this->getErrorRowNumber($status, "取引先コード");
                break;
            case strpos($status, 'edaban') !== false:
                $message = $this->getErrorRowNumber($status, "取引先枝番");
                break;
            case strpos($status, 'customer_code_type') !== false:
                $message = $this->getErrorRowNumber($status, "取引先コード出力区分");
                break;
            case strpos($status, 'name') !== false:
                $message = $this->getErrorRowNumber($status, "取引先名");
                break;
            case strpos($status, 'department_name') !== false:
                $message = $this->getErrorRowNumber($status, "部門名");
                break;
            case strpos($status, 'postcode') !== false:
                $message = $this->getErrorRowNumber($status, "郵便番号");
                break;
            case strpos($status, 'address1') !== false:
                $message = $this->getErrorRowNumber($status, "住所１");
                break;
            case strpos($status, 'address2') !== false:
                $message = $this->getErrorRowNumber($status, "住所２");
                break;
            case strpos($status, 'address3') !== false:
                $message = $this->getErrorRowNumber($status, "住所３");
                break;
            case strpos($status, 'tel_no') !== false:
                $message = $this->getErrorRowNumber($status, "電話番号");
                break;
            case strpos($status, 'fax_no') !== false:
                $message = $this->getErrorRowNumber($status, "FAX番号");
                break;
            case strpos($status, 'order_code') !== false:
                $message = $this->getErrorRowNumber($status, "発注コード");
                break;
            case strpos($status, 'use_order_code') !== false:
                $message = $this->getErrorRowNumber($status, "オーダーリストの表示区分");
                break;
            case strpos($status, 'self_code1') !== false:
                $message = $this->getErrorRowNumber($status, "基幹特有コード１");
                break;
            case strpos($status, 'self_code2') !== false:
                $message = $this->getErrorRowNumber($status, "基幹特有コード２");
                break;
            case strpos($status, 'self_code3') !== false:
                $message = $this->getErrorRowNumber($status, "基幹特有コード３");
                break;
            case strpos($status, 'use_customer_close_time') !== false:
                $message = $this->getErrorRowNumber($status, "個別締め時間許可フラグ");
                break;
            case strpos($status, 'customer_close_time') !== false:
                $message = $this->getErrorRowNumber($status, "個別締め時間");
                break;
            case strpos($status, 'use_customer_delivery_days') !== false:
                $message = $this->getErrorRowNumber($status, "個別休日を使うか");
                break;
            case strpos($status, 'customer_delivery_days') !== false:
                $message = $this->getErrorRowNumber($status, "個別休日");
                break;
            case strpos($status, 'holiday_delivery_type') !== false:
                $message = $this->getErrorRowNumber($status, "休業日の扱いについて");
                break;
            case strpos($status, 'tax_amount_calc_type') !== false:
                $message = $this->getErrorRowNumber($status, "消費税額計算方法");
                break;
            case strpos($status, 'orderlist_version') !== false:
                $message = $this->getErrorRowNumber($status, "オーダーリストバージョン情報");
                break;
        }

        return $message;
    }

    /**
     * PDOの主キーエラーメッセージを整形する
     *
     * @param string $status
     * @param string $columnName
     * @return string $message
     */
    private function getErrorMessagePrimary($status, $columnName)
    {
        $message = "";
        $primaryValue = "";
        $separator = '\'';

        $length = strlen($status);
        $start = strpos($status, $separator);
        $end = strpos($status, $separator, ($start + 1));
        $primaryValue = substr($status, ($start + 1), ($end - $start - 1));

        if ($primaryValue !== "") {
            $message = $columnName . "＝" . $primaryValue . E_V0005;
        } else {
            $message = $columnName . E_V0005;
        }

        return $message;
    }

    /**
     * PDOのエラーメッセージを整形する
     *
     * @param string $status
     * @param string $columnName
     * @return string $message
     */
    private function getErrorRowNumber($status, $columnName)
    {
        $message = "";
        if (strpos($status, 'row') !== false) {
            $length = strlen($status);
            $start = strpos($status, 'row');
            $rowNumber = substr($status, ($start + 4), ($length - $start - 4));
            $message = $this->rowCountDone + $rowNumber . "行目の" . $columnName . E_V0004;
        } else {
            $message = $columnName . E_V0004;
        }
        return $message;
    }

    /**
     * 入数と単価（前）と単価（後）の書式をチェックする
     *
     * @param string $numeric
     * @return boolean $isNumeric
     */
    private function isNumericFormat($numeric)
    {
        $isNumeric = true;
        //半角数値または半角ピリオドのみで構成されているかチェックする
        if (preg_match("/[^0-9.,]/", $numeric)) {
            $isNumeric = false;
        }

        //数値かどうかチェックする（小数点が複数ある場合はエラーを返す）
        if (!(is_numeric($numeric))) {
            $isNumeric = false;
        }

        //整数部が11桁以内かどうかチェックする
        if (strlen((string)(floor($numeric))) > 11) {
            $isNumeric = false;
        }

        //小数点が存在する場合、小数部が4桁以内かどうかチェックする
        if (strpos($numeric, '.')) {
            $numericCut = strlen(substr($numeric, strpos($numeric, '.') + 1));
            if ($numericCut > 4) {
                $isNumeric = false;
            }
        }

        return $isNumeric;
    }

    /**
     * 第一引数の値（日付や時刻や日時）が第二引数の書式として有効かどうか調べて有効ならtrueを返す
     * 有効な例：$date = "20190930", $format = "Ymd"
     * 無効な例：$date = "2019-09-31 00:00:00", $format = "Y-m-d H:i:s"
     *
     * @param string $date
     * @param string $format
     * @return boolean
     */
    private function isValidateDate($date, $format)
    {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) == $date;
    }
}
