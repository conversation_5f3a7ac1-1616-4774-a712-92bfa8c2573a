/////////////////////////////////////////////////////////
// 管理画面の初期化処理
// 遷移元画面の判定、検索条件の保存を行う処理

// ページ情報を取得する
const prevController = sessionStorage.getItem("currentController");
const currentController = $('input[name="currentController"]').val();
const currentAction = $('input[name="currentAction"]').val();
const searchKey = currentController + "-" + currentAction + "-search-info";

// HTMLに取得値を埋め込み
$('input[name="prevController"]').val(prevController);
$('input[name="searchKey"]').val(searchKey);

// メニュー画面からの遷移の場合、セッションストレージから検索条件を削除する
if (prevController === "Menu") sessionStorage.removeItem(searchKey);

// 保存対象外ページの場合、セッションストレージから検索条件を削除する
if (currentController === "OrderList"
    && (prevController === "download"
        || prevController === "file_list")) sessionStorage.removeItem(searchKey);

// 現在のページを保存
sessionStorage.setItem("currentController", currentController);
