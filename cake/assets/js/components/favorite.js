/////////////////////////////////////////////////////////
// お気に入り商品の設定及び解除とお気に入り商品数の設定を行う

/**
 * お気に入りボタン押下時処理
 * PC版（ボタン表示を切り替えてDBに反映）
 */
$(document).on("click", ".products-favorite", function () {
    //商品データを取得する
    const objProduct = $(this).closest('.product-data-tablet');
    const customerCode = objProduct.data('customercode');
    const customerEdaban = objProduct.data('customeredaban');
    const productCode = objProduct.data('productscode');

    //お気に入り商品設定処理
    common.dispLoadingAnimation("body", "");
    const objStar = $(this).children();
    if (objStar.hasClass('fas')) {
        favoriteIconOff(objStar);
        orderFavoriteOff(productCode, customerCode, customerEdaban);
    } else {
        favoriteIconOn(objStar);
        orderFavoriteOn(productCode, customerCode, customerEdaban);
    }
    setTimeout(function(){
        common.removeLoadingAnimation();
    },500);
});

/**
 * お気に入りボタン押下時処理
 * スマホ版（ダイアログのお気に入りマークの更新）
 * DB更新はダイアログを閉じた際に行う
 */
$('.order-dialog__star').on('click', function () {
    //お気に入り商品設定処理
    const objStar = $(this).children();
    favoriteIconToggle(objStar);
});

/**
 * 指定したお気に入りマークをONにする
 * @param objStar お気に入りマークのオブジェクト
 */
export function favoriteIconOn(objStar) {
    objStar.removeClass('fas').removeClass('far');
    objStar.addClass('fas');
}

/**
 * 指定したお気に入りマークをOFFにする
 * @param objStar お気に入りマークのオブジェクト
 */
export function favoriteIconOff(objStar) {
    objStar.removeClass('fas').removeClass('far');
    objStar.addClass('far');
}

/**
 * 指定したお気に入りマークを切り替える
 * @param objStar お気に入りマークのオブジェクト
 */
export function favoriteIconToggle(objStar) {
    if (objStar.hasClass('fas')) {
        favoriteIconOff(objStar)
    } else {
        favoriteIconOn(objStar)
    }
}

/**
 * DBにお気に入りを追加
 * @param productCode string 商品コード
 * @param customerCode string
 * @param customerEdaban string
 */
export function orderFavoriteOn(productCode, customerCode, customerEdaban) {
    uploadOrderFavorite(productCode, customerCode, customerEdaban, 1);
}

/**
 * DBからお気に入りを削除
 * @param productCode string 商品コード
 * @param customerCode string
 * @param customerEdaban string
 */
export function orderFavoriteOff(productCode, customerCode, customerEdaban) {
    uploadOrderFavorite(productCode, customerCode, customerEdaban, 0);
}

/**
 * DBからお気に入り情報を更新する
 * @param productCode string 商品コード
 * @param customerCode string
 * @param customerEdaban string
 * @param isFavorite int 0:お気に入りOFF, 1:お気に入りON
 */
export function uploadOrderFavorite(productCode, customerCode, customerEdaban, isFavorite = 0) {
    let data = {
        'customer_code': customerCode,
        'customer_edaban': customerEdaban,
        'management_product_code': productCode,
        'is_favorite': isFavorite,
    };

    let ajaxUrl = $('[name="ajaxUrl"]').val() + "/ajaxOrderFavorite";

    // CSRFトークンを設定
    $.ajaxSetup({
        headers: {'X-CSRF-TOKEN': $('input[name=_csrfToken]').val()}
    });

    $.ajax({
        url: ajaxUrl,
        type: 'POST',
        data: data,
        timeout: 30000,
    }).done(function (data) {
        if (data.status === 'success') {
            setFavoriteNum(data.html);
            
        } else if (data.status === 'api_error') {
            // 認証エラー
            auth.forceLogout(message.E003);
            return false;
        } else if (data.status === 'nodata') {
            alert('お気に入りの設定に失敗しました。');
            return false;
        }
    }).fail(function (data) {
        let msg = "エラー";

        if (data.statusText === "timeout") msg = message.E001;
        if (data.statusText === "Internal Server Error") msg = message.E002;

        alert(msg);
        return false;
    });
}

/**
 * お気に入り商品数を設定する
 */
function setFavoriteNum(favoriteNum = "") {
    if (favoriteNum === "") {
        return;
    }

    favoriteNum = favoriteNum["0"]["favorite_num"];

    if (!$.isNumeric(favoriteNum)) {
        return;
    }

    $('.category__num--favorite').text(favoriteNum);
}