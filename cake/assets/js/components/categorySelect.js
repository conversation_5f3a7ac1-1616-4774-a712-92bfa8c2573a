/////////////////////////////////////////////////////////
// 発注一覧画面のカテゴリ選択ボタンを押した際の処理

$('.category-select__button').on('click', function (e) {
    const targetObj = $("#order_category_select");

    // 選択したボタンの情報をカテゴリボタンに反映させる
    targetObj.html($(this).html());
    targetObj.data('category', $(this).data('category'));
    targetObj.data('name', $(this).data('name'));

    // ボタンに選択フラグを付ける
    $('.category-select__button').each(function () {
        $(this).removeClass('category__selected');
    })
    $(this).addClass('category__selected');

    $("#category-select").collapse('hide');

    load.doSearch();
});

$('.sort__button').on('click', function (e) {
    // ボタンに選択フラグを付ける
    $('.sort__button').each(function () {
        $(this).removeClass('sort__button--selected');
    })
    $(this).addClass('sort__button--selected');

    let sortType = 1;
    switch ($(this).data('sort')) {
        case "import_order":
            sortType = 1;
            break;

        case "product_code":
            sortType = 2;
            break;

        case "order_num":
            sortType = 3;
            break;

        case "user":
            sortType = 4;
            break;
    }

    // 営業マンでログインしている場合、ユーザー設定は利用しない
    const loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    const loginType = loginInfo.login_type;
    if (Number(loginType) !== 1 && sortType === 4) {
        sortType = $('[name="orderSortType"]').val();
    }

    // ユーザータイプをセッションストレージから取得
    const userType = common.getSessionUserType();

    localStorage.setItem('order_sort_type-' + userType + "-"  + common.getLoginId(), sortType.toString());

    $("#category-select").collapse('hide');

    load.doSearch();
});


$('.searchbar-input__button').on('click', function () {
    load.doSearch();
});

//検索ワード欄でEnterキー押下時、検索処理を実行する
$('.search-enter').keypress(function (e) {
    if (e.which == 13) {
        load.doSearch();
    }
});