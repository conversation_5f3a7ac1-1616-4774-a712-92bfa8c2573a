/////////////////////////////////////////////////////////
// スマホ版発注画面の処理

// ヘッダアイコンのクリック動作

/**
 * カートボタン
 */
$('.header-menu__button--confirm').on('click', function () {
    window.location.href = $(this).attr('href');
});

/**
 * 検索ボタン
 */
$('.header-menu__button--search').on('click', function () {
    const currentController = sessionStorage.getItem("order.currentController");

    // オーダーリスト画面：ページトップへ移動
    if (currentController === "OrderList") {
        $('html,body').animate({scrollTop:0}, 'fast');
        return false;
    }

    // それ以外の画面：オーダーリストへ移動
    window.location.href = $(this).attr('href');
});

/**
 * メニュー外をクリックしてメニューを閉じる処理
 */
$(document).click(function (event) {
    const clickOver = $(event.target);
    const _opened = $(".navbar-collapse").hasClass("show");
    if (_opened === true && !clickOver.hasClass("navbar-toggler")) {
        $("button.navbar-toggler").click();
    }
});