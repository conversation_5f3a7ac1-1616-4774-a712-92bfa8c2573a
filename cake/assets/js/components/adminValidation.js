/////////////////////////////////////////////////////////
// 管理画面用のバリデーション設定

/**
 * フォームにバリデーションをセット
 */
$('form').validationEngine();


/**
 * クリアボタンを押したときにバリデーションを消す処理
 */
$('form button[type="reset"]').on({
    "mousedown": function () {
        $(this).parents('form').validationEngine('hideAll');
    },
    "click": function () {
        $(this).parents('form').validationEngine('hideAll');
    },
});


/**
 * submitボタンを押下した際に、バリデーションを行う
 */
$('form button[type="submit"]').on('click', function () {
    //入力欄のバリデーションチェック
    let validateResult = $(this).parents('form').validationEngine('validate');
    if (!validateResult) return false;

    return true;
});

$(function () {
    $(document).on("blur", ".list-area-product__input,.list-area-order-list__input", function () {
        $('form').validationEngine();
    });
});