/**
 * ログイン時のLSの内容を画面の隠し要素にセットする処理
 * (※不具合調査のための一時的な処理)
 */
window.onload = function () {
    // ユーザータイプの取得
    const loginType = localStorage.getItem("loginInfo")
    if (!loginType) userType = '';

    const loginData = JSON.parse(loginType);
    if (!loginData['login_type']) userType = '';

    if (loginData['login_type'] !== '') {
        userType = 'u';
    } else {
        userType = 'b';
    }

    // ローカルストレージの情報を取得
    const smaData = localStorage.getItem("order-" + userType + "-" + loginData['login_id']);
    const catalogData = localStorage.getItem("catalog-" + userType + "-" + loginData['login_id']);
    const timeData = localStorage.getItem("order-time-" + userType + "-" + loginData['login_id']);

    // 画面上の隠し要素に値を設定
    let lsDataSma = document.getElementById('ls_data_sma');
    let lsDataCatalog = document.getElementById('ls_data_catalog');
    let lsDataTime = document.getElementById('ls_data_time');
    lsDataSma.value = smaData;
    lsDataCatalog.value = catalogData;
    lsDataTime.value = timeData;
}