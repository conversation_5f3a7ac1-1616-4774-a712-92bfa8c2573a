/////////////////////////////////////////////////////////
// DatePickerの初期化設定

// 基本的なdatepickerの設定
$(function () {

    // =================================================
    // 日付用Datepicker設定
    // =================================================
    /**
     * Datepicker options
     */
    let datepickerOptions = {
        dayViewHeaderFormat: 'YYYY年 MMM',
        tooltips: {
            today: '本日',
            close: '閉じる',
            clear: 'クリア',
            selectMonth: '月を選択',
            prevMonth: '前月',
            nextMonth: '次月',
            selectYear: '年を選択',
            prevYear: '前年',
            nextYear: '次年',
            selectTime: '時間を選択',
            selectDate: '日付を選択',
            prevDecade: '前期間',
            nextDecade: '次期間',
            selectDecade: '期間を選択',
            prevCentury: '前世紀',
            nextCentury: '次世紀'
        },
        icons: {
            clear: 'fa fa-trash' // or any font awesome icon of your choosing
        },
        buttons: {
            showToday: true,
            showClear: true,
            showClose: true
        },
        locale: 'ja',
        toolbarPlacement: 'bottom',
        widgetPositioning: {
            vertical: 'bottom',
        },
        useCurrent: false,
        viewMode: 'days',
        format: 'L',
        ignoreReadonly: true,
        // debug: true, // TODO 本番の際に外す
    };

    // 画面内のデイトピッカーをすべて取得する
    $('.datetimepicker-input').each(function () {
        let id = $(this).attr('id');
        let defaultValue = $(this).data('default');

        if (defaultValue) defaultValue.toString();

        // IEの場合、startsWithがサポートされていないので、独自関数を追加する
        if (!String.prototype.startsWith) {
            String.prototype.startsWith = function (searchString, position) {
                position = position || 0;
                return this.substr(position, searchString.length) === searchString;
            };
        }

        // 種類に合わせてオプションを設定
        if (id.startsWith('date')) {
            // 日付選択
            datepickerOptions.format = 'L';
            datepickerOptions.viewMode = 'days';
            // 日付をYYYY-MM-DD形式に変更
            if (defaultValue) defaultValue = moment(defaultValue, 'YYYY/MM/DD');
            datepickerOptions.defaultDate = defaultValue || false;
            datepickerOptions.buttons.showToday = true;
            datepickerOptions.buttons.showClear = true;

            const minDate = $(this).data('min-date');
            if (minDate && minDate !== '') {
                datepickerOptions.minDate = moment(minDate, 'YYYY/MM/DD');
            }

            const maxDate = $(this).data('max-date');
            if (maxDate && maxDate !== '') {
                datepickerOptions.maxDate = moment(maxDate, 'YYYY/MM/DD');
            }
        }
        if (id.startsWith('time')) {
            // 時刻選択
            datepickerOptions.format = 'LT';
            datepickerOptions.viewMode = 'times';

            if (defaultValue) {
                // 明示的に文字列に変換
                const str = defaultValue + '';

                let time = str.split(':');
                let hour = '';
                let min = '';
                if (time.length === 2) {
                    hour = time[0];
                    min = time[1];
                } else {
                    hour = str.substr(0, 2);
                    min = str.substr(2, 2);
                }
                datepickerOptions.defaultDate = moment({
                    hour: hour,
                    minute: min,
                });
            } else {
                datepickerOptions.defaultDate = false;
            }
            datepickerOptions.buttons.showToday = false;
            datepickerOptions.buttons.showClear = false;
        }

        // ダイアログを開く
        $(this).datetimepicker(datepickerOptions);
    });

    // カレンダー以外をクリックしたらカレンダーを閉じる設定
    $('body').click(function () {
        $('.datetimepicker-input').datetimepicker('hide');
    });

});
