/////////////////////////////////////////////////////////
// 共通関数
// common.関数名で使用可能

/**
 * URLからGetパラメータを取得する
 *
 * @param  name {string} パラメータのキー文字列
 * @return {string} 対象のURL文字列（任意）
 */
export function getParam(name) {
    const url = window.location.href;
    name = name.replace(/[\[\]]/g, "\\$&");
    const regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)");
    let results = regex.exec(url);
    if (!results) return '';
    if (!results[2]) return '';
    return decodeURIComponent(results[2].replace(/\+/g, " "));
}

/**
 * ログインユーザのIDを返す
 * @returns {string} ログインID
 */
export function getLoginId() {
    // セッションストレージよりログインIDを取得する
    return sessionStorage.getItem('login_id');
}

/**
 * 数値を金額表示に直す
 * 小数点以下を切り捨てる
 *
 * 例：
 *  num=1000 -> 1,000
 *  num=9999999 -> 9,999,999
 *  num=1234.1234 -> 1,234
 *
 * @param num
 * @returns {string}
 */
export function separatePriceWithRoundDown(num) {
    if (isNaN(num)) return '0';

    return separatePrice(Math.floor(num));
}

/**
 * 数値を金額表示に直す
 * 小数点以下はそのまま表示
 * 小数点以下が0で終わる場合は、0を削除する
 *
 * 例：
 *  num=1000 -> 1,000
 *  num=9999999 -> 9,999,999
 *  num=1234.1234 -> 1,234.1234
 *  num=1234.1200 -> 1,234.12
 *
 * @param num
 * @returns {string}
 */
export function separatePrice(num) {
    // .で整数部分と小数部分を分割
    return Number(num).toLocaleString(undefined, {maximumFractionDigits: 4});
}

/**
 * 指定した発注数量テキストボックスの内容が、正常な数値か判定する
 * 異常値の場合はアラートメッセージを表示する
 *
 * @param value
 * @param minValue
 * @param isDecimal
 * @return boolean 正常値：true, 異常値：false
 */
export function isCorrectOrderValue(value, minValue = 0, isDecimal = false) {
    let maxValue = 9999;
    if (isDecimal) maxValue = 9999.99

    const result = isCorrectOrderValueNoAlert(value, minValue, isDecimal)

    switch (result) {
        case 1:
            alert('発注数量は数値のみ入力してください。');
            return false;
        case 2:
            alert('発注数量は' + minValue + '以上の値を入力してください。');
            return false;
        case 3:
            alert('発注数量は' + separatePrice(maxValue) + '以下の値を入力してください。');
            return false;
        case 4:
            alert('この商品は発注数量の小数点以下の入力は許可されていません。');
            return false;
        case 5:
            alert('発注数量の小数部は2桁以内で入力してください。');
            return false;
        default:
    }

    return true;
}

/**
 * 指定した発注数量テキストボックスの内容が、正常な数値か判定する
 * @return boolean 正常値：0, 異常値：false
 */
export function isCorrectOrderValueNoAlert(value, minValue = 0, isDecimal = false) {
    let maxValue = 9999;
    if (isDecimal) maxValue = 9999.99

    // 正規表現で数値判定
    const pattern = /^[+,-]?\d+(\.\d+)?$/;
    const isNumber = pattern.test(value);

    // 数値ではない場合
    if (!isNumber) {
        return 1;
    }

    const valueNum = Number(value);

    // 規定値以下
    if (valueNum < minValue) {
        return 2;
    }

    // 規定値以上
    if (valueNum > maxValue) {
        return 3;
    }

    // 小数点不可の場合に小数点が入力された
    if (!isDecimal && (valueNum !== Math.floor(valueNum))) {
        return 4;
    }

    // 小数点可の場合に小数以下第3位以上が入力されていた

    const decimalValue = String(value).split(".")[1]
    if (isDecimal && decimalValue && decimalValue.length > 2) {
        return 5;
    }

    return 0;
}

/**
 * 指定した桁数までで切り捨てを行う
 * @param value 計算する数値
 * @param base 小数点以下の桁数（2の場合、小数点第2位まで取得）
 * @return number
 */
export function calcFloor(value, base) {
    const splitValue = String(value).split(".");
    let decimalValue = splitValue[1] ? splitValue[1] : '';

    if (base == 0)
        return Number(splitValue[0])

    // 小数点以下がbase以上の場合、切り捨てを行う
    if (decimalValue.length > base) {
        decimalValue = splitValue[1].substr(0, base)
    }

    let calcValue = splitValue[0]
    if (decimalValue !== '')
        calcValue += '.' + decimalValue;

    return Number(calcValue)
}

// ==========================================================================================
// ローディングアニメーション
// ==========================================================================================

/**
 * ローディングアニメーション表示
 * @param elementName string ローディングアニメーションを設置する要素名: "#id" ".class"など
 * @param message string ローディングメッセージ
 */
export function dispLoadingAnimation(elementName, message = "") {
    // 画面表示メッセージ
    const msgTag = '<span class="common-loading__spinner--message">' + message + '</span>';

    const loading = '<div class="common-loading">'
        + '<div class="common-loading__spinner">'
        + '<div class="spinner-border" role="status"></div>'
        + msgTag
        + '</div>'
        + '</div>';

    // ローディング画像が表示されていない場合のみ出力
    if ($(".common-loading").length === 0) {
        $(elementName).append(loading);
    }
}

/**
 * ローディングアニメーション削除
 */
export function removeLoadingAnimation() {
    $(".common-loading").remove();
}

// ==========================================================================================
// ページネーションのリンクを変更
// ==========================================================================================

/**
 * ページネーションのリンクを無効化し、data属性のurlに遷移先を保持させる
 */
export function linkDisable() {
    $('.pagination a').each(function (index, element) {
        if (!$(this).data('url')) {
            let url = $(this).attr('href');

            // ページが設定されていない場合はpage=1を追加する
            if (url && !url.match(/page=/)) {
                if (url.match(/\?/)) {
                    url += "&page=1";
                } else {
                    url += "?page=1";
                }
            }

            $(this).data('url', url);

            $(this).removeAttr('href');
        }
    });
}


// ==========================================================================================
// セレクトボックス式ページネーションの設定
// ==========================================================================================

/**
 * セレクトボックス式ページネーションの設定
 * （0件の場合は表示させない）
 *
 * @param baseUrl string 1ページめのURL（getパラメータ無し）
 * @param currentPage
 * @param totalPage
 */
export function setPaginationSelectbox(baseUrl, currentPage, totalPage) {
    // セレクトボックスの内容を作成
    let selectHtml = "";

    if (totalPage <= 1) {
        $('.searchbar-pagination').hide();
        return;
    }


    for (let i = 1; i <= totalPage; i++) {
        let selected = "";
        if (currentPage === i) selected = " selected";
        selectHtml += "<option value='" + baseUrl + "?page=" + i + "' " + selected + ">" + i + "</option>";
    }

    $('.searchbar-pagination__select').each(function () {
        $(this).html(selectHtml);
    });

    // 最大ページ数の設定
    $('.searchbar-pagination__total-page').each(function () {
        $(this).text(totalPage);
    });

    // 前へボタンの設定
    $('.searchbar-pagination__prev').each(function () {
        if (currentPage === 1) {
            $(this).prop('disabled', true);
            $(this).val();
        } else {
            $(this).prop('disabled', false);
            $(this).val(baseUrl + "?page=" + (currentPage - 1));
        }
    });

    // 次へボタンの設定
    $('.searchbar-pagination__next').each(function () {
        if (currentPage === totalPage) {
            $(this).prop('disabled', true);
            $(this).val();
        } else {
            $(this).prop('disabled', false);
            $(this).val(baseUrl + "?page=" + (currentPage + 1));
        }
    });

    $('.searchbar-pagination').show();
}


$(function () {
    // ラジオボタンを押した際に表示のON/OFFを切り替える処理
    $('.input__toggle--radio').on('change', function (e) {
        const type = $(this).val();
        const target = $('.' + $(this).attr('name'));
        if (type == 0) {
            target.hide();

        } else {
            target.show();
        }
        //納品可能曜日設定の個別設定の曜日のチェックボックスを表示する
        $("[id^='customer-delivery-days-']").show();
    });
})

/**
 *  ラジオボタン切り替え時に入力欄の制御を行う
 */
$('.input__display--radio').on('change', function (e) {
    const type = $(this).val();
    const target = $('.' + $(this).attr('name'));
    if (type == 0) {
        target.prop('disabled', true);
    } else {
        target.prop('disabled', false);
    }
})

/**
 * 現在時刻を取得(yyyymmddhhmm形式)
 */
export function getNowDate() {
    const now = new Date();
    const year = "" + now.getFullYear();
    const month = ("00" + (now.getMonth() + 1)).slice(-2);
    const date = ("00" + now.getDate()).slice(-2);
    const hour = ("00" + now.getHours()).slice(-2);
    const min = ("00" + now.getMinutes()).slice(-2);

    return (year + month + date + hour + min);
}

/**
 * ログアウトボタン押下時のチェック処理
 */
$(function () {
    $('.logout').on('click', function (e) {
        // ユーザータイプをセッションストレージから取得
        const userType = getSessionUserType();

        // 注文内容を取得
        const data = localStorage.getItem("order-" + userType + "-" + common.getLoginId());
        if (data && data !== "[]") {
            const msg = '注文されていない商品があります。\n本当にログアウトしてよろしいですか？';
            if (!confirm(msg)) {
                return false;
            }
        }
    });
})

/**
 * カッコ付きの税抜/税込/非課税のテキストを返す
 * @param taxType int 1:税抜、2:税込み、3:非課税
 * @returns {string}
 */
export function getTaxTypeText(taxType) {
    let taxString = '';
    switch (Number(taxType)) {
        case 1:
            taxString = message.TEXT_NO_TAX;
            break;
        case 2:
            taxString = message.TEXT_IN_TAX;
            break;
        case 3:
            taxString = message.TEXT_TAX_FREE;
            break;
    }
    return taxString;
}

// Enterキーの入力を無効にする
export function disableEnter(which, keyCode) {
    if ((which && which === 13) || (keyCode && keyCode === 13)) {
        return false;
    } else {
        return true;
    }
}

// ==========================================================================================
// user_type（ログイン者が一般ユーザ化営業マンかの判定）の操作
// ==========================================================================================

/**
 * SessionStorageからuser_typeを取得
 * 存在しない場合はLocalStorageから作成し、SessionStoragに追加する
 *
 * @returns {string}
 */
export function getSessionUserType() {
    let userType = sessionStorage.getItem('user_type');

    if (!userType || userType === '') {
        const loginType = localStorage.getItem("loginInfo")
        if (!loginType) return '';

        const loginData = JSON.parse(loginType);
        if (!loginData['login_type']) return '';

        if (loginData['login_type'] !== '') {
            userType = 'u';
        } else {
            userType = 'b';
        }
        setSessionUserType(loginData['login_type']);
    }

    return userType;
}

/**
 * SessionStoragにuser_typeを追加する
 *
 * @param loginType
 */
export function setSessionUserType(loginType) {
    // loginTypeに合わせて営業マンフラグをセッションに追加
    if (Number(loginType) === 1)
        sessionStorage.setItem("user_type", 'u');
    else
        sessionStorage.setItem("user_type", 'b');
}


// ==========================================================================================
// データの初期化
// ==========================================================================================

export function clearLocalStorage() {
    localStorage.clear();
}

export function clearSessionStorage() {
    sessionStorage.clear();
}
