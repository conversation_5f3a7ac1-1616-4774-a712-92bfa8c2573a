/////////////////////////////////////////////////////////
// 管理画面 取引先管理（ユーザ管理）用の処理

// TODO 削除処理
// TODO DB操作の処理

/**
 * 初期表示時の設定
 * ・リスト表示のためのデータを取得する
 */
$(function () {
    getUserList();
    setRequire();
});

/**
 * Ajax通信でリストデータを取得する
 */
function getUserList() {
    // 検索用urlを取得
    let url = $('[name="ajaxUrl"]').val();
    if (!url) return false;

    //ユーザー一覧画面の場合検索条件を取得する
    let data = "";
    if (~url.indexOf('/user_list')) {
        data = getSearchParam();
    }

    //更新アニメーション開始
    dispLoading("取得中...");

    // CSRFトークンを設定
    $.ajaxSetup({
        headers: {'X-CSRF-TOKEN': $('input[name=_csrfToken]').val()}
    });
    $.ajax({
        url: url,
        type: 'POST',
        data: data,
        timeout: 30000,
    }).done(function (data) {
        if (data.status === 'error') {
            window.location.href = data.url;
        } else {
            if (~url.indexOf('/user_list')) {
                updateListUsers(data.html);
            } else {
                updateList(data.html);
            }
        }
    }).fail(function (data) {
        let msg = "エラー";

        if (data.statusText === "timeout") msg = message.E001;
        if (data.statusText === "Internal Server Error") msg = message.E002;

        alert(msg);
    }).always(function (data) {
        //更新アニメーション終了
        removeLoading();
    });
}

/**
 * リストエリアに取得したリストを設定する
 *
 * @param data リストエリアのHTMLコード
 */
function updateList(data) {
    // リストを更新
    $('#register_user_list').html(data);
}

/**
 * リストエリアに取得したリストを設定する
 *
 * @param data リストエリアのHTMLコード
 */
function updateListUsers(data) {
    // リストを更新
    $('#list-template').html(data);

    common.linkDisable();
}

// ========================================================
// モーダルダイアログ処理
// ========================================================
/**
 * モーダルダイアログオープンイベント
 * 開くボタンにセットされているアカウント情報を取得して、ダイアログに設定する
 */
$(document).on('show.bs.modal', '#user-managed', function (e) {
    // 押下ボタン
    const button = $(e.relatedTarget);
    const id = $(e.relatedTarget).data('id');
    const modal = $(this);

    // ログインID欄の必須表示を修正
    const requireObj = $('.user-manage-dialog__required');
    // const tooltipObj = $('.user-manage-dialog__tooltip');

    const msgTmpPassword = "仮パスワードを変更する場合のみ入力してください";

    modal.find('.input__error').text('');

    // 企業コードが001以外の場合「取引先コード出力区分」を非表示とする
    if ($('[name="company_code"]').val() != '001') {
        modal.find('.customer_code_kubun').css('display', 'none');
        modal.find('#dialog_order_sort_type1').text('オーダーリスト取込順');
    }

    // idの有無に合わせて設定する
    if (!id) {
        modal.find('.modal-title').text('新規ユーザー追加');

        const value = button.data('value');
        modal.find('[name="dialog_user_id"]').val("");
        modal.find('[name="dialog_login_id"]').val("");
        modal.find('[name="dialog_mail_address"]').val("");
        modal.find('[name="dialog_tel_no"]').val("");
        modal.find('[name="dialog_password"]').val("");
        modal.find('[name="dialog_name"]').val("");
        modal.find('[name="dialog_is_active"]').val(["1"]);
        modal.find('[name="dialog_order_sort_type"]').val(["1"]);
        modal.find('[name="dialog_is_demo"]').val(["0"]);
        //企業コードが001の場合、取引先コード出力区分のデフォルトを「取引先コード＋取引先枝番」とする
        if ($('[name="company_code"]').val() == '001') {
            modal.find('[name="customer_code_type"]').val(["2"]);
        } else {
            modal.find('[name="customer_code_type"]').val(["1"]);
        }

        modal.find('[name="dialog_customer_id"]').val(value.customer_id);

        modal.find('.modal_submit').text('登　録');

        $('[name="dialog_login_id"]').prop('disabled', false);

        // 仮パスワードの表示欄
        modal.find('.dialog_password_view').text("");

        requireObj.show();
        // tooltipObj.hide();
    } else {
        modal.find('.modal-title').text('ユーザー情報更新');
        const value = button.data('value');
        var rename = value.name;
        rename = rename.replace(/"/g,"”");
        rename = rename.replace(/'/g,"’");
        modal.find('[name="dialog_user_id"]').val(value.user_id);
        modal.find('[name="dialog_login_id"]').val(value.login_id);
        modal.find('[name="dialog_mail_address"]').val(value.mail_address);
        modal.find('[name="dialog_tel_no"]').val(value.tel_no);
        modal.find('[name="dialog_password"]').val("");
        modal.find('[name="dialog_name"]').val(rename);
        modal.find('[name="dialog_is_active"]').val([value.is_active]);
        modal.find('[name="dialog_order_sort_type"]').val([value.order_sort_type]);
        modal.find('[name="dialog_is_demo"]').val([value.is_demo]);
        modal.find('[name="customer_code_type"]').val([value.customer_code_type]);
        modal.find('[name="dialog_customer_id"]').val(value.customer_id);

        modal.find('.modal_submit').text('更　新');

        $('[name="dialog_login_id"]').prop('disabled', true);

        // 仮パスワードの表示欄
        const tmpPasswordText = (value.tmp_password !== '' ? "現在の設定値： " + value.tmp_password : '');
        modal.find('.dialog_password_view').text(tmpPasswordText);

        requireObj.hide();
        // tooltipObj.show();
    }
});

// ダイアログで確定ボタンを押下
$(document).on('click', '.modal_submit', function (e) {
    const modal = $(this).parents('.modal');
    const id = modal.find('[name="dialog_user_id"]').val();

    // 記載項目を取得
    const data = {
        "user_login_id": modal.find('[name="dialog_login_id"]').val(),
        "tmp_password": modal.find('[name="dialog_password"]').val(),
        "user_name": modal.find('[name="dialog_name"]').val(),
        "mail_address": modal.find('[name="dialog_mail_address"]').val(),
        "tel_no": modal.find('[name="dialog_tel_no"]').val(),
        "is_active": modal.find('[name="dialog_is_active"]:checked').val(),
        "order_sort_type": modal.find('[name="dialog_order_sort_type"]:checked').val(),
        "is_demo": modal.find('[name="dialog_is_demo"]:checked').val(),
        "customer_code_type": modal.find('[name="customer_code_type"]:checked').val(),
        "customer_id": modal.find('[name="dialog_customer_id"]').val(),
    };

    //バリデーション処理
    let state = "更新"; //ダイアログ表示用
    const resultUserLoginId = validationElementLoginId(data["user_login_id"], ".error_login_id", id);
    const resultTmpPassword = validationElementTmpPassword(data["tmp_password"], ".error_password", id);
    const resultUserName = validationElementUserName(data["user_name"], ".error_name");
    const resultMailAddress = validationElementMailAddress(data["mail_address"], ".error_mail_address");
    const resultTelNumber = validationElementTelNumber(data["tel_no"], ".error_tel_number");

    // IDが未設定の場合、登録として扱う
    if (!id) {
        state = "登録";
    }

    //バリデーション処理に1つでも引っかかっていれば処理を中止する
    if (resultUserLoginId || resultTmpPassword || resultUserName || resultMailAddress || resultTelNumber) {
        return false;
    }

    // 確認ダイアログ
    if (!confirm("入力した内容で" + state + "します。\nよろしいですか？")) {
        /* キャンセルの時の処理 */
        return false;
    }

    // urlの設定
    let url = $('[name="modal_ajaxUrl"]').val();
    if (state === "更新") url += "/" + id;

    // Ajaxで登録
    updateData(url, data);

    // AWS環境移行後、モーダル画面が閉じない不具合の対応
    if(document.getElementsByClassName("modal-backdrop") != null) {
        document.getElementsByClassName("modal-backdrop").item(0).remove();
    }

    // OK→リスト更新
    // NG→エラーメッセージ表示、ダイアログそのまま

    return true;
});

/**
 * ログインIDのバリデーションを行う
 * ・必須チェック
 * ・最大文字数チェック
 * ・全角文字チェック
 *
 * @param {string} value
 * @param {string} element
 * @param {string} id
 */
function validationElementLoginId(value, element, id = "") {
    const maxLength = 20;   //最大文字数
    const resultRequired = validationConfirmRequired(value, id) //必須チェック処理
    let result2byte = false;
    let resultLength = false;

    //入力があれば全角チェック処理と最大文字数チェック処理を行う
    if (!resultRequired) {
        result2byte = validationConfirm2byte(value)
        resultLength = validationConfirmLength(value, maxLength)
    }

    //画面へのエラーメッセージ表示処理を行い結果を受け取る
    result = validationRender(element, maxLength, resultRequired, result2byte, resultLength)

    return result;
}

/**
 * 仮パスワードのバリデーションを行う
 * ・必須チェック
 * ・最大文字数チェック
 * ・全角文字チェック
 *
 * @param {string} value
 * @param {string} element
 * @param {string} id
 */
function validationElementTmpPassword(value, element, id = "") {
    const maxLength = 15;   //最大文字数
    const resultRequired = validationConfirmRequired(value, id)
    let result2byte = false;
    let resultLength = false;

    if (!resultRequired) {
        result2byte = validationConfirm2byte(value, id)
        resultLength = validationConfirmLength(value, maxLength)
    }

    result = validationRender(element, maxLength, resultRequired, result2byte, resultLength)

    return result;
}

/**
 * 担当者名のバリデーションを行う
 * ・必須チェック
 * ・最大文字数チェック
 *
 * @param {string} value
 * @param {string} element
 */
function validationElementUserName(value, element) {
    const maxLength = 50;   //最大文字数
    const resultRequired = validationConfirmRequired(value)
    let result2byte = false;
    let resultLength = false;

    if (!resultRequired) {
        resultLength = validationConfirmLength(value, maxLength)
    }

    result = validationRender(element, maxLength, resultRequired, result2byte, resultLength)

    return result;
}

/**
 * メールアドレスのバリデーションを行う
 * ・必須チェック
 * ・最大文字数チェック
 * ・全角文字チェック
 *
 * @param {string} value
 * @param {string} element
 */
function validationElementMailAddress(value, element) {
    const maxLength = 200;   //最大文字数
    const resultRequired = validationConfirmRequired(value)
    let result2byte = false;
    let resultLength = false;

    if (!resultRequired) {
        result2byte = validationConfirm2byte(value)
        resultLength = validationConfirmLength(value, maxLength)
    }

    result = validationRender(element, maxLength, resultRequired, result2byte, resultLength)

    return result;
}

/**
 * 電話番号のバリデーションを行う
 * ・最大文字数チェック
 * ・全角文字チェック
 *
 * @param {string} value
 * @param {string} id
 */
function validationElementTelNumber(value, element, id = "-") {
    const maxLength = 13;   //最大文字数
    const resultRequired = false;
    let result2byte = false;
    let resultLength = false;

    result2byte = validationConfirm2byte(value, id)
    resultLength = validationConfirmLength(value, maxLength)

    result = validationRender(element, maxLength, resultRequired, result2byte, resultLength)

    return result;
}

/**
 * 必須入力チェック処理を行う
 *
 * @param {string} value
 * @param {string} id
 */
function validationConfirmRequired(value, id = "") {
    //更新時、ログインIDと仮パスワードは空値でも良い
    //NGならtrue、OKならfalseを返す
    if (value == "" && id == "") {
        return true;
    } else {
        return false;
    }
}

/**
 * 全角文字チェック処理を行う
 *
 * @param {string} value
 */
function validationConfirm2byte(value, id = "") {
    //更新時かつ空の場合はOK（false）を返す
    if (value == "" && id !== "") {
        return false;
    }

    //全角文字が含まれていればtrue、含まれていなければfalse
    const result = (value.match(/^[\x20-\x7E]+$/)) ? false : true;

    //NGならtrue、OKならfalseを返す
    if (result) {
        return true;
    } else {
        return false;
    }
}

/**
 * 最大文字数チェック処理を行う
 *
 * @param {string} value
 * @param {integer} maxLength
 */
function validationConfirmLength(value, maxLength) {
    const length = value.length;
    //NGならtrue、OKならfalseを返す
    if (length > maxLength) {
        return true;
    } else {
        return false;
    }
}

/**
 * モーダル画面にバリデーションエラーメッセージを表示する
 *
 * @param {string} element
 * @param {integer} maxLength
 * @param {boolean} resultRequired
 * @param {boolean} result2byte
 * @param {boolean} resultLength
 */
function validationRender(element, maxLength, resultRequired, result2byte, resultLength) {
    let result = false;
    $(element).empty(); //要素の中を空にする
    if (resultRequired == true) {
        $(element).append('入力必須項目です。');
        result = true;
    }
    if (result2byte == true) {
        $(element).append('全角文字が含まれています。');
        result = true;
    }
    if (resultLength == true) {
        if ($(element).html().length !== 0) {
            $(element).append("<br>");
        }
        $(element).append('文字数が' + maxLength + '文字を超えています。');
        result = true;
    }

    return result;
}

/**
 * 承認するボタン押下時処理
 */
$(document).on('click', '.input__table-button--red', function () {
    const input = $(this).parents('.input');    //リストエリアの一番外の要素
    const rowNumber = $(this).val();    //承認するボタンのパラメータから画面上の行番号を取得する
    const customerId = input.find('[name="customer_id"]').val();    //hiddenから取引先IDを取得する
    const userId = input.find("[name='user_id[" + rowNumber + "]']").val(); //行番号を基にユーザーIDを取得する
    const userName = input.find("[name='user_name[" + rowNumber + "]']").val(); //行番号を基にユーザー名を取得する

    // 確認ダイアログ
    if (!confirm(userName + "さんを承認します。\nよろしいですか？")) {
        /* キャンセルの時の処理 */
        return false;
    }

    // 記載項目を取得
    const data = {
        "customer_id": customerId,
        "user_id": userId,
        "is_approved": "1",
    };

    // urlの設定
    let url = $('[name="approve_ajaxUrl"]').val();

    // Ajaxで登録
    updateData(url, data, "approve");

    // OK→リスト更新
    // NG→エラーメッセージ表示、ダイアログそのまま

    return true;
});

/**
 * 選択ユーザ削除ボタン押下時処理
 */
$(document).on('click', '.input__button--del', function () {
    const msgAlert = "削除するユーザーを選択してください";
    const msgConfirm = "選択したユーザーがすべて削除されますがよろしいですか？";

    //チェックされているチェックボックスのvalueからユーザーIDを取得する
    usersId = $('[class="delete"]:checked').map(function () {
        return 'usersId[]=' + $(this).val()
    }).get().join('&');

    //チェックされているチェックボックスが0なら処理を中断する
    if (usersId.length == 0) {
        alert(msgAlert);
        return false;
    }

    // 確認ダイアログ
    if (!confirm(msgConfirm)) {
        /* キャンセルの時の処理 */
        return false;
    }

    // urlの設定
    let url = $('[name="delete_ajaxUrl"]').val();

    // Ajaxで登録
    updateData(url, usersId, "delete");

    // OK→リスト更新
    // NG→エラーメッセージ表示、ダイアログそのまま

    return true;
});

/**
 * 一括承認ボタン押下時処理
 */
$(document).on('click', '.button__bulk-approve', function () {
    //チェックされているチェックボックスのvalueからユーザーIDを取得する
    usersId = $('[class="custom-control-input chkAll"]:checked').map(function () {
        return 'usersId[]=' + $(this).val()
    }).get().join('&');

    //チェックされているチェックボックスが0なら処理を中断する
    if (usersId.length == 0) {
        alert('承認するユーザーを選択してください');
        return false;
    }

    // 確認ダイアログ
    if (!confirm("選択されたユーザーを承認します。\nよろしいですか？")) {
        /* キャンセルの時の処理 */
        return false;
    }

    // urlの設定
    let url = $('[name="bulk_approve_ajaxUrl"]').val();

    // Ajaxで登録
    //ユーザー一覧画面の一括承認ボタンの場合true
    if ($(this).attr("id") == 'button__bulk-approve--user-list') {
        updateData(url, usersId, "bulk_approve_user_list");
    } else {
        updateData(url, usersId, "bulk_approve");
    }

    // OK→リスト更新
    // NG→エラーメッセージ表示、ダイアログそのまま

    return true;
});

/**
 * Ajax通信でリストデータを取得する
 */
function updateData(url, data, action = "") {
    //更新アニメーション開始
    dispLoading("処理中...", '.modal');

    // CSRFトークンを設定
    $.ajaxSetup({
        headers: {'X-CSRF-TOKEN': $('input[name=_csrfToken]').val()}
    });
    $.ajax({
        url: url,
        type: 'POST',
        data: data,
        timeout: 30000,
    }).done(function (data) {
        if (data.status === 'error') {
            if (action == "") {
                // エラーメッセージをダイアログに表示
                setErrorMessages(data.errorMessages);
            } else {
                if (action == 'bulk_approve_user_list') {
                    getUserListUsers();
                    //エラーメッセージを画面に表示する
                    setErrorMessagesWindowBulk(data.errorMessages);
                } else if (action == 'bulk_approve') {
                    getUserList();
                    setErrorMessagesWindowBulk(data.errorMessages);
                } else {
                    getUserList();
                    setErrorMessagesWindow(data.errorMessages);
                }
            }
            alert(data.message);
        } else {
            // 登録完了メッセージ
            alert(data.message);

            // ダイアログを閉じてリストを更新
            removeLoading('.modal');
            //ユーザー一覧画面の一括承認ボタンの場合true
            if (action == 'bulk_approve_user_list') {
                getUserListUsers(); //ユーザー一覧画面の一括承認ボタン押下時
            } else {
                getUserList();  //それ以外
            }
            $('#user-managed').modal('hide');
        }
    }).fail(function (data) {
        let msg = "エラー";

        if (data.statusText === "timeout") msg = message.E001;
        if (data.statusText === "Internal Server Error") msg = message.E002;

        alert(msg);
    }).always(function (data) {
        //更新アニメーション終了
        removeLoading('.modal');
    });
}

/**
 * モーダルに表示するエラーメッセージを設定する
 */
function setErrorMessages(msgArray) {

    $('.input__error').text('');

    // エラーメッセージに対応する入力欄にエラーを表示する
    Object.keys(msgArray).forEach(function (val) {
        let message = msgArray[val];
        switch (val) {
            case "login_id":
                $('.error_login_id').text(message);
                break;
            case "password":
                $('.error_password').text(message);
                break;
            case "name":
                $('.error_name').text(message);
                break;
            default:
                $('.error_db').text(message);
                break;
        }
    });
}

/**
 * 画面に表示するエラーメッセージを設定する
 */
function setErrorMessagesWindow(msgArray) {
    $('.input__error--render').text('');
    Object.keys(msgArray).forEach(function (val) {
        let message = msgArray[val];
        $('.input__error--render').text(message);
    });
}

/**
 * 画面に表示するエラーメッセージを設定する（複数行）
 */
function setErrorMessagesWindowBulk(msgArray) {
    $('.input__error--render').empty();
    $.each(msgArray, function (index, val) {
        $.each(val, function (index2, val2) {
            let message = val2;
            if ($('.input__error--render').html().length !== 0) {
                $('.input__error--render').append("<br>");
            }
            $('.input__error--render').append(message);
        });
    });
}

// ========================================================
// ローディングアニメーション
// ========================================================
/**
 * ローディングイメージ表示
 * @param msg ローディングメッセージ
 * @param place アニメーション表示位置
 */
function dispLoading(msg, place = "body") {
    // 引数なし（メッセージなし）を許容
    if (msg === undefined) {
        msg = "";
    }
    // 画面表示メッセージ
    const msgTag = '<span class="common-loading__spinner--message">' + msg + '</span>';

    const loading = '<div class="common-loading">'
        + '<div class="common-loading__spinner">'
        + '<div class="spinner-border" role="status"></div>'
        + msgTag
        + '</div>'
        + '</div>';

    // ローディング画像が表示されていない場合のみ出力
    if ($(".common-loading").length === 0) {
        $(place).append(loading);
    }
}

/**
 * Loading イメージ削除
 */
function removeLoading(place = 'body') {
    $(place + " .common-loading").remove();
}


/**
 * Ajax通信でリストデータを取得する
 */
function getUserListUsers() {
    const data = getSearchParam();

    // 検索用urlを取得
    let url = $('[name="ajaxUrl"]').val();

    if (!url) return false;

    //更新アニメーション開始
    dispLoading("取得中...");

    // CSRFトークンを設定
    $.ajaxSetup({
        headers: {'X-CSRF-TOKEN': $('input[name=_csrfToken]').val()}
    });
    $.ajax({
        url: url,
        type: 'POST',
        data: data,
        timeout: 30000,
    }).done(function (data) {
        if (data.status === 'error') {
            window.location.href = data.url;
        } else {
            updateListUsers(data.html);
        }
    }).fail(function (data) {
        let msg = "エラー";

        if (data.statusText === "timeout") msg = message.E001;
        if (data.statusText === "Internal Server Error") msg = message.E002;

        alert(msg);
    }).always(function (data) {
        //更新アニメーション終了
        removeLoading();
    });
}

/**
 * セッションストレージよりパラメータを取得する
 */
function getSearchParam() {
    // セッションストレージよりパラメータを取得して配列化
    const serialize = sessionStorage.getItem($('input[name="searchKey"]').val());

    const data = JSON.parse(serialize);
    return data;
}

/**
 * ログイン情報再送ボタン押下時処理
 */
$(document).on('click', '.input__table-button--blue', function () {
    const input = $(this).parents('.input');    //リストエリアの一番外の要素
    const rowNumber = $(this).val();    //承認するボタンのパラメータから画面上の行番号を取得する
    const customerId = input.find('[name="customer_id"]').val();    //hiddenから取引先IDを取得する
    const userId = input.find("[name='user_id[" + rowNumber + "]']").val(); //行番号を基にユーザーIDを取得する
    const userName = input.find("[name='user_name[" + rowNumber + "]']").val(); //行番号を基にユーザー名を取得する

    // 確認ダイアログ
    if (!confirm(userName + "様にログイン情報を再送付します。\n仮パスワードは自動で作成されます。\nよろしいですか？")) {
        /* キャンセルの時の処理 */
        return false;
    }

    // 記載項目を取得
    const data = {
        "customer_id": customerId,
        "user_id": userId,
    };

    // urlの設定
    let url = $('[name="login_info_resend_ajaxUrl"]').val();

    // Ajaxで登録
    updateData(url, data, "login_info_resend");

    // OK→リスト更新
    // NG→エラーメッセージ表示、ダイアログそのまま

    return true;
});

/**
 *  ラジオボタン切り替え時に入力欄の制御を行う
 */
$('.input__require--radio').on('change', function (e) {
    setRequire();
})

/**
 * 「発注コード」の入力必須制限の切り替えを行う
 */
function setRequire() {
    //「オーダーリストの表示」の選択値を取得
    const type = $('input[name=use_order_code]:checked').val();
    //取引先コード単位の場合
    if (type == 0) {
        //「発注コード」の必須制限を外す
        $('#order_code').removeClass('validate[required]');

        //発注コード単位の場合
    } else {
        //「発注コード」に必須制限を設ける
        $('#order_code').addClass('validate[required]');
    }
}

// ========================================================
// 承認チェックボックス一括選択
// ========================================================
$(function () {
    $(document).on('change', '#all', function () {
        $('.chkAll').prop("checked", $(this).prop("checked"));
    });
});
