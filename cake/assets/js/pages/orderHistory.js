/////////////////////////////////////////////////////////
// 発注履歴画面

$(function () {
    // 検索条件の読み込み
    setSearchInfo();

    // ページ番号の取得
    const page = $('input[name="historyPageNo"]').val() || 1;

    // リストエリアの更新
    doSearch(page);
});

/**
 * セッションストレージから保存された検索条件を取得し、セットする
 */
function setSearchInfo() {
    // 検索値をセッションストレージから検索searchList
    const searchKey = $('input[name="searchKey"]').val();

    const prevController = $('input[name="prevController"]').val();
    if (prevController !== 'OrderHistory') {
        // 発注履歴（明細）画面以外からの遷移時は検索内容をクリア
        sessionStorage.removeItem(searchKey);
        // チェックボックスの初期化
        $('input[name="customCheck1"]').prop("checked", true);
        return;
    }

    // 検索キーが設定されていれば取得
    const serialize = sessionStorage.getItem(searchKey);
    if (!serialize) return false;

    // JSON 形式を変換
    const searchKeyArr = JSON.parse(serialize);

    // 該当要素の項目に値を設定する
    searchKeyArr.forEach(searchKey => {
        if (searchKey.value !== "") {
            const inputArea = $('form.search [name="' + searchKey.name + '"]');
            const type = inputArea.attr('type');
            // inputエリア
            switch (type) {
                case "checkbox":
                    inputArea.prop("checked", true);
                    break;
                default:
                    inputArea.val(searchKey.value);
                    break;
            }
        }
    });

    // ページ番号の取得
    $('input[name="historyPageNo"]').val(sessionStorage.getItem('OrderHistoryPageNo'));
}

/**
 * 再表示ボタンをクリック
 */
$('.search__button--submit').on('click', function () {
    doSearch();
});


/**
 * 検索用URLを設定してリストを取得する
 */
function doSearch(page = 1) {
    // 検索の入力項目をシリアライズ
    const serialize = $('form.search').serializeArray();

    // 不要な項目を削除
    const data = serialize.filter(function (val) {
        return (val.name !== "_csrfToken" && val.name !== "_method");
    });

    // シリアライズ値をJSON化して保存
    const json = JSON.stringify(data);
    sessionStorage.setItem($('input[name="searchKey"]').val(), json);

    // 検索用urlを取得
    let url = $('[name="ajaxUrl"]').val();
    // 検索時はページを1に設定
    if (page !== 1) url = url + "?page=" + page;

    searchList(url, data);
}

/**
 * ページネーションのリンク押下時の処理
 * 該当ページのリストを取得する
 */
$(document).on('click', '#list-template .pagination a', function () {
    const url = $(this).data('url');

    if (!url) return false;

    const data = getSearchData();

    // リストの更新
    searchList(url, data);
});

/**
 * セッションストレージに保存された検索データを取得
 */
function getSearchData() {
    const searchData = sessionStorage.getItem($('input[name="searchKey"]').val());

    return JSON.parse(searchData);
}

/**
 * 一覧取得用Ajax処理
 * @param url ajax用URL
 * @param data シリアライズされたform入力値
 */
function searchList(url, data) {
    //更新アニメーション開始
    common.dispLoadingAnimation('#list-template', "取得中...");

    // CSRFトークンを設定
    $.ajaxSetup({
        headers: {'X-CSRF-TOKEN': $('input[name=_csrfToken]').val()}
    });
    $.ajax({
        url: url,
        type: 'POST',
        data: data,
        timeout: 30000,
    }).done(function (data) {
        // 文字列の場合はデコード
        if (typeof data === 'string') {
            data = JSON.parse(data);
        }

        if (data.status === 'error') {
            window.location.href = data.url;
        } else if (data.status === 'api_error') {
            auth.forceLogout(message.E003);
            return false;
        } else {
            updateList(data);
        }
    }).fail(function (data) {
        let msg = "エラー";

        if (data.statusText === "timeout") msg = message.E001;
        if (data.statusText === "Internal Server Error") msg = message.E002;

        alert(msg);
    }).always(function (data) {
        //更新アニメーション終了
        common.removeLoadingAnimation();
    });
}

/**
 * リストエリアに取得したリストを設定する
 *
 * @param data リストエリアのHTMLコード
 */
function updateList(data) {
    // リストを更新
    $('#list-template').html(data.html);

    // セレクトボックス式ページングを設定
    common.setPaginationSelectbox(data.url, data.currentPage, data.totalPage);

    // 現在のページ番号をセッションストレージに保存
    sessionStorage.setItem('OrderHistoryPageNo', data.currentPage);

    // 通常ページングの設定
    common.linkDisable();
}


/////////////////////////////////////////////////////////
// セレクトボックス式ページネーション
/////////////////////////////////////////////////////////

/**
 * セレクトボックス式ページネーションの選択時の処理
 * 該当ページのリストを取得する
 */
$(document).on('change', 'select.searchbar-pagination__select', function () {
    const ajaxUrl = $(this).val();

    const data = getSearchData();

    // リストの更新
    searchList(ajaxUrl, data);
});

/**
 * セレクトボックス式ページネーションの「前へ」ボタン押下処理
 */
$(document).on('click', '.searchbar-pagination__prev', function () {
    const ajaxUrl = $(this).val();
    const data = getSearchData();

    // リストの更新
    searchList(ajaxUrl, data);
});

/**
 * セレクトボックス式ページネーションの「次へ」ボタン押下処理
 */
$(document).on('click', '.searchbar-pagination__next', function () {
    const ajaxUrl = $(this).val();
    const data = getSearchData();

    // リストの更新
    searchList(ajaxUrl, data);
});