//////////////////////////////////////////////////////
// 企業管理画面の処理

$(function () {
    // 商品区分名称のラベルをリアルタイムに反映設定
    $('.input__order-type--text').on('change', function () {
        const value = $(this).val();
        let text = "";
        if (value.length === 1) text = value;
        if (value.length > 1) text = value[0] + " " + value[1];
        $(this).parents('.input__order-type').find('.input__badge--text').text(text);
    })

});

//「発注コード利用」ラジオボタン切り替え時の処理
$('input[name=is_order_code]').on('change', function (e) {
    // DB上の設定値を取得
    const defaultValue = $('[name=is_order_code_default]').val();
    // 現在の選択値を取得
    const type = $('input[name=is_order_code]:checked').val();
    if (defaultValue == 1 && type == 0) {
        const msg = '発注コードによる取りまとめを解除します。\nよろしいですか？';
        if (!confirm(msg)) {
            $('#is-order-code-1').prop('checked', true);
            return;
        }
    }
})

//「発注画面の金額表示」ラジオボタン切り替え時の処理
$('input[name=terminal_price_type]').on('change', function (e) {
    // DB上の設定値を取得
    const defaultValue = $('[name=terminal_price_type_default]').val();
    // 現在の選択値を取得
    const type = $('input[name=terminal_price_type]:checked').val();
    
    // 表示→非表示に変更時
    if (defaultValue == 1 && type == 0) {
        const msg = '全取引先の金額が非表示となります。\nよろしいですか？';
        if (!confirm(msg)) {
            $('#terminal-price-type-1').prop('checked', true);
            return;
        }
    // 非表示→表示に変更時
    } else if (defaultValue == 0 && type == 1) {
        const msg = '「取引先情報管理 登録/更新画面」での設定に合わせて、\n金額の表示・非表示が決定されるようになります。\n取引先ごとに設定してください。';
        if (!confirm(msg)) {
            $('#terminal-price-type-0').prop('checked', true);
            return;
        }
    }
})