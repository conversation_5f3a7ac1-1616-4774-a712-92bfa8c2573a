/////////////////////////////////////////////////////////
// 管理画面 発注情報管理画面（伝票一覧）画面用の処理

$(function () {
    //ラジオボタンの要素名を設定する
    var elementOrderTantou = "input:radio[name='orderHeadersOrderTantou']";
    var elementCustomerCode = "#text__customer-code";
    var elementBusinessmanCode = "#text__businessman-code";

    //設定したラジオボタンの値が変更されたら実行する
    $("input[type='radio']").change(elementOrderTantou,function () {
        //1つ目のラジオボタンがチェックされた
        if ($(elementOrderTantou + ":eq(0)").prop('checked')) {
            setDisabledInputArea(false,false);
            return;
        //2つ目のラジオボタンがチェックされた
        } else if ($(elementOrderTantou + ":eq(1)").prop('checked')) {
            setDisabledInputArea(false, true);
            setBlankInputArea(elementBusinessmanCode);
            return;
        //3つ目のラジオボタンがチェックされた
        } else if ($(elementOrderTantou + ":eq(2)").prop('checked')) {
            setDisabledInputArea(true, false);
            setBlankInputArea(elementCustomerCode);
            return;
        }
    });

    //ラジオボタンと虫眼鏡ボタンの操作可否を引数を基に設定する
    function setDisabledInputArea(isCustomer, isBusinessman) {
        $(elementCustomerCode).prop('disabled', isCustomer);
        $('#button__customer-search--order-header').prop('disabled', isCustomer);
        $(elementBusinessmanCode).prop('disabled', isBusinessman);
        $('#button__businessman-search--order-header').prop('disabled', isBusinessman);
    }

    //引数のテキストボックスの値を空にする
    function setBlankInputArea($element = null) {
        $($element).val('');
    }

    //クリアボタン押下時処理
    $(document).on('click', '.search__button--clear', function () {
        setDisabledInputArea(false, false);
    });
});