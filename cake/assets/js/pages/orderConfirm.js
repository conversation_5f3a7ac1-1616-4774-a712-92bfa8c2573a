/////////////////////////////////////////////////////////
// 発注確認画面

$(function () {

    //セッションストレージの年月日時分秒を確認
    checkOrderTime();

});

function setInqueryList() {
    //お問い合わせ内容を取得
    let inquiry = order.loadBikouFromLS();
    if (!inquiry) inquiry = "";

    // ユーザータイプをセッションストレージから取得
    const userType = common.getSessionUserType();

    //ローカルストレージから発注内容を取得
    let items = localStorage.getItem('order-' + userType + "-" + common.getLoginId());

    if (items === "[]" || !items) {
        items = "";
    }

    //hiddenにデータをセット(POST用)
    $('#order_items').val(items);
    const data = $("#list-template").text();


    //hiddenにお問い合わせ内容をセット(POST用)
    $('#inquiry').val(inquiry);

    // お問い合わせ欄の表示/非表示切り替え
    if (inquiry === "") {
        $('.confirm__inquiry').hide();
    } else {
        $('.confirm__inquiry').show();
        $(".confirm__inquiry--value").html(inquiry.replace(/\r?\n/g, '<br>'));
    }

    //発注データ・問い合わせの両方とも空の場合、「発注確定ボタン」を非表示にする
    if (items.length === 0 && (!inquiry)) {
        $('.content__submit-button--center').hide();

    } else {
        $('.content__submit-button--center').show();
    }
}

// お問い合わせダイアログの登録ボタン押下
$('#regist-inquiry').on('click', function () {
    setInqueryList();
});

/**
 * 発注確定ボタン押下処理
 */
$('.content__submit-button--center').on('click', function () {

    //発注確定ボタンを非活性にする
    $('.content__submit-button--center').prop('disabled', true);

    //ヘッダのaタグを無効にする（PC・タブレット用）
    $('#header a').css('pointer-events', 'none');
    //ヘッダのボタンを無効にする（スマホ用）
    $('#header button').prop('disabled', true);

    //確認ダイアログ表示
    if (!confirm('発注を確定します。よろしいですか？\n※確定後、発注完了画面が表示されるまでは画面を終了しないでください。')) {
        $('.content__submit-button--center').prop('disabled', false);
        $('#header a').css('pointer-events', '');
        $('#header button').prop('disabled', false);

        return false;
    }

    //更新アニメーション開始
    common.dispLoadingAnimation("body", "注文確定中<br>他の操作を行わないでください");

    // カタログの数量チェック
    let isValid = true;
    $('form').find('.confirm__data--text-order-num').each(function () {
        const value = $(this).val()
        const dataDecimal = $(this).parents('.catalog-order-item').data('decimalpoint');

        const result = common.isCorrectOrderValue(value, 1, Number(dataDecimal) === 1);
        // 異常値の場合、アラートを出して何もしない
        if (!result) {
            $(this).focus().select();
            isValid = false;
            //更新アニメーション終了
            common.removeLoadingAnimation();
            $('.content__submit-button--center').prop('disabled', false);
            $('#header a').css('pointer-events', '');
            $('#header button').prop('disabled', false);
            return false;
        }
    })

    if (!isValid) return false;

    //カタログ機能の利用有無
    const useCatalog = $('[name="useCatalog"]').val();

    if (!useCatalog) {
        if (order.getOrderListNum() === 0
            && order.loadBikouFromLS() == '') {
            auth.silentLogout(message.E003);
            return false;
        }

    } else {
        if (order.getOrderListNum() === 0
            && order.loadBikouFromLS() == ''
            && order.getCatalogOrderListNum() == '') {
            auth.silentLogout(message.E003);
            return false;
        }
    }

    //現在時刻を取得(yyyymmddhhmm形式)
    const nowDate = common.getNowDate();

    //締め時刻を取得(yyyymmddhhmm形式)
    const close_datetime = sessionStorage.getItem('close_datetime');

    // 確認画面を開いて確認ボタンを押すまで5分間の猶予を持たせる
    if (parseInt(nowDate) > parseInt(close_datetime) + 5) {
        //更新アニメーション終了
        common.removeLoadingAnimation();

        const message = "ログインしてから発注までの間に締め時間が過ぎました。\n納品予定日に変更がありますので、再度発注情報をご確認いただいてから発注確定をしてください。";

        auth.silentLogout(message);

        return false;
    }

    const loginId = sessionStorage.getItem('login_id');
    $('[name="login_id"]').val(loginId);

    // ユーザータイプをセッションストレージから取得
    const userType = common.getSessionUserType();

    //form内のhidden項目にPOSTデータをセット(カタログ)
    let catalogItems = localStorage.getItem("catalog-" + userType + "-" + common.getLoginId());
    if (catalogItems === "[]" || !catalogItems) {
        catalogItems = "";
    }
    $('#catalog_order_items').val(catalogItems);

    //form内のhidden項目にPOSTデータをセット(スマ発)
    let orderItems = localStorage.getItem("order-" + userType + "-" + common.getLoginId());
    if (orderItems === "[]" || !orderItems) {
        orderItems = "";
    }
    $('#order_items').val(orderItems);
    
    //form内のhidden項目にPOSTデータをセット(カタログ発注リスト)
    let fromCatalogItems = localStorage.getItem("from_catalog-" + userType + "-" + common.getLoginId());
    if (fromCatalogItems === "[]" || !fromCatalogItems) {
        fromCatalogItems = "";
    }
    $('#from_catalog_items').val(fromCatalogItems);

    //hidden項目の年月日時分秒を取得
    $orderTime = $('#orderTime').val();

    //ローカルストレージに年月日時分秒を設定する
    order.saveOrdertimeToLS($orderTime);

    $('form').submit();

});

/**
 * 発注データ取得用Ajax処理
 */
function createListData() {
    // Ajax用URL取得
    const ajaxUrl = $('[name="ajaxUrl"]').val();

    // ローカルストレージから発注データ取得
    let orderDataAll = order.loadDataFromLS();
    if (!orderDataAll) orderDataAll = [];

    let data = {
        'order_data': orderDataAll,
    };

    //更新アニメーション開始
    common.dispLoadingAnimation('#list-template', "取得中...");

    // CSRFトークンを設定
    $.ajaxSetup({
        headers: {'X-CSRF-TOKEN': $('input[name=_csrfToken]').val()}
    });
    $.ajax({
        url: ajaxUrl,
        type: 'POST',
        data: data,
        timeout: 30000,
    }).done(function (data) {
        // 文字列の場合はデコード
        if (typeof data === 'string') {
            data = JSON.parse(data);
        }

        if (data.status === 'error') {
            window.location.href = data.url;
        } else if (data.status === 'api_error') {
            auth.forceLogout(message.E003);
            return false;
        } else if (data.status === 'nodata') {
            displayNonData();
            // ローカルストレージより発注データを削除する
            order.clearDataFromLS();
            // カテゴリの「今回の発注」の数量を設定する
            order.setCurrentCategoryNum();
        } else {
            updateList(data);
            setTotalPriceVal();
        }
    }).fail(function (data) {
        let msg = "エラー";

        if (data.statusText === "timeout") msg = message.E001;
        if (data.statusText === "Internal Server Error") msg = message.E002;

        alert(msg);
    }).always(function (data) {
        //更新アニメーション終了
        common.removeLoadingAnimation();
    });
}

/**
 * カタログ商品 発注データ取得用Ajax処理
 */
function createCatalogListData() {
    // Ajax用URL取得
    const ajaxUrl = $('[name="ajaxUrlCatalog"]').val();

    //URLが未設定（=カタログ機能の利用OFF）の場合、処理を抜ける
    if (!ajaxUrl) {
        return false;
    }

    // ローカルストレージからカタログ商品の発注データ取得
    let catalogDataAll = order.loadCatalogDataFromLS();

    //データがない場合は処理を抜ける
    if (!catalogDataAll || !Object.keys(catalogDataAll).length) {
        return false;
    }

    let data = {
        'catalog_data': catalogDataAll,
    };

    //更新アニメーション開始
    common.dispLoadingAnimation('#list-template-catalog', "取得中...");

    // CSRFトークンを設定
    $.ajaxSetup({
        headers: {'X-CSRF-TOKEN': $('input[name=_csrfToken]').val()}
    });
    $.ajax({
        url: ajaxUrl,
        type: 'POST',
        data: data,
        timeout: 30000,
    }).done(function (data) {
        // 文字列の場合はデコード
        if (typeof data === 'string') {
            data = JSON.parse(data);
        }

        if (data.status === 'error') {
            window.location.href = data.url;
        } else if (data.status === 'api_error') {
            auth.forceLogout(message.E003);
            return false;
        } else if (data.status === 'nodata') {
            //ローカルストレージよりカタログ商品発注データを削除する
            order.clearCatalogDataFromLS();
            // カテゴリの「今回の発注」の数量を設定する
            order.setCurrentCategoryNum();
        } else if (data.status === 'rankInfo_nodata') {
            //ローカルストレージよりカタログ商品発注データを削除する
            order.clearCatalogDataFromLS();
        } else {
            updateCatalogList(data);
            // 単位リストの値と設定値を設定
            setUnitList();
            setTotalPriceVal();
        }
    }).fail(function (data) {
        let msg = "エラー";

        if (data.statusText === "timeout") msg = message.E001;
        if (data.statusText === "Internal Server Error") msg = message.E002;

        alert(msg);
    }).always(function (data) {
        //更新アニメーション終了
        common.removeLoadingAnimation();
    });
}

/**
 * リストエリアに取得したリストを設定する
 *
 * @param data リストエリアのHTMLコード
 */
function updateList(data) {
    // リストを更新
    $('#list-template').html(data.html);

    // ローカルストレージより発注データを削除する
    order.clearDataFromLS();

    //ローカルストレージ更新
    $('.order-item').each(function () {
        const productCode = $(this).data('productscode');
        const customerCode = $(this).data('customercode');
        const customerEdaban = $(this).data('customeredaban');
        const productName = $(this).data('productsname');
        const bikou = $(this).data('bikou');
        const nouhinbi = $(this).data('nouhinbi');
        const orderNum = $(this).data('num');
        const leatime = $(this).data('leadtime');
        const unitCode = $(this).data('unitcode');

        order.setItem(productCode,
            customerCode,
            customerEdaban,
            productName,
            orderNum,
            nouhinbi,
            bikou,
            leatime,
            unitCode)
    })

    // 発注確定ボタンを表示
    $('.content__submit-button--center').show();
}

/**
 * カタログのリストエリアに取得したリストを設定する
 *
 * @param data リストエリアのHTMLコード
 */
function updateCatalogList(data) {
    // リストを更新
    $('#list-template-catalog').html(data.html);

    //ローカルストレージよりカタログ商品発注データを削除する
    order.clearCatalogDataFromLS();

    //ローカルストレージ更新
    $('.catalog-order-item').each(function () {
        const productCode = $(this).data('productscode');
        const customerCode = $(this).data('customercode');
        const customerEdaban = $(this).data('customeredaban');
        const productName = $(this).data('productsname');
        const bikou = $(this).data('bikou');
        let nouhinbi = $(this).data('nouhinbi');
        let unit_code = $(this).data('unit_code');
        let unit_name = $(this).data('unit_name');
        const objNouhinbi = $(this).find(".confirm__data--select-nouhinbi");
        const orderNum = $(this).find(".confirm__data--text-order-num").val();

        // 納品予定日プルダウンの選択値を設定
        objNouhinbi.val(nouhinbi);

        // 納品予定日がリストに無かったら、先頭の値に設定する
        if (objNouhinbi.val() === null || objNouhinbi.val() === "") {
            objNouhinbi.prop("selectedIndex", 0);
            nouhinbi = objNouhinbi.val();
        }

        // ローカルストレージ更新
        order.setCatalogItem(
            productCode,
            customerCode,
            customerEdaban,
            productName,
            orderNum,
            nouhinbi,
            bikou,
            unit_code,
            unit_name);
    })

    //カタログ商品エリアを表示
    $('.row-catalog').show();

    // 発注確定ボタンを表示
    $('.content__submit-button--center').show();
}

/**
 * 合計金額を表示
 */
function setTotalPriceVal() {

    let dispTaxType = "";

    //スマ発商品の発注データの合計金額を取得
    let totalPrice = Math.floor($('#total_price').val());
    if (!totalPrice || totalPrice == 0) {
        totalPrice = 0;
    }

    //スマ発商品の合計金額に表示する税区分を取得
    const taxTypeTotal = $('#total_tax_type').val();
    if (taxTypeTotal) {
        dispTaxType = taxTypeTotal;
    }

    //カタログ機能の利用有無
    const useCatalog = $('[name="useCatalog"]').val();
    if (useCatalog) {

        //カタログ商品の発注データの合計金額を取得
        let totalPriceCatalog = getTotalPriceCatalog();
        totalPrice += totalPriceCatalog;

        //カタログ商品の合計金額に表示する税区分を取得
        const taxTypeTotalCatalog = $('#total_tax_type_catalog').val();
        if (dispTaxType == "") {
            if (taxTypeTotalCatalog) {
                dispTaxType = taxTypeTotalCatalog;
            }
        }
    }

    if (totalPrice == 0) {
        $('.confirm__total-price-value').text("－");
        $('.confirm__notice').hide();
    } else {
        totalPrice = common.separatePriceWithRoundDown(totalPrice);
        $('.confirm__total-price-value').text(totalPrice + " 円 " + common.getTaxTypeText(dispTaxType));
        $('.confirm__notice').show();
    }
}

/**
 * カタログ商品の合計金額を取得
 */
function getTotalPriceCatalog() {

    let totalPriceCatalog = 0;

    //行ごとに「単価×数量」を算出
    for (var i = 0; i < $('.catalog-order-item').length; i++) {

        const target = $('.catalog-order-item').eq(i);

        //納品予定日
        const targetNouhinbi = target.find(".confirm__data--select-nouhinbi");
        const nouhinbi = targetNouhinbi.val();
        //発注数量
        const targetOrderNum = target.find(".confirm__data--text-order-num");
        const orderNum = targetOrderNum.val();
        //単価切り替え日
        const switchDate = target.data('price_switch_day');
        // ケース/バラ区分
        const targetUnitCode = target.find(".confirm__unit--select-unit");
        const unit_code = targetUnitCode.val();
        // ケース/バラ選択区分
        const caseSeparately = target.data('case_separately');
        // 前単価/後単価
        let priceBefore = 0;
        let priceAfter = 0;
        if (unit_code == 2) {
            // ケース
            priceBefore = target.data('case_price_before');
            priceAfter = target.data('case_price_after');
        } else if (unit_code == 1) {
            // バラ
            priceBefore = target.data('separately_price_before');
            priceAfter = target.data('separately_price_after');
        } else {
            if (caseSeparately == 2) {
                // ケースのみ
                priceBefore = target.data('case_price_before');
                priceAfter = target.data('case_price_after');
            } else {
                // バラのみ
                priceBefore = target.data('separately_price_before');
                priceAfter = target.data('separately_price_after');
            }
        }
        //税区分
        const taxType = target.data('tax_type');
        //単価を再取得
        let price = 0;
        if (nouhinbi >= switchDate) {
            price = priceAfter;
        } else {
            price = priceBefore;
        }
        //税区分（テキスト）
        const taxText = common.getTaxTypeText(taxType);
        //単価を再表示
        target.find(".confirm__data--price").text(common.separatePriceWithRoundDown(price) + " 円 " + taxText);
        //行の合計金額を取得
        totalPriceCatalog += Math.floor(price * orderNum);
    }
    return totalPriceCatalog;
}

// 納品取引先を選択
$('body').on('change', '.confirm__data--catalog-customer-code', function () {
    saveCatalogItem($(this));
})

// 納品予定日選択
$('body').on('change', '.confirm__data--select-nouhinbi', function () {
    saveCatalogItem($(this));
})

/**
 * カタログ商品のケース/バラリストを設定
 */
function setUnitList() {
    $('.catalog-order-item').each(function () {
        $(this).find('.confirm__unit--select-unit').append($('<option>').html('バラ').val('1'));
        $(this).find('.confirm__unit--select-unit').append($('<option>').html('ケース').val('2'));

        const unit_code = $(this).data('unit_code');
        $(this).find(".confirm__unit--select-unit").val(unit_code);

        // 予期しない値がセットされていた場合、バラに設定する。
        if ($(this).find(".confirm__unit--select-unit").val() == null){
            $(this).find(".confirm__unit--select-unit").val("1");
        }
    })
}

function displayNonData() {
    // 商品なし描画を表示
    $('.confirm__data--nothing').show();
}

// 削除ボタン押下処理
$(document).on('click', '.delete-catalog-item', function () {

    // 確認ダイアログ表示
    if (!confirm('選択した項目が削除されますがよろしいですか？')) {
        return false;
    }
    const objProduct = $(this).parents('.catalog-order-item');

    const productCode = objProduct.data('productscode');
    const customerCode = objProduct.data('customercode');
    const customerEdaban = objProduct.data('customeredaban');

    //指定したオブジェクトをローカルストレージから削除する
    order.deleteCatalogItem(productCode, customerCode, customerEdaban);

    //画面リロード
    window.location.reload(false);
})

$('body').on('change', '.confirm__data--text-order-num', function () {
    const value = $(this).val();
    const isDecimalPoint = $(this).parents('.catalog-order-item').data('decimalpoint');

    // 数量の判定
    const result = common.isCorrectOrderValue(value, 1, Number(isDecimalPoint) === 1)
    // 異常値の場合、アラートを出して何もしない
    if (!result) {
        $(this).focus().select();
        return false;
    }

    let value2 = 0;

    if (value > 0) {
        if (Number(isDecimalPoint) === 1) {
            // 小数点第2位まで表示
            value2 = common.calcFloor(value, 2);
        } else {
            // 整数で表示
            value2 = Math.floor(value);
        }
    }
    $(this).val(value2);

    // console.log(value + ' -> ' + value2)

    saveCatalogItem($(this));

})

/**
 * 単位変更時の処理
 */
$('body').on('change', '.confirm__unit--select-unit', function () {
    saveCatalogItem($(this));
})

/**
 * 「納品予定日」・「数量」・「単位」変更時の処理
 */
function saveCatalogItem(obj) {
    //データ取得
    const objProduct = obj.parents('.catalog-order-item');
    const productCode = objProduct.data('productscode');
    const customerCode = objProduct.find('.confirm__data--catalog-customer-code').val();
    const customerEdaban = objProduct.data('customeredaban');
    const productName = objProduct.data('productsname');
    const num = objProduct.find(".confirm__data--text-order-num").val();
    const nouhinbi = objProduct.find(".confirm__data--select-nouhinbi").val();
    const bikou = objProduct.data('bikou');
    const unit_code = objProduct.find(".confirm__unit--select-unit").val();
    let unit_name = '';
    if (unit_code == 1) {
        unit_name = objProduct.data('unit_name');
    } else {
        unit_name = 'ケース';
    }

    // ローカルストレージ更新
    order.setCatalogItem(
        productCode,
        customerCode,
        customerEdaban,
        productName,
        num,
        nouhinbi,
        bikou,
        unit_code,
        unit_name
    )

    // ケース/バラリストの再表示
    objProduct.find(".confirm__unit--select-unit").val(unit_code);

    //単価の再表示・合計金額の再取得
    setTotalPriceVal();
}

/**
 * ローカルストレージの年月日時分秒をチェックする
 *
 */
 function checkOrderTime() {
    // ローカルストレージの年月日時分秒を取得する
    let orderTime = order.getOrdertimeFromLS();

    //値が設定されていない(前回の発注が正常終了している)場合、後続の処理を行う
    if (!orderTime || orderTime == "") {
        setOrderConfirmArea();
        return false;
    }

    //値が設定されている(前回の発注が異常終了している)場合、発注情報管理管理テーブルの検索を行う
    let data = {
        'orderTime': orderTime,
    };
    let ajaxUrl = $('[name="ajaxUrlCheckOrder"]').val();

    // CSRFトークンを設定
    $.ajaxSetup({
        headers: {'X-CSRF-TOKEN': $('input[name=_csrfToken]').val()}
    });

    $.ajax({
        url: ajaxUrl,
        type: 'POST',
        data: data,
        timeout: 30000,
    }).done(function (data) {
        if (data.status === 'notExist') {
            //ローカルストレージから年月日時分秒を削除する
            order.clearOrdertimeFromLS();

        } else if (data.status === 'Exist') {
            //発注完了のステータスとなるよう、ローカルストレージを更新する
            order.clearOrdertimeFromLS();
            order.clearDataFromLS();
            order.clearBikouFromLS();
            //カタログ機能利用がONの場合、ローカルストレージよりカタログ商品発注データを削除する
            const useCatalog = $('[name="useCatalog"]').val();
            if (useCatalog) {
                order.clearCatalogDataFromLS();
                order.clearFromCatalogItemsFromLS();
            }

        } else if (data.status === 'api_error') {
            // 認証エラー
            auth.forceLogout(message.E003);
            return false;
        }

    }).fail(function (data) {
        let msg = "エラー";

        if (data.statusText === "timeout") msg = message.E001;
        if (data.statusText === "Internal Server Error") msg = message.E002;

        alert(msg);
    }).always(function (data) {
        setOrderConfirmArea();
        alert('前回の発注が正常に終了されなかった為、この商品は既に発注されている可能性があります。\n発注履歴画面へお進み頂き、発注内容をご確認ください。');
    });
}

/**
 * 発注内容エリア（商品・問い合わせ）の描画を行う
 */
 function setOrderConfirmArea() {

    // セッションストレージに保存された内容をリストに書き出す
    setInqueryList();

    // カタログ商品の発注データを表示
    createCatalogListData();

    // 発注データを表示
    createListData();
}