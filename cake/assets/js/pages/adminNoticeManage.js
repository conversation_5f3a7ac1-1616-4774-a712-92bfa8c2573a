/////////////////////////////////////////////////////////
// 管理画面 お知らせ登録用の処理

$(function () {
    //チェックボックスの同期
        //親=発注画面に表示（id=checkboxViewUser、チェック状態=propViewUser）
        //子1=ログイン画面に表示（id=checkboxLogin、チェック状態=propLogin）
        //子2=ポップアップ表示（id=checkboxPopup、チェック状態=propPopup）
        //子のいずれかがオンなら親をオン
    //子1のチェックボックスのチェック状態が変化した時の処理
    $(document).on("change", "#checkboxLogin", function() {
        var obj = getPropCheckBox();
        //子1がオンかつ親がオフ
        if (obj['propLogin'] == true && obj['propViewUser'] == false) {
            //親をオン
            $("#checkboxViewUser").prop("checked", true);
        }
        return;
    });

    //子2のチェックボックスのチェック状態が変化した時の処理
    $(document).on("change", "#checkboxPopup", function() {
        var obj = getPropCheckBox();
        //子2がオンかつ親がオフ
        if (obj['propPopup'] == true && obj['propViewUser'] == false) {
            //親をオン
            $("#checkboxViewUser").prop("checked", true);
        }
        return;
    });

    //同期させるチェックボックスのチェック状態を取得する処理
    function getPropCheckBox() {
        var obj = {};
        obj['propViewUser'] = $('#checkboxViewUser').prop('checked');
        obj['propLogin'] = $('#checkboxLogin').prop('checked');
        obj['propPopup'] = $('#checkboxPopup').prop('checked');
        return obj;
    }
});