///////////////////////////////////////////////
// 発注画面 モバイルヘッダーの色設定（actnet以外）
@import '../config/variables';

.header {
  margin-bottom: 10px;
}

// ヘッダ内のメニューエリア
.header-menu {
  background: $body-bg;
  border-bottom: 1px solid $gray-200;

  &__title {
    color: $white;
  }

  &__button-area {
    //円ボタン
    button {
      background: white;
      border: 1px solid white;
      color: $gray-600;
    }
  }

  &__menu-button {
    background: white;
    border: 1px solid $main-color;
  }


  &__items {
    background: $gray-100;

    .nav-item {
      .nav-link {
        color: $gray-900
      }
    }
  }
}

// ヘッダ内のタイトルエリア
.header-title {
  display: none;
}