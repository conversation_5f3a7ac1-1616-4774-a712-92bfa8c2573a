///////////////////////////////////////////////
// 発注画面 ヘッダー（モバイル）
@import '../config/variables';
@import '../module/media_query';
@import '../module/mixins';

body {
  @include select-none();
}

// ヘッダ内のメニューエリア
.header-menu {
  // ヘッダの固定化設定
  //@include sticky-header();
  //@include clearfix();

  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;

  &__title-area {
    padding: 8px;

    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-wrap: wrap;

    width: 100%;
  }

  &__title {
    color: $main-color;

    font-size: 2.6rem;
    font-style: normal;
    font-weight: 700;

    font-family: cooper-black-std, serif;
    letter-spacing: -0.08em;

    margin-right: auto;
  }

  &__button-area {
    float: right;
    text-align: right;
    position: relative;

    margin-right: 2px;

    //円ボタン
    &__radius-button {
      width: 30px;
      height: 30px;
      border-radius: 15px;
      line-height: 1.0;
      padding: 0px;
      text-align: center;

      background: none;
      border: 1px solid gray;
      color: gray;

      margin-right: 10px;
      font-size: 1.6rem;
    }

    &__non-radius-button {
      width: 30px;
      height: 30px;
      line-height: 1.0;
      padding: 0px;
      text-align: center;

      background: none;
      border: none;

      margin-right: 10px;
    }
  }

  &__button-alert {
    @extend .badge;
    @extend .badge-pill;
    @extend .badge-danger;

    position: absolute;
    left: 18px;
    top: -2px;

    font-size: 1rem;
    padding-left: 0.3em;
    padding-right: 0.3em;
  }

  &__menu-button {
    float: right;
    font-size: 1.4rem;
    border: 1px solid gray;
  }

  &__items {
    text-align: center;
    font-size: 1.6rem;

    .nav-item {
      padding: 5px 0;
    }
  }
}

// 固定ヘッダでbodyが隠れないように設定
.header-padding {
  padding-top: 50px;
}

.login-header {
  margin-top: 15px;

  &__title {
    text-align: center;
    font-size: 1.6rem;
    font-style: normal;
    font-weight: 700;
    color: $gray-100;
    padding: 2px;
    margin-top: 10px;

    background: $main-color;
  }
}

// ヘッダ内のタイトルエリア
.header-title {
  font-size: 2.0rem;
  background: $main-color;
  text-align: center;
  color: white;

  // アプリタイトル
  &__title {
    color: $main-color;
    margin: 0 auto;

    font-size: 3.4rem;
    font-style: normal;
    font-weight: 700;

    font-family: cooper-black-std, serif;
    letter-spacing: -0.08em;
  }

  // ロゴエリア
  &__logo {
    margin: 0 auto;

    &--text {
      color: $gray-900;
    }
  }

}
