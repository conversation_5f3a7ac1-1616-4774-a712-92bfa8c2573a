///////////////////////////////////////////////
// 共通スタイル
@import '../config/variables';
@import '../module/media_query';
@import '../module/mixins';
@import '../module/date_picker';

html {
  font-size: 62.5%; // 基準フォントサイズの指定-> 10px;
}

body {
  width: 100%;
}

.clearfix {
  @include clearfix();
}

// 共通部品
.main {

  // 戻るボタン
  &__back-button {
    @include button-gray(14);
    margin: 15px 10px;
  }

  // 登録ボタン
  &__submit-button {
    @include button-red(14);
    margin: 15px 10px;
  }
}

// エラー画面
.error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;

  // エラーメッセージの表示
  .message.error {
    color: $red;
    font-size: 1.9rem;
    margin: 40px auto 20px;
    text-align: center;
  }

  // 戻るボタン
  &__back-button {
    @include button-gray();

    width: 20rem;
    margin: 20px auto 0;
  }
}

// エラーメッセージの表示
.message.error {
  color: $red;
  font-size: 1.4rem;
  font-weight: bold;
  text-align: center;
  margin: 10px 0;
}

// 完了画面
.success {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
}

// 完了メッセージの表示
.message.success {
  color: $red;
  font-size: 1.4rem;
  font-weight: bold;
  text-align: center;
  margin: 10px 0;
}

// 共通機能
.common {

  // ローディングイメージ
  &-loading {
    display: flex;
    justify-content: center;
    align-items: center;

    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background-color: #fff;
    opacity: 0.8;
    z-index: 100;

    &__spinner {
      display: flex;
      flex-direction: column;
      justify-content: center;

      .spinner-border {
        width: 5rem;
        height: 5rem;
        margin: auto;
      }

      &--message {
        font-size: 1.6rem;
        margin-top: 10px;
        text-align: center;
      }
    }
  }
}

.modal-content {
  .common-loading {
    position: absolute;
  }
}

table {
  td {
    &.left {
      text-align: left;
    }
  }
}

// 商品区分名称のバッチ
@mixin badge_gradation($textColor, $mainColor, $subColor) {
  color: $textColor;
  background-color: $mainColor; /* ～IE9（グラデーション無し） */
  //background: linear-gradient(to bottom, $subColor, $mainColor); /* IE10 */
  //background: -webkit-gradient(linear, center top, center bottom, from($subColor), to($mainColor));
}

.product__badge {
  &--0 {
    @include badge_gradation($gray-100, #00cc00, #66ff66);
  }

  &--1 {
    @include badge_gradation($gray-100, #F15A24, #FBB03B);
  }

  &--2 {
    @include badge_gradation($gray-100, #0071BC, #29ABE2);
  }

  &--3 {
    @include badge_gradation($gray-100, #E229AB, #EA69C4);
  }

  &--4 {
    @include badge_gradation($gray-100, #710FFB, #9B57FC);
  }

  &--5 {
    @include badge_gradation($gray-100, #6E4C0A, #998153);
  }

  &--6 {
    @include badge_gradation($gray-100, #326600, #6F934C);
  }

  &--7 {
    @include badge_gradation($gray-100, #C40000, #D54C4C);
  }

  &--8 {
    @include badge_gradation($gray-100, #3442C2, #707AD4);
  }

  &--9 {
    @include badge_gradation($gray-700, #EEB500, #F3CB4C);
  }
}

// IME日本語入力を有効化する（IEのみ）
.enable-ime {
  -ms-ime-mode: active;
}

// IME日本語入力を無効化する（IEのみ）
.disable-ime {
  -ms-ime-mode: disabled;
}