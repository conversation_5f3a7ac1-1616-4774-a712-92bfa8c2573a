///////////////////////////////////////////////
// 管理画面 検索エリア
@import '../config/variables';
@import '../module/media_query';
@import '../module/mixins';

// 検索エリア全体
.search {
  width: 100%;
  max-width: 860px;
  margin: 0px auto;
  border: 1px solid $gray-600;
  display: flex;
  flex-wrap: wrap;
  padding: 15px;
  background: $gray-100;
  font-size: 1.4rem;

  // 検索エリア全体のタイトル
  &__title {
    width: 100%;
    text-align: center;
    color: $main-color;
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 1.6rem;
  }

  // 検索条件の1項目
  &__content {
    display: flex;
    margin-bottom: 10px;
    min-width: 50%;
    width: 100%;

    @include mq('md') {
      width: 50%;
    }

    // 1コンテンツで改行させる場合に指定
    &--one-line {
      width: 100%;
    }

    // 検索/クリアボタンエリア
    &--button-area {
      margin-top: 10px;
      justify-content: center;
    }

    // 各項目の項目名
    &-title {
      font-weight: bold;
      min-width: 120px;

      @include set-vertical-center();
    }

    // 各項目の入力エリア
    &-data {
      @include set-vertical-center();
      margin: 0 15px;
      width: 100%;
      padding-right: 10px;

      label {
        margin-right: 10px;
      }

      // 数値の場合（IMEを半角に）
      &--code {
        width: 100%;
        @include text-area-style();
      }

      // 全角OKの部分（IMEを全角に）
      &--text {
        width: 100%;
        @include text-area-style();
      }

      // datepickerを設定するエリア
      &--calendar-area {
        padding: 0;
        display: flex;
      }

      // datepickerを1つのみ配置
      &--calendar-single {
        width: 100%;
        @include text-area-style();
      }

      // datepickerを from ～ to 形式の配置
      &--calendar-dual {
        width: 44%;
        @include text-area-style();
      }

      // ～ 記号
      &--bar {
        width: 12%;
        text-align: center;
      }

      // コード選択ダイアログを開くボタン
      &--button {
        width: 36px;
        font-size: 1.3rem;
      }
    }
  }

  // 検索/クリアボタンの設定
  &__button {
    &--submit {
      @include button-blue();
      margin: 0 10px;
      
      &-files{
        @extend .search__button--submit;
      }
    }
      
    &--clear {
      @include button-gray();
      margin: 0 10px;

    }
  }
}