///////////////////////////////////////////////
// 発注画面 商品欄 （グリッドビュー）
@import '../config/variables';
@import '../module/media_query';
@import '../module/mixins';


@mixin products__text() {

}

@mixin products__value() {

}

$grid-font-size: 1.1rem;
$circle-width: 3.5rem;
$circle-margin: 5px;


// グリッドレイアウト
.products {
  margin: 1rem 0 2rem;

  &-grid {
    @extend .card; // bootstrap
    font-size: $grid-font-size;
    float: left;

    // スマホ画面は2列
    width: 48%;
    margin: 1%;
    padding: 5px;

    //タブレット横画面以上は3列
    @include mq(md) {
      width: 30.6%;
      margin: 1%;
      padding: 10px 10px;
    }

    //タブレット横画面以上は4列
    @include mq(lg) {
      width: 23%;
      margin: 1%;
      padding: 5px 10px;
    }

    // グリッド内の各行
    &__col {
      display: flex;
      flex-wrap: wrap;
      width: 100%;

      &--picture {
        position: relative;
      }
    }

    // 商品名
    &__name {
      font-size: 1.3rem;
      font-weight: bold;
      line-height: 1.3;

      padding: 5px 0;
      height: 4rem;
    }

    // 商品コード
    &__product-code {

      &-sub {
      }
    }

    // お気に入りマーク
    &__star {
      position: absolute;
      top: 4px;
      right: 6px;

      font-weight: bold;
      color: $yellow;
      font-size: 2rem;
    }

    // ゴミ箱マーク
    &__trash {
      position: absolute;
      bottom: -4px;
      right: 6px;

      font-weight: bold;
      color: $secondary;
      font-size: 2rem;
    }

    // 発注区分
    &__order-type {
      @extend .badge;
      width: 4rem;
      font-size: 1rem;
      height: 1.6rem;
      margin-left: auto;
      letter-spacing: 0.5rem;
      padding-left: 0.7rem;
    }

    // 画像エリア
    &__picture {
      width: 60%;
      margin: 0 auto;
      background: $white;
      position: relative;

      &:before {
        content: "";
        display: block;
        padding-top: 100%;
      }

      &--area {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;

        img {
          max-width: 100%;
          max-height: 100%;
          //width: 100%;
          height: 100%;
          margin: auto;
          object-fit: contain;
        }
      }
    }

    // 分類名
    &__category {
      margin-left: 1.1rem;
    }

    &__param {
      display: flex;
      justify-content: normal;
      align-items: center;
      margin-left: 1.1rem;

      &--text {
      }

      &--value {
      }
    }

    &__nouhinbi {
      display: flex;
      justify-content: normal;
      margin: 3px 0 0;
      flex-wrap: wrap;
      width: 100%;
      align-items: center;

      &-text {
        margin-bottom: 0;
        width: $grid-font-size * 3;
      }

      &-value {
        border: 1px solid $gray-600;
        height: 3rem;
        font-size: 1.6rem;
        margin: 0 5px;
        flex: 1;
        width: calc(100% - #{$grid-font-size * 3});

        &:empty {
          background: pink;
        }
      }
    }

    &__unit-list {
      display: flex;
      justify-content: normal;
      margin: 3px 0 0;
      flex-wrap: wrap;
      width: 100%;
      align-items: center;

      &-text {
        margin-bottom: 0;
        width: $grid-font-size * 3;
      }

      &-value {
        border: 1px solid $gray-600;
        height: 3rem;
        font-size: 1.6rem;
        margin: 0 5px;
        flex: 1;
        width: calc(100% - #{$grid-font-size * 3});

        &:empty {
          background: pink;
        }
      }
    }

    &__order-num {
      @extend .products-grid__nouhinbi;
      flex-wrap: wrap;

      @include mq('md') {
        flex-wrap: initial;
      }

      &-text {
        margin-bottom: 0;
        width: $grid-font-size * 3;
      }

      &-value {
        display: flex;
        flex: 1;
        align-items: center;
        width: calc(100% - #{$grid-font-size * 3});
      }

      button {
        @extend .rounded-circle;
        border: 1px solid $gray-600;
        padding: 0;
        height: $circle-width;
        width: $circle-width;
        margin: 0 $circle-margin;
      }

      &-button {
        &--plus {
          color: $white;
          background: $blue;

          &:after {
            content: "\f067";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            font-size: 2rem;
          }
        }

        &--minus {
          color: $white;
          background: $orange;

          &:after {
            content: "\f068";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            font-size: 2rem;
          }
        }
      }

      &-input {
        //width: $grid-font-size * 5;
        width: 100%;
        height: 3.5rem;
        max-width: calc(100% - #{$circle-width * 2} - #{$circle-margin * 4});
        font-size: 2rem;
        line-height: 1.0;
        border: 1px solid $gray-600;
        flex: 1;
        text-align: right;
        padding: 0 3px;

        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }

        -moz-appearance: textfield;
      }
    }

    &__bikou {
      @extend .products-grid__nouhinbi;

      &-text {
        @include products__text;
        width: $grid-font-size * 3;
      }

      &-input {
        line-height: 1.0;
        flex: 1;
        width: calc(100% - #{$grid-font-size * 3});
        border: 1px solid $gray-600;
        height: 3.6rem;
        font-size: 1.3rem;
        margin: 0 5px;
      }
    }

    &__unit {
      @extend .products-grid__nouhinbi;

      &-text {
        @include products__text;
        width: $grid-font-size * 3;
      }

      &-input {
        line-height: 1.0;
        flex: 1;
        width: calc(100% - #{$grid-font-size * 3});
        border: 1px solid $gray-600;
        height: 3.6rem;
        font-size: 1.3rem;
        margin: 0 5px;
      }
    }

    &__trash {
      &--button {
        @extend .content__edit-button;
        margin: 15px auto 10px;
        width: 80%;
        padding: 10px;
      }
    }
  }
}
