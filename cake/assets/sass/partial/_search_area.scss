///////////////////////////////////////////////
// 発注画面 検索欄
@import '../config/variables';
@import '../module/media_query';
@import '../module/mixins';
@import "../module/category_select";

@mixin search_bar_title() {
  margin-bottom: -3px;
}

.searchbar {
  width: 100%;
  padding: 5px 10px;
  display: flex;
  font-size: 1.2rem;
  flex-wrap: wrap;

  justify-content: center;
  align-items: flex-end;

  @include mq(md) {
    justify-content: flex-start;
  }

  //カテゴリ選択欄
  &-category {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    padding-right: 10px;

    &__title {
      @include search_bar_title();
    }

    &__select {
      select {
        height: 30px;
        border-radius: 15px;

        option {
          width: 70%;
        }
      }
    }

    &__button {
      @extend .page-link; // bootstrap
      @extend .text-dark; // bootstrap
      @extend .d-inline-block; // bootstrap

      height: 30px;
      border-radius: 15px;
      width: 150px;
      font-size: 1.1rem;
    }
  }

  //テキスト検索欄
  &-input {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    flex: 1;

    &__title {
      @include search_bar_title();
    }

    &__inputarea {
    }

    &__textarea {
      width: 100%;
      height: 30px;
      float: left;
      padding: 0 30px 0 10px;
      left: 5px;
      top: 0;
      border-radius: 15px;
      outline: 0;
    }

    &__button {
      height: 30px;
      width: 30px;
      float: left;
      margin-left: -30px;
      top: 0;
      background: #7fbfff;
      color: #fff;
      border: none;
      border-radius: 0 15px 15px 0;
    }
  }

  &-view {
    display: flex;

    padding-left: 10px;
    width: 60px;

    &__title {
      @include search_bar_title();
    }

    &__button {
      background: transparent;
      border: 1px solid rgba(0, 0, 0, 0.1);;
      padding: 0 5px;
      color: $gray-600;
      width: 100%;
      font-size: 1.8rem;
    }


    &__dropdown {
      &-item {
        font-size: 1.3rem;
        margin-bottom: 3px;
        padding: 0 10px;

        i {
          margin-right: 10px;
          width: 13px;
        }
      }

      &-item:last-child {
        margin-bottom: 0;
      }
    }
  }

  &-nouhinbi, &-limit, &-pagination {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    align-items: center;

    &__block {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }

    &__title {
    }
  }

  &-nouhinbi {

    &__select {
      margin-left: 10px;
      padding: 4px;
    }

    &__button {
      @extend .btn; //bootstrap
      @extend .btn-primary; //bootstrap
      font-size: 1.2rem;
      margin: 0 10px;
      padding: 4px;
    }
  }

  &-limit {
    margin-left: 0;
    margin-right: 20px;

    &__select {
      margin-left: 10px;
      padding: 4px;
    }

  }

  &-pagination {
    margin-left: 0;
    margin-top: 10px;

    @include mq('md') {
      margin-left: auto;
    }

    &__button {
      @extend .btn; //bootstrap
      @extend .btn-primary; //bootstrap
      font-size: 1.2rem;
      margin: 0 10px;
      padding: 4px 0px;
      width: 4rem;

      &:disabled {
        //background-color: $gray-200;
      }
    }

    &__next {
      @extend .searchbar-pagination__button;
    }

    &__prev {
      @extend .searchbar-pagination__button;
    }

    &__disable {

    }

    &__select {
      padding: 4px;
    }

    &__separator {
      margin-left: 10px;
    }

    &__total-page {
      margin-left: 10px;
    }
  }
}

