///////////////////////////////////////////////
// 発注画面 商品欄 （グリッドビュー）
@import '../config/variables';
@import '../module/media_query';
@import '../module/mixins';

$font-size: 1.1rem;

// グリッドレイアウト
.products {
  font-size: 1.0rem;

  &-grid {
    @extend .card; // bootstrap
    font-size: $font-size;

    // スマホ画面は2列
    width: 48%;
    margin: 1%;

    //タブレット横画面以上は3列
    @include mq(md) {
      width: 30.6%;
      margin: 1%;
      padding: 10px 10px;
    }

    //タブレット横画面以上は4列
    @include mq(lg) {
      width: 23%;
      margin: 1%;
      padding: 5px 10px;
    }

    // グリッド内の各行
    &__col {
      justify-content: space-between;
      width: 100%;
      padding: 7px 10px;
    }

    // 商品名
    &__name {
      font-size: $font-size;
      line-height: 1.3;

      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    // 部門名
    &__department-name {
      font-size: $font-size;
      line-height: 1.3;

      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    // 商品コード
    &__text {
      font-size: $font-size;
    }

    // 数量
    &__num {
      width: 6rem;
    }

    // 画像エリア
    &__picture {
      position : relative;
      width: 100%;
      height: auto;
      background: $white;

      &:before {
        content: "";
        display: block;
        padding-top: 100%;
      }

      &--area {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;

        img {
          max-width: 100%;
          max-height: 100%;
          width: 100%;
          height: 100%;
          margin: auto;
          object-fit: contain;
        }
      }
    }

    &__order-type {
      @extend .badge;
      width: 4.2rem;
      font-size: 1.3rem;
      height: 2rem;
      margin: 5px;
      letter-spacing: 0.5rem;

      position: absolute;
      top: 0;
      left: 0;
    }

    &__star {
      @extend .badge;
      @extend .badge-pill;
      @extend .badge-light;

      position: absolute;
      top: 0;
      right: 0;

      margin: 3px;

      color: $yellow;
      background: $white;
      font-size: 1.3rem;
      padding: 0.7rem;
    }

    &__checked {
      @extend .badge;
      @extend .badge-pill;
      @extend .badge-danger;

      background: $red;
      position: absolute;
      right: 0;
      bottom: 0px;
      margin: 3px;
      font-size: 1.6rem;
    }

    &__trash {
      &--button {
        @extend .content__edit-button;
        margin: 0 auto 10px;
        width: 80%;
        padding: 10px;
        font-size: 1rem;
      }
    }
  }
}
