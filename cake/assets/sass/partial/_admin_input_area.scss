///////////////////////////////////////////////
// 管理画面 入力エリア
@import '../config/variables';
@import '../module/media_query';
@import '../module/mixins';

$input-width: $breakpoints-md;
// 入力エリア全体
.input {
  //width: 100%;
  margin: 0px auto;
  display: flex;
  flex-wrap: wrap;
  //background: $gray-100;
  font-size: 1.4rem;

  // イレギュラー対応
  &.checkbox {
    background: none;
    margin: 0;
  }

  // 入力エリアの横幅（最大幅を指定）
  &__row {
    width: 100%;
    max-width: $input-width;
    margin: 10px auto;
    display: flex;
    flex-flow: wrap;

    // 入力エリアの横幅（最大幅無し）
    &--full {
      width: 98%;
      margin: 10px auto;
      display: flex;
      flex-flow: wrap;
    }
  }

  // 入力欄の1項目
  &__content {
    display: flex;
    margin-top: -1px; // セルを重ねるときにborderが太くなるのを打ち消す
    width: 100%;

    // 2項目横並びで表示するときの設定
    &--two-column {
      @include mq('lg') {
        width: 50%;
        max-width: ($input-width / 2);
      }
    }

    & &--disabled{
        background-color: $gray-400;
    }

    // 各項目の項目名
    &-title {
      font-weight: bold;
      min-width: 190px;
      border: solid 1px $gray-300;
      padding: 10px 20px;
      background: $gray-400;
      color: $gray-800;

      @include set-vertical-center();

      &--full-width {
        width: 100%;
      }

      &--light-red {
        background: #fb8283;
      }
    }

    // 各項目の入力エリア
    &-data {
      @include set-vertical-center();
      //margin: 0 15px;
      width: 100%;
      padding: 8px 10px;
      border: solid 1px $gray-300;
      margin-left: -1px; // borderが太くなるのを打ち消す
      flex-flow: wrap;

      label {
        margin-right: 10px;
      }

      // 数値の場合（IMEを半角に）
      &--code {
        width: 163px;
        @include text-area-style();
        margin-right: 10px;
      }

      // 全角OKの部分（IMEを全角に）
      &--text {
        width: 100%;
        @include text-area-style();
        margin: 2px 0;
      }

      // datepickerを設定するエリア
      &--calendar-area {
        padding: 0;
        display: flex;
      }

      // datepickerを1つのみ配置
      &--calendar-single {
        width: 100%;
        @include text-area-style();
      }

      // コード選択ダイアログを開くボタン
      &--button {
        width: 36px;
        font-size: 1.3rem;
      }

      // 縦並びに設定
      &--column {
        flex-flow: column;
      }

      &--full {
        width: 100%;
      }

      &--subline {
        display: flex;
      }
    }

    //注意書き用の行
    &-attention {
      width: 100%;
      font-weight: bold;
      border: solid 1px $gray-300;
      padding: 10px 20px;
      background: #fb8283;
      color: $gray-800;
    }
  }

  // 企業情報更新画面：商品区分名称
  &__order-type {
    margin: 5px 10px 5px 0;

    &--block {
      width: 7rem;
    }

    &--text {
      width: 5rem;
      padding: 0;
      line-height: 1rem;
      margin-bottom: 5px;
    }
  }

  // 商品区分名称用のバッジ表記
  &__badge {
    padding: 3px 8px;
    margin: 0 auto;
    font-size: 1.1rem;
    text-align: center;
    width: 45px;
    height: 2.3rem;
  }

  // 各種別を分割するセパレーター
  &__separator {
    width: 100%;
    margin: 10px 0;
    padding: 5px;

    font-size: 1.6rem;
    font-weight: bold;
    text-align: center;
    color: $main-color;

    border-top: 2px solid $main-color;
    border-bottom: 2px solid $main-color;
  }

  // ボタンの設定
  &__button {
    @include button-red();
    margin: 0 10px;

    // ボタンの表示エリア
    &-area {
      margin: 0 auto;
      justify-content: center;
    }

    &--submit {
      @include button-blue();
      margin: 0 10px;
    }

    // 追加ボタン
    &--add {
      @include button-red();
      margin: 0 10px;
      width: auto;

      //ボタン内アイコンの設定
      i {
        margin-left: -10px;
        margin-right: 5px;
      }
    }

    &--del {
      @include button-blue();
      margin: 0 10px;
      width: auto;

      //ボタン内アイコンの設定
      i {
        margin-left: -10px;
        margin-right: 5px;
      }
    }

    &--clear {
      @include button-gray();
      margin: 0 10px;

    }
  }

  // 表形式の入力エリア
  &__table {
    &--full {
      overflow-x: auto;
      overflow-y: hidden;
    }

    $button-size: 12;
    // 表の中のボタン
    &-button {
      &--red {
        @include button-red($button-size);
        margin: 0;
        padding: 3px 15px;
        width: auto;
      }

      &--blue {
        @include button-blue($button-size);
        margin: 0;
        padding: 3px 15px;
        width: auto;
      }

      &--gray {
        @include button-gray($button-size);
        margin: 0;
        padding: 3px 15px;
        width: auto;
      }

      &--action {
        &-red {
          @include button-red($button-size);
          margin: 0;
          padding: 3px 15px;
          width: auto;
        }
      }
    }

    &-badge {
      //@extend .badge;
      padding: 5px 10px;
      font-size: 1.3rem;

      &--red {
        @extend .input__table-badge;
        @extend .badge-danger;
      }

      &--gray {
        @extend .input__table-badge;
        @extend .badge-secondary;
      }

      &--yellow {
        @extend .input__table-badge;
        @extend .badge-warning;
      }

      &--blue {
        @extend .input__table-badge;
        @extend .badge-info;
      }

      &--pink {
        @extend .input__table-badge;
        background-color: $pink;
        color: $white;
      }
    }
  }

  &__notice {
    color: $gray-600;
  }

  &__error {
    color: $red;
    font-weight: bold;
    text-align: center;
  }

  // 表の詳細
  table.input-table {
    @include table-template();

    thead {
      background: $gray-900;
      color: $gray-100;
    }

    th {
      font-weight: normal;
      text-align: center;
    }

    td {
      text-align: center;
    }
  }

  &__alert{
    background: $pink;
  }
}

// 必須の表記
.required {
  margin-left: 10px;
  color: $red;

  &:after {
    content: '(必須)';
  }
}
