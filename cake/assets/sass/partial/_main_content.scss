///////////////////////////////////////////////
// 発注画面の基本的な設定
@import '../config/variables';
@import '../module/media_query';
@import '../module/mixins';
@import '../module/date_picker';

// 発注画面の共通部品
.content {
  // ページネーションのスタイル
  &__pagination {
    text-align: center;
    justify-content: center;
    display: flex;
    width: 100%;

    .page-item {
      margin: 0 3px;
    }

    .page-link {
      font-size: 1.4rem;
    }

    &--searchbar {

    }

    .pagination {
      margin-bottom: 0;
    }
  }

  // 発注画面のボタン用マージン
  &__margin {
    margin-bottom: 70px;
  }

  // 戻るボタン
  &__back-button {
    @extend .btn; //bootstrap
    @extend .btn-light; //bootstrap

    font-size: 1.2rem;
    padding: 3px 3rem;
    font-weight: bold;
    margin: 5px 10px;

    &:before {
      content: "\f053";
      font-family: "Font Awesome 5 Free";
      font-weight: 900;
      margin-left: -13px;
      margin-right: 13px;
    }
  }

  &__submit-button {
    @extend .btn; //bootstrap
    @extend .btn-danger; //bootstrap

    font-size: 1.4rem;
    font-weight: bold;
    margin: 5px 10px;


    &--center {
      @extend .content__submit-button;
      padding: 3px 2rem;

      @include mq(md) {
        padding: 3px 3rem;
      }
    }
  }


  &__edit-button {
    @extend .btn; //bootstrap
    @extend .btn-primary; //bootstrap

    font-size: 1.4rem;
    font-weight: bold;
    margin: 5px 10px;


    &--center {
      @extend .content__edit-button;
      padding: 3px 2rem;

      @include mq(md) {
        padding: 3px 3rem;
      }
    }
  }

  &__disable {
    display: none;
  }
}


// ログイン画面、パスワード登録画面などの入力エリア系
.input {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  flex-direction: column;

  width: 100%;
  padding: 10px 30px;

  font-size: 1.6rem;
  margin: 0 auto;
  @include mq('md') {
    width: 640px;
  }

  &__login-area {
    width: 100%;
    @include mq('md') {
      width: auto;
    }
  }

  // ログイン画面用に幅の短いエリアを設定する
  &__login {
    width: 100%;
    @include mq('md') {
      width: 400px;
    }
  }

  // 各入力項目ブロック
  &__content {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    align-items: flex-start;

    width: 100%;
    margin: 10px auto 0;

    @include mq('md') {
      flex-direction: row;
      align-items: center;
      margin: 15px auto 0;
    }

    // テキストを表示するエリア
    &--text {
      text-align: center;
      margin-bottom: 15px;
      width: 100%;

      @include mq('md') {
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 0;
      }

      .input__content--br {
        display: block;

        @include mq('md') {
          display: inline;
        }
      }
    }

    // スマホ表示の際にタイトル欄を非表示にする
    // (テキストボックスにはプレースホルダを設定)
    &--mobile {
      height: 40px;

      // 入力欄
      .input__content--data {
        input {
          &::placeholder {
            color: $gray-700;

            @include mq('md') {
              color: transparent;
            }
          }
        }
      }

      .input__content--title {
        display: none;

        @include mq('md') {
          display: flex;
        }
      }
    }

    // 入力欄
    &--data {
      width: 100%;
      padding: 0 1rem;
      flex: 1;

      @include mq('md') {
        flex: 1;
      }

      input {
        width: 100%;
        padding: 0.5rem 1.5rem;

        &::placeholder {
          color: transparent;
        }
      }
    }

    &--title {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      margin-bottom: 3px;

      @include mq('md') {
        justify-content: flex-end;
        margin-bottom: 0;
      }

      &.input__content--login {
        width: 12rem;
      }

      &.input__content--register {
        width: 20rem;
      }

      &.input__content--forgot {
        width: 18rem;
      }
    }
  }

  // 必須マーク
  .required {
    margin-left: 5px;
    color: $red;
  }

  // エラーメッセージの表示
  .message.error {
    color: $red;
    margin-top: 10px;
  }

  // お知らせエリア
  &__news {
    font-size: 1.3rem;
    width: 100%;
    height: 200px;
    border: 1px solid $gray-500;
    overflow-y: scroll;
    margin-top: 20px;
    padding: 10px 15px;

    &--head {
      text-align: center;
      font-weight: bold;
      color: $main-color;
      font-size: 1.6rem;
    }

    &--title {
      margin-top: 5px;
      padding-top: 10px;
      font-weight: bold;
      margin-bottom: 10px;

      a {
        color: inherit;
        text-decoration: underline;
      }
    }

    &--contents {
      padding-left: 1rem;
      margin-bottom: 10px;
      // display: flex;
      // flex-wrap: wrap;

      & .link {
        width: 100%;
        text-align: right;
      }

      a {
        width: 100%;
        text-align: right;
        margin-left: auto;
        text-decoration: underline;
      }
    }
  }

  &__link {
    @extend .input__content;
    margin-top: 30px;

    a {
      width: 100%;
      text-align: center;
      margin-top: 10px;
    }
  }

  // OK/クリアボタンの設定
  &__button {
    display: flex;
    justify-content: center;

    &--area {
      width: 100%;
      margin-top: 20px;

      @include mq('md') {
      }
    }

    // submitボタン
    &--submit {
      @extend .btn; //bootstrap
      @extend .btn-primary; //bootstrap
      font-size: 1.4rem;
      font-weight: bold;
      margin-left: 10px;
      margin-right: 10px;
      margin-top: 5px;
      padding: 10px 2rem;

      width: 100%;

      @include mq('md') {
        width: 230px;
        padding: 10px 3rem;
      }
    }

    // resetボタン
    &--clear {
      @extend .btn; //bootstrap
      @extend .btn-secondary; //bootstrap
      font-size: 1.4rem;
      font-weight: bold;
      margin-left: 10px;
      margin-right: 10px;
      margin-top: 10px;
      padding: 10px 2rem;

      width: 100%;

      @include mq('md') {
        width: 230px;
        padding: 10px 3rem;
      }
    }

    // completeボタン
    &--complete {
      @extend .btn; //bootstrap
      @extend .btn-primary; //bootstrap
      font-size: 1.4rem;
      font-weight: bold;
      margin: 0 10px;
      padding: 10px 2rem;

      @include mq('md') {
        padding: 10px 3rem;
      }
    }
    
    // 赤ボタン
    &--red {
      @extend .btn; //bootstrap
      @extend .btn-danger; //bootstrap
      font-size: 1.4rem;
      font-weight: bold;
      margin-left: 10px;
      margin-right: 10px;
      margin-top: 5px;
      padding: 10px 2rem;

      width: 100%;

      @include mq('md') {
        width: 230px;
        padding: 10px 3rem;
      }
    }

    // ボタンを１つのみ表示する場合
    &--single {
      font-size: 1.4rem;
      font-weight: bold;
      margin: 0 10px;
      padding: 10px 2rem;
      justify-content: center;

      width: auto;

      @include mq('md') {
        width: auto;
        padding: 10px 3rem;
      }
    }
  }
}

#orderlist-view {
  width: 100%;
}
