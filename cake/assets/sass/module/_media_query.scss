@import "../config/variables";

/////////////////////////
// メディアクエリの定義
// モバイルファーストで指定
$breakpoints: (//メディアクエリ指定無し：スマホ縦以上
        'xs': $breakpoints-xs,
        'sm': $breakpoints-sm, // スマホ横以上
        'md': $breakpoints-md, // タブレット縦以上
        'lg': $breakpoints-lg, // タブレット横以上
) !default;

/////////////////////////
// 設定用関数
// 使用例：@include mq('md'){....}
//  - メディアクエリ指定無し：スマホ縦以上
//  - mq(sm): スマホ横以上
//  - mq(md): タブレット縦以上
//  - mq(lg): タブレット横以上
@mixin mq($breakpoint: sm) {
  @media screen and (min-width: #{map-get($breakpoints, $breakpoint)}) {
    @content;
  }
}

/////////////////////////
// 設定用関数
// 使用例：@include mq('md'){....}
//  - メディアクエリ指定無し：タブレット横以上
//  - mq(lg): タブレット横以下
//  - mq(md): タブレット縦以下
//  - mq(sm): スマホ横以下
//  - mq(xs): スマホ縦以下
@mixin mq-max($breakpoint: sm) {
  @media screen and (max-width: #{map-get($breakpoints, $breakpoint) - 1}) {
    @content;
  }
}