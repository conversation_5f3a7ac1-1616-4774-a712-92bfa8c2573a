/////////////////////////
// ボタンテンプレートの定義

// 基本設定
// 引数の値に合わせてボタンサイズを調整
@mixin button-template($font-size:14) {
  font-size: $font-size/10+rem;
  padding: 7px 20px;
  width: $font-size+rem;
  font-weight: bold;
}

// 基本設定
// 引数の値に合わせてボタンサイズを調整
@mixin button-mini-template($font-size:14) {
  font-size: $font-size/10+rem;
  padding: 3px 2px;
  width: $font-size/2+rem;
  font-weight: bold;
}

// 赤色ボタン
@mixin button-red($font-size:14) {
  @extend .btn; //bootstrap
  @extend .btn-danger; //bootstrap
  @include button-template($font-size);
}

// 青色ボタン
@mixin button-blue($font-size:14) {
  @extend .btn; //bootstrap
  @extend .btn-primary; //bootstrap
  @include button-template($font-size);
}

// 緑色ボタン
@mixin button-green($font-size:14) {
  @extend .btn; //bootstrap
  @extend .btn-success; //bootstrap
  @include button-template($font-size);
}

// グレーボタン
@mixin button-gray($font-size:14) {
  @extend .btn; //bootstrap
  @extend .btn-secondary; //bootstrap
  @include button-template($font-size);
}

// 白色ボタン
@mixin button-light($font-size:14) {
  @extend .btn; //bootstrap
  @extend .btn-light; //bootstrap
  @include button-template($font-size);
}

// 赤色ボタン（小）
@mixin button-red-mini($font-size:14) {
  @extend .btn; //bootstrap
  @extend .btn-danger; //bootstrap
  @include button-mini-template($font-size);
}

// 青色ボタン（小）
@mixin button-blue-mini($font-size:14) {
  @extend .btn; //bootstrap
  @extend .btn-primary; //bootstrap
  @include button-mini-template($font-size);
}

// グレーボタン（小）
@mixin button-gray-mini($font-size:14) {
  @extend .btn; //bootstrap
  @extend .btn-secondary; //bootstrap
  @include button-mini-template($font-size);
}
// 白色ボタン（小）
@mixin button-light-mini($font-size:14) {
  @extend .btn; //bootstrap
  @extend .btn-light; //bootstrap
  @include button-mini-template($font-size);
}

