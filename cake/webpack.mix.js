let mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

mix.setPublicPath('webroot')
    .js('assets/js/app.js', 'js')
    .js('assets/js/common.js', 'js')
    .js('assets/js/common-mobile.js', 'js')
    .js('assets/js/admin-common.js', 'js')
    .js('assets/js/admin-users.js', 'js')
    .js('assets/js/admin-news.js', 'js')
    .js('assets/js/admin-product.js', 'js')
    .js('assets/js/admin-companies.js', 'js')
    .js('assets/js/admin-order-headers.js', 'js')
    .js('assets/js/admin-order-list.js', 'js')
    .js('assets/js/order-list.js', 'js')
    .js('assets/js/order-list-mobile.js', 'js')
    .js('assets/js/order-history.js', 'js')
    .js('assets/js/order-history-details.js', 'js')
    .js('assets/js/order-delivery.js', 'js')
    .js('assets/js/order-delivery-details.js', 'js')
    .js('assets/js/order-invoice.js', 'js')
    .js('assets/js/order_complete.js', 'js')
    .js('assets/js/order-confirm.js', 'js')
    .js('assets/js/order-error.js', 'js')
    .js('assets/js/order-sort.js', 'js')
    .js('assets/js/register.js', 'js')
    .js('assets/js/businessman.js', 'js')
    .js('assets/js/clear-ls.js', 'js')
    .js('assets/js/add-data-ls.js', 'js')
    // .sass('assets/sass/app.scss', 'css')
    .sass('assets/sass/style_tablet.scss', 'css')
    .sass('assets/sass/style_mobile.scss', 'css')
    .sass('assets/sass/style_tablet_act.scss', 'css')
    .sass('assets/sass/style_mobile_act.scss', 'css')
    .sass('assets/sass/style_admin.scss', 'css')
    .sourceMaps(false);

if (mix.inProduction()) {
    mix.version();
} else {
    mix.sourceMaps()
        // .webpackConfig({devtool: 'source-map'});
        .webpackConfig({devtool: 'inline-source-map'});
}
