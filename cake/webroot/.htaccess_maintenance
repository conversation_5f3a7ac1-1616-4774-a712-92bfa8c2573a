### メンテナンス用の.htaccess設定
## BMC、アクト中食： Basic認証を表示。Basic認証をパスすると通常操作可能
## 一般ユーザ： メンテナンスページを表示

# .htaccess/.htpasswdのアクセス制限
<Files ~ "^\.(htaccess|htpasswd)$">
Deny from all
</Files>

# Basic認証
AuthType Basic
AuthUserFile /var/www/html/act-aws/001/.htpasswd
AuthGroupFile /dev/null
AuthName "Please enter your ID and password"
Require valid-user

# Basic認証をかけるIPアドレス
Satisfy any
order allow,deny
allow from all
## 制限先を増やす場合は下記行を追加
deny from **************
deny from ***************
deny from ***************

# メンテナンス画面を表示
ErrorDocument 503 /001/maintenance/001.html

<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{REQUEST_URI} !=/001/maintenance/001.html
    ## 制限先を増やす場合は下記行を追加
    RewriteCond %{REMOTE_ADDR} !=**************
    RewriteCond %{REMOTE_ADDR} !=***************
    RewriteCond %{REMOTE_ADDR} !=***************
    RewriteCond %{REQUEST_FILENAME} !\.(css|jpe?g|gif|png|js)$
    RewriteRule ^.*$ - [R=503,L]

    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

#session
php_value session.gc_maxlifetime 86400
php_value session.gc_probability 100

#メモリ使用量の上限
php_value memory_limit 128M
#POSTデータの最大サイズ　※合計サイズ
php_value post_max_size 128M

#1ファイルあたりの最大アップロードサイズ
php_value upload_max_filesize 128M
