#!/bin/bash

# <PERSON><PERSON><PERSON> để renew SSL certificate
# Chạy script này định kỳ để renew certificate

# <PERSON><PERSON><PERSON> sắc cho output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== SSL Certificate Renewal ===${NC}"
echo -e "${YELLOW}$(date): Starting certificate renewal...${NC}"

# Renew certificates
echo -e "${YELLOW}### Renewing SSL certificates ...${NC}"
docker-compose run --rm certbot renew --quiet

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Certificate renewal successful${NC}"
    
    # Reload nginx
    echo -e "${YELLOW}### Reloading nginx ...${NC}"
    docker-compose exec nginx nginx -s reload
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Nginx reloaded successfully${NC}"
        echo -e "${GRE<PERSON>}$(date): SSL certificate renewal completed!${NC}"
    else
        echo -e "${RED}✗ Failed to reload nginx${NC}"
        exit 1
    fi
else
    echo -e "${RED}✗ Certificate renewal failed${NC}"
    exit 1
fi
