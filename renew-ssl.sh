#!/bin/bash

# <PERSON><PERSON><PERSON> để renew SSL certificate
# Chạy script n<PERSON><PERSON> định kỳ để renew certificate

# <PERSON><PERSON><PERSON> sắc cho output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Detect docker-compose command
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    echo -e "${RED}Error: Neither 'docker-compose' nor 'docker compose' found${NC}"
    exit 1
fi

echo -e "${YELLOW}=== SSL Certificate Renewal ===${NC}"
echo -e "${YELLOW}$(date): Starting certificate renewal...${NC}"

# Renew certificates
echo -e "${YELLOW}### Renewing SSL certificates ...${NC}"
$DOCKER_COMPOSE run --rm certbot renew --quiet

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Certificate renewal successful${NC}"

    # Reload nginx
    echo -e "${YELLOW}### Reloading nginx ...${NC}"
    $DOCKER_COMPOSE exec nginx nginx -s reload
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Nginx reloaded successfully${NC}"
        echo -e "${GREEN}$(date): SSL certificate renewal completed!${NC}"
    else
        echo -e "${RED}✗ Failed to reload nginx${NC}"
        exit 1
    fi
else
    echo -e "${RED}✗ Certificate renewal failed${NC}"
    exit 1
fi
